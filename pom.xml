<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.atour.hotel</groupId>
        <artifactId>hotel-manage-parent</artifactId>
        <version>1.0.26.RELEASE</version>
        <relativePath>./hotel-manage-parent</relativePath>
    </parent>

    <artifactId>hotel-manage-app-api</artifactId>
    <packaging>war</packaging>

    <name>hotel-manage-app-api</name>
    <description>hotel-manage-app-api</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <hotel-manage-api.version>1.0.26.RELEASE</hotel-manage-api.version>
    </properties>

    <dependencies>



        <!-- 日志组件start -->
        <dependency>
            <groupId>com.yaduo.log</groupId>
            <artifactId>log-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yaduo.log</groupId>
            <artifactId>log-client</artifactId>
        </dependency>



        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-acl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yaduo.log</groupId>
            <artifactId>log-springmvc</artifactId>
        </dependency>

        <!-- 日志组件end -->

        <dependency>
            <groupId>com.yaduo.owner</groupId>
            <artifactId>owner-biz-api</artifactId>
            <version>1.0.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.atour.hotel</groupId>
            <artifactId>hotel-manage-api</artifactId>
            <version>${hotel-manage-api.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.yaduo.chainbiz</groupId>
            <artifactId>chain-biz-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour.bigdata.bi.gateway</groupId>
            <artifactId>bigdata-bi-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.louislivi.fastdep</groupId>
            <artifactId>fastdep-redis</artifactId>
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-redis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>1.2</version> <!-- 指定正确的版本 -->
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.0.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
            </exclusions> <!-- 或更高版本3.4.x -->
        </dependency>


        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>atour-hlm-api</artifactId>
            <version>1.3.22</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kotlin-stdlib-common</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour.task</groupId>
            <artifactId>task-center-biz-api</artifactId>
            <version>1.0.2.jdk8.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.yaduo</groupId>
            <artifactId>fusion-sentinel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>concurrentlinkedhashmap-lru</artifactId>
                    <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yaduo.infras</groupId>
            <artifactId>tomcat-skywalking-logger</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-core -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.22</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>2.6.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>questionnaire-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>data-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>franchise-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tika-core</artifactId>
                    <groupId>org.apache.tika</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour.asso</groupId>
            <artifactId>asso-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>pay-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>crs-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-web</artifactId>
            <version>${atour-root.version}</version>
        </dependency>

        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-concurrency</artifactId>
            <version>${atour-root.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dypnsapi</artifactId>
        </dependency>


        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>notify-center-api</artifactId>
        </dependency>
        <!--atour-root包-->
        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>galaxy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>order-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.atour</groupId>
                    <artifactId>galaxy-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>rbac-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>atour-dicts</artifactId>
                    <groupId>com.atour</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>atour-web</artifactId>
                    <groupId>com.atour</groupId>
                </exclusion>
            </exclusions>
            <version>${rbac-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yaduo.dcms</groupId>
            <artifactId>dcms-service-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tika</groupId>
                    <artifactId>tika-core</artifactId>
                </exclusion>
            </exclusions>
            <version>${dcms-service-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yaduo.dcms</groupId>
            <artifactId>dcms-service-api-enum</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tika</groupId>
                    <artifactId>tika-core</artifactId>
                </exclusion>
            </exclusions>
            <version>${dcms-service-api.version}</version>
        </dependency>
        <!--monitor-->
        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-monitor</artifactId>
        </dependency>
        <!-- 数据字典 -->
        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-dicts</artifactId>
        </dependency>
        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.atour</groupId>
                    <artifactId>galaxy-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.atour</groupId>
                    <artifactId>galaxy-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>easyexcel</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.atour.root</groupId>
            <artifactId>atour-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>security-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- chain-center -->
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>chain-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.atour.eureka</groupId>
            <artifactId>eureka-spring-consumer-starter</artifactId>
            <version>${eureka-spring.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.atour.eureka</groupId>
            <artifactId>eureka-spring-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-ribbon</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rxjava</artifactId>
                    <groupId>io.reactivex</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.netflix.ribbon</groupId>
            <artifactId>ribbon-eureka</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-security-rsa</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>jedis</artifactId>
            <groupId>redis.clients</groupId>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
        </dependency>

        <!-- google gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- mybatis依赖相关包 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <!--jdbc driver -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <!--mysql driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!--swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- slf4j-api -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>db-helper-api</artifactId>
        </dependency>
        <!-- pms remote api -->
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>pms-provider-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>commons-fileupload</artifactId>
            <groupId>commons-fileupload</groupId>
            <version>${commons-fileupload.version}</version>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>corp-center-api</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>com.atour</groupId>
            <artifactId>application-oss-api</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.yaduo.resource</groupId>
            <artifactId>resource-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tika-core</artifactId>
                    <groupId>org.apache.tika</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yaduo.infras</groupId>
            <artifactId>core-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>apm-toolkit-trace</artifactId>
                    <groupId>org.apache.skywalking</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.yaduo.infras</groupId>-->
<!--            <artifactId>core-lib</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>okhttp</artifactId>-->
<!--                    <groupId>com.squareup.okhttp3</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>spring-cloud-starter-loadbalancer</artifactId>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>spring-cloud-loadbalancer</artifactId>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>corp-wechat-api</artifactId>
            <version>${wechat-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.vanroy</groupId>
            <artifactId>spring-data-jest</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- orika -->
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.4.6</version>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>activiti-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
            <version>1.0-alpha1</version>
        </dependency>

        <dependency>
            <groupId>com.github.jai-imageio</groupId>
            <artifactId>jai-imageio-core</artifactId>
            <version>1.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-sleuth-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>pms-biz-common</artifactId>
            <version>${pms-biz-common.version}</version>
        </dependency>

        <!-- 库存系统 -->
        <dependency>
            <groupId>com.atour</groupId>
            <artifactId>inventory-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${easypoi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cglib</groupId>
                    <artifactId>cglib</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yaduo.user</groupId>
            <artifactId>app-repository-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yaduo.hotel.task.center</groupId>
            <artifactId>hotel-task-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yaduo.infras</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yaduo.infras</groupId>
            <artifactId>message-springmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yaduo.flow</groupId>
            <artifactId>flow-base-api</artifactId>
        </dependency>
    </dependencies>

    <profiles>

        <profile>
            <!-- test环境 -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>utf-8</encoding>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
