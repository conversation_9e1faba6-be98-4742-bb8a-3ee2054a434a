启动参数 输出日志到控制台
-Dlog_to_console=local

## filters
``` text
 corsFilter org.springframework.web.filter.CorsFilter
 characterEncodingFilter org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter
 hotelLogFilter com.atour.hotel.framework.filter.HotelLogFilter 禁用
 webMvcMetricsFilter org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter
 tracingFilter brave.servlet.TracingFilter
 httpLogFilter com.atour.web.filter.RequestResponseInfoLogFilterV2
 exceptionLoggingFilter org.springframework.cloud.sleuth.instrument.web.ExceptionLoggingFilter
 responseTraceFilter com.atour.web.filter.ResponseTraceFilter
 hiddenHttpMethodFilter org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter
 httpPutFormContentFilter org.springframework.boot.web.servlet.filter.OrderedHttpPutFormContentFilter
 requestContextFilter org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter
 assoAutoLoginFilter org.springframework.web.filter.DelegatingFilterProxy
 authFilter org.springframework.web.filter.DelegatingFilterProxy
 hotelOwnerAuthFilter org.springframework.web.filter.DelegatingFilterProxy
 assoFilter org.springframework.web.filter.DelegatingFilterProxy
 httpTraceFilter org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter
 rbacFilter com.atour.hotel.framework.filter.RbacFilter
 webStatFilter com.alibaba.druid.support.http.WebStatFilter
 Tomcat WebSocket (JSR356) Filter org.apache.tomcat.websocket.server.WsFilter
```