package com.atour.hotel;

import com.alibaba.fastjson.JSON;
import com.atour.hotel.common.page.PageRequest;
import com.atour.hotel.common.page.Pager;
import com.atour.hotel.framework.configuration.HotelApolloConfig;
import com.atour.hotel.module.energy.biz.CostDayReportBiz;
import com.atour.hotel.module.energy.biz.CostMonthReportBiz;
import com.atour.hotel.module.energy.biz.OtherBiz;
import com.atour.hotel.module.energy.dto.CostDayReportQueryDTO;
import com.atour.hotel.module.energy.dto.CostMonthReportQueryDTO;
import com.atour.hotel.module.energy.dto.OtherHeatPriceRestructureDTO;
import com.atour.hotel.module.energy.request.other.OtherSaveParam;
import com.atour.hotel.module.energy.request.report.CostDayParam;
import com.atour.hotel.module.energy.request.report.CostMonthParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ApollpTests {

    static {
        System.setProperty("catalina.base", "/tmp");
    }

    @Resource
    private HotelApolloConfig hotelApolloConfig;

    @Test
    public void contextLoads() {
        System.out.println(hotelApolloConfig.getEnergyReportConfigBean()
            .canEnergyReport(114));
    }

}
