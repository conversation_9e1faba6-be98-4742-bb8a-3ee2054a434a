package com.atour.hotel.module.workOrder;

import com.atour.hotel.BaseTest;
import com.atour.hotel.common.util.CommonParamsDTO;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.workorder.param.AddWorkOrderParam;
import com.atour.hotel.module.workorder.service.WorkOrderService;
import com.atour.rbac.api.response.UserPermissionDTO;
import org.assertj.core.util.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2020/9/27
 */
public class WorkOrderServiceTest extends BaseTest {

    @Resource
    private WorkOrderService workOrderService;

    @Test
    public void testAdd() {

        final CommonParamsDTO commonParamsDTO = new CommonParamsDTO();
        UserPermissionDTO userPermissionDTO = new UserPermissionDTO();
        userPermissionDTO.setUserId(3508270);
        commonParamsDTO.setUserPermissionDTO(userPermissionDTO);
        CommonParamsManager.set(commonParamsDTO);
        final AddWorkOrderParam addWorkOrderParam = new AddWorkOrderParam();
        addWorkOrderParam.setChainId(114);
        addWorkOrderParam.setContent("ffff");
        addWorkOrderParam.setImageUrls(Collections.emptyList());
        addWorkOrderParam.setTypeId(23);
        workOrderService.addWorkOrder(addWorkOrderParam);
    }

}
