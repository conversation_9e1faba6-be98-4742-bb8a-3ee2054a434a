package com.atour.hotel.module.email.service;

import com.atour.hotel.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-09-06 17:40
 */
public class EmailServiceTest extends BaseTest {

    @Resource
    private EmailService emailService;
    @Test
    public void sendMailByRemote() throws InterruptedException {
        emailService.sendMailByRemote("这是一封日常邮件提醒", "份额阿富汗份额阿哥阿哥阿嘎份额阿发饿啊", "<EMAIL>", "");
        while (true) {
            Thread.sleep(1000);
            System.out.println("1");
        }
    }
}