package com.atour.hotel.module.message;

import com.atour.hotel.BaseTest;
import com.atour.hotel.module.school.request.StudyTaskParam;
import com.atour.hotel.module.school.service.StudyTaskService;
import com.atour.utils.ObjectUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/2
 */
@Slf4j
public class StudyTaskServiceTest extends BaseTest {

    @Resource
    private StudyTaskService studyTaskService;

    @Test
    @SneakyThrows
    public void testPushOwnerStudyTaskMessage() {

        String json = "{\n" + "  \"userId\": \"owner_account_508\",\n" + "  \"taskId\": 100,\n"
            + "  \"url\": \"http://jmxt-h5.yaduo.com/transfer.aspx\",\n" + "  \"title\": \"测试标题\",\n"
            + "  \"startTime\": \"2020-05-25 00:00:00\",\n" + "  \"endTime\": \"2021-05-31 23:59:59\",\n" + "  \"state\": 1,\n"
            + "  \"taskType\": 7\n" + "}";

        studyTaskService.pushOwnerStudyTaskMessage(Lists.newArrayList(ObjectUtil.fromJson(json, StudyTaskParam.class)));
    }

}
