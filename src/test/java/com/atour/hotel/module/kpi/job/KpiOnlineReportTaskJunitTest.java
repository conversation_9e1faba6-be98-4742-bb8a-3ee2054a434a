package com.atour.hotel.module.kpi.job;

import com.alibaba.fastjson.JSON;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum;
import com.atour.hotel.module.kpi.utils.KpiTimeUtil;
import com.atour.hotel.module.kpi.utils.entity.KpiTimeEntity;
import com.atour.rbac.api.response.KpiOnlineCareerTrackDTO;
import com.atour.utils.DateUtil;
import com.atour.utils.StringUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-02-21 11:09
 */
public class KpiOnlineReportTaskJunitTest{

    @Test
    public void execute() throws Exception {
        KpiOnlineReportTask task = new KpiOnlineReportTask();
        LocalDate localDate = StringUtil.isEmpty("2022-03-14")
                ? LocalDate.now().plusDays(-1L)
                : CommonUtils.asLocalDate(DateUtil.parseDatetime("2022-02-28", DateUtil.DATE_FORMAT));

        KpiTimeEntity kpiTimeEntity = KpiTimeUtil.getKpiTimeEntity(localDate);
        KpiOnlineCareerTrackDTO kpiOnlineCareerTrackDTO = JSON.parseObject("{\"id\":1835,\"employeeId\":\"WZ2017080804\",\"flowerName\":\"徽因\",\"chainId\":340108,\"post\":\"现长\",\"startDate\":\"2022-03-01 00:00:00\",\"endDate\":\"2022-03-05 00:00:00\",\"syncTime\":\"2022-03-09 23:10:07\",\"createTime\":\"2022-03-04 23:15:14\",\"updateTime\":\"2022-03-09 23:15:22\",\"deleted\":0,\"syncId\":31520,\"postId\":\"Lx3\"}",
                KpiOnlineCareerTrackDTO.class);
        Pair<Date, Date> startAndEndDate = task.getStartAndEndDate(kpiOnlineCareerTrackDTO, localDate, kpiTimeEntity, KpiPeriodTypeEnum.MONTH);
        System.out.println(startAndEndDate);
        startAndEndDate = task.getStartAndEndDate(kpiOnlineCareerTrackDTO, localDate, kpiTimeEntity, KpiPeriodTypeEnum.QUARTER);
        System.out.println(startAndEndDate);

        Date startDate = startAndEndDate.getLeft();
        Date endDate = startAndEndDate.getRight();
        int allTimeDays = Objects.nonNull(startDate) && Objects.nonNull(endDate) ? DateUtil.betweenDays(startDate, endDate).size() : 0;
        System.out.println(allTimeDays);
    }
}
