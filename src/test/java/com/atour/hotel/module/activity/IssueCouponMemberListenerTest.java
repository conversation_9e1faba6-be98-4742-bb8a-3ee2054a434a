package com.atour.hotel.module.activity;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.Message;
import com.atour.hotel.BaseTest;
import com.atour.hotel.framework.mq.listener.IssueCouponMemberListener;
import com.atour.mq.bean.param.user.CouponParam;
import com.atour.mq.bean.param.user.IssueCouponParam;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
public class IssueCouponMemberListenerTest extends BaseTest {

    @Resource
    private IssueCouponMemberListener issueCouponMemberListener;

    @Test
    public void test() {

        final IssueCouponParam issueCouponParam = new IssueCouponParam();
        issueCouponParam.setOperateTime(DateUtil.getCurrentDateTime());
        issueCouponParam.setActivityId(440);
        issueCouponParam.setMemberId(1);

        final CouponParam couponParam = new CouponParam();
        couponParam.setActivityId(440);
        couponParam.setAuditCode("text");
        couponParam.setCouponId(4L);
        final CouponParam couponParam2 = new CouponParam();
        couponParam2.setActivityId(440);
        couponParam2.setAuditCode("textdd");
        couponParam2.setCouponId(9L);
        issueCouponParam.setCoupons(Sets.newHashSet(couponParam, couponParam2));

        final Message message = new Message();
        message.setBody(ObjectUtil.toJsonQuietly(issueCouponParam).getBytes());
        final Action result = issueCouponMemberListener.consume(message, null);
        Assert.assertEquals(Action.CommitMessage, result);
    }

}
