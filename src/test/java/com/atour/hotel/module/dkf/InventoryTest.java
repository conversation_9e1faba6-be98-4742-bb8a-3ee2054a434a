package com.atour.hotel.module.dkf;

import com.atour.hotel.module.dkf.web.hotel.request.CommonSuppliesInventoryParam;
import com.atour.hotel.module.dkf.web.hotel.request.GuestSuppliesInventoryListReq;
import com.atour.hotel.module.dkf.web.hotel.response.GuestSuppliesInventoryListResponse;
import com.atour.hotel.module.dkf.web.hotel.response.GuestSuppliesInventoryResponse;
import com.atour.hotel.module.dkf.web.hotel.response.StorageAddResponse;
import com.atour.hotel.module.dkf.web.hotel.service.GuestSuppliesInventoryService;
import com.atour.hotel.module.dkfb2b.job.B2BStockInSyncTask;
import com.atour.hotel.persistent.hotel.dao.ConsumerGoodsConsumptionDao;
import com.atour.hotel.persistent.hotel.entity.ConsumerGoodsConsumptionEntity;
import com.atour.hotel.persistent.ops.dao.GuestSuppliesConfigDao;
import com.atour.hotel.persistent.roommanage.dao.GuestSuppliesMonthInventoryDao;
import com.atour.hotel.persistent.roommanage.implforaop.GuestSuppliesMonthInventoryServiceImplForAop;
import joptsimple.internal.Strings;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019/12/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class InventoryTest {

    @Resource
    private GuestSuppliesInventoryService guestSuppliesInventoryService;
    @Resource
    private ConsumerGoodsConsumptionDao consumerGoodsConsumptionDao;

    @Resource
    private GuestSuppliesMonthInventoryServiceImplForAop guestSuppliesMonthInventoryServiceImplForAop;


    @Resource
    private GuestSuppliesMonthInventoryDao guestSuppliesMonthInventoryDao;

    @Resource
    private GuestSuppliesConfigDao guestSuppliesConfigDao;

    @Resource
    private B2BStockInSyncTask b2BStockInSyncTask;

    static {
        System.setProperty("catalina.base", "/tmp");
    }

    @Test
    public void test() {

        GuestSuppliesInventoryResponse guestSuppliesInventoryResponse = guestSuppliesInventoryService.listInventory(
            GuestSuppliesInventoryListReq.builder()
                .chainId(114)
                .inventoryMonth("2019-5")
                .build());

        List<StorageAddResponse> storageAddResponses = guestSuppliesInventoryService.storageAdd(CommonSuppliesInventoryParam.builder().build());

        Stream<GuestSuppliesInventoryListResponse> sorted = guestSuppliesInventoryResponse.getGuestList()
            .stream()
            .sorted(Comparator.comparing(GuestSuppliesInventoryListResponse::getGuestSuppliesId));

        sorted.forEach(x -> System.out.println(x));
        storageAddResponses.forEach(x -> System.out.println(x));
    }

    @Test
    public void selectConsumer() {
        List<ConsumerGoodsConsumptionEntity> consumerGoodsConsumptionEntities =
            consumerGoodsConsumptionDao.selectByDateRangeAndType(114, LocalDate.of(2019, 10, 1),
                LocalDate.of(2019, 10, 31), 1);
        System.out.println(consumerGoodsConsumptionEntities);
    }


    @Test
    public void stockIn() throws Exception {
        b2BStockInSyncTask.execute(Strings.EMPTY);
    }
}
