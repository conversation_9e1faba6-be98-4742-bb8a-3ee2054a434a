package com.atour.hotel.module.ue.manage.comment.wrapper;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.atour.hotel.module.ue.manage.comment.dto.CommonPolarityDTO;
import com.atour.utils.StringUtil;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022-04-22 16:47
 */
@Slf4j
public class HuiPingCommentWrapperTest extends TestCase {

    /**
     * 同步的返回，不推荐使用，如果数据量大会把数据放到内存里面
     */
    @Test
    public void testGetColorfulComment() {
        String fileName = "/Users/<USER>/Downloads/执行结果e.xlsx";
        // 这里 也可以不指定class，返回一个list，然后读取第一个sheet 同步读取会自动finish
        List<Map<Integer, String>> listMap = EasyExcel.read(fileName).sheet().doReadSync();
        for (Map<Integer, String> data : listMap) {
            if (StringUtil.isEmpty(data.get(0))) {
                continue;
            }
            // 返回每条数据的键值对 表示所在的列 和所在列的值
            List<CommonPolarityDTO> commonPolarityDTOList;
            try {
                commonPolarityDTOList = JSON.parseArray(data.get(1), CommonPolarityDTO.class);
            } catch (Exception e) {
                continue;
            }
            Optional<Integer> max = commonPolarityDTOList.stream().map(CommonPolarityDTO::getStartIndex).max(Comparator.comparing(Function.identity()));
            log.info("{} 读取到数据: max_start_length:{},comment_length:{},{}，{}",data.get(2), max, data.get(0).length(), data.get(1), data.get(0));
            String colorfulComment = null;
            try {
                colorfulComment = HuiPingCommentWrapper.getColorfulComment(data.get(1), data.get(0));
            } catch (Exception e) {
                log.error("",e);
            }
            log.info(colorfulComment);
        }
    }
}