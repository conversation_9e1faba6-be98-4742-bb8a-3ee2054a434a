package com.atour.hotel.module.ue.job;

import com.atour.hotel.BaseTest;
import com.atour.hotel.module.ue.manage.comment.job.CommentLengthJob;
import com.atour.hotel.module.ue.manage.comment.job.EmotionAnalysisJob;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/8/4
 */
public class EmotionAnalysisJobTest extends BaseTest {

    @Resource
    private EmotionAnalysisJob emotionAnalysisJob;

    @Test
    @SneakyThrows
    public void test() {

        final ReturnT<String> result = emotionAnalysisJob.execute("force=true");
        Assert.assertEquals(result, ReturnT.SUCCESS);
    }

}
