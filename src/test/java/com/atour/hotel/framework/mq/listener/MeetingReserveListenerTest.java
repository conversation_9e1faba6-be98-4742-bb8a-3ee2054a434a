package com.atour.hotel.framework.mq.listener;

import com.atour.hotel.BaseTest;
import com.atour.hotel.persistent.atourapp.entity.MeetingRoomReservationEntity;
import com.atour.rbac.api.response.UserDTO;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

public class MeetingReserveListenerTest extends BaseTest {

    @Resource
    private MeetingReserveListener meetingReserveListener;

    @Test
    public void testSendCorpWechatAndEmail() {
        UserDTO userDTO = new UserDTO();
        userDTO.setEmail("<EMAIL>");

        //MeetingRoomReservationEntity reservationEntity = reservationDAO.selectById(26L);
        //meetingReserveListener.sendCorpWechatAndEmail( Lists.newArrayList(userDTO), null);

    }
}