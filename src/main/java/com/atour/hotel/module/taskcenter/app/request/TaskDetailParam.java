package com.atour.hotel.module.taskcenter.app.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * 任务详情
 *
 * <AUTHOR>
 * @date 2020年7月27日11:16:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TaskDetailParam", description = "任务详情")
public class TaskDetailParam {

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    @NotNull(message = "酒店Id不能为空")
    private Integer chainId;

    /**
     * 主键id
     **/
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Integer id;

    /**
     * 任务id
     **/
    @ApiModelProperty(value = "任务id")
    @NotNull(message = "任务id不能为空")
    private Long taskId;

    /**
     * 任务版本
     **/
    @ApiModelProperty(value = "任务版本")
    @NotNull(message = "任务版本不能为空")
    private Integer version;

}
