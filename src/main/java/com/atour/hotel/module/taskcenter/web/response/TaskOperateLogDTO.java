package com.atour.hotel.module.taskcenter.web.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务操作日志
 *
 * <AUTHOR>
 *@date 2020年7月27日11:16:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TaskOperateLogDTO", description = "任务操作日志")
public class TaskOperateLogDTO implements Serializable {

    /**
     * 操作人
     **/
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 创建时间 yyyy-MM-dd HH:mm:ss
     **/
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 操作内容
     **/
    @ApiModelProperty(value = "操作内容")
    private String content;

    /**
     * 是否为发布操作：true是，false否
     **/
    @ApiModelProperty(value = "是否为发布操作：true是，false否")
    private Boolean isRelease;

    /**
     * 任务id
     **/
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    /**
     * 版本号
     **/
    @ApiModelProperty(value = "版本号")
    private Integer version;


}
