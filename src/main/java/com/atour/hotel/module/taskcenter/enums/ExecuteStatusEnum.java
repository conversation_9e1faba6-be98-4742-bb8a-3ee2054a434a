package com.atour.hotel.module.taskcenter.enums;

import java.util.Objects;

/**
 * 任务执行状态 枚举
 *
 * <AUTHOR>
 * @date 2020年7月28日14:28:52
 */
public enum ExecuteStatusEnum {

    /**
     * 待执行
     */
    WAIT(0, "待执行"),
    /**
     * 已完成
     */
    DONE(1, "已完成"),
    /**
     * 已过期 (只用于展示)
     */
    OVERDUE(2, "已过期");

    private Integer value;

    private String desc;

    ExecuteStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value
     * @return
     */
    public static ExecuteStatusEnum getInstance(Integer value) {

        for (ExecuteStatusEnum obj : ExecuteStatusEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }
}
