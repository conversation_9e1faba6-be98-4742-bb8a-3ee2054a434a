package com.atour.hotel.module.taskcenter.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 任务完成信息
 *
 * <AUTHOR>
 * @date 2020年7月27日11:16:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TaskFinishDTO", description = "任务完成信息")
public class TaskFinishDTO {

    /**
     * 主键id
     **/
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 任务id
     **/
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    /**
     * 任务版本
     **/
    @ApiModelProperty(value = "任务版本")
    private Integer version;

    /**
     * 任务类型：0一次性任务、1日任务、2周任务、3月任务、4季任务
     *
     * @see com.atour.hotel.module.taskcenter.enums.TaskTypeEnum
     */
    @ApiModelProperty(value = "任务类型：0一次性任务、1日任务、2周任务、3月任务、4季任务")
    private Integer taskType;

    /**
     * 执行周期-开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "执行周期-开始时间  yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executeStartTime;

    /**
     * 执行周期-结束时间  yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "执行周期-结束时间  yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executeEndTime;

    /**
     * 可修改结束时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "执行周期-结束时间  yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyEndTime;


    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String title;

    /**
     * 任务执行状态：0待执行、1已完成、2已过期
     * @see com.atour.hotel.module.taskcenter.enums.ExecuteStatusEnum
     */
    @ApiModelProperty(value = "任务执行状态：0待执行、1已完成、2已过期")
    private Integer executeStatus;

    /**
     * 任务子状态： 0未做，1已做，2修改
     * @see com.atour.hotel.module.taskcenter.enums.ExecuteSubstateEnum
     */
    @ApiModelProperty(value = "任务子状态： 0未做，1已做，2修改")
    private Integer executeSubstate;

    /**
     * 任务等级:1普通、2重要
     *
     * @see com.atour.hotel.module.taskcenter.enums.TaskGradeEnum
     */
    @ApiModelProperty(value = "任务等级:1普通、2重要")
    private Integer grade;

    /**
     * 可执行岗位json
     *
     */
    @ApiModelProperty(value = "可执行岗位json")
    private String jobJson;

    /**
     * 是否允许完成：true是，false否
     */
    @ApiModelProperty(value = "是否允许完成：true是，false否")
    private Boolean isAllowFinish;


    /**
     * 任务完成时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "任务完成时间  yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;


}
