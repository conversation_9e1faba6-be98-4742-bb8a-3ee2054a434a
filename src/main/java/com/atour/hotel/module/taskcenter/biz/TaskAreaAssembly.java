package com.atour.hotel.module.taskcenter.biz;

import com.atour.hotel.module.common.service.RegionChainService;
import com.atour.hotel.module.taskcenter.enums.TaskGranularityEum;
import com.atour.hotel.module.taskcenter.web.request.TaskSumUpParam;
import com.atour.hotel.module.taskcenter.web.response.DataNumDTO;
import com.atour.hotel.module.taskcenter.web.response.TaskPercentageDTO;
import com.atour.hotel.module.taskcenter.web.service.TaskSumUpService;
import com.atour.hotel.persistent.roommanage.dao.TaskFinishInfoMapper;
import com.atour.rbac.api.param.GetRegionBChainIdRequest;
import com.atour.rbac.api.response.RegionChainDto;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目名称：hotel-manage-app-api
 * 类 名 称：TaskAreaAssembly
 * 类 描 述：区域数据组装
 * 创建时间：2020-09-02 9:39 下午
 * 创 建 人：杨过
 */
@Service
public class TaskAreaAssembly implements TaskAssembly{

    @Resource
    private TaskSumUpService taskSumUpService;
    @Resource
    private RegionChainService regionChainService;
    @Resource
    private TaskFinishInfoMapper taskFinishInfoMapper;

    @Override
    public TaskGranularityEum support() {
        return TaskGranularityEum.TASK_AREA;
    }

    @Override
    public List<TaskPercentageDTO> assenblyData(TaskSumUpParam param) {
        List<TaskPercentageDTO> resultList = Lists.newArrayList();
        GetRegionBChainIdRequest regionBChainIdRequest = new GetRegionBChainIdRequest();
        regionBChainIdRequest.setChainId(param.getChainIds());
        Map<String, List<Integer>>  regionChainMap = regionChainService.getMapChainIdsByDeptId(param.getChainIds());
        //总任务个数
        List<DataNumDTO> totalList =  taskFinishInfoMapper.getTaskTotal(param);
        if(MapUtils.isEmpty(regionChainMap) || CollectionUtils.isEmpty(totalList)){
            return resultList;
        }
        //完成任务个数
        List<DataNumDTO> finishList = taskFinishInfoMapper.getTaskFinish(param);

        regionChainMap.forEach((regionId, chainIds) -> {
            List<DataNumDTO> tempTotalList = totalList.stream()
                .filter(f -> chainIds.contains(f.getChainId()))
                .collect(Collectors.toList());
            List<DataNumDTO> tempFinishList = Safes.of(finishList)
                .stream()
                .filter(f -> chainIds.contains(f.getChainId()))
                .collect(Collectors.toList());

            Map<String, List<DataNumDTO>> taskTotalMap = tempTotalList.stream()
                .collect(Collectors.groupingBy(DataNumDTO::getTaskId));
            Map<String, List<DataNumDTO>> taskFinishMap = tempFinishList.stream()
                .collect(Collectors.groupingBy(DataNumDTO::getTaskId));

            taskTotalMap.forEach((taskId, total) -> {
                int totalNum = Safes.of(total)
                    .stream()
                    .mapToInt(DataNumDTO::getTaskNum)
                    .sum();
                int finishNum = Safes.of(taskFinishMap.get(taskId))
                    .stream()
                    .mapToInt(DataNumDTO::getTaskNum)
                    .sum();
                resultList.add(taskSumUpService.calculationLogic(TaskPercentageDTO.builder()
                    .taskId(taskId)
                    .areaId(Integer.valueOf(regionId))
                    .chainId(total.get(NumberUtils.INTEGER_ZERO)
                        .getChainId())
                    .build(), totalNum, finishNum));
            });

        });
        return resultList;
    }

}
