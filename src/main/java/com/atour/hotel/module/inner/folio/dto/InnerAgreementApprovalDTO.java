package com.atour.hotel.module.inner.folio.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 协议价格审批 申请编号等相关信息实体类
 * 用于移动端app调用到oms后端查询接口查询时返回，用于移动端获取审批列表
 *
 * <AUTHOR>
 * @date 2021/12/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InnerAgreementApprovalDTO implements Serializable {

    /**
     * 主键id
     **/
    private Long id;

    /**
     * 申请id
     **/
    private String requestId;

    /**
     * 员工编号
     **/
    private String employeeId;

    /**
     * 申请人id
     **/
    private Integer userId;

    /**
     * 房单id
     **/
    private Long folioId;

    /**
     * 酒店id
     **/
    private Integer chainId;

    /**
     * 审批类型 1-重新开账、2-房费调整、3-特殊退款、4-冲减、5-转账、0-其他 FolioTransApproveTypeEnum
     **/
    private Integer approveType;

    /**
     * 审批状态 1-待审批、2-审批通过、3-审批拒绝、4-已撤回、5-待提交、0-其他 ApproveStateEnum
     **/
    private Integer processState;

    /**
     * 申请流程id
     **/
    private String processId;

    /**
     * 流程定义key
     **/
    private String processDefinitionKey;

    /**
     * 申请原因
     **/
    private String reason;

    /**
     * 授权实体类型 1-用户ID(employeeId)、2-角色ID、0-其他
     **/
    private Integer authEntityType;

    /**
     * 授权实体id
     **/
    private String authEntityId;

    /**
     * 授权时间
     **/
    private Date authTime;

    /**
     * 授权过期时间
     **/
    private Date expiryTime;

    /**
     * 记录创建时间
     **/
    private Date createTime;

    /**
     * 记录更新时间
     **/
    private Date updateTime;

    /**
     * 软删除标记, 0未删除, 1已经删除
     **/
    private Integer deleted;

    /**
     * 扩展字段
     **/
    private String extraContent;

    /**
     * 授权状态 1-未授权、2-已授权、3-已过期
     *
     * @see com.atour.dicts.db.atour_pms.ApproveAuthStateEnum
     **/
    private Integer authState;

    /**
     * 流程最后审批人id,系统填0
     **/
    private String lastApproveId;
}
