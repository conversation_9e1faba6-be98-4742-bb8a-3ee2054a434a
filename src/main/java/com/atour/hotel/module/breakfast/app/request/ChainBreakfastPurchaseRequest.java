package com.atour.hotel.module.breakfast.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 早餐购买、挂工作帐参数
 *
 * <AUTHOR>
 * @date 2022/05/12
 */
@Data
public class ChainBreakfastPurchaseRequest {
    /**
     * 购早类型（1：房单；2：工作帐）
     */
    @NotNull(message = "购早类型不能为空")
    @ApiModelProperty(value = "购早类型（1：房单；2：工作帐）")
    private Integer buyType;

    /**
     * 酒店ID
     */
    @NotNull(message = "酒店ID")
    @ApiModelProperty(value = "酒店ID")
    private Integer chainId;

    /**
     * 房单号
     */
    @ApiModelProperty(value = "房单号")
    private Long folioId;

    /**
     * 房间号（房单场景）
     */
    @ApiModelProperty(value = "房间号（房单场景）")
    private String roomNo;

    /**
     * 工作账名称（工作帐场景）
     */
    @ApiModelProperty(value = "工作账名称（工作帐场景）")
    private String name;

    /**
     * 工作帐备注（工作帐场景）
     */
    @ApiModelProperty(value = "工作帐备注（工作帐场景")
    private String workFolioRemark;

    /**
     * 是否验证手机号
     */
    @ApiModelProperty(value = "是否验证手机号")
    private Integer isVerify = 0;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 班次
     */
    @ApiModelProperty(value = "班次")
    private String shiftCode;

    /**
     * 科目id 1055
     */
    @NotNull(message = "科目ID不能为空")
    @ApiModelProperty(value = "科目id 1055")
    private Integer itemId;

    /**
     * 子科目id 1: 早餐；2：午餐；3:晚餐；4：下午茶；
     */
    @NotNull(message = "子科目id不能为空")
    @ApiModelProperty(value = "子科目id 1: 早餐；2：午餐；3:晚餐；4：下午茶；")
    private Integer subItemId;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private Integer count;

    /**
     * 关联码
     */
    @ApiModelProperty(value = "关联码")
    private String refNo;

    /**
     * 会员ID
     */
    @ApiModelProperty(value = "会员ID")
    private Integer mebId;

    /**
     * 手动入账(true: 手动入账 false: 非手动入账)
     */
    @ApiModelProperty(value = "手动入账(true: 手动入账 false: 非手动入账)")
    private boolean manual;

    /**
     * pos终端编号
     */
    @ApiModelProperty(value = "pos终端编号")
    private String terminalNo;

    /**
     * 是否已授权(true:已授权 false:未授权)
     */
    @ApiModelProperty(value = "是否已授权(true:已授权 false:未授权)")
    private boolean auth = Boolean.FALSE;

    /**
     * 财务编号
     */
    @ApiModelProperty(value = "财务编号")
    private String transNo;

    /**
     * 券码Id
     */
    @ApiModelProperty(value = "券码Id")
    private String couponId;

    /**
     * 忽略退款会员卡的校验
     */
    @ApiModelProperty(value = "忽略退款会员卡的校验")
    private boolean ignoreMebCardCheck;

    /**
     * 账务备注
     */
    @ApiModelProperty(value = "账务备注")
    private String transRemark;

}
