package com.atour.hotel.module.breakfast.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 早餐核销确认核销参数
 *
 * <AUTHOR>
 * @date 2022/04/30
 */
@Data
public class ChainBreakfastCheckOutRequest {

    /**
     * 酒店id
     */
    @NotNull(message = "酒店id不能为空")
    @ApiModelProperty(value = "酒店id")
    private Integer chainId;

    /**
     * 房单号
     */
    @NotNull(message = "房单号不能为空")
    @ApiModelProperty(value = "房单号")
    private Long folioId;

    /**
     * 房间号
     */
    @NotNull(message = "房间号不能为空")
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 核销商品列表
     */
    @NotEmpty(message = "核销商品列表不能为空")
    private List<TicketMealParam> ticketMealList;

    /**
     * 商品编码
     */
    /*@NotNull(message = "商品编码不能为空")
    @ApiModelProperty(value = "商品编码")
    private Integer subItemId;*/

    /**
     * 核销份数
     */
   /* @NotNull(message = "核销份数不能为空")
    @Min(value = 1L, message = "核销份数必须大于0")
    @ApiModelProperty(value = "核销份数")
    private Integer consumeNums;*/

    /**
     * 核销方式，1:PMS 2:NFC 3:输房号 4:联房
     */
    @NotNull(message = "核销方式不能为空")
    @ApiModelProperty(value = "核销方式，1:PMS 2:NFC 3:输房号 4:联房")
    private Integer consumeWay;

    /*@ApiModelProperty(value = "操作人名字")
    private String optName;

    @ApiModelProperty(value = "操作人id")
    private Long optId;*/
}
