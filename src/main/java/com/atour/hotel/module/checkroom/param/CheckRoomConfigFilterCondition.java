package com.atour.hotel.module.checkroom.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 打扫记时配置页面筛选条件
 */
@Data
public class CheckRoomConfigFilterCondition {

    /**
     * 酒店状态: 0全部，1开业 2筹建
     * @see com.atour.dicts.db.center.sys_chain.ChainStateEnum
     */
    @ApiModelProperty(value = "酒店状态: 0全部，1开业 2筹建")
    private Integer hotelState;

    /**
     * 酒店类型: 0特许，1直营 2全部
     * @see com.atour.hotel.module.cost.enums.ChainTypeEnum
     */
    private Integer hotelType;

    /**
     * 区域ids: -1代表全部
     */
    @ApiModelProperty(value = "区域ids: -1代表全部")
    private List<Integer> areaIds;

}
