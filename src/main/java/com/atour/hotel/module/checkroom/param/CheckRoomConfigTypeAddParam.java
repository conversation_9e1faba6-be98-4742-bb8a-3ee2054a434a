package com.atour.hotel.module.checkroom.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建检查放类型参数
 */
@Data
public class CheckRoomConfigTypeAddParam implements Serializable {

    @NotBlank(message = "检查类型不可以为空")
    @Length(max = 50, message = "检查类型长度不可大于50字符")
    @ApiModelProperty(value = "检查类型")
    private String configTypeValue;


}
