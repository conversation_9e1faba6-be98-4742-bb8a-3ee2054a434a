package com.atour.hotel.module.checkroom.service.impl;

import com.atour.api.bean.PageInfo;
import com.atour.api.bean.PageResult;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.module.checkroom.dto.CheckRoomTaskDTO;
import com.atour.hotel.module.checkroom.enums.CheckRoomStatuseEnum;
import com.atour.hotel.module.checkroom.enums.CheckRoomTaskStateEnum;
import com.atour.hotel.module.checkroom.param.*;
import com.atour.hotel.module.checkroom.service.CheckRoomService;
import com.atour.hotel.module.checkroom.service.CheckRoomTaskService;
import com.atour.hotel.module.dkf.app.dto.DkfCheckRoomTaskDTO;
import com.atour.hotel.module.dkf.app.dto.DkfCheckRoomTypeDTO;
import com.atour.hotel.module.energy.common.BizPreconditions;
import com.atour.hotel.persistent.franchise.dao.CheckRoomConfigTaskDao;
import com.atour.hotel.persistent.franchise.dao.CheckRoomConfigTypeDao;
import com.atour.hotel.persistent.franchise.dao.CheckRoomInfoDao;
import com.atour.hotel.persistent.franchise.dao.OmsOperateLogDAO;
import com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTaskEntity;
import com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity;
import com.atour.hotel.persistent.franchise.entity.CheckRoomInfoEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CheckRoomTaskServiceImpl implements CheckRoomTaskService {
    @Resource
    private CheckRoomConfigTaskDao checkRoomConfigTaskDao;
    @Resource
    private CheckRoomConfigTypeDao checkRoomConfigTypeDao;

    @Resource
    private CheckRoomService checkRoomService;

    @Resource
    private CheckRoomInfoDao checkRoomInfoDao;

    @Resource
    private OmsOperateLogDAO omsOperateLogDAO;

    @Override
    public Boolean addCheckRoomTask(CheckRoomTaskAddParam addParam) {

        CheckRoomConfigTypeEntity checkRoomConfigTypeEntity = checkRoomService.getCheckRoomConfigTypeEntity(addParam.getConfigTypeId());
        BizPreconditions.checkArgument(Objects.nonNull(checkRoomConfigTypeEntity), "检查类型不存在，请重新选择");
        Integer count = checkRoomConfigTaskDao.queryConfigTaskByConfigTypeIdAndTaskValue(addParam.getConfigTypeId(), addParam.getTaskValue().trim());
        BizPreconditions.checkArgument(count == 0, "检查类型和检查内容组合已经存在不可重复创建");

        //创建检查类型
        UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();
        CheckRoomConfigTaskEntity taskEntity = new CheckRoomConfigTaskEntity();
        taskEntity.setConfigTypeId(addParam.getConfigTypeId());
        taskEntity.setTaskValue(addParam.getTaskValue());
        taskEntity.setTaskState(CheckRoomTaskStateEnum.EFFECTIVE.getCode());
        taskEntity.setCreateTime(new Date());
        taskEntity.setCreateUserId(localUserHotel.getUserId());
        taskEntity.setCreateUserName(localUserHotel.getUserName());
        taskEntity.setDeleted(0);
        taskEntity.setVersion(0);
        checkRoomConfigTaskDao.insertSelective(taskEntity);
        return Boolean.TRUE;
    }

    @Override
    public Boolean modifyCheckRoomTask(CheckRoomTaskModifyParam modifyParam) {


        CheckRoomConfigTypeEntity checkRoomConfigTypeEntity = checkRoomService.getCheckRoomConfigTypeEntity(modifyParam.getConfigTypeId());
        BizPreconditions.checkArgument(Objects.nonNull(checkRoomConfigTypeEntity), "检查类型不存在，请重新选择”");

        CheckRoomConfigTaskEntity taskEntity = checkRoomConfigTaskDao.selectByPrimaryKey(modifyParam.getCheckTaskId());
        BizPreconditions.checkArgument(Objects.nonNull(taskEntity), "检查任务不存在");
        CheckRoomConfigTaskEntity checkRoomConfigTaskEntity = checkRoomConfigTaskDao.queryConfigTaskValue(modifyParam.getConfigTypeId(), modifyParam.getTaskValue().trim());
        if (Objects.nonNull(checkRoomConfigTaskEntity)) {
            BizPreconditions.checkArgument(checkRoomConfigTaskEntity.getId().equals(modifyParam.getCheckTaskId()), "检查类型和检查内容组合已经存在不可重复创建");
        }
        UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();
        taskEntity.setConfigTypeId(modifyParam.getConfigTypeId());
        taskEntity.setTaskValue(modifyParam.getTaskValue());
        taskEntity.setUpdateTime(new Date());
        taskEntity.setUpdateUserId(localUserHotel.getUserId());
        taskEntity.setUpdateUserName(localUserHotel.getUserName());
        taskEntity.setTaskState(CheckRoomTaskStateEnum.EFFECTIVE.getCode());
        checkRoomConfigTaskDao.updateByPrimaryKeySelective(taskEntity);
        return Boolean.TRUE;
    }

    @Override
    public Boolean delCheckRoomTask(CheckRoomTaskDelParam param) {
        UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();

        List<CheckRoomConfigTaskEntity> taskEntityList = checkRoomConfigTaskDao.queryAllCheckTaskByPrimaryKeyList(param.getCheckTaskIdList());
        List<CheckRoomConfigTaskEntity> taskEntitiesEffective = taskEntityList.stream().filter(bean -> bean.getTaskState() == CheckRoomTaskStateEnum.EFFECTIVE.getCode()).collect(Collectors.toList());
        BizPreconditions.checkArgument(CollectionUtils.isEmpty(taskEntitiesEffective), "被删除检查项中有仍在生效中的检查项，请先失效后再进行删除");
        for (CheckRoomConfigTaskEntity taskEntity : taskEntityList) {
            taskEntity.setTaskState(CheckRoomTaskStateEnum.INEFFICIENT.getCode());
            taskEntity.setUpdateTime(new Date());
            taskEntity.setUpdateUserId(localUserHotel.getUserId());
            taskEntity.setUpdateUserName(localUserHotel.getUserName());
            taskEntity.setVersion(taskEntity.getVersion() + 1);
            taskEntity.setDeleted(1);
            checkRoomConfigTaskDao.updateByPrimaryKeySelective(taskEntity);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean operationCheckRoomTask(OperationCheckTaskParam param) {
        List<CheckRoomConfigTaskEntity> taskEntityList = checkRoomConfigTaskDao.queryAllCheckTaskByPrimaryKeyList(param.getCheckTaskIdList());
        BizPreconditions.checkArgument(CollectionUtils.isNotEmpty(taskEntityList), "未查询到检查任务数据");
        UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();

        switch (CheckRoomTaskStateEnum.getInstance(param.getOperationType())) {
            case EFFECTIVE:
                List<CheckRoomConfigTaskEntity> taskEntitiesDelete = taskEntityList.stream().filter(bean -> bean.getDeleted() == 1).collect(Collectors.toList());
                BizPreconditions.checkArgument(CollectionUtils.isEmpty(taskEntitiesDelete), "存在【" + taskEntitiesDelete.size() + "】条检查项内容数据已经删除，请刷新后重试");
                List<CheckRoomConfigTaskEntity> taskEntitiesNoType = taskEntityList.stream().filter(bean -> bean.getConfigTypeId() == 0).collect(Collectors.toList());
                BizPreconditions.checkArgument(CollectionUtils.isEmpty(taskEntitiesNoType), "存在【" + taskEntitiesNoType.size() + "】条检查项内容数据缺少检查项类型，请刷新后重试");

                for (CheckRoomConfigTaskEntity taskEntity : taskEntityList) {
                    taskEntity.setTaskState(CheckRoomTaskStateEnum.EFFECTIVE.getCode());
                    taskEntity.setUpdateTime(new Date());
                    taskEntity.setUpdateUserId(localUserHotel.getUserId());
                    taskEntity.setUpdateUserName(localUserHotel.getUserName());
                    taskEntity.setVersion(taskEntity.getVersion() + 1);
                    checkRoomConfigTaskDao.updateByPrimaryKeySelective(taskEntity);
                }
                break;
            case INEFFICIENT:
                List<CheckRoomConfigTaskEntity> taskEntitiesDeleteINEFFICIENT = taskEntityList.stream().filter(bean -> bean.getDeleted() == 1).collect(Collectors.toList());
                BizPreconditions.checkArgument(CollectionUtils.isEmpty(taskEntitiesDeleteINEFFICIENT), "存在【" + taskEntitiesDeleteINEFFICIENT.size() + "】条检查项内容数据已经删除，请刷新后重试");

                for (CheckRoomConfigTaskEntity taskEntity : taskEntityList) {
                    taskEntity.setTaskState(CheckRoomTaskStateEnum.INEFFICIENT.getCode());
                    taskEntity.setUpdateTime(new Date());
                    taskEntity.setUpdateUserId(localUserHotel.getUserId());
                    taskEntity.setUpdateUserName(localUserHotel.getUserName());
                    taskEntity.setVersion(taskEntity.getVersion() + 1);
                    checkRoomConfigTaskDao.updateByPrimaryKeySelective(taskEntity);
                }
                break;
            default:
                break;
        }
        return Boolean.TRUE;
    }

    @Override
    public CheckRoomTaskDTO queryTaskIdInfo(Long checkTaskId) {

        BizPreconditions.checkArgument(Objects.nonNull(checkTaskId), "检查任务ID不可以为空");
        CheckRoomConfigTaskEntity taskEntity = checkRoomConfigTaskDao.selectByPrimaryKey(checkTaskId);
        BizPreconditions.checkArgument(Objects.nonNull(taskEntity), "检查任务不可以为空");
        CheckRoomConfigTypeEntity checkRoomConfigTypeEntity = checkRoomService.getCheckRoomConfigTypeEntity(taskEntity.getConfigTypeId());

        return CheckRoomTaskDTO.builder()
                .checkTaskId(taskEntity.getId())
                .taskState(taskEntity.getTaskState())
                .configTypeId(taskEntity.getConfigTypeId())
                .configTypeValue(Objects.nonNull(checkRoomConfigTypeEntity) ? checkRoomConfigTypeEntity.getConfigTypeValue() : "")
                .taskStateName(CheckRoomTaskStateEnum.getInstance(taskEntity.getTaskState()).getDescription())
                .taskValue(taskEntity.getTaskValue())
                .version(taskEntity.getVersion()).build();

    }


    @Override
    public Boolean updateTaskByConfigTypeId(List<Long> configTypeIdList) {
        if (CollectionUtils.isEmpty(configTypeIdList)) {
            log.warn("updateTaskByConfigTypeId  configTypeIdList is null ");
            return Boolean.TRUE;
        }
        UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();

        CheckRoomConfigTaskEntity taskEntity = new CheckRoomConfigTaskEntity();
        taskEntity.setTaskState(CheckRoomTaskStateEnum.INEFFICIENT.getCode());
        taskEntity.setConfigTypeId(0L);
        taskEntity.setUpdateTime(new Date());
        taskEntity.setUpdateUserId(localUserHotel.getUserId());
        taskEntity.setUpdateUserName(localUserHotel.getUserName());
        checkRoomConfigTaskDao.updateByPrimaryKeySelectiveConfigTypeId(taskEntity, configTypeIdList);

        return Boolean.TRUE;
    }

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<CheckRoomTaskDTO> pageList(CheckTaskPageParam param) {
        PageResult<CheckRoomTaskDTO> pageResult = new PageResult<>();
        Long pageCount = checkRoomConfigTaskDao.listPageCount(param);

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNo(param.getPageNo());
        pageInfo.setPageSize(param.getPageSize());
        pageInfo.setTotalCount(pageCount);
        pageResult.setPage(pageInfo);
        if (pageCount == 0L) {
            pageResult.setData(org.apache.commons.compress.utils.Lists.newArrayList());

            return pageResult;
        }
        // 获取分页信息
        Pair<Integer, Integer> pageParam = CommonUtils.getPageBetween(param.getPageNo(), param.getPageSize());
        CheckTaskPageCond checkTaskPageCond = new CheckTaskPageCond();
        BeanUtils.copyProperties(param, checkTaskPageCond);
        checkTaskPageCond.setPageEnd(pageParam.getRight());
        checkTaskPageCond.setPageStart(pageParam.getLeft());
        List<CheckRoomConfigTaskEntity> taskEntityList = checkRoomConfigTaskDao.listPage(checkTaskPageCond);

        if (CollectionUtils.isNotEmpty(taskEntityList)) {
            List<Long> configTypeIdList = taskEntityList.stream()
                    .filter(bean -> Objects.nonNull(bean.getConfigTypeId()) && bean.getConfigTypeId() > 0L)
                    .map(CheckRoomConfigTaskEntity::getConfigTypeId).collect(Collectors.toList());

             Map<Long, String> configTypeIdMap = checkRoomService.queryCheckRoomConfigTypeValueByIdList(configTypeIdList);

            List<CheckRoomTaskDTO> listCheckRoomTaskDTO = new ArrayList<>();

            for (CheckRoomConfigTaskEntity taskEntity : taskEntityList) {
                CheckRoomTaskDTO checkRoomTaskDTO = CheckRoomTaskDTO.builder()
                        .checkTaskId(taskEntity.getId())
                        .configTypeValue(configTypeIdMap!=null &&configTypeIdMap.size()>0?configTypeIdMap.get(taskEntity.getConfigTypeId()):"")
                        .configTypeId(taskEntity.getConfigTypeId())
                        .taskValue(taskEntity.getTaskValue())
                        .taskState(taskEntity.getTaskState())
                        .taskStateName(CheckRoomTaskStateEnum.getInstance(taskEntity.getTaskState()).getDescription())
                        .version(taskEntity.getVersion()).build();
                listCheckRoomTaskDTO.add(checkRoomTaskDTO);
            }
            pageResult.setData(listCheckRoomTaskDTO);
        }

        return pageResult;
    }

    @Override
    public List<DkfCheckRoomTypeDTO> queryCheckRoomTaskStateEffective(CheckRoomTaskStateEnum taskStateEnum) {


        List<DkfCheckRoomTypeDTO> resList = Lists.newArrayList();

        List<CheckRoomConfigTaskEntity> taskEntityList = checkRoomConfigTaskDao.queryAllByTaskState(taskStateEnum.getCode());
        if (CollectionUtils.isNotEmpty(taskEntityList)) {
            List<CheckRoomConfigTypeEntity> typeEntityList = checkRoomService.getCheckRoomConfigTypeEntityBatch(taskEntityList.stream().map(CheckRoomConfigTaskEntity::getConfigTypeId).distinct().collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(typeEntityList)) {
                return Lists.newArrayList();
            }
            Map<Long, List<CheckRoomConfigTaskEntity>> configTaskEntityMap = taskEntityList.stream().collect(Collectors.groupingBy(CheckRoomConfigTaskEntity::getConfigTypeId));

            for (CheckRoomConfigTypeEntity checkRoomConfigTypeEntity : typeEntityList) {
                DkfCheckRoomTypeDTO dkfCheckRoomTypeDTO = DkfCheckRoomTypeDTO.builder()
                        .configTypeId(checkRoomConfigTypeEntity.getId())
                        .configTypeValue(checkRoomConfigTypeEntity.getConfigTypeValue())
                        .build();

                List<CheckRoomConfigTaskEntity> entityList = configTaskEntityMap.get(checkRoomConfigTypeEntity.getId());
                if (CollectionUtils.isNotEmpty(entityList)) {
                    List<DkfCheckRoomTaskDTO> taskDTOList = Lists.newArrayList();
                    for (CheckRoomConfigTaskEntity taskEntity : entityList) {
                        DkfCheckRoomTaskDTO roomTaskDTO = DkfCheckRoomTaskDTO.builder().checkStatus(1).checkTaskId(taskEntity.getId()).taskValue(taskEntity.getTaskValue()).build();
                        taskDTOList.add(roomTaskDTO);
                    }
                    dkfCheckRoomTypeDTO.setTaskDTOList(taskDTOList);
                }
                resList.add(dkfCheckRoomTypeDTO);
            }
        }
        return resList;
    }

    @Override
    public void dateDeal(DateDealParam dateDealParam) {
        if(Objects.isNull(dateDealParam)){
            return;
        }
        dateDealParam.getChainIdList().forEach(bean->{
            List<CheckRoomInfoEntity> checkRoomInfoEntities = checkRoomInfoDao.queryInfoByChainAndAccDate(bean, dateDealParam.getAccDate());
            if(CollectionUtils.isNotEmpty(checkRoomInfoEntities)){
                for (CheckRoomInfoEntity checkRoomInfoEntity : checkRoomInfoEntities) {
                    checkRoomInfoEntity.setCheckRoomStatus(CheckRoomStatuseEnum.DROP.getCode());
                    checkRoomInfoDao.updateByPrimaryKeySelective(checkRoomInfoEntity);
                }
            }
        });

    }

    @Override
    public void deleteCheckRoomInfoById(DateDealParam dateDealParam) {
        if(Objects.isNull(dateDealParam)){
            return;
        }
        checkRoomInfoDao.deleteCheckRoomInfoById(dateDealParam.getMaxId(),dateDealParam.getMinId());

    }

    @Override
    public void deleteomslog(DateDealParam param) {
        if(Objects.isNull(param)){
            return;
        }
        omsOperateLogDAO.deletelog(param.getMaxId(), param.getMinId());
    }


}
