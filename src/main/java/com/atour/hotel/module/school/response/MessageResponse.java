package com.atour.hotel.module.school.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 消息
 *
 * <AUTHOR>
 * @date 2019/5/27
 */
@Data
@ApiModel
public class MessageResponse {

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", dataType = "Integer")
    private Integer userId;

    /**
     * 任务id
     */
    @ApiModelProperty(name = "extendId", value = "任务id", dataType = "Integer")
    private Integer extendId;

    /**
     * 任务链接
     */
    @ApiModelProperty(name = "url", value = "任务链接", dataType = "String")
    private String url;

    /**
     * 课程名称
     */
    @ApiModelProperty(name = "title", value = "学堂任务里是课程名称，系统消息是消息的标题", dataType = "String")
    private String title;

    @ApiModelProperty(name = "title", value = "消息的内容", dataType = "String")
    private String content;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startTime", value = "开始时间", dataType = "String")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间", dataType = "String")
    private String endTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间，系统消息里是消息的时间", dataType = "String")
    private String createTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(name = "state", value = "任务状态,系统消息是消息状态, 0: 已读, 1: 未读", dataType = "Integer")
    private Integer state;

    @ApiModelProperty(value = "消息日期")
    private String accDate;



    @ApiModelProperty(value = "酒店ID")
    private String chainId;

    @ApiModelProperty(value = "uid")
    private String uid;

    @ApiModelProperty(value = "type")
    private String type;

}
