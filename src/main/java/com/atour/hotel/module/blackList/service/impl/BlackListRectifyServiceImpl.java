package com.atour.hotel.module.blackList.service.impl;

import com.atour.hotel.module.blackList.service.BlackListRectifyService;
import com.atour.hotel.persistent.franchise.dao.DzjBlackListRectifyDao;
import com.atour.hotel.persistent.franchise.entity.DzjBlackListRectifyEntity;
import lombok.extern.log4j.Log4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Log4j
@Service
public class BlackListRectifyServiceImpl implements BlackListRectifyService {

    @Resource
    private DzjBlackListRectifyDao dzjBlackListRectifyDao;

    @Override
    public DzjBlackListRectifyEntity queryBlackListRectifyByBlackListId(Long blackListId) {

        if (Objects.isNull(blackListId)) {
            return null;
        }
        return dzjBlackListRectifyDao.queryByBlackListId(blackListId);
    }

    @Override
    public void insertOrUpdateDzjBlackListRectifyEntity(DzjBlackListRectifyEntity rectifyEntity) {
        if(Objects.isNull(rectifyEntity.getId())){
            dzjBlackListRectifyDao.insertSelective(rectifyEntity);
        }else{
            dzjBlackListRectifyDao.updateByPrimaryKeySelective(rectifyEntity);
        }
    }

    @Override
    public List<DzjBlackListRectifyEntity> batchQueryBlackListRectifyById(List<Long> blackListIdList) {

        return dzjBlackListRectifyDao.batchQueryByBlackListId(blackListIdList);
    }


}
