/**
 * 
 */
package com.atour.hotel.module.workorder.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.workorder.dto.WorkOrderDetailDTO;
import com.atour.hotel.module.workorder.dto.WorkOrderMessagePageDTO;
import com.atour.hotel.module.workorder.dto.WorkOrderPageDTO;
import com.atour.hotel.module.workorder.dto.WorkOrderTypeDTO;
import com.atour.hotel.module.workorder.param.AddWorkOrderParam;
import com.atour.hotel.module.workorder.param.WorkOrderReplyParam;
import com.atour.hotel.module.workorder.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Api(value = "工单", tags = "工单")
@RestController
@RequestMapping("/app/workOrder")
public class WorkOrderController {
	
	@Resource
	private WorkOrderService workOrderService;
	
	/**
	 * 工单列表
	 * @return
	 */
	@ApiOperation(value = "工单列表", httpMethod = "GET")
	@RequestMapping(value = "/getWorkOrders", method = RequestMethod.GET)
	public ApiResult<WorkOrderPageDTO> getWorkOrders(@ApiParam(value = "pageSize", name = "页大小", required = true) @RequestParam Integer pageSize, 
			@ApiParam(value = "pageNo", name = "页码", required = true) @RequestParam Integer pageNo){
		Integer userId = CommonParamsManager.getCurrUserId();
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, workOrderService.getWorkOrders(userId, pageSize, pageNo));
	}
	
	/**
	 * 提交工单
	 * @return
	 */
	@ApiOperation(value = "提交工单", httpMethod = "POST")
	@RequestMapping(value = "/addWorkOrder", method = RequestMethod.POST)
	public ApiResult<String> addWorkOrder(@ApiParam(value = "AddWorkOrderParam", name = "添加工单参数", required = true) 
			@Valid @RequestBody AddWorkOrderParam addWorkOrderParam) {
		workOrderService.addWorkOrder(addWorkOrderParam);
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, null);
	}
	
	/**
	 * 问题分类列表
	 * @return
	 */
	@ApiOperation(value = "问题分类列表", httpMethod = "GET")
	@RequestMapping(value = "/getWorkOrderTypes", method = RequestMethod.GET)
	public ApiResult<List<WorkOrderTypeDTO>> getWorkOrderTypes(){
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, workOrderService.getWorkOrderTypes());
	}
	
	/**
	 * 工单消息列表
	 * @return
	 */
	@ApiOperation(value = "工单消息列表", httpMethod = "GET")
	@RequestMapping(value = "/getWorkOrderMessages", method = RequestMethod.GET)
	public ApiResult<WorkOrderMessagePageDTO> getWorkOrderMessages(@ApiParam(value = "pageSize", name = "页大小", required = true) @RequestParam Integer pageSize, 
			@ApiParam(value = "pageNo", name = "页码", required = true) @RequestParam Integer pageNo){
		Integer userId = CommonParamsManager.getCurrUserId();
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, workOrderService.getWorkOrderMessages(userId, pageSize, pageNo));
	}
	
	/**
	 * 工单详情
	 * @return
	 */
	@ApiOperation(value = "工单详情", httpMethod = "GET")
	@RequestMapping(value = "/getWorkOrderDetail", method = RequestMethod.GET)
	public ApiResult<WorkOrderDetailDTO> getWorkOrderDetail(@ApiParam(value = "orderNum", name = "工单号", required = true) 
			@RequestParam String orderNum){
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, workOrderService.getWorkOrderDetail(orderNum));
	}
	
	/**
	 * 回复意见
	 * @return
	 */
	@ApiOperation(value = "回复意见", httpMethod = "POST")
	@RequestMapping(value = "/reply", method = RequestMethod.POST)
	public ApiResult<String> reply(@ApiParam(value = "WorkOrderReplyParam", name = "回复意见参数", required = true) 
			@Valid @RequestBody WorkOrderReplyParam workOrderReplyParam){
		workOrderService.reply(workOrderReplyParam);
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, null);
	}
	
	
	/**
	 * 工单已解决
	 * @return
	 */
	@ApiOperation(value = "工单已解决", httpMethod = "GET")
	@RequestMapping(value = "/finish", method = RequestMethod.GET)
	public ApiResult<String> finish(@ApiParam(value = "orderNum", name = "工单号", required = true) @RequestParam String orderNum,
			@ApiParam(value = "replyId", name = "回复ID", required = true) @RequestParam Long replyId){
		workOrderService.finish(orderNum, replyId);
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, null);
	}
	
	/**
	 * 更新回复状态
	 * @return
	 */
	@ApiOperation(value = "更新回复状态", httpMethod = "GET")
	@RequestMapping(value = "/updateReplyStatus", method = RequestMethod.GET)
	public ApiResult<String> updateReplyStatus(@ApiParam(value = "replyId", name = "回复ID", required = false) @RequestParam(required = false) Long replyId,
			@ApiParam(value = "orderNum", name = "工单号", required = false) @RequestParam(required = false) String orderNum){
		if(replyId!=null || orderNum!=null) {
			workOrderService.updateReplyStatus(replyId, orderNum);
		}
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, null);
	}



	/**
	 * 发送消息
	 * @return
	 */
	@ApiOperation(value = "发送消息", httpMethod = "POST")
	@RequestMapping(value = "/sendMessage", method = RequestMethod.POST)
	public ApiResult<String> sendMessage(@ApiParam(value = "orderNum", name = "工单号", required = true)
	@RequestParam String orderNum) {
		workOrderService.sendMessage(orderNum);
		return ApiResult.success(ApiResult.DEFAULT_SUCCEED_MESSAGE, null);
	}
}
