/**
 * 
 */
package com.atour.hotel.module.workorder.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class WorkOrderDetailDTO implements Serializable {

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = -5312313083733125107L;
	
	/**
	 * 工单号
	 */
	@ApiModelProperty(value = "工单号")
	private String orderNum;
	
	/**
	 * 工单标题
	 */
	@ApiModelProperty(value = "工单标题")
	private String title;
	
	/**
	 * 工单内容
	 */
	@ApiModelProperty(value = "工单内容")
	private String content;
	
	/**
	 * 图片链接
	 */
	@ApiModelProperty(value = "图片链接")
	private List<String> imageUrls;
	
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;
	
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Long createUserId;
	
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy年MM月dd日 HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	
	/**
	 * 工单回复
	 */
	@ApiModelProperty(value = "工单回复")
	private List<WorkOrderReplyDTO> workOrderReplies;

	/**
	 * 当前节点提醒
	 */
	@ApiModelProperty(value = "当前节点提醒")
	private String nodeTip;


	/**
	 * 提醒处理按钮状态
	 */
	@ApiModelProperty(value = "提醒处理按钮状态")
	private Integer ShowButtonStatus;
}
