/**
 * 
 */
package com.atour.hotel.module.workorder.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class WorkOrderReplyDTO implements Serializable {

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 424998096342449535L;
	
	/**
	 * 回复ID
	 */
	private Long replyId;
	
	/**
	 * 回复人ID
	 */
	@ApiModelProperty(value = "回复人ID")
	private Long fromUserId;
	
	/**
	 * 回复人姓名
	 */
	@ApiModelProperty(value = "回复人姓名")
	private String fromUserName;
	
	/**
	 * 被回复人ID
	 */
	@ApiModelProperty(value = "被回复人ID")
	private Long toUserId;
	
	/**
	 * 被回复人姓名
	 */
	@ApiModelProperty(value = "被回复人姓名")
	private String toUserName;
	
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	
	/**
	 * 图片链接
	 */
	@ApiModelProperty(value = "图片链接")
	private List<String> imageUrls;
	
	/**
	 * 时间
	 */
	@ApiModelProperty(value = "时间")
	@JsonFormat(pattern = "yyyy年MM月dd日 HH:mm", timezone = "GMT+8")
	private Date createTime;


	/**
	 * 回复类型
	 */
	@ApiModelProperty(value = "回复类型")
	private Integer replyType;


	/**
	 * 如果是转发 那么存转发用户的id列表
	 */
	@ApiModelProperty(value = "如果是转发 那么存转发用户的id列表")
	private String forwardToIds;

	/**
	 * 如果是转发 那么存转发用户的name列表
	 */
	@ApiModelProperty(value = "如果是转发 那么存转发用户的name列表")
	private String forwardToNames;
}
