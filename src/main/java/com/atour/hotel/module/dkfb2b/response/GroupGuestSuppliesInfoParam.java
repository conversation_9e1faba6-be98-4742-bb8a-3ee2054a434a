package com.atour.hotel.module.dkfb2b.response;

import com.atour.hotel.module.dkf.web.hotel.request.GuestSuppliesInfoParam;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/9/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GroupGuestSuppliesInfoParam extends GuestSuppliesInfoParam {

    /**
     * 入库单id
     */
    private Integer consignmentInfoId;

    /**
     * 发货数量
     */
    private Integer sendCount;

    /**
     * 采购单位
     */
    private String unitName;

    /**
     * 采购单价
     */
    private BigDecimal unitPrice;

    /**
     * 箱规
     */
    private String boxGauge;

}
