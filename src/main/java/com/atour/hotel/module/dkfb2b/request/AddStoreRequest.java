package com.atour.hotel.module.dkfb2b.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 入库请求体，包含数据部分
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddStoreRequest {

    /**
     * 数据部分
     */
    private List<Data> data;

    // 数据部分的类
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Data {

        /**
         * 单据类型编码，固定值im_PurInBill_STD_BT_S
         */
        private String billtype_number;

        /**
         * 采购组织，传门店编码
         */
        private String bizorg_number;

        /**
         * 收货日期，"2022-07-04"
         */
        private String biztime;

        /**
         * 业务类型，采购入库传"110"，采购退货传"1101"
         */
        private String biztype_number;

        /**
         * 备注
         */
        private String comment;

        /**
         * 库存事务，采购入库传"110"，采购退货出传”1101“
         */
        private String invscheme_number;

        /**
         * 是否含税，固定值1
         */
        private Long istax;

        /**
         * 库存组织，传门店编码
         */
        private String org_number;

        /**
         * 收货人，门店花名或”系统“
         */
        private String shkd_operator;

        /**
         * 收货单号
         */
        private String shkd_outbillno;

        /**
         * 供应商，指主体
         */
        private String supplier_number;

        /**
         * 单据条目列表
         */
        private List<BillEntry> billentry;
    }

    // 单据条目类
    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BillEntry {

        /**
         * 物料明细.基本数量，同sku数量
         */
        private String baseqty;

        /**
         * 基本单位.编码，固定值”pcs“
         */
        private String baseunit_name;

        /**
         *
         */
        private String unit_name;

        /**
         * 入库保管者.编码
         */
        private String keeper_number;

        /**
         * 物料明细.入库保管者类型 [bos_org:库存组织, bd_supplier:供应商, bd_customer:客户]，固定值"bos_org"
         */
        private String keepertype;

        /**
         * 行类型，固定值”010“
         */
        private String linetype_number;

        /**
         * sku编码
         */
        private String material_number;

        /**
         * sku含税单价，oms自己做系数转换
         */
        private BigDecimal priceandtax;

        /**
         * sku数量，oms自己做系数转换
         */
        private BigDecimal qty;

        /**
         * 物料明细，收货单明细行id（集市给）
         */
        private String srcsysbillentryid;

        /**
         * 来源，固定值”OMS“
         */
        private String srcsystem;

        /**
         * 税率，13
         */
        private BigDecimal taxrateid_taxrate;


        /**
         * 仓库编码，前台、客房、大厅
         */
        private String warehouse_number;

        /**
         * 物料明细税额
         */
        private BigDecimal taxamount;

        /**
         * 物料明细价税合计
         */
        private BigDecimal amountandtax;
        /**
         * 物料明细单价
         */
        private BigDecimal price;
        /**
         * 物料明细金额
         */
        private BigDecimal amount;
        /**
         * 物料明细实际单价
         */
        private BigDecimal actualprice;
        /**
         * 物料明细实际含税单价（本位币）
         */
        private BigDecimal actualtaxprice;
        /**
         * 物料明细税额（本位币）
         */
        private BigDecimal curtaxamount;
        /**
         * 物料明细金额
         */
        private BigDecimal curamount;
        /**
         * 物料明细税价合计
         */
        private BigDecimal curamountandtax;
    }
}
