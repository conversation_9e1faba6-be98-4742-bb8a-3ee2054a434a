package com.atour.hotel.module.meb.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 会员打标签入参
 * <AUTHOR>
 * @date 2021/6/10
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagRequest {

    /**
     * 酒店id
     */
    @NotNull(message = "酒店id不能为空")
    private Integer chainId;
    /**
     * 房单id
     */
    @NotNull(message = "房单号不能为空")
    private Long folioId;
    /**
     * 入住人会员id
     */
    @NotNull(message = "会员id不能为空")
    private Integer mebId;
    /**
     * 打标签的标签id
     */
    private Set<Integer> markCategoryIds;
    /**
     * 取消标签的标签id
     */
    private Set<Integer> unmarkCategoryIds;

}
