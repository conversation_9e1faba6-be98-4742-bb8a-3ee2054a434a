package com.atour.hotel.module.meb.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMebTagDto {

    /**
     * 酒店Id
     */
    @NotNull(message = "酒店id不能为空")
    private Integer chainId;

    /**
     * 订单id
     */
    private Long folioId;

    /**
     * 会员信息
     */
    private List<MemberDto> memberDtos;
    /**
     * 会员提醒范围
     * 1 排房时  2 离店时  3 打扫时  4 查房时
     */
    @NotNull(message = "会员提醒范围不能为空")
    private Integer triggerEventType;
}
