package com.atour.hotel.module.internalAssistant.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.atour.common.internalassistant.dto.SignReportPageDTO;
import com.atour.common.internalassistant.dto.SignReportSummaryDTO;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.internalAssistant.dto.export.SignReportSummaryChainDTO;
import com.atour.hotel.module.internalAssistant.dto.export.SignReportSummaryGroupDTO;
import com.atour.hotel.module.internalAssistant.param.OmsSignReportListParam;
import com.atour.hotel.module.internalAssistant.param.OmsSignReportSummaryParam;
import com.atour.hotel.module.internalAssistant.service.AbstractSignReportExport;
import com.atour.hotel.module.internalAssistant.service.SignReportService;
import com.atour.hotel.module.internalAssistant.service.SignReportSummaryExportService;
import com.atour.monitor.AMonitor;
import com.atour.pms.api.enums.sign.OmsAreaGranularityEnum;
import com.atour.utils.excel.EasyExcelUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * 区域粒度为集团是报表导出
 * <AUTHOR>
 * @date 2021/3/16
 */
@Service
@Slf4j
public class SignReportExportGroupServiceImpl extends AbstractSignReportExport implements SignReportSummaryExportService {

    @Resource
    private SignReportService signReportService;

    @Override
    public String exportSummary(HttpServletResponse response, OmsSignReportSummaryParam param) {
        return super.exportSummary(response, param);
    }

    @Override
    public OmsAreaGranularityEnum supportType() {
        return OmsAreaGranularityEnum.GROUP;
    }

    @Override
    protected void doExportSummary(int pageNo, int pageSize, int datasSize, ExcelWriter writer,
                                   OmsSignReportSummaryParam param, OutputStream outputStream,
                                   OmsAreaGranularityEnum omsAreaGranularityEnum) throws Exception{
        Sheet sheet = new Sheet(DEFAULT_SHEET_NO, DEFAULT_HEAD_LINE, SignReportSummaryGroupDTO.class);
        while (datasSize == pageSize && pageNo <= MAX_RETRY_COUNT){
            param.setPageNo(pageNo);
            SignReportPageDTO<SignReportSummaryDTO> summary = signReportService.summary(param);
            List<SignReportSummaryDTO> list = summary.getList();
            List<SignReportSummaryGroupDTO> exportDTO = Lists.newArrayList();
            for (SignReportSummaryDTO dto : list) {
                exportDTO.add(
                        SignReportSummaryGroupDTO.builder()
                                .hasSignedCount(dto.getHasSignedCount())
                                .historyQualifiedCount(dto.getHistoryQualifiedCount())
                                .historySpotCheckCount(dto.getHistorySpotCheckCount())
                                .onTimeCount(dto.getOnTimeCount())
                                .onTimePercent(dto.getOnTimePercent() + PERCENT_STR)
                                .qualifiedPercent(dto.getQualifiedPercent() + PERCENT_STR)
                                .signQualifiedCount(dto.getSignQualifiedCount())
                                .signTypeStr(dto.getSignTypeStr())
                                .signUnQualifiedCount(dto.getSignUnQualifiedCount())
                                .spotCheckPercent(dto.getSpotCheckPercent() + PERCENT_STR)
                                .timeStr(dto.getTimeStr())
                                .totalCount(dto.getTotalCount())
                                .unSignCount(dto.getUnSignCount())
                                .build()
                );
            }
            writer.write(exportDTO, sheet);
            datasSize = list.size();
            pageNo++;
        }
    }

    @Override
    protected void doExportReport(int pageNo, int pageSize, int datasSize, ExcelWriter writer, OmsSignReportListParam param, OutputStream outputStream) throws Exception{
        return;
    }
}
