package com.atour.hotel.module.internalAssistant.dto;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户余额异常提醒
 * <AUTHOR>
 * @date 2021/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OmsAlertAccountBalanceDTO extends OmsAlertBaseDTO {

    /**
     * 房单号
     */
    private Long folioId;
    /**
     * 主入住人
     */
    private String guestName;
    /**
     * 余额
     */
    private Double balance;

}
