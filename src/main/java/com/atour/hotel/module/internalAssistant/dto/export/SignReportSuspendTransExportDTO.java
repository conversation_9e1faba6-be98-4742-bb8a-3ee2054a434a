package com.atour.hotel.module.internalAssistant.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 签阅报表悬单DTO
 * <AUTHOR>
 * @date 2021/3/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignReportSuspendTransExportDTO extends BaseRowModel {
    /**
     * 区域名称
     */
    @ExcelProperty("区域")
    private String areaName;

    /**
     * 酒店名称
     */
    @ExcelProperty("门店")
    private String chainName;
    /**
     * 应收账户名称
     */
    @ExcelProperty("应收账户")
    private String memberName;

    /**
     * 账龄0-7金额
     */
    @ExcelProperty("0-7")
    private BigDecimal lessAmount;

    /**
     * 账龄over7金额
     */
    @ExcelProperty("over7")
    private BigDecimal overAmount;

    /**
     * 总金额
     */
    @ExcelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 审阅结果
     * @see com.atour.dicts.db.atour_oms.internalAssistant.StateEnum
     */
    @ExcelProperty(value = "签阅状态")
    private String stateStr;

    /**
     * 签阅、审核记录
     */
    @ExcelProperty(value = "签阅、审核记录")
    private String signRemark;

}
