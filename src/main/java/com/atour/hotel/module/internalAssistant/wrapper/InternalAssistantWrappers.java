package com.atour.hotel.module.internalAssistant.wrapper;

import com.atour.common.internalassistant.dto.InternalAssistantConfigDTO;
import com.atour.dicts.db.atour_oms.internalAssistant.StateEnum;
import com.atour.hotel.module.internalAssistant.dto.ExamineHomePageDTO;
import com.atour.hotel.module.internalAssistant.dto.HomePageDTO;
import com.atour.hotel.module.internalAssistant.dto.InternalAssistantExamineChainGroupDTO;
import com.atour.hotel.module.internalAssistant.dto.InternalAssistantExamineGroupDTO;
import com.atour.hotel.module.internalAssistant.dto.InternalAssistantGroupDTO;
import com.atour.hotel.module.internalAssistant.dto.InternalAssistantReportExamineChainGroupDTO;
import com.atour.hotel.module.internalAssistant.dto.InternalAssistantTypeListDTO;
import com.atour.hotel.module.internalAssistant.dto.OperateDTO;
import com.atour.hotel.module.internalAssistant.param.OmsSignReportListParam;
import com.atour.hotel.module.internalAssistant.request.OmsSignApprovalParam;
import com.atour.hotel.module.internalAssistant.request.OmsSignRevokeParam;
import com.atour.hotel.module.internalAssistant.request.OmsSignSubmitParam;
import com.atour.hotel.module.internalAssistant.request.OmsSignTransSubmitParam;
import com.atour.hotel.module.internalAssistant.request.SignReportOmsListParam;
import com.atour.pms.api.param.signreport.SignApprovalParam;
import com.atour.pms.api.param.signreport.SignRevokeParam;
import com.atour.pms.api.param.signreport.SignSubmitParam;
import com.atour.pms.api.param.signreport.SignTransSubmitParam;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 签约报表Warpper
 *
 * <AUTHOR>
 * @date 2021年03月05日20:09:25
 */
@Slf4j
public class InternalAssistantWrappers {


    /**
     * 参数转换
     * @param signReportOmsListParam
     * @return
     */
    public static OmsSignReportListParam transferQueryParam(SignReportOmsListParam signReportOmsListParam) {
        if (signReportOmsListParam == null) {
            return null;
        }
        OmsSignReportListParam omsSignReportListParam = new OmsSignReportListParam();
        omsSignReportListParam.setChainIds(signReportOmsListParam.getChainIds());
        omsSignReportListParam.setAccBeginDate(signReportOmsListParam.getAccBeginDate());
        omsSignReportListParam.setAccEndDate(signReportOmsListParam.getAccEndDate());
        omsSignReportListParam.setSignResult(signReportOmsListParam.getSignResult());
        omsSignReportListParam.setState(signReportOmsListParam.getState());
        omsSignReportListParam.setSignType(signReportOmsListParam.getSignType());
        omsSignReportListParam.setPageNo(signReportOmsListParam.getPageNo());
        omsSignReportListParam.setPageSize(signReportOmsListParam.getPageSize());
        return omsSignReportListParam;
    }

    /**
     * 构建首页数据
     * @param monthDTOList  月份汇总数据
     * @param lastDayDTOList 昨日汇总数据
     * @param list 配置表数据
     * @return
     */
    public static HomePageDTO buildHomePageDTO(List<InternalAssistantTypeListDTO> monthDTOList, List<InternalAssistantTypeListDTO> lastDayDTOList, List<InternalAssistantConfigDTO> list){
        //根据类型分组月汇总数据
        Map<Integer, InternalAssistantTypeListDTO> monthMap = Safes.of(monthDTOList).stream()
                .collect(Collectors.toMap(InternalAssistantTypeListDTO::getTypeId, Function.identity()));
        //根据类型分组昨日汇总数据
        Map<Integer, InternalAssistantTypeListDTO> lastDayMap = Safes.of(lastDayDTOList).stream()
                .collect(Collectors.toMap(InternalAssistantTypeListDTO::getTypeId, Function.identity()));

        List<InternalAssistantGroupDTO> groupList =Lists.newArrayList();
        //构建明细数据
        for (InternalAssistantConfigDTO internalAssistantConfigDTO : list) {
            InternalAssistantTypeListDTO monthDTO = monthMap.get(internalAssistantConfigDTO.getTypeId());
            InternalAssistantTypeListDTO lastDayDTO = lastDayMap.get(internalAssistantConfigDTO.getTypeId());
            //如果数据为空，构建个数为0的数据
            monthDTO = Objects.nonNull(monthDTO) ? monthDTO :InternalAssistantTypeListDTO.initDefaultDTO();
            lastDayDTO = Objects.nonNull(lastDayDTO) ? lastDayDTO :InternalAssistantTypeListDTO.initDefaultDTO();

            InternalAssistantGroupDTO build = InternalAssistantGroupDTO.builder()
                    .type(internalAssistantConfigDTO.getTypeId())
                    .typeName(internalAssistantConfigDTO.getTypeName())
                    .currentMonthSignQualifiedCount(monthDTO.getSignQualifiedCount())
                    .currentMonthUnSignQualifiedCount(monthDTO.getSignUnQualifiedCount())
                    .currentMonthTotalCount(getTotalCount(Lists.newArrayList(monthDTO), -1))
                    .lastTotalCount(getTotalCount(Lists.newArrayList(lastDayDTO), -1))
                    .lastUnSignCount(getTotalCount(Lists.newArrayList(lastDayDTO), StateEnum.UN_SIGN.getCode()))
                    .build();
            groupList.add(build);
        }
        //构建返回数据
       return HomePageDTO.builder()
                .lastUnSignCount(getTotalCount(lastDayDTOList,StateEnum.UN_SIGN.getCode()))
                .currentMonthUnSignQualifiedCount(getTotalCount(monthDTOList,StateEnum.SIGN_UNQUALIFIED.getCode()))
                .typeList(groupList)
                .build();
    }


    /**
     * 获取汇总数据
     * @param list
     * @param state
     * @return
     */
    public static Integer  getTotalCount(List<InternalAssistantTypeListDTO> list, Integer state) {
        if (StateEnum.UN_SIGN.getCode().equals(state)) {
            return Safes.of(list).stream()
                    .mapToInt(dto-> Safes.of(dto.getUnSignCount(),0))
                    .sum();
        }
        if (StateEnum.HAS_SIGNED.getCode().equals(state)) {
            return Safes.of(list)
                    .stream()
                    .mapToInt(dto-> Safes.of(dto.getHasSignedCount(),0))
                    .sum();
        }
        if (StateEnum.SIGN_UNQUALIFIED.getCode().equals(state)) {

            return Safes.of(list).stream()
                    .mapToInt(dto-> Safes.of(dto.getSignUnQualifiedCount(),0))
                    .sum();
        }
        if (StateEnum.SIGN_QUALIFIED.getCode().equals(state)) {

            return Safes.of(list)
                    .stream()
                    .mapToInt(dto-> Safes.of(dto.getSignQualifiedCount(),0))
                    .sum();
        }
        return getTotalCount(list, StateEnum.UN_SIGN.getCode()) +
                getTotalCount(list, StateEnum.HAS_SIGNED.getCode()) +
                getTotalCount(list, StateEnum.SIGN_UNQUALIFIED.getCode()) +
                getTotalCount(list, StateEnum.SIGN_QUALIFIED.getCode());
    }

    /**
     * 构建审批首页数据
     * @param monthGroupChainIdDTOList
     * @param lastGroupChainIdDTOList
     * @param chainIdList
     * @param chainNameMapping
     * @param configDTOList
     * @return
     */
    public static ExamineHomePageDTO buildExamineHomePageDTO(List<InternalAssistantReportExamineChainGroupDTO> monthGroupChainIdDTOList,
                                                             List<InternalAssistantReportExamineChainGroupDTO> lastGroupChainIdDTOList,
                                                             List<Integer> chainIdList,
                                                             Map<Integer, String> chainNameMapping,
                                                             List<InternalAssistantConfigDTO> configDTOList) {

        //月数据汇总
       List<InternalAssistantTypeListDTO> monthGroupDTOList = getTotalCountByTypeId(monthGroupChainIdDTOList);
        //昨日数据汇总
        List<InternalAssistantTypeListDTO> lastDayGroupDTOList = getTotalCountByTypeId(lastGroupChainIdDTOList);


        //根据类型分组月汇总数据
        Map<Integer, InternalAssistantTypeListDTO> monthMap = Safes.of(monthGroupDTOList).stream()
                .collect(Collectors.toMap(InternalAssistantTypeListDTO::getTypeId, Function.identity()));
        //根据类型分组昨日汇总数据
        Map<Integer, InternalAssistantTypeListDTO> lastDayMap = Safes.of(lastDayGroupDTOList).stream()
                .collect(Collectors.toMap(InternalAssistantTypeListDTO::getTypeId, Function.identity()));

        List<InternalAssistantExamineGroupDTO> groupList =Lists.newArrayList();
        //当前月汇总数据
        Integer currentMonthTotal = NumberUtils.INTEGER_ZERO;
        //当前月有效审核数据
        Integer currentMonthExamineTotal = NumberUtils.INTEGER_ZERO;


        Map<Integer, List<InternalAssistantReportExamineChainGroupDTO>> monthChainDetailGroupMap = Safes.of(monthGroupChainIdDTOList).stream()
                .collect(Collectors.groupingBy(InternalAssistantReportExamineChainGroupDTO::getTypeId));
        Map<Integer, List<InternalAssistantReportExamineChainGroupDTO>> lastChainDetailGroupMap = Safes.of(lastGroupChainIdDTOList).stream()
                .collect(Collectors.groupingBy(InternalAssistantReportExamineChainGroupDTO::getTypeId));

        //构建明细数据
        for (InternalAssistantConfigDTO internalAssistantConfigDTO : configDTOList) {
            InternalAssistantTypeListDTO monthDTO = monthMap.get(internalAssistantConfigDTO.getTypeId());
            InternalAssistantTypeListDTO lastDayDTO = lastDayMap.get(internalAssistantConfigDTO.getTypeId());

            Map<Integer, InternalAssistantReportExamineChainGroupDTO> monthChainGroupDTOMap = Safes.of(monthChainDetailGroupMap.get(internalAssistantConfigDTO.getTypeId())).stream()
                    .collect(Collectors.toMap(InternalAssistantReportExamineChainGroupDTO::getChainId, Function.identity()));

            Map<Integer, InternalAssistantReportExamineChainGroupDTO> lastChainsGroupDTOMap = Safes.of(lastChainDetailGroupMap.get(internalAssistantConfigDTO.getTypeId())).stream()
                    .collect(Collectors.toMap(InternalAssistantReportExamineChainGroupDTO::getChainId, Function.identity()));

            //根据typeId获取数据
            monthDTO = Objects.nonNull(monthDTO) ? monthDTO :InternalAssistantTypeListDTO.initDefaultDTO();
            lastDayDTO = Objects.nonNull(lastDayDTO) ? lastDayDTO :InternalAssistantTypeListDTO.initDefaultDTO();

            Integer currentMonthTypeTotal = getTotalCount(Lists.newArrayList(monthDTO), StateEnum.NONE.getCode());
            //计算抽查率
            BigDecimal currentMonthSpotCheckRate = currentMonthTypeTotal.equals(NumberUtils.INTEGER_ZERO) ?
                    BigDecimal.ZERO :
                    BigDecimal.valueOf(monthDTO.getExamineCount()).divide(BigDecimal.valueOf(currentMonthTypeTotal),4,RoundingMode.HALF_UP);

            currentMonthTotal += currentMonthTypeTotal;
            currentMonthExamineTotal += monthDTO.getExamineCount();
            List<InternalAssistantExamineChainGroupDTO> list =Lists.newArrayList();
            //根据用户门店权限维度展示数据，chainIdList为用户有的权限
            for (Integer chainId : chainIdList) {
                InternalAssistantReportExamineChainGroupDTO monthChainDTO = monthChainGroupDTOMap.getOrDefault(chainId,InternalAssistantReportExamineChainGroupDTO.initZero());
                InternalAssistantReportExamineChainGroupDTO lastChainDTO = lastChainsGroupDTOMap.getOrDefault(chainId,InternalAssistantReportExamineChainGroupDTO.initZero());
                //本月汇总数据
                Integer monthTotalCount = getTotalCount(Lists.newArrayList(InternalAssistantTypeListDTO.builder()
                        .hasSignedCount(monthChainDTO.getHasSignedCount())
                        .signQualifiedCount(monthChainDTO.getSignQualifiedCount())
                        .signUnQualifiedCount(monthChainDTO.getSignUnQualifiedCount())
                        .unSignCount(monthChainDTO.getUnSignCount())
                        .build()), StateEnum.NONE.getCode());
                //昨日汇总数据
                Integer lastTotalCount = getTotalCount(Lists.newArrayList(InternalAssistantTypeListDTO.builder()
                        .hasSignedCount(lastChainDTO.getHasSignedCount())
                        .signQualifiedCount(lastChainDTO.getSignQualifiedCount())
                        .signUnQualifiedCount(lastChainDTO.getSignUnQualifiedCount())
                        .unSignCount(lastChainDTO.getUnSignCount())
                        .build()), StateEnum.NONE.getCode());

                //构建返回明细数据
                list.add(InternalAssistantExamineChainGroupDTO.builder()
                        .chainId(chainId)
                        .chainName(chainNameMapping.getOrDefault(chainId, Strings.EMPTY))
                        .currentMonthCount(getTotalCount(Lists.newArrayList(InternalAssistantTypeListDTO.builder()
                                .hasSignedCount(monthChainDTO.getHasSignedCount())
                                .signQualifiedCount(monthChainDTO.getSignQualifiedCount())
                                .signUnQualifiedCount(monthChainDTO.getSignUnQualifiedCount())
                                .unSignCount(monthChainDTO.getUnSignCount())
                                .build()),StateEnum.NONE.getCode()))
                        .spotCheckRate(
                                monthTotalCount.equals(NumberUtils.INTEGER_ZERO) ? BigDecimal.ZERO :
                                BigDecimal.valueOf(monthChainDTO.getExamineCount()).divide(BigDecimal.valueOf(monthTotalCount),4,BigDecimal.ROUND_HALF_UP))
                        .lastTotalCount(lastTotalCount)
                        .build());

            }

            //构建返回数据
            InternalAssistantExamineGroupDTO build = InternalAssistantExamineGroupDTO.builder()
                    .type(internalAssistantConfigDTO.getTypeId())
                    .typeName(internalAssistantConfigDTO.getTypeName())
                    .lastTotalCount(getTotalCount(Lists.newArrayList(lastDayDTO), StateEnum.NONE.getCode()))
                    .currentMonthCount(currentMonthTypeTotal)
                    .spotCheckRate(currentMonthSpotCheckRate)
                    .chainGroupDTOList(list)
                    .build();
            groupList.add(build);
        }

        BigDecimal currentMonthSpotCheckTotalRate = currentMonthTotal.equals(NumberUtils.INTEGER_ZERO) ?
                BigDecimal.ZERO :
                BigDecimal.valueOf(currentMonthExamineTotal).divide(BigDecimal.valueOf(currentMonthTotal),4, RoundingMode.HALF_UP);

        //构建返回数据
        return ExamineHomePageDTO.builder()
                .totalSpotCheckRate(currentMonthSpotCheckTotalRate)
                .typeList(groupList)
                .build();

    }


    /**
     * 构建签阅提交数据
     * @param param
     * @param employeeId
     * @param elementCodeSet
     * @return
     */
    public static SignSubmitParam buildSignSubmit(OmsSignSubmitParam param, String employeeId,Set<String> elementCodeSet,String submitEmployeeId) {
        if (Objects.isNull(param)) {
            return null;
        }
        SignSubmitParam signSubmitParam = new SignSubmitParam();
        signSubmitParam.setIds(param.getIds());
        signSubmitParam.setSignResult(param.getSignResult());
        signSubmitParam.setRemark(param.getRemark());
        signSubmitParam.setOssKeyList(param.getOssKeyList());
        signSubmitParam.setTypeId(param.getTypeId());
        signSubmitParam.setSubmitEmployeeId(employeeId);
        signSubmitParam.setResourceCodeSet(elementCodeSet);
        signSubmitParam.setChainId(param.getChainId());
        signSubmitParam.setSubmitEmployeeId(submitEmployeeId);
        return signSubmitParam;
    }


    /**
     * 构建AR账 悬单 签阅数据
     * @param param
     * @param employeeId
     * @param elementCodeSet
     * @return
     */
    public static SignTransSubmitParam buildSignTransSubmit(OmsSignTransSubmitParam param,String employeeId,Set<String> elementCodeSet) {
        if (Objects.isNull(param)) {
            return null;
        }
        SignTransSubmitParam signTransSubmitParam = new SignTransSubmitParam();
        signTransSubmitParam.setIds(param.getIds());
        signTransSubmitParam.setPlanRecoveryDate(param.getPlanRecoveryDate());
        signTransSubmitParam.setRemark(param.getRemark());
        signTransSubmitParam.setRecoveryName(param.getRecoveryName());
        signTransSubmitParam.setOssKeyList(param.getOssKeyList());
        signTransSubmitParam.setSubmitEmployeeId(employeeId);
        signTransSubmitParam.setTypeId(param.getTypeId());
        signTransSubmitParam.setResourceCodeSet(elementCodeSet);
        signTransSubmitParam.setChainId(param.getChainId());
        return signTransSubmitParam;
    }


    /**
     * 构建签阅撤销数据
     * @param param
     * @param employeeId
     * @param elementCodeSet
     * @return
     */
    public static SignRevokeParam transferRevoke(OmsSignRevokeParam param,String employeeId,Set<String> elementCodeSet) {
        if (Objects.isNull(param)) {
            return null;
        }
        SignRevokeParam signRevokeParam = new SignRevokeParam();
        signRevokeParam.setIds(param.getIds());
        signRevokeParam.setRemark(param.getRemark());
        signRevokeParam.setTypeId(param.getTypeId());
        signRevokeParam.setOperateEmployeeId(employeeId);
        signRevokeParam.setResourceCodeSet(elementCodeSet);
        return signRevokeParam;
    }

    /**
     * 构建审核撤销数据
     * @param param
     * @param employeeId
     * @param elementCodeSet
     * @return
     */
    public static SignRevokeParam transferExamineRevoke(OmsSignRevokeParam param,String employeeId,Set<String> elementCodeSet) {
        if (Objects.isNull(param)) {
            return null;
        }
        SignRevokeParam signRevokeParam = new SignRevokeParam();
        signRevokeParam.setIds(param.getIds());
        signRevokeParam.setRemark(param.getRemark());
        signRevokeParam.setTypeId(param.getTypeId());
        signRevokeParam.setOperateEmployeeId(employeeId);
        signRevokeParam.setResourceCodeSet(elementCodeSet);
        return signRevokeParam;
    }

    /**
     * 构建审批数据
     * @param param
     * @param employeeId
     * @param elementCodeSet
     * @return
     */
    public static SignApprovalParam transferApproval(OmsSignApprovalParam param,String employeeId,Set<String> elementCodeSet) {
        if (Objects.isNull(param)) {
            return null;
        }
        SignApprovalParam signApprovalParam = new SignApprovalParam();
        signApprovalParam.setIds(param.getIds());
        signApprovalParam.setIsAgree(param.getIsAgree());
        signApprovalParam.setRemark(param.getRemark());
        signApprovalParam.setCurrentState(param.getCurrentState());
        signApprovalParam.setTypeId(param.getTypeId());
        signApprovalParam.setOperateEmployeeId(employeeId);
        signApprovalParam.setResourceCodeSet(elementCodeSet);
        return signApprovalParam;
    }

    /**
     * 构建日志数据
     * @param optLogDTOList
     * @return
     */
    public static List<OperateDTO> transferOptLog(List<com.atour.pms.api.dto.internalAssistant.OptLogDTO> optLogDTOList,Map<String, String> employIdToFlowerNameDict) {
        if (CollectionUtils.isEmpty(optLogDTOList)) {
            return Collections.emptyList();
        }
        //根据主键id倒序排序
        optLogDTOList = optLogDTOList.stream()
                .sorted(Comparator.comparing(com.atour.pms.api.dto.internalAssistant.OptLogDTO::getId).reversed())
                .collect(Collectors.toList());
        //构建返回前端的日志体
        List<OperateDTO> list = Lists.newArrayList();
        for (com.atour.pms.api.dto.internalAssistant.OptLogDTO optLogDTO : optLogDTOList) {
            OperateDTO operateDTO = new OperateDTO();
            operateDTO.setOperator(employIdToFlowerNameDict.getOrDefault(optLogDTO.getOperator(),Strings.EMPTY));
            operateDTO.setOperatorType(optLogDTO.getOperatorType());
            operateDTO.setDiffContent(optLogDTO.getDiffContent());
            operateDTO.setCreateTime(optLogDTO.getCreateTime());
            list.add(operateDTO);
        }
        return list;
    }

    /**
     * 门店维度数据转换类型维度数据
     * @param internalAssistantExamineChainGroupDTOS
     * @return
     */
    public static List<InternalAssistantTypeListDTO> getTotalCountByTypeId(List<InternalAssistantReportExamineChainGroupDTO> internalAssistantExamineChainGroupDTOS){
        //根据酒店维度汇总数据
        Map<Integer, List<InternalAssistantReportExamineChainGroupDTO>> typeMap = Safes.of(internalAssistantExamineChainGroupDTOS).stream()
                .collect(Collectors.groupingBy(InternalAssistantReportExamineChainGroupDTO::getTypeId));

        List<InternalAssistantTypeListDTO> list =Lists.newArrayList();
        for (Map.Entry<Integer, List<InternalAssistantReportExamineChainGroupDTO>> typeEntry : typeMap.entrySet()) {
            List<InternalAssistantReportExamineChainGroupDTO> chainGroupDTOList = typeEntry.getValue();
            //已签阅条数
            Integer hasSignedCount = Safes.of(chainGroupDTOList).stream()
                    .map(InternalAssistantReportExamineChainGroupDTO::getHasSignedCount)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();
            //有效审核条数
            Integer examineCount = Safes.of(chainGroupDTOList).stream()
                    .map(InternalAssistantReportExamineChainGroupDTO::getExamineCount)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();
            //签阅合格条数
            Integer signQualifiedCount = Safes.of(chainGroupDTOList).stream()
                    .map(InternalAssistantReportExamineChainGroupDTO::getSignQualifiedCount)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();
            //签阅不合格条数
            Integer signUnqualifiedCount = Safes.of(chainGroupDTOList).stream()
                    .map(InternalAssistantReportExamineChainGroupDTO::getSignUnQualifiedCount)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();
            //未签阅条数
            Integer unSignCount = Safes.of(chainGroupDTOList).stream()
                    .map(InternalAssistantReportExamineChainGroupDTO::getUnSignCount)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();

            //构建数据
            list.add(InternalAssistantTypeListDTO.builder()
                    .typeId(typeEntry.getKey())
                    .signQualifiedCount(signQualifiedCount)
                    .signUnQualifiedCount(signUnqualifiedCount)
                    .hasSignedCount(hasSignedCount)
                    .examineCount(examineCount)
                    .unSignCount(unSignCount)
                    .build());

        }

        return list;
    }

}
