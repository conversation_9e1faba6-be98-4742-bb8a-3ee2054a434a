package com.atour.hotel.module.internalAssistant.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 悬单异常提醒
 * <AUTHOR>
 * @date 2021/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OmsAlertSuspendTransDTO extends OmsAlertBaseDTO {

    /**
     * 应收账户
     */
    private String memberName;
    /**
     * 超期金额
     */
    private BigDecimal overAmount;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;

}
