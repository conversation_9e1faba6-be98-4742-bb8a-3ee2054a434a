package com.atour.hotel.module.internalAssistant.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 审阅报表免费房DTO
 * <AUTHOR>
 * @date 2021/3/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SignReportFreeRoomExportDTO extends BaseRowModel {

    /**
     * 区域名称
     */
    @ExcelProperty("区域")
    private String areaName;

    /**
     * 酒店名称
     */
    @ExcelProperty("门店")
    private String chainName;

    /**
     * 免费类型：1免费房、2自用房
     */
    @ExcelProperty("类型")
    private String freeTypeStr;

    /**
     * 房单号
     */
    @ExcelProperty("房单号")
    private Long folioId;

    /**
     * 房间号
     */
    @ExcelProperty("房号")
    private String roomNo;

    /**
     * 房型名称
     */
    @ExcelProperty("房型")
    private String roomTypeName;

    /**
     * 预订人名称
     */
    @ExcelProperty("预订人")
    private String bookMember;

    /**
     * 入账科目名称
     */
    @ExcelProperty("入账科目")
    private String itemName;

    /**
     * 消费金额
     */
    @ExcelProperty("消费金额")
    private BigDecimal debit;

    /**
     * 结算金额
     */
    @ExcelProperty("结算金额")
    private BigDecimal credit;

    /**
     * 入账营业日
     */
    @ExcelProperty("入账营业日")
    private String createAccDate;

    /**
     * 操作人
     */
    @ExcelProperty("入账操作人")
    private String createOptUserName;
    /**
     * 现长确认结果
     * @see com.atour.dicts.db.atour_oms.internalAssistant.SignResultEnum
     */
    @ExcelProperty(value = "现长确认结果")
    private String signResultStr;

    /**
     * 审阅结果
     * @see com.atour.dicts.db.atour_oms.internalAssistant.StateEnum
     */
    @ExcelProperty(value = "签阅状态")
    private String stateStr;

    /**
     * 签阅、审核记录
     */
    @ExcelProperty(value = "签阅、审核记录")
    private String signRemark;
}
