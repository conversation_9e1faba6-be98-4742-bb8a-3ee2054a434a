package com.atour.hotel.module.internalAssistant.service.impl;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.atour.common.internalassistant.dto.SignReportFreeRoomDTO;
import com.atour.common.internalassistant.dto.SignReportPageDTO;
import com.atour.dicts.db.atour_oms.internalAssistant.SignTypeEnum;
import com.atour.hotel.module.internalAssistant.dto.export.SignReportFreeRoomExportDTO;
import com.atour.hotel.module.internalAssistant.param.OmsSignReportListParam;
import com.atour.hotel.module.internalAssistant.param.OmsSignReportSummaryParam;
import com.atour.hotel.module.internalAssistant.service.AbstractSignReportExport;
import com.atour.hotel.module.internalAssistant.service.SignReportExportService;
import com.atour.pms.api.enums.sign.OmsAreaGranularityEnum;
import com.atour.utils.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/16
 */
@Service
@Slf4j
public class SignReportFreeRoomExportServiceImpl extends AbstractSignReportExport implements SignReportExportService {


    @Override
    protected void doExportSummary(int pageNo, int pageSize, int dataSize, ExcelWriter writer,
                                   OmsSignReportSummaryParam param, OutputStream outputStream,
                                   OmsAreaGranularityEnum omsAreaGranularityEnum) throws Exception {
        return;
    }

    @Override
    protected void doExportReport(int pageNo, int pageSize, int datasSize, ExcelWriter writer, 
                                  OmsSignReportListParam param, OutputStream outputStream) throws Exception{
        Sheet sheet = new Sheet(DEFAULT_SHEET_NO, DEFAULT_HEAD_LINE, SignReportFreeRoomExportDTO.class);
        while (datasSize == pageSize && pageNo <= MAX_RETRY_COUNT){
            param.setPageNo(pageNo);
            SignReportPageDTO<SignReportFreeRoomDTO> datas = signReportService.list(param);
            List<SignReportFreeRoomDTO> list = datas.getList();
            List<SignReportFreeRoomExportDTO> exportDTO = Lists.newArrayList();
            for (SignReportFreeRoomDTO dto : list) {
                exportDTO.add(
                        SignReportFreeRoomExportDTO.builder()
                                .areaName(dto.getAreaName())
                                .chainName(dto.getChainName())
                                .signRemark(dto.getSignRemark())
                                .stateStr(getStateStr(dto.getState()))
                                .signResultStr(getSignResultStr(dto.getSignResult()))
                                .bookMember(dto.getBookMember())
                                .createAccDate(dto.getCreateAccDate())
                                .createOptUserName(dto.getCreateOptUserName())
                                .credit(dto.getCredit())
                                .debit(dto.getDebit())
                                .freeTypeStr(dto.getFreeType())
                                .itemName(dto.getItemName())
                                .roomNo(dto.getRoomNo())
                                .roomTypeName(dto.getRoomTypeName())
                                .folioId(dto.getFolioId())
                                .build()
                );
            }
            writer.write(exportDTO, sheet);
            datasSize = list.size();
            pageNo++;
        }
    }

    @Override
    public SignTypeEnum supportType() {
        return SignTypeEnum.FREE_ROOM;
    }
}
