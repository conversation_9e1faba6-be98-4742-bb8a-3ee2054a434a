package com.atour.hotel.module.internalAssistant.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * oms审核条件分组查询结果
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OmsExamineCountDTO {

    /**
     * 酒店id
     */
    private Integer chainId;
    /**
     * 总条数
     */
    private Integer totalCount;
    /**
     * 抽查条数
     */
    private Integer examineCount;

}
