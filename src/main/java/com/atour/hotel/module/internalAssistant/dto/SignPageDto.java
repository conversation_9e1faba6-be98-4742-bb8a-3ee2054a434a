package com.atour.hotel.module.internalAssistant.dto;

import com.atour.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SignPageDto", description = "内控助手app返回dto")
public class SignPageDto<T> {

    /**
     * 数据集合
     */
    private InternalAssistantListDTO data;


      /**
     * 具体数据
     */
    private List<T> signInfoList;

    /**
     * 分页数据
     */
    private PageInfo pageInfo;

}