package com.atour.hotel.module.internalAssistant.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 功能描述:退房后恢复入住报表导出
 *
 * @author: quan.ning
 * @date: 2023/7/18 16:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SignReportReCheckInExportDTO extends BaseRowModel {
    /**
     * 区域名称
     */
    @ExcelProperty("区域")
    private String areaName;

    /**
     * 酒店名称
     */
    @ExcelProperty("门店")
    private String chainName;

    /**
     * 房单号
     */
    @ExcelProperty("房单")
    private Long folioId;

    /**
     * 房号
     */
    @ExcelProperty("房号")
    private String roomNo;

    /**
     * 恢复营业日
     */
    @ExcelProperty("恢复营业日")
    private String reCheckInAccDate;

    /**
     * 恢复后离店时间
     */
    @ExcelProperty("恢复后离店时间")
    private Date depart;

    /**
     * 原入住人
     */
    @ExcelProperty("原入住人")
    private String oldGuestName;

    /**
     * 恢复后入住人
     */
    @ExcelProperty("恢复后入住人")
    private String newGuestName;

    /**
     * 恢复前结算金额
     */
    @ExcelProperty("恢复前结算金额")
    private BigDecimal oldSumDebit;

    /**
     * 恢复日夜审时结算金额
     */
    @ExcelProperty("恢复日夜审时结算金额")
    private BigDecimal reCheckInSumDebit;

    /**
     * 现长确认结果
     *
     * @see com.atour.dicts.db.atour_oms.internalAssistant.SignResultEnum
     */
    @ExcelProperty(value = "现长确认结果")
    private String signResultStr;

    /**
     * 审阅结果
     *
     * @see com.atour.dicts.db.center.sys_message.StateEnum
     */
    @ExcelProperty(value = "签阅状态")
    private String stateStr;

    /**
     * 签阅、审核记录
     */
    @ExcelProperty(value = "签阅、审核记录")
    private String signRemark;
}
