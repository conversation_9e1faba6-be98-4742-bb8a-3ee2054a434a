package com.atour.hotel.module.user.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.login.response.UserHotelDTO;
import com.atour.hotel.module.user.dto.SysUserDto;
import com.atour.hotel.module.user.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * 个人中心controller
 *
 * <AUTHOR>
 * @date 2019年5月27日
 */
@RestController
@RequestMapping(value = "/sysUser", produces = {"application/json;charset=UTF-8"})
@Api(tags = "用户信息")
public class SysUserController {

    @Resource
    private SysUserService sysUserService;

    /**
     * 用户个人信息
     *
     * @param userId
     * @return
     */
    @GetMapping(value = "/getUserInfo")
    @ApiOperation(value = "用户个人信息", httpMethod = "GET")
    public ApiResult<SysUserDto> getUserInfo(
        @ApiParam(value = "userId", required = true) @NotNull @RequestParam(value = "userId") Integer userId) {
        return ApiResult.success(sysUserService.getUserInfo(userId));
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @GetMapping(value = "/getUserInfoByToken")
    @ApiOperation(value = "获取登录用户信息", httpMethod = "GET")
    public ApiResult<UserHotelDTO> getUserInfoByToken(HttpServletRequest request) {
        return ApiResult.success(sysUserService.getUserInfo(request));
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @GetMapping(value = "/getUserInfoByTokenNoCache")
    @ApiOperation(value = "获取登录用户信息", httpMethod = "GET")
    public ApiResult<UserHotelDTO> getUserInfoByTokenNoCache(HttpServletRequest request) {
        return ApiResult.success(sysUserService.getUserInfoNoCache(request));
    }
}
