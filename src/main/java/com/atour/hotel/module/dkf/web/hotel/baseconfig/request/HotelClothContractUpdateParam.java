package com.atour.hotel.module.dkf.web.hotel.baseconfig.request;

import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/20
 * 变更合同信息入参传输对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class HotelClothContractUpdateParam implements Serializable {

    /**
     * 布草合同ID
     */
    private Long contractId;
    /**
     * 合同变更详情
     */
    private List<ContractDetail> contractUpdateDetails;
    @Data
    @Builder
    @AllArgsConstructor
    @RequiredArgsConstructor
    @Valid
    public static class ContractDetail {
        /**
         * 布草ID
         */
        @NotNull(message = "布草id不能为空")
        private Long clothId;
        /**
         * 展示状态（1：展开显示，2：折叠）
         */
        @NotNull(message = "展示状态不能为空")
        @Min(value = 1, message = "状态范围有误")
        @Max(value = 2, message = "状态范围有误")
        private Integer state;

    }
}
