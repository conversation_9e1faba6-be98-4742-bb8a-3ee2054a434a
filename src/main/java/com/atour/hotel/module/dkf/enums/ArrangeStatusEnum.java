package com.atour.hotel.module.dkf.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 排房状态枚举
 * @date 2024年04月26日17:04
 * @since JDK1.8
 */
@Getter
@AllArgsConstructor
public enum ArrangeStatusEnum {
    /**
     * 未排房
     */
    NO_ARRANGE_ROOM(0, "未排房"),

    /**
     * 已排房
     */
    ARRANGE_ROOM(1, "已排房"),

    /**
     * 取消排房
     */
    CANCEL_ROOM(2, "取消排房"),

    /**
     * 排房且打扫完成
     */
    COMPLETE_ROOM(3, "排房且打扫完成"),
    ;

    private Integer state;

    private String name;

    /**
     * 获取值对应的枚举对象
     * @param state
     * @return
     */
    public static ArrangeStatusEnum getInstance(Integer state) {
        for (ArrangeStatusEnum obj : ArrangeStatusEnum.values()) {
            if (obj.getState().equals(state) ) {
                return obj;
            }
        }
        return null;
    }
}

