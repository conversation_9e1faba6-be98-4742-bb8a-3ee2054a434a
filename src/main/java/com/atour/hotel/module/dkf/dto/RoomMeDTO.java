package com.atour.hotel.module.dkf.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 查询分配给此用户的房间信息结果实例
 *
 * <AUTHOR>
 * @date 2019-04-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "RoomMeDTO", description = "查询分配给此用户的房间信息结果实例")
public class RoomMeDTO implements Serializable {

    /**
     * 当前列表中最大的房间号
     */
    @ApiModelProperty(value = "当前列表中最大的房间主键ID")
    private Integer maxRoomNo;

    /**
     * 所有分配给此用户的房间
     */
    @ApiModelProperty(value = "所有分配给此用户的房间")
    private List<RoomQueryDTO> roomQueryMes;

    /**
     * 是否展示新版二级查房页面 true：展示；false:不展示
     */
    private boolean isShowNewSecRoundRoom = false;
}
