package com.atour.hotel.module.dkf.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 大清可排房的房间信息
 * @date 2024年04月25日15:55
 * @since JDK1.8
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CleanArrangeRoomDTO implements Serializable {
    private static final long serialVersionUID = 4973902395205816546L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    private Integer chainId;

    /**
     * 房间ID
     */
    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 房态
     */
    @ApiModelProperty(value = "房态(1:空隔夜 2:空脏房 3:在住脏换床单 4:在住脏房 5:空清洁房 6:空干净房 7:在住干净房 8:停售房)")
    private Integer roomState;

    /**
     * 房型名称
     */
    @ApiModelProperty(value = "房型名称")
    private String roomTypeName;

    /**
     * 打扫状态(1:未打扫 2:打扫中 3:已打扫)
     */
    @ApiModelProperty(value = "打扫状态(1:未打扫 2:打扫中 3:已打扫)")
    private Integer cleanStatus;

    /**
     * 房间楼层
     */
    private Integer floor;

    @Schema(description = "查房标识 1=查房中 0=未查房")
    private Integer checkRoomFlag;
    @Schema(description = "是否可用 1=不可用 0=可用")
    private Integer houseKeepState;
    @Schema(description = "入住状态 1=已入住 ")
    private Integer checkInState;
    @Schema(description = "是否预离店 1=是 ")
    private Integer isDepart;
    private Integer isTimeRoom;
}
