package com.atour.hotel.module.dkf.web.hotel.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.atour.common.versionlock.org.apache.commons.lang3.tuple.Pair;
import com.atour.db.redis.RedisLock;
import com.atour.hotel.common.enums.DeleteFlagEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.common.util.Summarizer;
import com.atour.hotel.framework.apllo.conf.BrokenMorningApolloConf;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.framework.template.ServiceHandleTemplate;
import com.atour.hotel.framework.template.calback.BaseServiceProcessCallBackNoResult;
import com.atour.hotel.module.dkf.app.request.inventory.*;
import com.atour.hotel.module.dkf.app.response.inventory.HotelRelationGoodsDTO;
import com.atour.hotel.module.dkf.app.response.inventory.InventoryRegisterMonthDetailQueryDTO;
import com.atour.hotel.module.dkf.app.response.inventory.InventoryRegisterMonthQueryDTO;
import com.atour.hotel.module.dkf.app.service.DkfBusinessRequestTaskService;
import com.atour.hotel.module.dkf.app.service.DkfInventoryRegisterBusinessService;
import com.atour.hotel.module.dkf.app.service.GoodsRegisterInventoryDetailService;
import com.atour.hotel.module.dkf.app.service.GoodsRegisterInventoryService;
import com.atour.hotel.module.dkf.enums.*;
import com.atour.hotel.module.dkf.web.hotel.baseconfig.service.GuestSupplyService;
import com.atour.hotel.module.dkf.web.hotel.json.GoldWarehouseInfo;
import com.atour.hotel.module.dkf.web.hotel.request.*;
import com.atour.hotel.module.dkf.web.hotel.response.*;
import com.atour.hotel.module.dkf.wrapper.DkfInventoryRegisterWrapper;
import com.atour.hotel.module.dkfb2b.request.AddStoreRequest;
import com.atour.hotel.module.dkfb2b.request.QueryStoreRequest;
import com.atour.hotel.module.dkfb2b.response.AddStoreResponse;
import com.atour.hotel.module.dkfb2b.response.QueryStoreResponse;
import com.atour.hotel.module.dkfb2b.service.GoldenButterflyService;
import com.atour.hotel.module.rbac.service.RbacUserService;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.param.supplies.OrderStoreParam;
import com.atour.hotel.persistent.roommanage.dao.GuestSuppliesConfigNewDao;
import com.atour.hotel.persistent.roommanage.dao.GuestSupplySkuNewEntityDao;
import com.atour.hotel.persistent.roommanage.dao.WarehouseDeptInfoMapper;
import com.atour.hotel.persistent.roommanage.dto.GuestSupplySkuDto;
import com.atour.hotel.persistent.roommanage.entity.*;
import com.atour.hotel.persistent.roommanage.param.GuestSupplySkuParam;
import com.atour.rbac.api.response.UserDTO;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 金蝶相关客用品
 */
@Log4j2
@Service
public class GoldGuestSuppliesInventoryService {

    @Resource
    private GoldenButterflyService goldenButterflyService;


    @Resource
    private GuestSupplyService guestSupplyService;

    @Resource
    private CommentHelper commentHelper;

    @Resource
    private RbacUserService rbacUserService;

    @Resource
    private GuestSupplySkuNewEntityDao guestSupplySkuNewEntityDao;
    @Resource
    private DkfInventoryRegisterBusinessService dkfInventoryRegisterBusinessService;
    @Autowired
    private GuestSuppliesConfigNewDao guestSuppliesConfigNewDao;

    @Autowired
    private DkfBusinessRequestTaskService dkfBusinessRequestTaskService;


    @Resource
    private BrokenMorningApolloConf brokenMorningApolloConf;


    @Resource
    private RedisLock lockRedis;

    @ApolloJsonValue("${gold.warehouse.info:[]}")
    private List<GoldWarehouseInfo> goldWarehouseInfoList;

    @Resource
    private WarehouseDeptInfoMapper warehouseDeptInfoMapper;

    @Resource
    private GoodsRegisterInventoryService registerInventoryService;

    @Resource
    private GoodsRegisterInventoryDetailService registerInventoryDetailService;


    private final String checkOutUserName = "离店客耗自动结算";


    public static List<Integer> syncStateList =Lists.newArrayList(BusinessRequestSyncStateEnum.NO_SYNC_STATE.getState(),BusinessRequestSyncStateEnum.FAIL_SYNC_STATE.getState());


   public List<GuestSuppliesDeptResponse> queryAllDeptList(Integer chainId){

       List<GuestSuppliesDeptResponse> list = goldWarehouseInfoList.stream().map(info -> GuestSuppliesDeptResponse.builder()
               .deptId(info.getCode())
               .deptName(info.getDesc())
               .status(1)
               .defaultDept(info.getDefaultWarehouse())
               .build()).collect(Collectors.toList());


       List<WarehouseDeptInfo> warehouseDeptInfoList = warehouseDeptInfoMapper.selectList(new LambdaQueryWrapper<WarehouseDeptInfo>()
               .eq(WarehouseDeptInfo::getChainId, chainId));

       Map<Integer, Integer> codeStatusMap = Safes.of(warehouseDeptInfoList).stream()
               .collect(Collectors.toMap(WarehouseDeptInfo::getCode, WarehouseDeptInfo::getStatus));
       Map<Integer, String> codeDescMap = Safes.of(list).stream()
               .filter(deptResponse-> Objects.equals(deptResponse.getDefaultDept(),Boolean.TRUE))
               .collect(Collectors.toMap(GuestSuppliesDeptResponse::getDeptId, GuestSuppliesDeptResponse::getDeptName));


       Map<Integer, String> dbNameMap = Safes.of(warehouseDeptInfoList).stream()
               .collect(Collectors.toMap(WarehouseDeptInfo::getCode, WarehouseDeptInfo::getWarehouseName));



       for (GuestSuppliesDeptResponse guestSuppliesDeptResponse : list) {
         Integer status =  codeStatusMap.getOrDefault(guestSuppliesDeptResponse.getDeptId(),0);
         String name =  codeDescMap.getOrDefault(guestSuppliesDeptResponse.getDeptId(),dbNameMap.get(guestSuppliesDeptResponse.getDeptId()));
           guestSuppliesDeptResponse.setStatus(status);
           guestSuppliesDeptResponse.setDeptName(StringUtils.isNotBlank(name) ? name :guestSuppliesDeptResponse.getDeptName());
           guestSuppliesDeptResponse.setIsMaster(Objects.equals(1,guestSuppliesDeptResponse.getDeptId()));
           if(guestSuppliesDeptResponse.getIsMaster()){
               guestSuppliesDeptResponse.setStatus(1);
           }
       }
    return list;
   }


    public List<GuestSuppliesDeptResponse> queryUseDeptList(Integer chainId){
        List<GuestSuppliesDeptResponse> guestSuppliesDeptResponses = queryAllDeptList(chainId);
        return guestSuppliesDeptResponses.stream()
                .filter(resp-> Objects.equals(resp.getStatus(),1))
                .collect(Collectors.toList());
    }


    public void deptUpdate(GoldDeptUpdateReq goldDeptUpdateReq) {
        List<WarehouseDeptInfo> warehouseDeptInfoList = warehouseDeptInfoMapper.selectList(new LambdaQueryWrapper<WarehouseDeptInfo>()
                .eq(WarehouseDeptInfo::getChainId, goldDeptUpdateReq.getChainId()));

        Map<String, List<GoldDeptUpdateReq.GoldDeptUpdateDetail>> collect = goldDeptUpdateReq.getList().stream()
                .collect(Collectors.groupingBy(GoldDeptUpdateReq.GoldDeptUpdateDetail::getDeptName));

        List<GoldDeptUpdateReq.GoldDeptUpdateDetail> goldDeptUpdateDetails = collect.values().stream()
                .filter(list -> list.size() > 1)
                .findFirst().orElse(null);

        if (CollectionUtils.isNotEmpty(goldDeptUpdateDetails)) {
            throw new BusinessException("仓库名称重复，请修改后再次添加");
        }

        Map<Integer, WarehouseDeptInfo> warehouseDeptInfoMap = warehouseDeptInfoList.stream()
                .collect(Collectors.toMap(WarehouseDeptInfo::getCode, Function.identity()));


        for (GoldDeptUpdateReq.GoldDeptUpdateDetail detail : goldDeptUpdateReq.getList()) {
            WarehouseDeptInfo warehouseDeptInfo = warehouseDeptInfoMap.get(detail.getDeptId());
            if(Objects.isNull(warehouseDeptInfo)){
                warehouseDeptInfoMapper.insert(WarehouseDeptInfo.builder()
                        .status(detail.getStatus())
                        .code(detail.getDeptId())
                        .chainId(goldDeptUpdateReq.getChainId())
                        .warehouseName(detail.getDeptName())
                        .build());
                continue;
            }
            warehouseDeptInfoMapper.updateById(WarehouseDeptInfo.builder()
                    .status(detail.getStatus())
                    .code(detail.getDeptId())
                    .chainId(goldDeptUpdateReq.getChainId())
                    .warehouseName(detail.getDeptName())
                            .id(warehouseDeptInfo.getId())
                    .build());
        }
    }






    public Boolean monthInventory(AddMonthInventoryParam addMonthInventoryParam){
        Set<Long> suppliesIdSet = addMonthInventoryParam.getStoreDetailList().stream()
                .map(AddMonthInventoryParam.StoreDetail::getGuestSuppliesId)
                .collect(Collectors.toSet());

        Map<Long, List<GuestSupplySkuDto>> suppliesMap = guestSupplyService.getSuppliesMap(GuestSupplySkuParam.builder()
                .supplyIdList(suppliesIdSet)
                .build());


        AddMonthInventoryParam.StoreDetail guestSuppLiesIdNull = addMonthInventoryParam.getStoreDetailList().stream()
                .filter(param -> Objects.isNull(suppliesMap.get(param.getGuestSuppliesId())))
                .findAny().orElse(null);

        if (Objects.nonNull(guestSuppLiesIdNull)) {
            throw new BusinessException("未找到客用品:" + guestSuppLiesIdNull.getGuestSuppliesName() + "对应的sku，请稍后重试");
        }

        List<GoldAddOtherOutRequest.ShkdSkuGsupOtherout> addStoreList = addMonthInventoryParam.getStoreDetailList().stream()
                .filter(param -> Objects.equals(param.getInventoryType(), 1))
                .map(storeDetail -> {
                    List<GuestSupplySkuDto> supplySkuGroupList = suppliesMap.get(storeDetail.getGuestSuppliesId());

                    List<GoldAddOtherOutRequest.ShkdSkuMatOtherout> collect = supplySkuGroupList.stream()
                            .map(supplyGroup -> {
                                return GoldAddOtherOutRequest.ShkdSkuMatOtherout.builder()
                                        .shkd_sku_materiel_number(supplyGroup.getSkuEntity().getSkuCode())
                                        .shkd_warehouse_number(storeDetail.getGoldWarehouseInfo().getGoldCode())
                                        .build();
                            }).collect(Collectors.toList());

                    return GoldAddOtherOutRequest.ShkdSkuGsupOtherout.builder()
                            .shkd_gsuppliesid(storeDetail.getGuestSuppliesId().toString())
                            .shkd_gsuppliesname(storeDetail.getGuestSuppliesName())
                            .shkd_gsuppliesqty(storeDetail.getCount())
                            .shkd_sku_mat_otherout(collect)
                            .build();
                }).filter(shkdSkuGsupOtherout ->  Safes.of(shkdSkuGsupOtherout.getShkd_gsuppliesqty(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());


        List<GoldAddOtherOutRequest.ShkdSkuGsupOtherout> addOutList = addMonthInventoryParam.getStoreDetailList().stream()
                .filter(param -> Objects.equals(param.getInventoryType(), 0))
                .map(storeDetail -> {
                    List<GuestSupplySkuDto> supplySkuGroupList = suppliesMap.get(storeDetail.getGuestSuppliesId());

                    List<GoldAddOtherOutRequest.ShkdSkuMatOtherout> collect = supplySkuGroupList.stream()
                            .map(supplyGroup -> {
                                return GoldAddOtherOutRequest.ShkdSkuMatOtherout.builder()
                                        .shkd_sku_materiel_number(supplyGroup.getSkuEntity().getSkuCode())
                                        .shkd_warehouse_number(storeDetail.getGoldWarehouseInfo().getGoldCode())
                                        .build();
                            }).collect(Collectors.toList());

                    return GoldAddOtherOutRequest.ShkdSkuGsupOtherout.builder()
                            .shkd_gsuppliesid(storeDetail.getGuestSuppliesId().toString())
                            .shkd_gsuppliesname(storeDetail.getGuestSuppliesName())
                            .shkd_gsuppliesqty(storeDetail.getCount().stripTrailingZeros())
                            .shkd_sku_mat_otherout(collect)
                            .build();
                }).filter(shkdSkuGsupOtherout -> Safes.of(shkdSkuGsupOtherout.getShkd_gsuppliesqty(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addStoreList)) {
            GoldAddOtherOutRequest.Data build = GoldAddOtherOutRequest.Data.builder()
                    .billno(addMonthInventoryParam.getBillNo())
                    .shkd_bizdate(DateUtil.formatDate(addMonthInventoryParam.getBusinessDate()))
                    .org_number(addMonthInventoryParam.getChainId().toString())
                    .shkd_comment("几木里-月度盘点-盘盈")
                    .shkd_biztype_number("3551")
                    .shkd_invscheme_number("3551")
                    .shkd_sku_gsup_otherout(addStoreList)
                    .shkd_isforceotherin(Boolean.TRUE)
                    .build();
            requestGold("[对接金蝶]-[月度盘点][盘盈]",build,()-> goldenButterflyService.addOtherOut(GoldAddOtherOutRequest.builder()
                    .data(build)
                    .build()),Boolean.TRUE);
        }
        if (CollectionUtils.isNotEmpty(addOutList)) {
            GoldAddOtherOutRequest.Data build = GoldAddOtherOutRequest.Data.builder()
                    .billno(addMonthInventoryParam.getBillNo()+"_1")
                    .shkd_bizdate(DateUtil.formatDate(addMonthInventoryParam.getBusinessDate()))
                    .org_number(addMonthInventoryParam.getChainId().toString())
                    .shkd_comment("几木里-月度盘点-盘亏")
                    .shkd_biztype_number("351")
                    .shkd_invscheme_number("351")
                    .shkd_sku_gsup_otherout(addOutList)
                    .build();

            requestGold("[对接金蝶]-[月度盘点][盘亏]",build,()-> goldenButterflyService.addOtherOut(GoldAddOtherOutRequest.builder()
                    .data(build)
                    .build()),Boolean.TRUE);
        }
        return Boolean.TRUE;
    }


    public void omsTransferDir(TransferDirInventoryParam param) {
        if (StringUtils.isBlank(param.getId())) {
            transferDir(param);
            return;
        }

        List<GoldGuestSuppliesConsumeDetailResp> goldGuestSuppliesConsumeDetailResps = queryTransferDetail(param.getChainId(),null,null, DateUtil.printMonth(param.getBusinessDate()));
        GoldGuestSuppliesConsumeDetailResp goldGuestSuppliesConsumeDetailResp = Safes.of(goldGuestSuppliesConsumeDetailResps).stream()
                .filter(resp -> Objects.equals(resp.getId(), param.getId()))
                .findAny().orElse(null);
        if (Objects.isNull(goldGuestSuppliesConsumeDetailResp)) {
            throw new BusinessException("编辑时，未找到对应的调拨数据");
        }

        Map<Long, Integer> saveTransferMap = Safes.of(goldGuestSuppliesConsumeDetailResp.getConsumeDetailList()).stream()
                .collect(Collectors.toMap(GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail::getId
                        , GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail::getConsumerCount));
        Map<Long, GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail> map = Maps.newHashMap();

        List<TransferDirInventoryParam.TransferDirDetail> list = Lists.newArrayList();

        for (TransferDirInventoryParam.TransferDirDetail transferDirDetail : param.getStoreDetailList()) {
            Integer originCount = saveTransferMap.getOrDefault(transferDirDetail.getGuestSuppliesId(),0);

            if (!Objects.equals(transferDirDetail.getCount().intValue(), originCount)) {
                Integer updateCount = transferDirDetail.getCount().intValue() - originCount;
                list.add(TransferDirInventoryParam.TransferDirDetail.builder()
                        .guestSuppliesId(transferDirDetail.getGuestSuppliesId())
                        .guestSuppliesName(transferDirDetail.getGuestSuppliesName())
                        .toSuppliesDeptEnum(goldGuestSuppliesConsumeDetailResp.getToSuppliesDeptEnum())
                        .originSuppliesDeptEnum(goldGuestSuppliesConsumeDetailResp.getOriginSuppliesDeptEnum())
                        .count(BigDecimal.valueOf(updateCount))
                        .build());
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("调拨数据无变动，无需修改");
        }
        param.setStoreDetailList(list);
        transferDir(param);
    }


    /**
     * @param param
     * @return
     * @throws Exception
     */
    public String transferDir(TransferDirInventoryParam param) {


        Date now = new Date();

        Set<Long> suppliesIdSet = param.getStoreDetailList().stream()
                .map(TransferDirInventoryParam.TransferDirDetail::getGuestSuppliesId)
                .collect(Collectors.toSet());

        Map<Long, List<GuestSupplySkuDto>> suppliesMap = guestSupplyService.getSuppliesMap(GuestSupplySkuParam.builder()
                .supplyIdList(suppliesIdSet)
                .build());

        List<GoldenTransferDirRequest.ShkdGsupplies> list = Lists.newArrayList();

        for (TransferDirInventoryParam.TransferDirDetail transferDirDetail : param.getStoreDetailList()) {

            List<GuestSupplySkuDto> guestSupplySkuDtos = suppliesMap.get(transferDirDetail.getGuestSuppliesId());
            if (CollectionUtils.isEmpty(guestSupplySkuDtos)) {
                continue;
            }

            Set<String> skuCodeSet = guestSupplySkuDtos.stream()
                    .map(GuestSupplySkuDto::getSkuEntity)
                    .map(GuestSupplySkuNewEntity::getSkuCode).collect(Collectors.toSet());

            List<GoldenTransferDirRequest.ShkdSkuMatTransdir> transdirList = skuCodeSet.stream()
                    .map(skuCode -> {
                        return GoldenTransferDirRequest.ShkdSkuMatTransdir.builder()
                                .shkd_sku_materiel_number(skuCode)
                                .shkd_warehouse_number(transferDirDetail.getOriginSuppliesDeptEnum().getGoldCode().toString())
                                .shkd_warehousein_number(transferDirDetail.getToSuppliesDeptEnum().getGoldCode().toString())
                                .build();

                    }).collect(Collectors.toList());


            list.add(GoldenTransferDirRequest.ShkdGsupplies.builder()
                    .shkd_gsuppliesid(transferDirDetail.getGuestSuppliesId().toString())
                    .shkd_gsuppliesqty(transferDirDetail.getCount().stripTrailingZeros())
                    .shkd_gsuppliesname(transferDirDetail.getGuestSuppliesName())
                    .shkd_sku_mat_transdir(transdirList)
                    .build());
        }
        if (CollectionUtils.isNotEmpty(list)) {
            GoldenTransferDirRequest.Data build = GoldenTransferDirRequest.Data.builder()
                    .billno(param.getBillNo())
                    .shkd_biztime(DateUtil.formatDate(param.getBusinessDate()))
                    .shkd_bookdate(DateUtil.formatDate(param.getBusinessDate()))
                    .shkd_operatorid(param.getOperationEmployeeId())
                    .shkd_comment(param.getComment())
                    .shkd_outorg_number(param.getChainId().toString())
                    .org_number(param.getChainId().toString())
                    .shkd_sku_gsup_transdir(list)
                    .build();

            requestGold("[对接金蝶][调拨]", build, () -> goldenButterflyService.transferDir(GoldenTransferDirRequest.builder()
                    .data(build)
                    .build()), Boolean.TRUE);
        }
        return param.getBillNo();
    }


    /**
     * 日盘点
     *
     * @param addDayInventoryParam
     * @return
     * @throws Exception
     */
    public Boolean dayInventory(AddDayInventoryParam addDayInventoryParam) throws Exception {

        List<AddDayInventoryParam.StoreDetail> collect = addDayInventoryParam.getStoreDetailList().stream()
                .filter(param -> !Objects.equals(param.getCount(), BigDecimal.ZERO))
                .collect(Collectors.toList());

        addDayInventoryParam.setStoreDetailList(collect);

        //盘盈的数量取反
        List<AddDayInventoryParam.StoreDetail> panyingList = addDayInventoryParam.getStoreDetailList()
                .stream()
                .filter(param -> Objects.equals(param.getInventoryType(), 1))
                .collect(Collectors.toList());
        //盘盈的数量取反
        List<AddDayInventoryParam.StoreDetail> pankuiList = addDayInventoryParam.getStoreDetailList()
                .stream()
                .filter(param -> Objects.equals(param.getInventoryType(), 0))
                .collect(Collectors.toList());


        if(CollectionUtils.isNotEmpty(pankuiList)){
            addDayInventoryParam.setStoreDetailList(pankuiList);
            inventory(addDayInventoryParam,"320");
        }

        if(CollectionUtils.isNotEmpty(panyingList)){
            addDayInventoryParam.setStoreDetailList(panyingList);
            addDayInventoryParam.setBillNo(addDayInventoryParam.getBillNo()+"_1");
            inventory(addDayInventoryParam,"3201");
        }

        return Boolean.TRUE;
    }


    public Boolean inventory(AddDayInventoryParam addDayInventoryParam,String inventoryCode) throws Exception{
        Set<Long> suppliesIdSet = addDayInventoryParam.getStoreDetailList().stream()
                .map(AddDayInventoryParam.StoreDetail::getGuestSuppliesId)
                .collect(Collectors.toSet());

        Map<Long, List<GuestSupplySkuDto>> suppliesMap = guestSupplyService.getSuppliesMap(GuestSupplySkuParam.builder()
                .supplyIdList(suppliesIdSet)
                .build());


        AddDayInventoryParam.StoreDetail guestSuppLiesIdNull = addDayInventoryParam.getStoreDetailList().stream()
                .filter(param -> Objects.isNull(suppliesMap.get(param.getGuestSuppliesId())))
                .findAny().orElse(null);

        if (Objects.nonNull(guestSuppLiesIdNull)) {
            throw new BusinessException("未找到客用品:" + guestSuppLiesIdNull.getGuestSuppliesName() + "对应的sku，请稍后重试");
        }

        List<GoldenUseConsumerRequest.ShkdGsupplies> lossSuppliesList = Lists.newArrayList();

        for (AddDayInventoryParam.StoreDetail storeDetail : addDayInventoryParam.getStoreDetailList()) {

            List<GuestSupplySkuDto> supplySkuGroupList = suppliesMap.get(storeDetail.getGuestSuppliesId());

            List<GoldenUseConsumerRequest.ShkdSkuMatTransdir> collect = supplySkuGroupList.stream()
                    .map(supplyGroup -> {
                        return GoldenUseConsumerRequest.ShkdSkuMatTransdir.builder()
                                .shkd_warehouse_number(storeDetail.getGoldDeptCode())
                                .shkd_sku_materiel_number(supplyGroup.getSkuEntity().getSkuCode())
                                .build();
                    }).collect(Collectors.toList());

            lossSuppliesList.add(GoldenUseConsumerRequest.ShkdGsupplies.builder()
                    .shkd_gsuppliesid(storeDetail.getGuestSuppliesId().toString())
                    .shkd_gsuppliesname(storeDetail.getGuestSuppliesName())
                    .shkd_gsuppliesqty(storeDetail.getCount())
                    .shkd_sku_mat_transdir(collect)
                    .build());
        }

        if (CollectionUtils.isNotEmpty(lossSuppliesList)) {
            GoldenUseConsumerRequest.Data build = GoldenUseConsumerRequest.Data.builder()
                    .billno(addDayInventoryParam.getBillNo())
                    .shkd_comment(addDayInventoryParam.getComment())
                    .org_number(addDayInventoryParam.getChainId().toString())
                    .shkd_biztime(DateUtil.formatDate(addDayInventoryParam.getBusinessDate()))
                    .shkd_operatorid(addDayInventoryParam.getOperationEmployeeId())
                    .shkd_sku_gsup_materialreq(lossSuppliesList)
                    .shkd_bizorg_number(addDayInventoryParam.getChainId().toString())
                    .shkd_invscheme_number(inventoryCode)
                    .shkd_biztype_number(inventoryCode)
                    .bizdept_number(inventoryCode)
                    .build();

            GoldenUseConsumerRequest build1 = GoldenUseConsumerRequest.builder()
                    .data(build)
                    .build();

            requestGold("[对接金蝶][日登记]", build1, () -> goldenButterflyService.addUseConsume(build1), Boolean.TRUE);
        }

        return Boolean.TRUE;
    }


    /**
     * 获得实时库存
     */
    public List<GetInventoryResponse> getInventory(GetInventoryParam getInventoryParam) {
        if (CollectionUtils.isEmpty(getInventoryParam.getSupplierIdList())) {
            return Collections.emptyList();
        }

        List<GetInventoryResponse> list = Lists.newArrayList();
        try {
            Map<String, List<GuestSupplySkuDto>> skuMap = guestSupplyService.getSkuMap(GuestSupplySkuParam
                    .builder()
                    .supplyIdList(getInventoryParam.getSupplierIdList())
                    .build());

            if (MapUtils.isEmpty(skuMap)) {
                return Collections.emptyList();
            }

            List<String> skuCodeList = skuMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(GuestSupplySkuDto::getSkuEntity)
                    .map(GuestSupplySkuNewEntity::getSkuCode)
                    .distinct()
                    .collect(Collectors.toList());


            GoldInventoryQueryRequest build = GoldInventoryQueryRequest.builder()
                    .omsInventoryQueryRequest(GoldInventoryQueryRequest.RequestInfo.builder()
                            .org_number(getInventoryParam.getChainId()
                                    .toString())
                            .material_numbers(skuCodeList)
                            .warehouse_numbers(Objects.isNull(getInventoryParam.getGoldWarehouseInfo()) ? Collections.emptySet() : Sets.newHashSet(getInventoryParam.getGoldWarehouseInfo().getGoldCode()))
                            .build()).build();


            Pair<Boolean,GoldInventoryQueryResponse> goldInventoryQueryResponse = requestGold("[对接金蝶][获取实时库存]",build,()->goldenButterflyService.inventoryQuery(build),Boolean.TRUE);
            if(!goldInventoryQueryResponse.getKey()){
                throw new BusinessException("获取实时库存异常，请稍后再试");
            }

            Map<String, BigDecimal> skuCodeCountMap = null;

            if (Objects.nonNull(getInventoryParam.getGoldWarehouseInfo())) {
                skuCodeCountMap = goldInventoryQueryResponse.getValue().getData().stream()
                        .filter(sku -> sku.getQty().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toMap(GoldInventoryQueryResponse.InventoryItem::getMaterial_number, GoldInventoryQueryResponse.InventoryItem::getQty, (o1, o2) -> o2));
            } else {
                skuCodeCountMap = Safes.of(goldInventoryQueryResponse.getValue().getData()).stream()
                        .filter(sku -> sku.getQty().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toMap(GoldInventoryQueryResponse.InventoryItem::getMaterial_number, GoldInventoryQueryResponse.InventoryItem::getQty, BigDecimal::add));
            }





            Map<Long, GuestSuppliesConfigNewEntity> guestSuppliesConfigNewEntityMap = parse(skuMap);

            Map<Long, BigDecimal> suppliesCountMap = buildGuestSuppliesCountMap(skuCodeCountMap, skuMap);

            Set<Long> supperIdSet = suppliesCountMap.keySet();
            Sets.SetView<Long> suppIdDiffSetView = Sets.symmetricDifference(supperIdSet, Sets.newHashSet(getInventoryParam.getSupplierIdList()));

            if(CollectionUtils.isNotEmpty(suppIdDiffSetView)){
                List<GuestSuppliesConfigNewEntity> guestSuppliesConfigNewEntities = guestSuppliesConfigNewDao.selectBatchIds(suppIdDiffSetView);
                Map<Long, BigDecimal> collect = Safes.of(guestSuppliesConfigNewEntities).stream()
                        .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, value->BigDecimal.ZERO));
                suppliesCountMap.putAll(collect);

                Map<Long, GuestSuppliesConfigNewEntity> entityMap = Safes.of(guestSuppliesConfigNewEntities).stream()
                        .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity()));
                guestSuppliesConfigNewEntityMap.putAll(entityMap);
            }


            for (Map.Entry<Long, BigDecimal> longBigDecimalEntry : suppliesCountMap.entrySet()) {

                GuestSuppliesConfigNewEntity entity = guestSuppliesConfigNewEntityMap.get(longBigDecimalEntry.getKey());

                list.add(GetInventoryResponse.builder()
                        .supplierId(longBigDecimalEntry.getKey())
                        .supplierName(entity.getName())
                        .count(longBigDecimalEntry.getValue().stripTrailingZeros())
                        .guestSuppliesConfigNewEntity(entity)
                        .build());
            }

        } catch (Exception e) {
            log.warn("获取实时库存出现异常，异常原因", e);
        }

        return list;
    }


    private Map<Long, GuestSuppliesConfigNewEntity> parse(Map<String, List<GuestSupplySkuDto>> map) {
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }
        return map.values().stream()
                .flatMap(Collection::stream)
                .map(GuestSupplySkuDto::getSuppliesEntity)
                .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 获取酒店的客用品维度实时库存
     *
     * @param getInventoryParam
     * @return 客用品和实时库存映射关系   k:客用品id  v: 实时库存数量
     */
    public Map<Long, BigDecimal> getInventoryToMap(GetInventoryParam getInventoryParam) {

        Map<Long, BigDecimal> map = new HashMap<>();
        if (map.size() > 0) {
            return map;
        }
        //客用品维度实时库存
        List<GetInventoryResponse> inventoryToMap = getInventory(getInventoryParam);
        if (CollectionUtils.isEmpty(inventoryToMap)) {
            return map;
        }

        //客用品和实时库存映射关系   k:客用品id  v: 实时库存数量
        return Safes.of(inventoryToMap).stream().
                collect(Collectors.toMap(GetInventoryResponse::getSupplierId, GetInventoryResponse::getCount, (a, b) -> b));
    }


    public List<MonthInventoryNumDetailDTO> getTransferDir(Integer chainId, GoldWarehouseInfo goldWarehouseInfo) {
        List<HotelRelationGoodsDTO> hotelRelationGoodsDTOS = dkfInventoryRegisterBusinessService.hotelRelationGoods(ChainGoodsQueryParam.builder()
                .chainId(chainId)
                .build());
        if (CollectionUtils.isEmpty(hotelRelationGoodsDTOS)) {
            return Collections.emptyList();
        }

        List<Long> supplyIdList = hotelRelationGoodsDTOS.stream()
                .map(HotelRelationGoodsDTO::getSupplyId)
                .distinct()
                .collect(Collectors.toList());

        List<GetInventoryResponse> inventoryList = getInventory(GetInventoryParam.builder()
                .chainId(chainId)
                .supplierIdList(supplyIdList)
                .goldWarehouseInfo(goldWarehouseInfo)
                .build());

        Map<Long, GetInventoryResponse> collect = inventoryList.stream()
                .collect(Collectors.toMap(GetInventoryResponse::getSupplierId, Function.identity(), (a, b) -> b));


        return hotelRelationGoodsDTOS.stream()
                .map(dto -> {
                    GetInventoryResponse entity = collect.getOrDefault(dto.getSupplyId(), new GetInventoryResponse());
                    return MonthInventoryNumDetailDTO.builder()
                            .guestSuppliesId(dto.getSupplyId())
                            .guestSuppliesName(dto.getSupplyName())
                            .guestSuppliesSource(entity.getGuestSuppliesConfigNewEntity().getSuppliesSource())
                            .guestSuppliesQuantity(Objects.isNull(entity.getCount()) ? 0 : entity.getCount().intValue())
                            .build();
                }).collect(Collectors.toList());
    }


    /**
     * 获取酒店的客用品维度实时库存
     *
     * @param getInventoryParam
     */
    public List<GoldGuestSuppliesConsumeInventoryResp> getInventoryCountByOms(GetInventoryParam getInventoryParam) {

        List<HotelRelationGoodsDTO> hotelRelationGoodsDTOS = dkfInventoryRegisterBusinessService.hotelRelationGoods(ChainGoodsQueryParam.builder()
                .chainId(getInventoryParam.getChainId())
                .build());
        if (CollectionUtils.isEmpty(hotelRelationGoodsDTOS)) {
            return Collections.emptyList();
        }
        List<Long> supperIdList = Safes.of(hotelRelationGoodsDTOS).stream()
                .map(HotelRelationGoodsDTO::getSupplyId)
                .collect(Collectors.toList());

        getInventoryParam.setSupplierIdList(supperIdList);
        //客用品维度实时库存
        List<GetInventoryResponse> inventoryList = getInventory(getInventoryParam);

        Map<Long, GetInventoryResponse> collect = Safes.of(inventoryList).stream()
                .collect(Collectors.toMap(GetInventoryResponse::getSupplierId, Function.identity()));

        List<GoldGuestSuppliesConsumeInventoryResp> list = Lists.newArrayList();

        for (HotelRelationGoodsDTO hotelRelationGoodsDTO : hotelRelationGoodsDTOS) {
            GetInventoryResponse response = collect.get(hotelRelationGoodsDTO.getSupplyId());
            list.add(GoldGuestSuppliesConsumeInventoryResp.builder()
                    .guestSuppliesId(hotelRelationGoodsDTO.getSupplyId())
                    .guestSuppliesName(hotelRelationGoodsDTO.getSupplyName())
                    .inventoryCount(Objects.isNull(response) ? 0 : Safes.of(response.getCount(), BigDecimal.ZERO).intValue())
                    .build());
        }

        return list;
    }


    /**
     * 查询入库明细
     *
     * @param chainId
     * @return
     * @throws Exception
     */
    public List<GoldGuestSuppliesStorageDetailResp> queryStorage(Integer chainId , String yearMonth) {


        Pair<String, String> firstAndLastDayOfMonth = getFirstAndLastDayOfMonth(yearMonth.replaceAll("-", ""));


        QueryStoreRequest.Data data = QueryStoreRequest.Data.builder()
                .biztime_begin(firstAndLastDayOfMonth.getKey())
                .biztime_end(firstAndLastDayOfMonth.getValue())
                .org_number(chainId.toString())
                .build();


        QueryStoreRequest build = QueryStoreRequest.
                builder()
                .pageNo(1)
                .pageSize(500)
                .data(data)
                .build();


        Pair<Boolean,QueryStoreResponse> queryStoreResponse = requestGold("[对接金蝶]-[入库明细]", build, () -> goldenButterflyService.queryStoreDetail(build), Boolean.TRUE);
        if(!queryStoreResponse.getKey()){
            return Collections.emptyList();
        }

        QueryStoreResponse.Data responseData = queryStoreResponse.getValue().getData();;

        List<String> skuCodeList = responseData.getRows().stream()
                .map(QueryStoreResponse.Data.Row::getBillentry)
                .flatMap(Collection::stream)
                .map(QueryStoreResponse.Data.BillEntry::getMaterial_number)
                .distinct()
                .collect(Collectors.toList());


        List<String> employeeIdList = responseData.getRows().stream().map(QueryStoreResponse.Data.Row::getShkd_operator)
                .filter(StringUtils::isNotBlank)
                .filter(operator -> operator.matches("\\d+"))
                .filter(operator -> !Objects.equals(operator, "0"))
                .distinct().collect(Collectors.toList());

        Map<String, List<GuestSupplySkuDto>> skuSupplyMap = guestSupplyService.getSkuMap(GuestSupplySkuParam.builder()
                .skuCodeList(skuCodeList)
                .queryAllFlag(1)
                .build());


        List<UserDTO> rbacUserDetailList = commentHelper.getRbacUserDetail(Sets.newHashSet(employeeIdList));
        Map<String, String> employeeIdNameMap = rbacUserDetailList.stream().collect(Collectors.toMap(UserDTO::getEmployeeId, UserDTO::getFlowerName));



        List<QueryStoreResponse.Data.Row> rows = responseData.getRows();
        rows.sort(Comparator.comparing(QueryStoreResponse.Data.Row::getCreatetime).reversed());

        List<GoldGuestSuppliesStorageDetailResp> detailRespList = Lists.newArrayList();
        for (QueryStoreResponse.Data.Row row : rows) {
            BigDecimal totalMoney = BigDecimal.ZERO;
            List<GoldGuestSuppliesStorageDetailGuestResp> list = Lists.newArrayList();

            for (QueryStoreResponse.Data.BillEntry billEntry : row.getBillentry()) {
                List<GuestSupplySkuDto> guestSupplySkuDtos = skuSupplyMap.get(billEntry.getMaterial_number());
                if (CollectionUtils.isEmpty(guestSupplySkuDtos)) {
                    continue;
                }
                GuestSuppliesConfigNewEntity suppliesEntity = guestSupplySkuDtos.get(0).getSuppliesEntity();
                totalMoney = totalMoney.add(Safes.of(billEntry.getAmountandtax(),BigDecimal.ZERO));
                list.add(GoldGuestSuppliesStorageDetailGuestResp.builder()
                        .guestSuppliesId(suppliesEntity.getId())
                        .guestSuppliesName(suppliesEntity.getName())
                        .taxRate(billEntry.getTaxrate().stripTrailingZeros()+"%")
                        .guestSuppliesCount(billEntry.getQty().stripTrailingZeros())
                        .totalMoney(billEntry.getAmountandtax())
                        .guestSuppliesAmountSingleInTax(divide(billEntry.getAmountandtax(),billEntry.getQty(),4))
                        .build());
            }
            detailRespList.add(GoldGuestSuppliesStorageDetailResp
                    .builder()
                    .id(row.getId())
                    .operationUserId(StringUtils.isNotBlank(row.getShkd_operator()) && row.getShkd_operator().matches("\\d+") ? row.getShkd_operator() : "0")
                    .operationUserName(StringUtils.isNotBlank(row.getShkd_operator()) && !Objects.equals(row.getShkd_operator(), "0") ? employeeIdNameMap.getOrDefault(row.getShkd_operator(), "") : "采购单入库")
                    .guestSuppliesSource(StringUtils.isNotBlank(row.getShkd_operator()) && !Objects.equals(row.getShkd_operator(), "0") ? "线下入库" : "采购单入库")
                    .storageTime(row.getBiztime())
                    .createTime(DateUtil.parseTimestamp(row.getCreatetime().getTime()))
                    .totalMoney(totalMoney)
                    .goldSuppliesRespList(list)
                    .build());

        }


        return detailRespList;
    }


    public List<GoldLastMonthDetailDTO> lastMonthDetail(Integer chainId, String queryMonth) {


        String goldQueryMonth = queryMonth.replaceAll("-", "");
        GoldInventoryReportRequest.Data build = GoldInventoryReportRequest.Data.builder()
                .startperiod(goldQueryMonth)
                .endperiod(goldQueryMonth)
                .mucalorg(chainId.toString())
                .mulwarehouse(Lists.newArrayList(SuppliesDeptEnum.GENERAL_LIBRARY.getGoldCode()))
                .material(Collections.emptyList())
                .build();


        Pair<Boolean,GoldCostRecordResponse> goldCostRecordResponse = requestGold("[月客用品-月度盘点报表-上月结余]", build, () -> goldenButterflyService.report(GoldInventoryReportRequest.builder()
                .data(build)
                .build()),Boolean.FALSE);

        if (Objects.isNull(goldCostRecordResponse) ||
                !goldCostRecordResponse.getKey() ||
                CollectionUtils.isEmpty(goldCostRecordResponse.getValue().getData())) {
            return Collections.emptyList();
        }


        Set<String> skuCodeSet = goldCostRecordResponse.getValue().getData().stream()
                .map(GoldCostRecordResponse.MaterialCost::getMaterial_number)
                .collect(Collectors.toSet());

        Map<String, List<GuestSupplySkuDto>> skuMap = guestSupplyService.getSkuMap(GuestSupplySkuParam.builder()
                .skuCodeList(skuCodeSet)
                .queryAllFlag(1)
                .build());



        List<GoldTotalCostResponse> inventoryTotalList = getInventoryTotal(goldCostRecordResponse.getValue().getData(), GoldCostRecordResponse.MaterialCost::getMaterial_number,
                GoldCostRecordResponse.MaterialCost::getActualcost,
                GoldCostRecordResponse.MaterialCost::getBaseqty,
                GoldCostRecordResponse.MaterialCost::getRate, skuMap);

        BigDecimal totalInTax = inventoryTotalList
                .stream()
                .map(GoldTotalCostResponse::getAmountInTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        List<GoldGuestSuppliesStorageDetailGuestResp> collect = inventoryTotalList.stream()
                .map(response -> {
                    GuestSuppliesConfigNewEntity entity = response.getGuestSuppliesConfigNewEntity();
                    return GoldGuestSuppliesStorageDetailGuestResp.builder()
                            .guestSuppliesName(entity.getName())
                            .guestSuppliesId(entity.getId())
                            .totalMoney(response.getAmountInTax().stripTrailingZeros())
                            .guestSuppliesCount(response.getCount().stripTrailingZeros())
                            .taxRate((response.getRate().multiply(BigDecimal.valueOf(100))).stripTrailingZeros()+"%")
                            .guestSuppliesAmountSingleInTax(divide(response.getAmountInTax(), response.getCount(), 4).stripTrailingZeros())
                            .build();
                }).collect(Collectors.toList());

        return Lists.newArrayList(GoldLastMonthDetailDTO.builder()
                .totalAmount(totalInTax)
                .guestSuppliesList(collect)
                .build());
    }


    /**
     * 按照客用品id维度 帮你组装含税总价 不含税总价 总数量
     *
     * @param goldCostRecordResponse
     * @param skuMapper
     * @param noTaxMapper
     * @param countMapper
     * @param rateMapper
     * @param skuMap
     * @return
     */
    public List<GoldTotalCostResponse> getInventoryTotal(List<GoldCostRecordResponse.MaterialCost> goldCostRecordResponse,
                                                         Function<? super GoldCostRecordResponse.MaterialCost, ? extends String> skuMapper,
                                                         Function<? super GoldCostRecordResponse.MaterialCost, ? extends BigDecimal> noTaxMapper,
                                                         Function<? super GoldCostRecordResponse.MaterialCost, ? extends BigDecimal> countMapper,
                                                         Function<? super GoldCostRecordResponse.MaterialCost, ? extends BigDecimal> rateMapper,
                                                         Map<String, List<GuestSupplySkuDto>> skuMap) {

        if (CollectionUtils.isEmpty(goldCostRecordResponse)) {
            return Collections.emptyList();
        }

        //不含税
        Map<String, BigDecimal> startTotalMoneyNoTax = goldCostRecordResponse.stream()
                .collect(Collectors.toMap(skuMapper, noTaxMapper));
        Map<Long, BigDecimal> startTotalMoneyGuestSuppliesNoTaxMap = buildGuestSuppliesCountMap(startTotalMoneyNoTax, skuMap);

        //含税
        Map<String, BigDecimal> startTotalMoneyInTaxMap = goldCostRecordResponse.stream()
                .collect(Collectors.toMap(skuMapper, cost -> noTaxMapper.apply(cost).multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> startTotalMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(startTotalMoneyInTaxMap, skuMap);


        //数量
        Map<String, BigDecimal> startTotalCountMap = goldCostRecordResponse.stream()
                .collect(Collectors.toMap(skuMapper, countMapper));
        Map<Long, BigDecimal> startTotalCountGuestSupplieMap = buildGuestSuppliesCountMap(startTotalCountMap, skuMap);


        Map<String, BigDecimal> rateMap = goldCostRecordResponse.stream()
                .collect(Collectors.toMap(skuMapper, rateMapper));
        Map<Long, BigDecimal> guestRateMap = buildGuestSuppliesCountMap(rateMap, skuMap);

        Map<String, BigDecimal> countMap = goldCostRecordResponse.stream()
                .collect(Collectors.toMap(skuMapper, cost -> new BigDecimal(1)));
        Map<Long, BigDecimal> guestCountMap = buildGuestSuppliesCountMap(countMap, skuMap);


        Collection<GuestSuppliesConfigNewEntity> values = skuMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(GuestSupplySkuDto::getSuppliesEntity)
                .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity(), (existing, replacement) -> existing))
                .values();
        List<GoldTotalCostResponse> list = Lists.newArrayList();
        for (GuestSuppliesConfigNewEntity entity : values) {
            BigDecimal inTax = startTotalMoneyGuestSuppliesInTaxMap.getOrDefault(entity.getId(), BigDecimal.ZERO);
            BigDecimal count = startTotalCountGuestSupplieMap.getOrDefault(entity.getId(), BigDecimal.ZERO);
            BigDecimal noTax = startTotalMoneyGuestSuppliesNoTaxMap.getOrDefault(entity.getId(), BigDecimal.ZERO);
            BigDecimal totalRate = guestRateMap.getOrDefault(entity.getId(), BigDecimal.ZERO);
            BigDecimal catecoryCount = guestCountMap.getOrDefault(entity.getId(), BigDecimal.ZERO);

            list.add(GoldTotalCostResponse.builder()
                    .rate(divide(totalRate, catecoryCount, 4).stripTrailingZeros())
                    .amountInTax(inTax.stripTrailingZeros())
                    .amountNoTax(noTax.stripTrailingZeros())
                    .count(count.stripTrailingZeros())
                    .guestSuppliesConfigNewEntity(entity)
                    .build());
        }
        return list;
    }


    /**
     * 查询调拨明细
     *
     * @param chainId
     * @param yearMonth
     * @return
     * @throws Exception
     */
    public List<GoldGuestSuppliesConsumeDetailResp> queryTransferDetail(Integer chainId, Integer type,Integer deptId,String yearMonth) {

        Pair<String, String> firstAndLastDayOfMonth = getFirstAndLastDayOfMonth(yearMonth.replaceAll("-", ""));


        GoldQueryTransferDirRequest.Data data = GoldQueryTransferDirRequest.Data.builder()
                .biztime_begin(firstAndLastDayOfMonth.getKey())
                .biztime_end(firstAndLastDayOfMonth.getValue())
                .org_number(chainId.toString())
                .outorg_number(chainId.toString())
                .build();

        GoldQueryTransferDirRequest build = GoldQueryTransferDirRequest.builder()
                .data(data)
                .pageNo(1)
                .pageSize(500)
                .build();

        GoldQueryTransferDirResponse goldQueryTransferDirResponse = requestGold("[月客用品-查询调拨明细-调用金蝶]", build, () -> goldenButterflyService.queryTransferDirDetail(build),Boolean.FALSE);

        if (Objects.isNull(goldQueryTransferDirResponse) ||
                Objects.isNull(goldQueryTransferDirResponse.getData())
                || CollectionUtils.isEmpty(goldQueryTransferDirResponse.getData().getRows())) {
            return Collections.emptyList();
        }


        List<String> skuCodeList = (goldQueryTransferDirResponse.getData().getRows()).stream()
                .map(GoldQueryTransferDirResponse.Row::getBillentry)
                .flatMap(List::stream)
                .filter(bill->{
                     String warehouseNumber = Objects.equals(type,0) ? bill.getOutwarehouse_number() : bill.getWarehouse_number();
                    return Objects.isNull(deptId) || Objects.isNull(type) || Objects.equals(warehouseNumber, SuppliesDeptEnum.getInstance(deptId,goldWarehouseInfoList).getGoldCode());
                })
                .map(GoldQueryTransferDirResponse.BillEntry::getMaterial_number)
                .collect(Collectors.toList());


        if(CollectionUtils.isEmpty(skuCodeList)){
            return Collections.emptyList();
        }

        Map<String, List<GuestSupplySkuDto>> skuMap = guestSupplyService.getSkuMap(GuestSupplySkuParam
                .builder()
                .skuCodeList(skuCodeList)
                        .queryAllFlag(1)
                .build());

        Set<String> employeeSet = goldQueryTransferDirResponse.getData().getRows().stream()
                .map(GoldQueryTransferDirResponse.Row::getShkd_operatorid)
                .filter(StringUtils::isNotBlank)
                .filter(operator -> !Objects.equals(operator, "0"))
                .collect(Collectors.toSet());

        List<UserDTO> rbacUserDetailList = rbacUserService.getRbacUserDetail(employeeSet);
        Map<String, String> employeeIdNameMap = Safes.of(rbacUserDetailList).stream()
                .collect(Collectors.toMap(UserDTO::getEmployeeId, UserDTO::getFlowerName));

        List<GoldQueryTransferDirResponse.Row> rows = Safes.of(goldQueryTransferDirResponse.getData().getRows());

        List<GuestSuppliesDeptResponse> guestSuppliesDeptResponses = queryAllDeptList(chainId);



        List<GoldGuestSuppliesConsumeDetailResp> collect = rows.stream()
                .map(response -> {


                    Map<String, BigDecimal> skuCodeCountMap = response.getBillentry().stream()
                            .collect(Collectors.groupingBy(
                                    GoldQueryTransferDirResponse.BillEntry::getMaterial_number,
                                    Collectors.reducing(BigDecimal.ZERO, entry -> new BigDecimal(entry.getQty()), BigDecimal::add)
                            ))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


                    String flowerName =StringUtils.isEmpty(employeeIdNameMap.get(response.getShkd_operatorid()))?  "" : employeeIdNameMap.get(response.getShkd_operatorid());

                    GoldWarehouseInfo originSuppliesDeptEnum = SuppliesDeptEnum.getInstanceByGoldCode(response.getBillentry().get(0).getOutwarehouse_number(),goldWarehouseInfoList,guestSuppliesDeptResponses);
                    GoldWarehouseInfo toSuppliesDeptEnum = SuppliesDeptEnum.getInstanceByGoldCode(response.getBillentry().get(0).getWarehouse_number(),goldWarehouseInfoList,guestSuppliesDeptResponses);

                    Map<Long, BigDecimal> longBigDecimalMap = buildGuestSuppliesCountMap(skuCodeCountMap, skuMap);

                    Map<Long, String> suppliesIdNameMap = skuMap.values().stream()
                            .flatMap(Collection::stream)
                            .map(GuestSupplySkuDto::getSuppliesEntity)
                            .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, GuestSuppliesConfigNewEntity::getName, (o1, o2) -> o2));

                    List<GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail> list = Lists.newArrayList();

                    for (Map.Entry<Long, BigDecimal> longBigDecimalEntry : longBigDecimalMap.entrySet()) {
                        Long id = longBigDecimalEntry.getKey();
                        BigDecimal count  = Safes.of(longBigDecimalEntry.getValue(),BigDecimal.ZERO);
                        String name = suppliesIdNameMap.getOrDefault(id, "");
                        list.add(GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail
                                .builder()
                                .consumerCount(count.intValue())
                                .name(name)
                                .id(id)
                                .build());
                    }

                    return GoldGuestSuppliesConsumeDetailResp.builder()
                            .batchNo(response.getBillno())
                            .operationUserId(StringUtils.isNotBlank(response.getShkd_operatorid()) ? response.getShkd_operatorid() : "")
                            .originSuppliesDeptEnum(originSuppliesDeptEnum)
                            .originConsumeDeptName(originSuppliesDeptEnum.getDesc())
                            .originDeptId(originSuppliesDeptEnum.getCode())
                            .toSuppliesDeptEnum(toSuppliesDeptEnum)
                            .toDeptId(toSuppliesDeptEnum.getCode())
                            .consumeDeptName(toSuppliesDeptEnum.getDesc())
                            .consumeTime(response.getBiztime().getTime())
                            .operationUserName(flowerName)
                            .consumeDetailList(list)
                            .id(response.getId())
                            .createTime(DateUtil.parseDatetime(response.getCreatetime()))
                            .build();
                }).collect(Collectors.toList());

        collect = collect.stream()
                .filter(bill->{
            Integer queryDeptId = Objects.equals(type,0) ? bill.getOriginDeptId() : bill.getToDeptId();
            return Objects.isNull(deptId) || Objects.isNull(type) || Objects.equals(queryDeptId,deptId);
        }).collect(Collectors.toList());

        collect.sort(Comparator.comparing(GoldGuestSuppliesConsumeDetailResp::getCreateTime).reversed());
        return collect;
    }


    public Map<Long, BigDecimal> buildGuestSuppliesCountMap(Map<String, BigDecimal> skuCountMap, Map<String, List<GuestSupplySkuDto>> skuMap) {
        Map<Long, BigDecimal> map = Maps.newHashMap();

        if (MapUtils.isEmpty(skuMap) || MapUtils.isEmpty(skuCountMap)) {
            return map;
        }

        for (Map.Entry<String, BigDecimal> stringBigDecimalEntry : skuCountMap.entrySet()) {
            BigDecimal value = stringBigDecimalEntry.getValue();
            List<GuestSupplySkuDto> guestSupplySkuList = skuMap.get(stringBigDecimalEntry.getKey());
            if (CollectionUtils.isEmpty(guestSupplySkuList)) {
                continue;
            }
            GuestSupplySkuDto guestSupplySkuDto = guestSupplySkuList.get(0);
            BigDecimal guestSuppliesCount = map.getOrDefault(guestSupplySkuDto.getSuppliesEntity().getId(), BigDecimal.ZERO);
            map.put(guestSupplySkuDto.getSuppliesEntity().getId(), value.add(guestSuppliesCount).setScale(4, RoundingMode.HALF_UP));
        }
        return map;
    }


    public List<ChainGoldStorageListResponse> getGuestSuppliesByChain(Integer chainId) {
        List<HotelRelationGoodsDTO> hotelRelationGoodsDTOList = dkfInventoryRegisterBusinessService.hotelRelationGoods(ChainGoodsQueryParam.builder()
                .chainId(chainId)
                .build());

        return Safes.of(hotelRelationGoodsDTOList).stream()
                .map(dto -> {
                    return ChainGoldStorageListResponse.builder()
                            .guestSuppliesId(dto.getSupplyId())
                            .guestSuppliesName(dto.getSupplyName())
                            .build();
                }).collect(Collectors.toList());

    }


    /**
     * 入库
     *
     * @param suppliesStoreSaveReq
     * @throws Exception
     */
    public void storageSave(SuppliesStoreSaveReq suppliesStoreSaveReq) {
        ServiceHandleTemplate.executeNoResult(new BaseServiceProcessCallBackNoResult() {
            AddStoreRequest request;
            Pair<Boolean,AddStoreResponse> addStoreResponse;

            @Override
            protected void processNoResult() {
                String reqChainId = suppliesStoreSaveReq.getChainId()
                        .toString();


                Boolean preChainId = brokenMorningApolloConf.isPre(suppliesStoreSaveReq.getChainId());

                if(!preChainId){
                    log.info("[调用金蝶-入库]非灰度门店，需要过滤,chainId:{}",suppliesStoreSaveReq.getChainId());
                    return;
                }


                List<AddStoreRequest.BillEntry> list = suppliesStoreSaveReq.getGuestSuppliesInfoParamList()
                        .stream()
                        .map(guestSuppliesStoreDetailSaveParam -> {

                            BigDecimal rate = divide(guestSuppliesStoreDetailSaveParam.getTaxRate(), BigDecimal.valueOf(100), 8);
                            BigDecimal priceInTax = guestSuppliesStoreDetailSaveParam.getGuestSuppliesUnitPriceIntax();
                            BigDecimal count = BigDecimal.valueOf(guestSuppliesStoreDetailSaveParam.getCount());
                            BigDecimal s = divide(priceInTax, BigDecimal.ONE.add(rate),8);
                            BigDecimal t = s.multiply(rate);
                            // 计算单价
                            BigDecimal unitPrice = priceInTax.subtract(t);
                            // 计算金额
                            BigDecimal amount = unitPrice.multiply(count);

                            // 计算税额
                            BigDecimal taxAmount = divide(priceInTax, BigDecimal.ONE.add(rate), 8).multiply(rate).multiply(count);

                            // 计算价税合计
                            BigDecimal priceTaxTotal = priceInTax.multiply(count);

                            // 计算实际单价、实际含税单价、税额(本位币)、金额(本位币)、价税合计(本位币)
                            BigDecimal actualUnitPrice = unitPrice;
                            BigDecimal actualPriceInTax = priceInTax;
                            BigDecimal taxAmountInCurrency = taxAmount;
                            BigDecimal amountInCurrency = amount;
                            BigDecimal priceTaxTotalInCurrency = priceTaxTotal;


                            return AddStoreRequest.BillEntry.builder()
                                    .linetype_number("010")
                                    .material_number(guestSuppliesStoreDetailSaveParam.getSkuCodeList()
                                            .get(0))
                                    .qty(BigDecimal.valueOf(guestSuppliesStoreDetailSaveParam.getCount()))
                                    .baseqty(BigDecimal.valueOf(guestSuppliesStoreDetailSaveParam.getCount())
                                            .toString())
                                    .taxrateid_taxrate(Objects.nonNull(guestSuppliesStoreDetailSaveParam.getTaxRate()) ? guestSuppliesStoreDetailSaveParam.getTaxRate().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                                    .priceandtax(guestSuppliesStoreDetailSaveParam.getGuestSuppliesUnitPriceIntax().setScale(2, RoundingMode.HALF_UP))
                                    .warehouse_number(guestSuppliesStoreDetailSaveParam.getSuppliesDeptEnum()
                                            .getGoldCode())
                                    .srcsystem("OMS")
                                    .baseunit_name("pcs")
                                    .unit_name("pcs")
                                    .keepertype("bos_org")
                                    .keeper_number(reqChainId)
                                    .srcsysbillentryid(guestSuppliesStoreDetailSaveParam.getSrcsysbillentryid())
                                    .taxamount(taxAmount.setScale(2, RoundingMode.HALF_UP))
                                    .amountandtax(priceTaxTotal.setScale(2, RoundingMode.HALF_UP))
                                    .price(unitPrice.setScale(2, RoundingMode.HALF_UP))
                                    .amount(amount.setScale(2, RoundingMode.HALF_UP))
                                    .actualprice(actualUnitPrice.setScale(2, RoundingMode.HALF_UP))
                                    .actualtaxprice(actualPriceInTax.setScale(2, RoundingMode.HALF_UP))
                                    .curtaxamount(taxAmountInCurrency.setScale(2, RoundingMode.HALF_UP))
                                    .curamount(amountInCurrency.setScale(2, RoundingMode.HALF_UP))
                                    .curamountandtax(priceTaxTotalInCurrency.setScale(2, RoundingMode.HALF_UP))
                                    .baseunit_name(guestSuppliesStoreDetailSaveParam.getUnitName())
                                    .unit_name(guestSuppliesStoreDetailSaveParam.getUnitName())
                                    .build();
                        }).collect(Collectors.toList());

                AddStoreRequest.Data addStoreRequestData = AddStoreRequest.Data.builder().
                        biztime(DateUtil.formatDate(new Date(suppliesStoreSaveReq.getStorageDate())))
                        .comment(suppliesStoreSaveReq.getComment())
                        .org_number(reqChainId)
                        .billtype_number("im_PurInBill_STD_BT_S")
                        .bizorg_number(reqChainId)
                        .biztype_number("110")
                        .invscheme_number("110")
                        .supplier_number(suppliesStoreSaveReq.getSupplierNumber())
                        .shkd_outbillno(suppliesStoreSaveReq.getStoreNo())
                        .istax(1L)
                        .shkd_operator(suppliesStoreSaveReq.getOperateId())
                        .billentry(list)
                        .build();
                List<AddStoreRequest.Data> requestDataList = Lists.newArrayList();
                requestDataList.add(addStoreRequestData);
                request = new AddStoreRequest();
                request.setData(requestDataList);

                addStoreResponse = requestGold("[月客用品-调用金蝶-入库]", request, () -> goldenButterflyService.addStore(request),Boolean.TRUE);
            }

            @Override
            public void afterProcess() {
                 if(Objects.isNull(addStoreResponse)){
                    return;
                }

                if(!addStoreResponse.getKey()){
                    throw new BusinessException("操作入库失败，请稍后再试");
                }
                //如果入库成功就更新一下对应的skuCode状态
                    //获取request里面的skuCode参数
                    List<String> skuCodeList = Optional.ofNullable(request)
                            .map(AddStoreRequest::getData)
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(AddStoreRequest.Data::getBillentry)
                            .flatMap(guestStoreBillEntryList -> guestStoreBillEntryList.stream()
                                    .map(AddStoreRequest.BillEntry::getMaterial_number)
                            )
                            .collect(Collectors.toList());
                    //更新这些skuCode的状态
                    List<Long> needUpdateGuestSupplySkuIdList = Safes.of(guestSupplySkuNewEntityDao.selectList(new LambdaQueryWrapper<GuestSupplySkuNewEntity>()

                                    .in(GuestSupplySkuNewEntity::getSkuCode, skuCodeList)
                                    .eq(GuestSupplySkuNewEntity::getDeleteFlag, DeleteFlagEnum.VALID.getValue())
                            ))
                            .stream()
                            .map(GuestSupplySkuNewEntity::getId)
                            .collect(Collectors.toList());

                    guestSupplyService.batchUpdateByIds(needUpdateGuestSupplySkuIdList, GuestSupplySkuNewEntity.builder()
                            //更新成已操作的状态
                            .operateStatus(NumberUtils.INTEGER_ONE)
                            .build());
                }
        });


    }


    /**
     * 手动入库
     *
     * @param req
     */
    public void addStore(GoldGuestSuppliesStorageSaveReq req) {

        String curEmployeeId = CommonParamsManager.getCurEmployeeId();
        Set<String> skuCodeSet = req.getGuestSuppliesInfoParamList().stream()
                .map(GuestSuppliesInfoSaveParam::getSkuCode)
                .collect(Collectors.toSet());

        List<GuestSupplySkuNewEntity> guestSupplySkuNewEntityList = guestSupplySkuNewEntityDao.selectList(new LambdaQueryWrapper<GuestSupplySkuNewEntity>()
                        .in(GuestSupplySkuNewEntity::getSkuCode,skuCodeSet));

        Map<String,String> unitMap = Safes.of(guestSupplySkuNewEntityList).stream()
                .collect(Collectors.toMap(GuestSupplySkuNewEntity::getSkuCode,GuestSupplySkuNewEntity::getBaseUnitName,(o1,o2)->o1));


        List<SuppliesStoreSaveReq.GuestSuppliesStoreDetailSaveParam> list = req.getGuestSuppliesInfoParamList().stream()
                .map(guestSuppliesStorageSaveParam -> {
                    return SuppliesStoreSaveReq.GuestSuppliesStoreDetailSaveParam.builder()
                            .guestSuppliesId(guestSuppliesStorageSaveParam.getGuestSuppliesId())
                            .skuCodeList(Lists.newArrayList(guestSuppliesStorageSaveParam.getSkuCode()))
                            .count(guestSuppliesStorageSaveParam.getGuestSuppliesQuantity())
                            .guestSuppliesUnitPriceIntax(guestSuppliesStorageSaveParam.getGuestSuppliesUnitPriceIntax())
                            .taxRate(GuestSuppliesInfoSaveParam.transferTaxRateType(guestSuppliesStorageSaveParam.getTaxRateType()))
                            .suppliesDeptEnum(SuppliesDeptEnum.GENERAL_LIBRARY)
                            .srcsysbillentryid("OMS_" + req.getChainId() + "_" + System.currentTimeMillis())
                            .supplierNumber("mendiangys")
                            .unitName(unitMap.get(guestSuppliesStorageSaveParam.getSkuCode()))
                            .build();
                }).collect(Collectors.toList());

        storageSave(SuppliesStoreSaveReq.builder()
                .chainId(req.getChainId())
                .storageDate(req.getStorageDate())
                .storeNo(req.getChainId() + "-" + System.currentTimeMillis())
                .comment("OMS手动入库")
                .operateId(curEmployeeId)
                .supplierNumber("mendiangys")
                .guestSuppliesInfoParamList(list)
                .build());
    }

    /**
     * 集市采购单入库
     *
     * @param orderStoreParamList
     */
    public void orderStore(List<OrderStoreParam> orderStoreParamList) {
        if (CollectionUtils.isEmpty(orderStoreParamList)) {
            log.error("orderStoreParamList empty");
            return;
        }
        log.info("orderStoreParamList :{}", JsonUtils.toJson(orderStoreParamList));
        for (OrderStoreParam orderStoreParam : orderStoreParamList) {
            DkfBusinessRequestTask build = DkfBusinessRequestTask.builder()
                    .businessType(BusinessRequestTypeEnum.PURCHASE_ORDER_INVENTORY.getState())
                    .syncState(BusinessRequestSyncStateEnum.NO_SYNC_STATE.getState())
                    .failMess("")
                    .requestContent(JSONObject.toJSONString(Lists.newArrayList(orderStoreParam)))
                    .createTime(new Date())
                    .deleted(0).build();
            dkfBusinessRequestTaskService.insert(build);
        }
    }
    /**
     * 同步采购单到金蝶定时任务
     */

    public void  purchaseOrderInventoryJob(){
        List<DkfBusinessRequestTask> dkfBusinessRequestTaskList = dkfBusinessRequestTaskService.queryTaskBySyncState(syncStateList,null);

        if(CollectionUtils.isEmpty(dkfBusinessRequestTaskList)){
            log.info("purchaseOrderInventoryJob 本次执行为 0");
            return;
        }
        for (DkfBusinessRequestTask requestTask : dkfBusinessRequestTaskList) {
            if(requestTask.getFailNum() >=50){
                continue;
            }
            RLock redisLock = lockRedis.getLock("purchaseOrderInventoryJob" + requestTask.getId());
            if (redisLock == null || !redisLock.tryLock()) {
                log.info("purchaseOrderInventoryJob fail, 存在执行中得任务");
               continue;
            }
            try {
                log.info("本次执行定时任务补偿 数据为 {}",JsonUtils.toJson(requestTask));
                switch (BusinessRequestTypeEnum.getInstance(requestTask.getBusinessType())){
                    case PURCHASE_ORDER_INVENTORY:
                        List<OrderStoreParam> orderStoreParams = JSONObject.parseArray(requestTask.getRequestContent(), OrderStoreParam.class);
                        orderStoreSyncJinDie(orderStoreParams);
                        requestTask.setSyncState(BusinessRequestSyncStateEnum.SUCCESS_SYNC_STATE.getState());
                        requestTask.setUpdateTime(new Date());
                        break;
                    case CHECK_OUT_INVENTORY:
                        List<GuestSuppliesOutCalParam> guestSuppliesOutCalParamList = JSONObject.parseArray(requestTask.getRequestContent(), GuestSuppliesOutCalParam.class);
                        checkOutSyncJinDie(requestTask.getChainId(),requestTask.getBusinessDate(),guestSuppliesOutCalParamList);
                        requestTask.setSyncState(BusinessRequestSyncStateEnum.SUCCESS_SYNC_STATE.getState());
                        requestTask.setUpdateTime(new Date());
                        break;
                    default:
                }
            }catch (Exception e){
                log.warn("本次执行定时任务补偿采购单操作 失败 {} 原因 ：{}",JsonUtils.toJson(requestTask),e.getMessage());
                requestTask.setSyncState(BusinessRequestSyncStateEnum.ERROR.getState());
                requestTask.setFailMess(e.getMessage());
                requestTask.setFailNum((Objects.nonNull(requestTask.getFailNum())?requestTask.getFailNum():0) +1);
            }finally {
                redisLock.unlock();
            }
            dkfBusinessRequestTaskService.updateById(requestTask);
        }
    }

    /**
     * 离店单同步金蝶
     * @param guestSuppliesOutCalParamList
     */
    public void  checkOutSyncJinDie(Integer chainId,Date businessDate , List<GuestSuppliesOutCalParam> guestSuppliesOutCalParamList) throws Exception {

        if (CollectionUtils.isEmpty(guestSuppliesOutCalParamList)) {
            return;
        }

        List<Long> guestSuppliesIdList = guestSuppliesOutCalParamList.stream()
                .map(GuestSuppliesOutCalParam::getGuestSuppliesId)
                .distinct()
                .collect(Collectors.toList());


        List<GuestSuppliesConfigNewEntity> guestSuppliesConfigNewEntities = guestSuppliesConfigNewDao.selectBatchIds(guestSuppliesIdList);

        Map<Long, String> guestNameMap = Safes.of(guestSuppliesConfigNewEntities).stream()
                .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, GuestSuppliesConfigNewEntity::getName));


        List<AddDayInventoryParam.StoreDetail> storeDetailList = guestSuppliesOutCalParamList.stream()
                .map(guestSuppliesOutCalParam -> AddDayInventoryParam.StoreDetail.builder()
                        .guestSuppliesId(guestSuppliesOutCalParam.getGuestSuppliesId())
                        .InventoryType(0)
                        .guestSuppliesName(guestNameMap.getOrDefault(guestSuppliesOutCalParam.getGuestSuppliesId(), ""))
                        .count(BigDecimal.valueOf(guestSuppliesOutCalParam.getCount()))
                        .goldDeptCode(SuppliesDeptEnum.getInstance(guestSuppliesOutCalParam.getDeptId(), goldWarehouseInfoList).getGoldCode())
                        .build())
                .collect(Collectors.toList());

        List<InventoryRegisterDetailParam> detailParamList = guestSuppliesOutCalParamList.stream()
                .map(param -> InventoryRegisterDetailParam.builder()
                        .supplyId(param.getGuestSuppliesId())
                        .supplyName(guestNameMap.getOrDefault(param.getGuestSuppliesId(), ""))
                        .useCount(param.getCount())
                        .inventoryCount(0)
                        .build())
                .collect(Collectors.toList());

        Integer deptId = guestSuppliesOutCalParamList.get(0).getDeptId();
        InventoryRegisterSaveParam inventoryRegisterSaveParam = InventoryRegisterSaveParam.builder()
                .accDate(DateUtil.formatDate(businessDate))
                .deptId(deptId)
                .detailParamList(detailParamList)
                .chainId(chainId)
                .build();

        DkfGoodsInventoryRegister inventoryRegister = registerInventoryService.builderDkfGoodsRegister(inventoryRegisterSaveParam, "0", checkOutUserName);
        AddDayInventoryParam checkOutInventoryParam = AddDayInventoryParam.builder()
                .billNo(inventoryRegister.getInventoryRegisterNo())
                .comment(checkOutUserName)
                .operationEmployeeId(checkOutUserName)
                .businessDate(businessDate)
                .chainId(chainId)
                .storeDetailList(storeDetailList)
                .build();
        dayInventory(checkOutInventoryParam);

        inventoryRegister = registerInventoryService.saveDkfGoodsRegister(inventoryRegister);
        log.info(("inventoryRegisterSave->酒店:{}执行日登记新增，登记单ID:{}"), chainId, inventoryRegister.getId());
        UserPermissionDTO userPermissionDTO = new UserPermissionDTO();
        userPermissionDTO.setHrId("0");
        userPermissionDTO.setUserId(0);
        userPermissionDTO.setUserName("自动出库");
        registerInventoryDetailService.saveDkfGoodsInventoryRegisterDetail(inventoryRegister, detailParamList,userPermissionDTO);
        AddCustomerInventoryRegisterLogReq addCustomerInventoryRegisterLogReq = DkfInventoryRegisterWrapper.buildInventoryRegisterLog(detailParamList, inventoryRegister);
        dkfInventoryRegisterBusinessService.addCustomerInventoryRegisterLogReq(addCustomerInventoryRegisterLogReq, InventoryRegisterLogTypeEnum.DAY_GRADE);
    }


    /**
     * 采购单同步金蝶
     * @param orderStoreParamList
     */
    public void  orderStoreSyncJinDie(List<OrderStoreParam> orderStoreParamList){

        List<String> skuCodeList = orderStoreParamList.stream().map(OrderStoreParam::getStoreDetailParamList)
                .flatMap(Collection::stream)
                .map(OrderStoreParam.StoreDetailParam::getSkuCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        //根据skuCode去查skuId
        // 首先查询酒店关联的并且是开启状态的 客需商品
        List<GuestSupplySkuNewEntity> skuEntitieList = guestSupplySkuNewEntityDao.selectList(new LambdaQueryWrapper<GuestSupplySkuNewEntity>().in(GuestSupplySkuNewEntity::getSkuCode,skuCodeList));

        Map<String,String> skuMap = Safes.of(skuEntitieList).stream()
                .collect(Collectors.toMap(GuestSupplySkuNewEntity::getSkuCode,GuestSupplySkuNewEntity::getBaseUnitName,(o1,o2)->o1));
        for (OrderStoreParam orderStoreParam : orderStoreParamList) {

            SuppliesStoreSaveReq suppliesStoreSaveReq = SuppliesStoreSaveReq.builder()
                    .storeNo(orderStoreParam.getStoreCode()).storageDate(new Date().getTime())
                    .chainId(orderStoreParam.getStoreDetailParamList().get(0).getChainId())
                    .operateId("0")
                    .supplierNumber(orderStoreParam.getStoreDetailParamList().get(0).getSupplierNumber())
                    .comment("采购单入库").build();
            //本次执行变更的客用品id
            Set<String> skuCodeSet = orderStoreParam.getStoreDetailParamList().stream().map(OrderStoreParam.StoreDetailParam::getSkuCode).collect(Collectors.toSet());
            List<GuestSupplySkuDto> guestSupplySkuDtos = guestSupplyService.queryAssociation(GuestSupplySkuParam.builder()
                    .skuCodeList(skuCodeSet)
                    .build());

            Map<String, Long> skuSupplyMap = DkfInventoryRegisterWrapper.buildSupplySkuMapping(guestSupplySkuDtos);
            List<SuppliesStoreSaveReq.GuestSuppliesStoreDetailSaveParam> detailSaveParamList = Lists.newArrayList();

            for (OrderStoreParam.StoreDetailParam storeDetailParam : orderStoreParam.getStoreDetailParamList()) {
                if(!skuSupplyMap.containsKey(storeDetailParam.getSkuCode())){
                    log.info("sukCode:{} 未关联客用品不同步金蝶",storeDetailParam.getSkuCode());
                    continue;
                }
                detailSaveParamList.add(DkfInventoryRegisterWrapper.bulidGuestSuppliesStoreDetailSaveParam(storeDetailParam,skuSupplyMap,skuMap));
            }
            if(CollectionUtils.isEmpty(detailSaveParamList)){
                log.info("同步金蝶本次执行为0不存在满足条件得数据 采购单:{}",orderStoreParam.getStoreCode());
                continue;
            }
            suppliesStoreSaveReq.setGuestSuppliesInfoParamList(detailSaveParamList);
            try {
                log.info("start同步金蝶参数 编码 ：{} 参数： {}",orderStoreParam.getStoreCode(),JsonUtils.toJson(suppliesStoreSaveReq));
                storageSave(suppliesStoreSaveReq);
            } catch (Exception e) {
                log.warn("采购单入库失败 编码：{} error：{}", orderStoreParam.getStoreCode(), JsonUtils.toJson(orderStoreParam));
                throw new BusinessException(e.getMessage());
            }}
    }


    public Map<Long, BigDecimal> calMonthPriceInTax(Integer chainId, String inventoryMonth, Set<Long> guestSuppliesIdSet) {
        if (CollectionUtils.isEmpty(guestSuppliesIdSet)) {
            return Collections.emptyMap();
        }

        Map<Long, List<GuestSupplySkuDto>> suppliesMap = guestSupplyService.getSuppliesMap(GuestSupplySkuParam.builder()
                .supplyIdList(guestSuppliesIdSet)
                .queryAllFlag(1)
                .build());

        if (MapUtils.isEmpty(suppliesMap)) {
            return Collections.emptyMap();
        }

        List<GuestSuppliesSkuMappingResp> guestSuppliesSkuMappingResps = guestSuppliesSkuMapping(chainId);

        if (CollectionUtils.isEmpty(guestSuppliesSkuMappingResps)) {
            return Collections.emptyMap();
        }

        Set<String> skuCodeSet = guestSuppliesSkuMappingResps.stream()
                .map(GuestSuppliesSkuMappingResp::getSkuList)
                .flatMap(Collection::stream)
                .map(GuestSuppliesSkuMappingResp.SkuInfo::getSkuCode)
                .collect(Collectors.toSet());


        GoldInventoryReportRequest.Data build = GoldInventoryReportRequest.Data.builder()
                .startperiod(inventoryMonth)
                .endperiod(inventoryMonth)
                .mucalorg(chainId.toString())
                .material(skuCodeSet)
                .mulwarehouse(Lists.newArrayList(SuppliesDeptEnum.GENERAL_LIBRARY.getGoldCode()))
                .build();

        GoldInventoryReportRequest build1 = GoldInventoryReportRequest.builder()
                .data(build)
                .build();

        Pair<Boolean,GoldCostRecordResponse> reportResponse = requestGold("[月客用品-调用金蝶-获取报表数据]", build1, () -> goldenButterflyService.report(build1),Boolean.FALSE);
        List<GoldCostRecordResponse.MaterialCost> data = Objects.nonNull(reportResponse) ? Safes.of(reportResponse.getValue().getData()) : Collections.emptyList();


        Map<String, List<GuestSupplySkuDto>> skuMap = suppliesMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(entity -> entity.getSkuEntity()
                        .getSkuCode()));

        //期初总价格 含税
        Map<String, BigDecimal> startTotalMoneyInTaxMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcost().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> startTotalMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(startTotalMoneyInTaxMap, skuMap);

        //期初数量
        Map<String, BigDecimal> startTotalCountMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty));
        Map<Long, BigDecimal> startTotalCountGuestSupplieMap = buildGuestSuppliesCountMap(startTotalCountMap, skuMap);


        //本月入库
        Map<String, BigDecimal> currentTotalCountMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty_in));
        Map<Long, BigDecimal> currentTotalCountGuestSuppliesMap = buildGuestSuppliesCountMap(currentTotalCountMap, skuMap);


        //本月入库总价格 含税
        Map<String, BigDecimal> currentTotalMoneyInTax = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcost_in().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> currentTotalMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(currentTotalMoneyInTax, skuMap);


        Map<Long, BigDecimal> map = Maps.newHashMap();

        for (GuestSuppliesSkuMappingResp guestSuppliesSkuMappingResp : guestSuppliesSkuMappingResps) {

            BigDecimal startTotalMoneyInTax = startTotalMoneyGuestSuppliesInTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal startTotalCount = startTotalCountGuestSupplieMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);

            BigDecimal currentTotalCountGuestSupplies = currentTotalCountGuestSuppliesMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal currentMoneyInTax = currentTotalMoneyGuestSuppliesInTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);

            BigDecimal currentCount = startTotalCount.add(currentTotalCountGuestSupplies);
            BigDecimal moneyInTax = startTotalMoneyInTax.add(currentMoneyInTax);
            BigDecimal divide = divide(moneyInTax, currentCount, 8);
            map.put(guestSuppliesSkuMappingResp.getGuestSuppliesId(), divide);
        }

        return map;
    }


    public List<GuestSuppliesInventoryListResponse> report(GoldGuestSuppliesInventoryListReq req) throws Exception {

        String requestMonth =req.getInventoryMonth();

        req.setInventoryMonth(req.getInventoryMonth().replaceAll("-", ""));

        List<GuestSuppliesSkuMappingResp> guestSuppliesSkuMappingResps = guestSuppliesSkuMapping(req.getChainId());

        if (CollectionUtils.isEmpty(guestSuppliesSkuMappingResps)) {
            return Collections.emptyList();
        }

        Set<String> skuCode = guestSuppliesSkuMappingResps.stream()
                .map(GuestSuppliesSkuMappingResp::getSkuList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(GuestSuppliesSkuMappingResp.SkuInfo::getSkuCode)
                .collect(Collectors.toSet());


        GoldInventoryReportRequest.Data build = GoldInventoryReportRequest.Data.builder()
                .startperiod(req.getInventoryMonth())
                .endperiod(req.getInventoryMonth())
                .mucalorg(req.getChainId().toString())
                .material(skuCode)
                .mulwarehouse(Objects.nonNull(req.getDeptType()) ? Lists.newArrayList(SuppliesDeptEnum.getInstance(req.getDeptType(),goldWarehouseInfoList).getGoldCode()) : Collections.emptyList())
                .build();

        GoldInventoryReportRequest build1 = GoldInventoryReportRequest.builder()
                .data(build)
                .build();

        Pair<Boolean,GoldCostRecordResponse> reportResponse = requestGold("[月客用品-调用金蝶-获取报表数据]", build1, () ->goldenButterflyService.report(build1),Boolean.FALSE);
        List<GoldCostRecordResponse.MaterialCost> data = Objects.nonNull(reportResponse) ? Safes.of(reportResponse.getValue().getData()) : Collections.emptyList();

        Set<String> skuCodeSet = data.stream()
                .map(GoldCostRecordResponse.MaterialCost::getMaterial_number)
                .collect(Collectors.toSet());

        Map<String, List<GuestSupplySkuDto>> skuMap = guestSupplyService.getSkuMap(GuestSupplySkuParam.builder()
                .skuCodeList(skuCodeSet)
                .queryAllFlag(1)
                .build());


        InventoryRegisterMonthQueryDTO inventoryRegisterMonthQueryDTO = dkfInventoryRegisterBusinessService.queryInventoryRegisterByMonthApproved(InventoryRegisterMonthQueryParam.builder()
                .month(requestMonth)
                .chainId(req.getChainId())
                        .deptId(req.getDeptType())
                .build());
        Map<Long, Integer> inventoryCountMap = null;
        if (Objects.nonNull(inventoryRegisterMonthQueryDTO) && CollectionUtils.isNotEmpty(inventoryRegisterMonthQueryDTO.getDetailQueryDTOList())) {
            inventoryCountMap = inventoryRegisterMonthQueryDTO.getDetailQueryDTOList().stream()
                    .collect(Collectors.toMap(InventoryRegisterMonthDetailQueryDTO::getSupplyId, InventoryRegisterMonthDetailQueryDTO::getUseCount));
        }


        //期初总价格不含税
        Map<String, BigDecimal> startTotalMoney = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getActualcost));
        Map<Long, BigDecimal> startTotalMoneyGuestSuppliesNoTaxMap = buildGuestSuppliesCountMap(startTotalMoney, skuMap);


        //期初总价格 含税
        Map<String, BigDecimal> startTotalMoneyInTaxMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcost().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> startTotalMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(startTotalMoneyInTaxMap, skuMap);

        //期初数量
        Map<String, BigDecimal> startTotalCountMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty));
        Map<Long, BigDecimal> startTotalCountGuestSupplieMap = buildGuestSuppliesCountMap(startTotalCountMap, skuMap);


        //期末结存数量
        Map<String, BigDecimal> currentBaseqtyBalTotalCountMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty_bal));
        Map<Long, BigDecimal> currentBaseqtyBalTotalCountGuestSuppliesMap = buildGuestSuppliesCountMap(currentBaseqtyBalTotalCountMap, skuMap);


        //期末结存数量 含税
        Map<String, BigDecimal> currentBaseqtyBalTotalMoneyMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcost_bal().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> currentTotalBaseQueryMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(currentBaseqtyBalTotalMoneyMap, skuMap);



        //期末结存数量 不含税
        Map<String, BigDecimal> currentBaseqtyBalTotalNoTaxMoneyMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getActualcost_bal));
        Map<Long, BigDecimal> currentTotalBaseQueryMoneyGuestSuppliesNoTaxMap = buildGuestSuppliesCountMap(currentBaseqtyBalTotalNoTaxMoneyMap, skuMap);



        //本月入库
        Map<String, BigDecimal> currentTotalCountMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty_in));
        Map<Long, BigDecimal> currentTotalCountGuestSuppliesMap = buildGuestSuppliesCountMap(currentTotalCountMap, skuMap);


        //本月入库总价格不含税
        Map<String, BigDecimal> currentTotalMoney = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getActualcost_in));
        Map<Long, BigDecimal> currentTotalMoneyGuestSuppliesNoTaxMap = buildGuestSuppliesCountMap(currentTotalMoney, skuMap);


        //本月入库总价格 含税
        Map<String, BigDecimal> currentTotalMoneyInTax = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcost_in().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> currentTotalMoneyGuestSuppliesInTaxMap = buildGuestSuppliesCountMap(currentTotalMoneyInTax, skuMap);


        //本月出库
        Map<String, BigDecimal> currentTotalCountOutMap = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getBaseqty_out));
        Map<Long, BigDecimal> currentTotalCountGuestSuppliesOutMap = buildGuestSuppliesCountMap(currentTotalCountOutMap, skuMap);

        //本月出库总价格不含税
        Map<String, BigDecimal> currentTotalOutMoney = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, GoldCostRecordResponse.MaterialCost::getActualcostOut));
        Map<Long, BigDecimal> currentTotalMoneyGuestSuppliesNoTaxOutMap = buildGuestSuppliesCountMap(currentTotalOutMoney, skuMap);

        //本月出库总价格 含税
        Map<String, BigDecimal> currentTotalMoneyInTaxOut = data.stream()
                .collect(Collectors.toMap(GoldCostRecordResponse.MaterialCost::getMaterial_number, cost -> cost.getActualcostOut().multiply(BigDecimal.valueOf(1).add(cost.getRate()))));
        Map<Long, BigDecimal> currentTotalMoneyGuestSuppliesInTaxOutMap = buildGuestSuppliesCountMap(currentTotalMoneyInTaxOut, skuMap);


        List<GoldGuestSuppliesConsumeDetailResp> goldGuestSuppliesConsumeDetailResps = queryTransferDetail(req.getChainId(),0,req.getDeptType(), req.getInventoryMonth());
        Map<Integer, List<GoldGuestSuppliesConsumeDetailResp>> collect = goldGuestSuppliesConsumeDetailResps.stream()
                .collect(Collectors.groupingBy(GoldGuestSuppliesConsumeDetailResp::getToDeptId));

        Map<Integer, Map<Long, Integer>> map = Maps.newHashMap();
        for (Map.Entry<Integer, List<GoldGuestSuppliesConsumeDetailResp>> suppliesDeptEnumListEntry : collect.entrySet()) {
            Map<Long, Integer> consumeCountMap = suppliesDeptEnumListEntry.getValue().stream()
                    .filter(resp-> Objects.isNull(req.getDeptType()) || Objects.equals(req.getDeptType(),resp.getOriginDeptId()) )
                    .map(GoldGuestSuppliesConsumeDetailResp::getConsumeDetailList)
                    .flatMap(List::stream)
                    .collect(Collectors.toMap(
                            GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail::getId, // key
                            GoldGuestSuppliesConsumeDetailResp.GuestSuppliesConsumeDetail::getConsumerCount, // value
                            Integer::sum));
            map.put(suppliesDeptEnumListEntry.getKey(), consumeCountMap);
        }

        Map<Long, GuestSuppliesConfigNewEntity> suppliesMap = skuMap.values().stream()
                .flatMap(List::stream)
                .map(GuestSupplySkuDto::getSuppliesEntity)
                .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity(), (o1, o2) -> o1));

        List<SuppliesDeptEnum> deptList = Lists.newArrayList(SuppliesDeptEnum.GENERAL_LIBRARY, SuppliesDeptEnum.MEETING, SuppliesDeptEnum.RECEPTION, SuppliesDeptEnum.RESTAURANT);

        Map<Long, Integer> masterMap = map.getOrDefault(SuppliesDeptEnum.GENERAL_LIBRARY.getCode(), Collections.emptyMap());
        Map<Long, Integer> receptionMap = map.getOrDefault(SuppliesDeptEnum.RECEPTION.getCode(), Collections.emptyMap());
        Map<Long, Integer> meetingMap = map.getOrDefault(SuppliesDeptEnum.MEETING.getCode(), Collections.emptyMap());
        Map<Long, Integer> restaurantMap = map.getOrDefault(SuppliesDeptEnum.RESTAURANT.getCode(), Collections.emptyMap());

        List<GuestSuppliesInventoryListResponse> list = Lists.newArrayList();
        Long index = 0L;

        Set<Long> mappingSuppliesIdSet = suppliesMap.keySet();
        Set<Long> chainMappingSuppliesIdSet = guestSuppliesSkuMappingResps.stream()
                .map(GuestSuppliesSkuMappingResp::getGuestSuppliesId)
                .collect(Collectors.toSet());

        Sets.SetView<Long> diffSuppliesIdSet = Sets.symmetricDifference(mappingSuppliesIdSet, chainMappingSuppliesIdSet);

        if (CollectionUtils.isNotEmpty(diffSuppliesIdSet)) {
            List<GuestSuppliesConfigNewEntity> diffSuppliesConfigList = guestSuppliesConfigNewDao.selectBatchIds(diffSuppliesIdSet);

            Map<Long, GuestSuppliesConfigNewEntity> diffSuppliesConfigMap = Safes.of(diffSuppliesConfigList).stream()
                    .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity(), (o1, o2) -> o1));
            suppliesMap.putAll(diffSuppliesConfigMap);
        }


        List<Long> guestSuppliesIdList = guestSuppliesSkuMappingResps.stream()
                .map(GuestSuppliesSkuMappingResp::getGuestSuppliesId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, BigDecimal> inventoryToMap = getInventoryToMap(GetInventoryParam.builder()
                .supplierIdList(guestSuppliesIdList)
                .goldWarehouseInfo(SuppliesDeptEnum.getInstance(req.getDeptType(),goldWarehouseInfoList))
                .chainId(req.getChainId())
                .build());


        for (GuestSuppliesSkuMappingResp guestSuppliesSkuMappingResp : guestSuppliesSkuMappingResps) {

            GuestSuppliesConfigNewEntity entity = suppliesMap.get(guestSuppliesSkuMappingResp.getGuestSuppliesId());

            BigDecimal startTotalMoneyInTax = startTotalMoneyGuestSuppliesInTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal startTotalCount = startTotalCountGuestSupplieMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal startTotalMoneyGuestNoTax = startTotalMoneyGuestSuppliesNoTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);

            BigDecimal currentTotalCountGuestSupplies = currentTotalCountGuestSuppliesMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal currentMoneyInTax = currentTotalMoneyGuestSuppliesInTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal currentMoneyNoTax = currentTotalMoneyGuestSuppliesNoTaxMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);


            BigDecimal currentTotalCountGuestSuppliesOut = currentTotalCountGuestSuppliesOutMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal currentMoneyInTaxOut = currentTotalMoneyGuestSuppliesInTaxOutMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            BigDecimal currentMoneyNoTaxOut = currentTotalMoneyGuestSuppliesNoTaxOutMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);

            Integer masterCount = masterMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0);
            Integer receptionCount = receptionMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0);
            Integer meetingCount = meetingMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0);
            Integer restaurantCount = restaurantMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0);


            BigDecimal unitPriceInTax = divide(currentMoneyInTax.add(startTotalMoneyInTax),startTotalCount.add(currentTotalCountGuestSupplies),4);
            BigDecimal unitPriceNoTax = divide(currentMoneyNoTax.add(startTotalMoneyGuestNoTax),startTotalCount.add(currentTotalCountGuestSupplies),4);


            Integer totalConsumeCount = masterCount + receptionCount + meetingCount + restaurantCount;

            List<ConsumeQuantityDetailDTO> consumeQuantityList = deptList.stream()
                    .map(enums -> {
                        Map<Long, Integer> countMap = map.getOrDefault(enums.getCode(), Collections.emptyMap());
                        Integer count = countMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0);
                        return ConsumeQuantityDetailDTO.builder()
                                .deptName(enums.getDesc())
                                .quantity(count)
                                .build();
                    }).collect(Collectors.toList());

            BigDecimal inventoryCount = inventoryToMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), BigDecimal.ZERO);
            Integer endPeriodInventory = currentBaseqtyBalTotalCountGuestSuppliesMap.containsKey(guestSuppliesSkuMappingResp.getGuestSuppliesId()) ?
                    currentBaseqtyBalTotalCountGuestSuppliesMap.get(guestSuppliesSkuMappingResp.getGuestSuppliesId()).intValue() : 0;

            list.add(GuestSuppliesInventoryListResponse.builder()
                    .id(index++)
                    .chainId(req.getChainId())
                    .inventoryMonth(req.getInventoryMonth())
                    .guestSuppliesId(guestSuppliesSkuMappingResp.getGuestSuppliesId())
                    .suppliesType(entity.getSuppliesType())
                    .guestSuppliesName(entity.getName())
                    .suppliesCategoryName(StringUtils.isBlank(entity.getProductCategoryName()) ? "其他品类" : entity.getProductCategoryName())
                    .lastMonthQuantity(startTotalCount.intValue())
                    .lastMonthUnitPriceInTax(divide(startTotalMoneyInTax, startTotalCount, 4).stripTrailingZeros())
                    .lastMonthUnitPriceNonTax(divide(startTotalMoneyGuestNoTax, startTotalCount, 4).stripTrailingZeros())
                    .monthStorageQuantity(currentTotalCountGuestSupplies.intValue())
                    .monthStorageAmountInTax(currentMoneyInTax.stripTrailingZeros())
                    .monthStorageAmountNonTax(currentMoneyNoTax.stripTrailingZeros())
                    .monthUnitPriceInTax(unitPriceInTax.stripTrailingZeros())
                    .monthUnitPriceNonTax(unitPriceNoTax.stripTrailingZeros())
                    .consumeQuantity(totalConsumeCount)
                    .monthRemainingQuantity(MapUtils.isEmpty(inventoryCountMap) ? 0 : inventoryCountMap.getOrDefault(guestSuppliesSkuMappingResp.getGuestSuppliesId(), 0))
                    .monthUseQuantity(currentTotalCountGuestSuppliesOut.intValue())
                    .monthConsumeAmountInTax(currentMoneyInTaxOut.stripTrailingZeros())
                    .monthConsumeAmountNonTax(currentMoneyNoTaxOut.stripTrailingZeros())
                    .consumeQuantityList(consumeQuantityList)
                    .useQuantityFront(receptionCount)
                    .useQuantityMeeting(meetingCount)
                    .useQuantityCanteen(restaurantCount)
                    .useQuantityMaster(masterCount)
                    .inventoryCount(inventoryCount.intValue())
                            .endPeriodInventory(endPeriodInventory)
                    .build());
        }

        GuestSuppliesInventoryListResponse summarize = Summarizer.summarize(list, GuestSuppliesInventoryListResponse.class);
        summarize.setId(-1L);
        summarize.setChainId(req.getChainId());
        summarize.setInventoryMonth(req.getInventoryMonth());
        summarize.setGuestSuppliesName("合计");
        summarize.setSuppliesCategoryName("合计");
        summarize.setGuestSuppliesId(-1L);
        summarize.setSuppliesType(SuppliesTypeEnum.DEFAULT.getCode());
        list.add(summarize);
        return list;
    }


    public BigDecimal divide(BigDecimal origin, BigDecimal to, Integer number) {
        if (Objects.isNull(origin) || Objects.isNull(to)) {
            return BigDecimal.ZERO;
        }

        if (to.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return origin.divide(to,number, RoundingMode.HALF_UP);
    }




    /**
     * 将年月（例如 "202408"）转换为该月的第一天和最后一天的字符串。
     *
     * @param yearMonth 输入的年月字符串，格式为 "yyyyMM"
     * @return 一个包含第一天和最后一天的数组，格式为 ["yyyy-MM-01", "yyyy-MM-31"]
     */
    public static Pair<String, String> getFirstAndLastDayOfMonth(String yearMonth) {
        // 提取年份和月份
        int year = Integer.parseInt(yearMonth.substring(0, 4));
        int month = Integer.parseInt(yearMonth.substring(4, 6));

        // 获取该月的第一天
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);

        // 获取该月的最后一天
        LocalDate lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth());

        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return Pair.of(firstDayOfMonth.format(formatter), lastDayOfMonth.format(formatter));
    }


    public String parseTime(Long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        // 将时间戳转换为Date对象
        Date date = new Date(timestamp);

        // 格式化日期
        return sdf.format(date);

    }

    public List<MonthInventoryDetailDTO> queryInventoryRegisterByMonthApproved(@Valid GoldenGuestSuppliesInventoryParam param) throws ParseException {
        InventoryRegisterMonthQueryDTO inventoryRegisterMonthQueryDTO = dkfInventoryRegisterBusinessService.queryInventoryRegisterByMonthApproved(InventoryRegisterMonthQueryParam.builder()
                .month(parseTime(param.getInventoryTime()))
                .chainId(param.getChainId())
                .deptId(param.getDeptId())
                .build());

        if (Objects.isNull(inventoryRegisterMonthQueryDTO) || CollectionUtils.isEmpty(inventoryRegisterMonthQueryDTO.getDetailQueryDTOList())) {
            return Collections.emptyList();
        }

        List<GuestSuppliesConfigNewEntity> guestSuppliesConfigNewEntities = guestSuppliesConfigNewDao.selectBatchIds(inventoryRegisterMonthQueryDTO.getDetailQueryDTOList().stream()
                .map(InventoryRegisterMonthDetailQueryDTO::getSupplyId)
                .collect(Collectors.toSet()));

        Map<Long, GuestSuppliesConfigNewEntity> collect = Safes.of(guestSuppliesConfigNewEntities).stream()
                .collect(Collectors.toMap(GuestSuppliesConfigNewEntity::getId, Function.identity(), (o1, o2) -> o1));


        List<MonthInventoryNumDetailDTO> collect1 = inventoryRegisterMonthQueryDTO.getDetailQueryDTOList().stream()
                .map(dto -> {
                    GuestSuppliesConfigNewEntity entity = collect.get(dto.getSupplyId());
                    return MonthInventoryNumDetailDTO.builder()
                            .guestSuppliesSource(entity.getSuppliesSource())
                            .guestSuppliesId(entity.getId())
                            .guestSuppliesName(entity.getName())
                            .guestSuppliesQuantity(Safes.of(dto.getUseCount(),0))
                            .originGuestSuppliesQuantity(dto.getInventoryCount())
                            .diffGuestSuppliesQuantity(dto.getDiffCount())
                            .build();
                }).collect(Collectors.toList());

        return Lists.newArrayList(MonthInventoryDetailDTO.builder()
                .guestSuppliesList(collect1)
                .channel("APP")
                .operationUserId(0L)
                .operationUserName(inventoryRegisterMonthQueryDTO.getInventoryRegisterName())
                .isUpdate(Boolean.FALSE)
                .storageTime(DateUtil.printMonth(DateUtils.parseDate(inventoryRegisterMonthQueryDTO.getInventoryRegisterDate())))
                .createTime(LocalDateTimeUtil.of(inventoryRegisterMonthQueryDTO.getCreateTime()))
                .build());

    }

    public <T> T requestGold(String operateLog, Object req, Callable<T> callable,Boolean throwException) {
        T result = null;
        try {
            return callable.call();
        } catch (Exception e) {
            log.warn(operateLog + "出现异常，请求参数:{},返回结果:{}", req, result, e);
            if(throwException){
                throw new BusinessException("系统异常，请稍后再试");
            }
        }
        return result;
    }


    public List<GuestSuppliesSkuMappingResp> guestSuppliesSkuMapping(Integer chainId) {
        List<HotelRelationGoodsDTO> hotelRelationGoodsDTOS = dkfInventoryRegisterBusinessService.hotelRelationGoods(ChainGoodsQueryParam.builder()
                .chainId(chainId)
                .build());

        if (CollectionUtils.isEmpty(hotelRelationGoodsDTOS)) {
            return Collections.emptyList();
        }

        Set<Long> suppliesIdSet = hotelRelationGoodsDTOS.stream()
                .map(HotelRelationGoodsDTO::getSupplyId)
                .collect(Collectors.toSet());

        Map<Long, List<GuestSupplySkuDto>> suppliesMap = guestSupplyService.getSuppliesMap(GuestSupplySkuParam.builder()
                .supplyIdList(suppliesIdSet)
                .build());

        List<GuestSuppliesSkuMappingResp> list = Lists.newArrayList();
        for (HotelRelationGoodsDTO hotelRelationGoodsDTO : hotelRelationGoodsDTOS) {
            List<GuestSupplySkuDto> guestSupplySkuDtoList = suppliesMap.get(hotelRelationGoodsDTO.getSupplyId());
            List<GuestSuppliesSkuMappingResp.SkuInfo> collect = Safes.of(guestSupplySkuDtoList).stream()
                    .map(dto -> GuestSuppliesSkuMappingResp.SkuInfo.builder()
                            .skuName(dto.getSkuEntity().getProductName())
                            .skuCode(dto.getSkuEntity().getSkuCode())
                            .build()).collect(Collectors.toList());
            list.add(GuestSuppliesSkuMappingResp.builder()
                    .guestSuppliesId(hotelRelationGoodsDTO.getSupplyId())
                    .guestSuppliesName(hotelRelationGoodsDTO.getSupplyName())
                    .skuList(collect)
                    .build());
        }
        return list;
    }

}
