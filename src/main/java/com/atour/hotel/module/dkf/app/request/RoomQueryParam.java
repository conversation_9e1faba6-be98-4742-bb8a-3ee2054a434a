package com.atour.hotel.module.dkf.app.request;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * WEB层: 查询房间信息请求参数
 *
 * <AUTHOR>
 * @since 2019/04/02
 */
@ApiModel(value = "RoomQueryParam", description = "查询房间信息请求参数")
@Data
public class RoomQueryParam implements Serializable {

    /**
     * 酒店ID
     */
    @Schema(description = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    /**
     * 房间号
     */
    @Schema(description = "房间号")
    @NotBlank(message = "请输入房间号")
    private String roomNo;

    @Schema(description = "用户ID")
    private Integer userId;
}
