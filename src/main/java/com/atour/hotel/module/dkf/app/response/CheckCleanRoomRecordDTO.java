package com.atour.hotel.module.dkf.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 查房记录表
 * @date 2024年04月22日15:14
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckCleanRoomRecordDTO implements Serializable {
    private static final long serialVersionUID = 4529437591533056998L;

        /**
         * 查房时间
         */
        @Schema(description = "查房时间")
        private String checkRoomTime;

        /**
         * 查房人姓名
         */
        @Schema(description = "查房人姓名")
        private String checkRoomUserName;

        /**
         * 检查状态 0.待检查 1.未通过 2.通过
         */
        @Schema(description = "查房状态 0.待检查 1.未通过 2.通过")
        private Integer checkRoomStatus;


        @Schema(description = "查房检查项")
        private String checkTaskItem;

        @Schema(description = "查房备注")
        private String remark;
}
