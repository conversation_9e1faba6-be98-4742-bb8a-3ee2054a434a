package com.atour.hotel.module.dkf.app.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.dkf.app.response.DkfRoomStatusDTO;
import com.atour.hotel.module.dkf.app.request.*;
import com.atour.hotel.module.dkf.app.service.DkfRoomStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 朵客房-移动排房
 * @date 2024年06月17日17:07
 * @since JDK1.8
 */
@Api("朵客房-房态")
@RestController
@RequestMapping(value = "/inner/api/app/dkf/roomStatus/", produces = {"application/json;charset=UTF-8"})
public class DkfRoomStatusController {

    @Resource
    private DkfRoomStatusService roomStatusService;

    /**
     * 房态查询
     * @param roomStatusQueryParam
     * @return
     */
   @PostMapping(value = "/getRoomStatusList")
   @ApiOperation(value = "获取房态信息", httpMethod = "POST")
   public ApiResult<DkfRoomStatusDTO> getRoomStatusList(@RequestBody @Valid RoomStatusQueryParam roomStatusQueryParam){
        return ApiResult.success(roomStatusService.getRoomStatusList(roomStatusQueryParam.getChainId()));
   }

    /**
     * 楼层查询
     * @param roomStatusQueryParam
     * @return
     */
    @PostMapping(value = "/getFoorList")
    @ApiOperation(value = "获取楼层列表", httpMethod = "POST")
    public ApiResult<List<Integer>> getFoorList(@RequestBody @Valid RoomStatusQueryParam roomStatusQueryParam){
        return ApiResult.success(roomStatusService.getFoorList(roomStatusQueryParam.getChainId()));
    }



}
