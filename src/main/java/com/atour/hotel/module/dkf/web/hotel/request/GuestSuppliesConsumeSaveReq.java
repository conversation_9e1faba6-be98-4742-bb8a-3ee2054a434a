package com.atour.hotel.module.dkf.web.hotel.request;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 月客用品盘点 - 新增入库对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GuestSuppliesConsumeSaveReq {

	/**
	 * 酒店ID
	 */
	@NotNull(message = "酒店ID不能为空")
	private Integer chainId;
	/**
	 * 领用日期
	 */
	@NotNull(message = "领用日期不能为空")
	private Long consumeDate;
	/**
	 * 领用部门
	 */
	@NotBlank(message = "领用部门不能为空")
	private String consumeDept;

	@Valid
	@NotNull(message = "客用品明细不能为空")
	private List<GuestSuppliesInfoParam> guestSuppliesInfoParamList;
}
