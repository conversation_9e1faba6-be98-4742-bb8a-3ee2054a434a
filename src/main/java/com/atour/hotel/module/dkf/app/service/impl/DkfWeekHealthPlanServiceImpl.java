package com.atour.hotel.module.dkf.app.service.impl;


import com.atour.galaxy.api.bean.room.RoomDTO;
import com.atour.galaxy.api.param.RoomQueryParam;
import com.atour.galaxy.api.remote.GalaxyRoomRemote;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.CommonHelp;
import com.atour.hotel.module.dkf.app.dto.HealthPlanInfoDTO;
import com.atour.hotel.module.dkf.app.request.DkfHealthPlanWeekAppParams;
import com.atour.hotel.module.dkf.app.request.DkfHealthPlanWeekDetailAppParams;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanWeekDetailDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanWeekMainDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanWeekDetailListDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanWeekMainDetailDTO;
import com.atour.hotel.module.dkf.app.service.DkfWeekHealthPlanService;
import com.atour.hotel.module.dkf.enums.HealthFinishEnum;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekDetailListReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekDetailReportDto;
import com.atour.hotel.persistent.hotel.dao.RoundsRecordDao;
import com.atour.hotel.persistent.hotel.entity.RoundsRecordEntity;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/***
 * 朵客房计划卫生周计划
 */
@Slf4j
@Component
public class DkfWeekHealthPlanServiceImpl implements DkfWeekHealthPlanService {

    @Resource
    private RoundsRecordDao roundsRecordDao;
    @Resource
    private GalaxyRoomRemote galaxyRoomRemote;

    @Override
    public DkfHealthPlanWeekMainDTO weekQuery(DkfHealthPlanWeekAppParams weekParams) {
        Date beginDate;
        Date endDate;
        try {
            // 日期正确性校验
            beginDate = CommonUtils.convertStr2Date(weekParams.getBeginDate());
            endDate = CommonUtils.convertStr2Date(weekParams.getEndDate());
        } catch (Exception e) {
            throw new BusinessException("请确认完成日期是否合法 " + weekParams.getBeginDate() + "~" + weekParams.getEndDate());
        }

        //获取开始和结束只查
        long numOfDaysBetween =
                ChronoUnit.DAYS.between(LocalDate.parse(weekParams.getBeginDate()), LocalDate.parse(weekParams.getEndDate()));
        if (numOfDaysBetween > 7) {
            throw new BusinessException("查询数据跨度不能大于7天");
        }

        if (!DayOfWeek.MONDAY.equals(DayOfWeek.of(CommonHelp.date2LocalDate(beginDate).get(ChronoField.DAY_OF_WEEK))) || !DayOfWeek.SUNDAY.equals(
                DayOfWeek.of(CommonHelp.date2LocalDate(endDate).get(ChronoField.DAY_OF_WEEK)))) {
            throw new BusinessException("日期区间必须为1个整周");
        }

        List<RoundsRecordEntity> roundsList = roundsRecordDao.selectByParam(RoundsRecordEntity.builder()
                .chainId(weekParams.getChainId())
                .startTime(beginDate)
                .endTime(endDate)
                .build());

        List<RoomDTO> roomDTOList = galaxyRoomRemote.queryRooms(RoomQueryParam.builder()
                .chainId(weekParams.getChainId())
                .build());
        Map<String, RoomDTO> roomMap = Safes.of(roomDTOList).stream().collect(Collectors.toMap(RoomDTO::getRoomNo, Function.identity(), (k1, k2) -> k2));
        // 获取完成详情
        Map<String, List<DkfHealthPlanWeekDetailListDTO>> detailMap = getDetails(roundsList, roomDTOList,null);

        // 获取完成单项数
        int doneSingleNum = getDoneSingleNum(detailMap);

        // 获取计划名称
        List<String> healthPlanNames = Safes.of(detailMap).values()
                .stream()
                .flatMap(Collection::stream)
                .map(DkfHealthPlanWeekDetailListDTO::getHealthPlanName)
                .distinct()
                .collect(Collectors.toList());


        DkfHealthPlanWeekMainDTO mainDTO = new DkfHealthPlanWeekMainDTO();
        List<DkfHealthPlanWeekMainDetailDTO> retDetails = new ArrayList<>();
        Map<String, Integer> doneGroupByHealthPlanName = getDoneGroupByHealthPlanName(detailMap);
        //组装完成详情
        healthPlanNames.forEach(healthPlanName -> {
            DkfHealthPlanWeekMainDetailDTO detail = new DkfHealthPlanWeekMainDetailDTO();
            detail.setHealthPlanName(healthPlanName);
            int doneNum = Objects.nonNull(doneGroupByHealthPlanName.get(healthPlanName)) ? doneGroupByHealthPlanName.get(healthPlanName) : 0;
            detail.setCompleteNum(doneNum);
            detail.setShouldCompleteNum(roomDTOList.size());
            detail.setDoneRate(new BigDecimal(doneNum).divide(new BigDecimal(roomDTOList.size()), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
            retDetails.add(detail);
        });
        mainDTO.setDetails(retDetails);

        // 补全计划名称
        detailMap.forEach((roomNo, detailListDTOS) -> {
            List<DkfHealthPlanWeekDetailListDTO> detailList = Safes.of(detailListDTOS);
            Map<String, DkfHealthPlanWeekDetailListDTO> planWeekDetailMap = detailList.stream()
                    .collect(Collectors.toMap(DkfHealthPlanWeekDetailListDTO::getHealthPlanName, Function.identity(), (k1, k2) -> k2));

            healthPlanNames.forEach(healthPlanName -> {
                DkfHealthPlanWeekDetailListDTO healthPlanWeekDetailListReportDto = planWeekDetailMap.get(healthPlanName);
                if (Objects.isNull(healthPlanWeekDetailListReportDto)) {
                    DkfHealthPlanWeekDetailListDTO detailListDTO = new DkfHealthPlanWeekDetailListDTO();
                    detailListDTO.setHealthPlanName(healthPlanName);
                    detailListDTO.setDone(false);
                    detailListDTO.setRoomTypeName(roomMap.get(roomNo).getRoomTypeName());
                    detailListDTO.setRoomNo(roomNo);
                    detailList.add(detailListDTO);
                }
            });
        });


        // 获取完成房间数
        int completeRoomNum = getDoneRoomNum(detailMap);

        // 获取需要完成的房间数 房间数乘以所有计划数
        int needCompleteHealthPlanRoomNum = roomDTOList.size() * healthPlanNames.size();

        // 平均完成率计算 单项完成数除以所有房间数乘以所有计划数
        BigDecimal avgCompleteRate = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        if(doneSingleNum != 0){
            avgCompleteRate = BigDecimal.valueOf(doneSingleNum).divide(new BigDecimal(String.valueOf(needCompleteHealthPlanRoomNum)),4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        // 完成率
        BigDecimal done = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        if(0!= completeRoomNum){
            done = new BigDecimal(String.valueOf(completeRoomNum)).divide(new BigDecimal(String.valueOf(roomDTOList.size())), 4,
                    BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        mainDTO.setAvgCompleteRate(avgCompleteRate);
        mainDTO.setTotalCompleteRate(done);
        mainDTO.setTotalCompleteRoomNum(completeRoomNum);
        mainDTO.setTotalRoomNum(roomDTOList.size());

        return mainDTO;
    }


    @Override
    public DkfHealthPlanWeekDetailDTO weekDetail(DkfHealthPlanWeekDetailAppParams weekParams) {
        Date beginDate;
        Date endDate;
        try {
            // 日期正确性校验
            beginDate = CommonUtils.convertStr2Date(weekParams.getBeginDate());
            endDate = CommonUtils.convertStr2Date(weekParams.getEndDate());
        } catch (Exception e) {
            throw new BusinessException("请确认完成日期是否合法 " + weekParams.getBeginDate() + "~" + weekParams.getEndDate());
        }

        //获取开始和结束只查
        long numOfDaysBetween =
                ChronoUnit.DAYS.between(LocalDate.parse(weekParams.getBeginDate()), LocalDate.parse(weekParams.getEndDate()));
        if (numOfDaysBetween > 7) {
            throw new BusinessException("查询数据跨度不能大于7天");
        }

        if (!DayOfWeek.MONDAY.equals(DayOfWeek.of(CommonHelp.date2LocalDate(beginDate).get(ChronoField.DAY_OF_WEEK))) || !DayOfWeek.SUNDAY.equals(
                DayOfWeek.of(CommonHelp.date2LocalDate(endDate).get(ChronoField.DAY_OF_WEEK)))) {
            throw new BusinessException("日期区间必须为1个整周");
        }

        List<RoundsRecordEntity> roundsList = roundsRecordDao.selectByParam(RoundsRecordEntity.builder()
                .chainId(weekParams.getChainId())
                .startTime(beginDate)
                .endTime(endDate)
                .build());

        List<RoomDTO> roomDTOList = galaxyRoomRemote.queryRooms(RoomQueryParam.builder()
                .chainId(weekParams.getChainId())
                .build());
        // 获取完成详情
        Map<String, List<DkfHealthPlanWeekDetailListDTO>> detailMap = getDetails(roundsList, roomDTOList,weekParams.getHealthPlanName());
        DkfHealthPlanWeekDetailDTO dkfHealthPlanWeekDetailDTO = new DkfHealthPlanWeekDetailDTO();
        List<DkfHealthPlanWeekDetailListDTO> retDetails = new ArrayList<>();
        int completeRoomNum = 0;
        for (RoomDTO roomDTO : roomDTOList) {
            DkfHealthPlanWeekDetailListDTO detailListDTO = new DkfHealthPlanWeekDetailListDTO();
            if (Objects.nonNull(detailMap.get(roomDTO.getRoomNo())) && detailMap.get(roomDTO.getRoomNo()).size()>0) {
                detailListDTO = detailMap.get(roomDTO.getRoomNo()).get(0);
                if(detailListDTO.getDone()){
                    completeRoomNum++;
                }
            }else{
                detailListDTO.setHealthPlanName(weekParams.getHealthPlanName());
                detailListDTO.setRoomNo(roomDTO.getRoomNo());
                detailListDTO.setRoomTypeName(roomDTO.getRoomTypeName());
                detailListDTO.setDone(false);
            }
            retDetails.add(detailListDTO);
        }
        dkfHealthPlanWeekDetailDTO.setDetails(retDetails);
        dkfHealthPlanWeekDetailDTO.setCompleteNum(completeRoomNum);
        dkfHealthPlanWeekDetailDTO.setShouldCompleteNum(roomDTOList.size());
        dkfHealthPlanWeekDetailDTO.setHealthPlanName(weekParams.getHealthPlanName());
        dkfHealthPlanWeekDetailDTO.setDoneRate(BigDecimal.valueOf(completeRoomNum).divide(new BigDecimal(String.valueOf(roomDTOList.size())),4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP));
        return dkfHealthPlanWeekDetailDTO;
    }

    /**
     * 获取完成详情根据房间号分组
     * @param roundsList
     * @param roomDTOList
     * @return
     */
    private Map<String,List<DkfHealthPlanWeekDetailListDTO>> getDetails(List<RoundsRecordEntity> roundsList, List<RoomDTO> roomDTOList,String planName) {

        Map<String,List<DkfHealthPlanWeekDetailListDTO>> detailMap = new HashMap<>();

        Map<String, List<RoundsRecordEntity>> roundsRecordMap = roundsList.stream()
                .filter(roundsRecordEntity -> Objects.nonNull(roundsRecordEntity.getAccDate()))
                .collect(Collectors.groupingBy(RoundsRecordEntity::getRoomNo));


        Safes.of(roomDTOList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(roomDTO -> {
                    List<RoundsRecordEntity> roundRecords = roundsRecordMap.get(roomDTO.getRoomNo());

                    List<DkfHealthPlanWeekDetailListDTO> detailList = Safes.of(roundRecords)
                            .stream()
                            .map(roundsEntity -> {
                                List<HealthPlanInfoDTO> weekHealthList = JsonUtils.parseList(roundsEntity.getWeekHealthJson(), HealthPlanInfoDTO.class);

                                return Safes.of(weekHealthList)
                                        .stream()
                                        .filter(Objects::nonNull)
                                        .map(healthPlanInfoDTO -> {
                                            DkfHealthPlanWeekDetailListDTO detailListDTO = new DkfHealthPlanWeekDetailListDTO();
                                            detailListDTO.setHealthPlanName(healthPlanInfoDTO.getHealthName());
                                            detailListDTO.setDone(Objects.equals(healthPlanInfoDTO.getStatus(), HealthFinishEnum.FINISH.getCode()));
                                            detailListDTO.setRoomTypeName(roomDTO.getRoomTypeName());
                                            detailListDTO.setRoomNo(roomDTO.getRoomNo());
                                            return detailListDTO;
                                        })
                                        .collect(Collectors.toList());
                            })
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    List<DkfHealthPlanWeekDetailListDTO> weekDetailList = Lists.newArrayList();
                    detailList.stream()
                            .collect(Collectors.groupingBy(DkfHealthPlanWeekDetailListDTO::getHealthPlanName))
                            .forEach((healthPlanName, weekDetails) -> {
                                // 判断是否是当前计划
                                if((Objects.nonNull(planName) && healthPlanName.equals(planName)) || Objects.isNull(planName)){
                                    boolean done = weekDetails.stream()
                                            .filter(weekDetailListDTO -> Objects.nonNull(weekDetailListDTO.getDone()))
                                            .anyMatch(DkfHealthPlanWeekDetailListDTO::getDone);
                                    DkfHealthPlanWeekDetailListDTO detailListDTO = new DkfHealthPlanWeekDetailListDTO();
                                    detailListDTO.setHealthPlanName(healthPlanName);
                                    detailListDTO.setDone(done);
                                    detailListDTO.setRoomTypeName(roomDTO.getRoomTypeName());
                                    detailListDTO.setRoomNo(roomDTO.getRoomNo());
                                    weekDetailList.add(detailListDTO);
                                }

                            });

                    detailMap.put(roomDTO.getRoomNo(), weekDetailList);
                });

        return detailMap;
    }

    /**
     * 获取完成单项数
     * @param details
     * @return
     */
    private Map<String,Integer> getDoneGroupByHealthPlanName(Map<String,List<DkfHealthPlanWeekDetailListDTO>> details) {
        Map<String,Integer> doneMap = new HashMap<>();
        List<DkfHealthPlanWeekDetailListDTO> allDetailList = details.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        allDetailList.stream().collect(Collectors.groupingBy(DkfHealthPlanWeekDetailListDTO::getHealthPlanName)).forEach((healthPlanName, detailList) -> {
                int doneNum = (int) detailList.stream().filter(DkfHealthPlanWeekDetailListDTO::getDone).count();
                doneMap.put(healthPlanName, doneNum);
        });
        return doneMap;
    }
    /**
     * 获取所有单项完成数
     * @param details
     * @return
     */
    private int getDoneSingleNum(Map<String,List<DkfHealthPlanWeekDetailListDTO>> details) {
        int doneRoomNum = 0;
        for (Map.Entry<String, List<DkfHealthPlanWeekDetailListDTO>> entry : details.entrySet()) {
            List<DkfHealthPlanWeekDetailListDTO> detailList = entry.getValue();
            for (DkfHealthPlanWeekDetailListDTO detail : detailList) {
                if (detail.getDone()) {
                    doneRoomNum++;
                }
            }
        }
        return doneRoomNum;
    }

    private int getDoneRoomNum(Map<String,List<DkfHealthPlanWeekDetailListDTO>> details) {
        int doneRoomNum = 0;
        for (Map.Entry<String, List<DkfHealthPlanWeekDetailListDTO>> entry : details.entrySet()) {
            List<DkfHealthPlanWeekDetailListDTO> detailList = entry.getValue();
            boolean allMatch = Safes.of(detailList)
                    .stream()
                    .allMatch(DkfHealthPlanWeekDetailListDTO::getDone);
            long count = Safes.of(detailList)
                    .stream()
                    .map(DkfHealthPlanWeekDetailListDTO::getHealthPlanName)
                    .distinct()
                    .count();
            // 全部周计划卫生合格且计划卫生数量>=7才算
            if (allMatch && count >= 7) {
                doneRoomNum++;
            }
        }
        return doneRoomNum;

    }



}
