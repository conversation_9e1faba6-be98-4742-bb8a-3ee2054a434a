package com.atour.hotel.module.dkf.job.json;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

// 最外层响应结构
@Data
public class OutCalResponse implements Serializable {

    // data字段内的分页数据结构
        private int totalNum;
        private int pageSize;
        private int pageNum;
        private List<RowItem> rows;

    // 每一行的具体数据
    @Data
    public static class RowItem implements Serializable {
        private String calDate;     // JSON字段: cal_date
        private Integer chainId;     // JSON字段: chain_id
        private Long suppliesId; // JSON字段: supplies_id (根据示例值为数字但文档标记为string，按需调整)
        private Integer count;
    }
}

