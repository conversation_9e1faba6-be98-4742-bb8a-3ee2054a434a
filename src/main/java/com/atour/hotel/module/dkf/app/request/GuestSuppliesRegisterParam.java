package com.atour.hotel.module.dkf.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年05月31日20:27
 * @since JDK1.8
 */
@Data
public class GuestSuppliesRegisterParam implements Serializable {
    private static final long serialVersionUID = 8788110077439281842L;

    /**
     * 酒店ID
     */
    @Schema(description = "[int]:酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    /**
     * 客用品ID
     */
    @Schema(description = "[int]:客用品ID")
    @NotNull(message = "客用品ID不能为空")
    private Integer supplyId;

    /**
     * 剩余数量
     */
    @Schema(description = "[int]:剩余数量")
    @NotNull(message = "剩余数量不能为空")
    private Integer surplusCount;

    /**
     * 营业日
     */
    @Schema(description = "营业日")
    private LocalDate accDate;

    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 酒店营业日 用于http 请求调用传参使用
     */
    private String chainAccDate;
}
