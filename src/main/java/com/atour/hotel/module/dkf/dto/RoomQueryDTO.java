package com.atour.hotel.module.dkf.dto;

import com.atour.hotel.module.dkf.app.response.SimpleGuestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 查询房间信息响应结果实例
 *
 * <AUTHOR>
 * @date 2019-04-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "RoomQueryDTO", description = "查询房间信息响应结果实例")
public class RoomQueryDTO implements Serializable {

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    private Integer chainId;

    /**
     * 房间ID
     */
    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 打扫编号
     */
    @ApiModelProperty(value = "打扫编号")
    private String cleanNo;

    /**
     * 房态
     */
    @ApiModelProperty(value = "房态(1:空隔夜 2:空脏房 3:在住脏换床单 4:在住脏房 5:空清洁房 6:空干净房 7:在住干净房 8:停售房)")
    private Integer roomState;

    /**
     * 房型名称
     */
    @ApiModelProperty(value = "房型名称")
    private String roomTypeName;

    /**
     * 被排房人
     */
    @ApiModelProperty(value = "被排房人")
    private String rowRoomExecuteUserName;

    /**
     * 住脏房如果是当日离店的，显示离店时间
     * 是否展示离店时间(true:展示 false:不展示)
     */
    @ApiModelProperty(value = "是否展示离店时间(true:展示 false:不展示)")
    private Boolean displayDate;

    /**
     * 入住时间
     */
    @ApiModelProperty(value = "入住时间")
    private String arrival;

    /**
     * 离店时间
     */
    @ApiModelProperty(value = "离店时间")
    private String departTime;

    /**
     * 打扫状态(1:未打扫 2:打扫中 3:已打扫)
     */
    @ApiModelProperty(value = "打扫状态(1:未打扫 2:打扫中 3:已打扫)")
    private Integer cleanStatus;

    /**
     * 是否本人(最近打扫ID对应的打扫人是否是登录账号本人)
     */
    @ApiModelProperty(value = "是否本人(最近打扫ID对应的打扫人是否是登录账号本人 true:本人 false:不是本人)")
    private Boolean isMySelf;

    /**
     * 打扫开始时间
     */
    @ApiModelProperty(value = "打扫开始时间")
    private String cleanStartTime;

    /**
     * 打扫结束时间
     */
    @ApiModelProperty(value = "打扫结束时间")
    private String cleanEndTime;

    /**
     * 是否vip房间
     */
    private Integer vipTypeId;
    /**
     * 是否勿扰房
     */
    private Integer isDoNotDisturbTheRoom;
    /**
     * 是否保密房
     */
    private Integer surreptitious;

    /**
     * 是否检查房
     */
    private Integer checkRoomFlag;

    /**
     * 楼层
     */
    @ApiModelProperty(value = "楼层")
    private Integer floorNo;

    /**
     * 预订人会员类型
     */
    @ApiModelProperty(value = "预订人会员类型")
    private Integer bookMebType;

    /**
     * 入住人
     */
    @ApiModelProperty(value = "入住人")
    private List<SimpleGuestDTO> guestList;

    /**
     * 预约打扫开始时间
     */
    @ApiModelProperty(value = "预约打扫开始时间")
    private String planStartTime;

    /**
     * 预约打扫结束时间
     */
    @ApiModelProperty(value = "预约打扫结束时间")
    private String planEndTime;

    /**
     * 预约备注
     */
    @ApiModelProperty(value = "预约备注")
    private String planRemarkContext;

    /**
     * 是否请勿打扰
     */
    @ApiModelProperty(value = "是否请勿打扰 1:预约打扫, 2: 不用打扫")
    private Integer isDisturb;


    /**
     * 是否展示房间有人状态 (true:展示 false:不展示)
     */
    @ApiModelProperty(value = "是否展示房间有人状态(true:展示 false:不展示)")
    private Boolean hasInductionState;

    /**
     * 房间状态：null 未安装设备；0 无人；1 有人；3 设备坏掉
     */
    @ApiModelProperty(value = "是否展示离店时间(true:展示 false:不展示)")
    private Integer inductionState;

    private Integer rightAction;

    private String statusEndTime;

    /**
     * 是否是大清房 0.否 1.是
     */
    private Integer isCleanRoom;

    /**
     * 大清房打扫id
     */
    private Long cleanId;

    /**
     * 是否有大清查房  0.否 1.是
     */
    private Integer hasCheckStateCleanRoom;
}
