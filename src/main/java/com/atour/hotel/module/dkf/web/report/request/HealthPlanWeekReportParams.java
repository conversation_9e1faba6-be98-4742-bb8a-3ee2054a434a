package com.atour.hotel.module.dkf.web.report.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Web层: 周计划卫生完成报表
 *
 * <AUTHOR>
 * @date 2019-09-18
 */
@ApiModel(value = "周计划卫生完成报表")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@Valid
public class HealthPlanWeekReportParams implements Serializable {

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private List<Integer> chainIds;

    /**
     * 开始完成日期
     */
    @ApiModelProperty(value = "开始完成日期")
    @NotBlank(message = "开始日期不能为空")
    private String beginDate;

    /**
     * 结束完成日期
     */
    @ApiModelProperty(value = "结束完成日期")
    @NotBlank(message = "结束日期不能为空")
    private String endDate;
}
