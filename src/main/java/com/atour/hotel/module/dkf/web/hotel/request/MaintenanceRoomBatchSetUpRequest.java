package com.atour.hotel.module.dkf.web.hotel.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class MaintenanceRoomBatchSetUpRequest {

    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    @NotEmpty(message = "房间信息不能为空")
    @Valid
    private List<MaintenanceRoomInfoReq> roomInfoReqList;

    /**
     * 维保原因
     */
    @ApiModelProperty(value = "维保原因")
    @NotBlank(message = "维保原因不能为空")
    @Size(max = 200, message = "维保原因不超过200字")
    private String reason;

}
