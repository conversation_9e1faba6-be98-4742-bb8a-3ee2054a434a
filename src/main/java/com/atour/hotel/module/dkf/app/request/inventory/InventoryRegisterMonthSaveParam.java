package com.atour.hotel.module.dkf.app.request.inventory;

import com.atour.utils.Safes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InventoryRegisterMonthSaveParam  implements Serializable {

    /**
     * 酒店ID不可以为空
     */
    @NotNull(message = "酒店ID不可以为空")
    @ApiModelProperty(value = "酒店ID")
    private Integer chainId;

    /**
     * 月份
     */
    @NotNull(message = "月份不可以为空")
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 保存状态
     */
    @NotNull(message = "保存状态不可以为空")
    @ApiModelProperty(value = "0：保存 1：盘点生成")
    private Integer inventoryRegisterState;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 客用品盘点明细
     */
    @ApiModelProperty(value = "客用品盘点明细")
    private List<InventoryRegisterDetailParam> detailParamList;


    public Set<Long>  getSupplyIdList(){
        return Safes.of(this.detailParamList).stream().map(InventoryRegisterDetailParam::getSupplyId).collect(Collectors.toSet());
    }

}
