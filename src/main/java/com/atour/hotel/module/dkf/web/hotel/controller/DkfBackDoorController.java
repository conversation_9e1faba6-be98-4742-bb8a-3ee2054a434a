package com.atour.hotel.module.dkf.web.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.dkf.web.hotel.request.*;
import com.atour.hotel.module.dkf.web.hotel.response.*;
import com.atour.hotel.module.dkf.web.hotel.service.DkfBackDoorService;
import com.atour.hotel.module.dkf.web.hotel.service.GuestSuppliesInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 月客用品盘点后门
 */
@RestController
@RequestMapping("/api/web/dkf/backDoor")
@Api(value = "月客用品盘点相关")
public class DkfBackDoorController {

	@Resource
	private DkfBackDoorService dkfBackDoorService;


	/**
	 * 清洗每月客用品成本表数据
	 * @param param
	 * @return
	 */
	@PostMapping(value = "/addguestSuppliesCostJson")
	public ApiResult<Boolean> saveLastMonthDetail(@RequestBody @Valid AddguestSuppliesCostJsonParam param) {
		dkfBackDoorService.addguestSuppliesCostJson(param);
		return ApiResult.success(Boolean.TRUE);
	}

	/**
	 * 客用品入库记录 新增字段：taxRate（税率）
	 *
	 * @param maxId 清洗数据开始的最大id
	 * @return
	 */
	@GetMapping(value = "/guestsuppliesInfoAddTaxRate")
	public ApiResult<Boolean> guestsuppliesInfoAddTaxRate(@RequestParam Long maxId) {
		dkfBackDoorService.guestsuppliesInfoAddTaxRate(maxId);
		return ApiResult.success(Boolean.TRUE);
	}
}
