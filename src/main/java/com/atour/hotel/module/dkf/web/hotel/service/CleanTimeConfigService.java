package com.atour.hotel.module.dkf.web.hotel.service;

import com.alibaba.fastjson.JSON;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.configuration.NewBusinessCodeConfBean;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.app.request.RoomCleanEndParam;
import com.atour.hotel.module.dkf.app.response.CheckRoomCleanEndDTO;
import com.atour.hotel.module.dkf.enums.CleanConfigOperateType;
import com.atour.hotel.module.dkf.enums.CleanStatusEnum;
import com.atour.hotel.module.dkf.statemachine.context.RoomCleanContext;
import com.atour.hotel.module.dkf.web.hotel.json.CleanTimeConfigFilterCondition;
import com.atour.hotel.module.dkf.web.hotel.json.CleaningTimeConfigJson;
import com.atour.hotel.module.dkf.web.hotel.request.CleanTimeConfigQueryParam;
import com.atour.hotel.module.dkf.web.hotel.request.CleanTimeConfigSaveRequest;
import com.atour.hotel.module.dkf.web.hotel.request.CleaningConfigChainMappingQueryCondition;
import com.atour.hotel.module.dkf.web.hotel.response.CleanTimeConfigDetailDTO;
import com.atour.hotel.module.dkf.web.hotel.response.CleanTimeConfigSaveResultDTO;
import com.atour.hotel.persistent.hotel.entity.CleanRoomEntity;
import com.atour.hotel.persistent.roommanage.dao.CleaningConfigChainMappingMapper;
import com.atour.hotel.persistent.roommanage.dao.CleaningTimeConfigMapper;
import com.atour.hotel.persistent.roommanage.dao.CleaningTimeConfigOperationLogMapper;
import com.atour.hotel.persistent.roommanage.entity.CleaningConfigChainMappingEntity;
import com.atour.hotel.persistent.roommanage.entity.CleaningTimeConfigEntity;
import com.atour.hotel.persistent.roommanage.entity.CleaningTimeConfigOperationLogEntity;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import com.yaduo.infra.skywalking.logger.framework.YaduoTraceContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Minutes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CleanTimeConfigService {

    @Resource
    private CleaningTimeConfigMapper cleaningTimeConfigMapper;

    @Resource
    private CleaningConfigChainMappingMapper cleaningConfigChainMappingMapper;

    @Resource
    private CleaningTimeConfigOperationLogMapper cleaningTimeConfigOperationLogMapper;

    /**
     * 客房公共服务接口
     */
    @Autowired
    private RoomDomain roomDomain;

    @Resource
    private NewBusinessCodeConfBean newBusinessCodeConfBean;


    /**
     * 查询打扫时长配置详情
     *
     * @param queryParam
     * @return
     */
    public CleanTimeConfigDetailDTO queryDetail(CleanTimeConfigQueryParam queryParam) {
        CleaningTimeConfigEntity cleaningTimeConfigEntity = cleaningTimeConfigMapper.selectOneByCondition(queryParam);
        if (Objects.isNull(cleaningTimeConfigEntity)) {
            return null;
        }
        // 配置详情
        String configDetail = cleaningTimeConfigEntity.getConfigDetail();
        List<CleaningTimeConfigJson> cleaningTimeConfigJsonList = null;
        if (StringUtils.isNotBlank(configDetail)) {
            cleaningTimeConfigJsonList = JSON.parseArray(configDetail, CleaningTimeConfigJson.class);
        }
        // 筛选条件详情
        String filterCondition = cleaningTimeConfigEntity.getFilterCondition();
        CleanTimeConfigFilterCondition  cleanTimeConfigFilterCondition = null;
        if(StringUtils.isNotBlank(filterCondition)) {
            cleanTimeConfigFilterCondition = JSON.parseObject(filterCondition, CleanTimeConfigFilterCondition.class);
        }

        CleaningConfigChainMappingQueryCondition condition = new CleaningConfigChainMappingQueryCondition();
        condition.setConfigId(cleaningTimeConfigEntity.getId());
        List<CleaningConfigChainMappingEntity> configChainMappingEntities = cleaningConfigChainMappingMapper.selectByCondition(condition);

        List<Integer> chainIdList = Safes.of(configChainMappingEntities).stream().map(CleaningConfigChainMappingEntity::getChainId).distinct().collect(Collectors.toList());
        return CleanTimeConfigDetailDTO.builder()
                .id(cleaningTimeConfigEntity.getId())
                .cleaningTimeConfigJsonList(cleaningTimeConfigJsonList)
                .releatedChainIdList(chainIdList)
                .filterCondition(cleanTimeConfigFilterCondition)
                .build();
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "roomManageTransactionManager")
    public CleanTimeConfigSaveResultDTO save(CleanTimeConfigSaveRequest request) {
        // 对前端传值进行校验
        checkParam(request);

        CleaningTimeConfigEntity cleaningTimeConfigEntity = null;
        Integer operateType = null;
        if (Objects.isNull(request.getId())) {
            // 前端传的配置ID为空,则需要判断表里面是否已存在配置,已存在配置,不传配置ID不允许更新
            CleaningTimeConfigEntity currentConfig = cleaningTimeConfigMapper.selectOneByCondition(CleanTimeConfigQueryParam.builder().build());
            if (Objects.nonNull(currentConfig)) {
                throw new BusinessException("已存在打扫记时配置,更新时配置ID不能为空");
            }
            // 不存在则直接插入新记录
            cleaningTimeConfigEntity = new CleaningTimeConfigEntity();
            cleaningTimeConfigEntity.setConfigDetail(CollectionUtils.isEmpty(request.getCleaningTimeConfigJsonList())?
                    null : JSON.toJSONString(request.getCleaningTimeConfigJsonList()));
            cleaningTimeConfigEntity.setFilterCondition(Objects.isNull(request.getFilterCondition())?
                    null : JSON.toJSONString(request.getFilterCondition()));
            cleaningTimeConfigEntity.setCreateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
            cleaningTimeConfigEntity.setUpdateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
            cleaningTimeConfigEntity.setTraceId(YaduoTraceContext.traceId());
            cleaningTimeConfigMapper.insertSelective(cleaningTimeConfigEntity);
         /*   // 这里需要判断前端传的酒店是否已存在对应配置
            List<CleaningConfigChainMappingEntity> existConfigMappings = cleaningConfigChainMappingMapper.selectByCondition(CleaningConfigChainMappingQueryCondition.builder()
                    .chainIdList(request.getRelatedChainIds())
                    .build());
            // 已存在关联关系的酒店需要删除对应配置，再重新插入新关联关系
            if(CollectionUtils.isNotEmpty(existConfigMappings)){
                List<Long> needDeleteIdList = existConfigMappings.stream().map(CleaningConfigChainMappingEntity::getId).collect(Collectors.toList());
                cleaningConfigChainMappingMapper.batchDeleteByPrimarykeyList(needDeleteIdList);
            }
            // 插入酒店关联配置
            List<CleaningConfigChainMappingEntity> mappingEntityList = buildMappingEntityList(cleaningTimeConfigEntity, request.getRelatedChainIds());
            if(CollectionUtils.isNotEmpty(mappingEntityList)){
                cleaningConfigChainMappingMapper.batchInsert(mappingEntityList);
            }*/
            operateType = CleanConfigOperateType.ADD.getCode();
        } else {
            // 查询前端传的配置ID对应的配置是否存在
            CleaningTimeConfigEntity currentConfig = cleaningTimeConfigMapper.selectByPrimaryKey(request.getId());
            if(Objects.isNull(currentConfig)){
                throw new BusinessException("根据传的配置ID查询的配置结果为空");
            }
            // 配置存在则更新配置
            cleaningTimeConfigEntity = new CleaningTimeConfigEntity();
            cleaningTimeConfigEntity.setId(currentConfig.getId());
            cleaningTimeConfigEntity.setConfigDetail(CollectionUtils.isEmpty(request.getCleaningTimeConfigJsonList())?
                    null : JSON.toJSONString(request.getCleaningTimeConfigJsonList()));
            cleaningTimeConfigEntity.setFilterCondition(Objects.isNull(request.getFilterCondition())?
                    null : JSON.toJSONString(request.getFilterCondition()));
            cleaningTimeConfigEntity.setUpdateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
            cleaningTimeConfigEntity.setTraceId(YaduoTraceContext.traceId());
            cleaningTimeConfigMapper.updateByPrimaryKeySelective(cleaningTimeConfigEntity);
          /*  // 这里根据配置ID已存在对应配置的酒店
            List<CleaningConfigChainMappingEntity> existConfigMappings = cleaningConfigChainMappingMapper.selectByCondition(CleaningConfigChainMappingQueryCondition.builder()
                    .configId(currentConfig.getId())
                    .build());*/
         /*   // 获取存在该配置的酒店
            List<Integer> existChainIdList = Safes.of(existConfigMappings).stream().map(CleaningConfigChainMappingEntity::getChainId).distinct().collect(Collectors.toList());
            Map<Integer, Long> existChainIDConfigMap =
                    Safes.of(existConfigMappings).stream().collect(Collectors.toMap(CleaningConfigChainMappingEntity::getChainId, CleaningConfigChainMappingEntity::getId));
*/
           /* // 再根据前端所传酒店ID判断，已存在关联关系的酒店不用再生成对应配置, 没有的生成。前端没传的酒店，但是有对应配置的，需要删除
            List<Integer> requestRelatedChainIds = request.getRelatedChainIds();

            //  遍历获取需要删除的酒店配置
            List<Long> needDeleteIdList = Lists.newArrayList();
            Set<Integer> existChainIdSet = existChainIDConfigMap.keySet();
            Safes.of(existChainIdSet).stream().forEach(existChainId ->{
                if(CollectionUtils.isEmpty(requestRelatedChainIds) || !requestRelatedChainIds.contains(existChainId)){
                    needDeleteIdList.add(existChainIDConfigMap.get(existChainId));
                }
            });
            if(CollectionUtils.isNotEmpty(needDeleteIdList)){
                cleaningConfigChainMappingMapper.batchDeleteByPrimarykeyList(needDeleteIdList);
            }*/
            // 遍历获取需要新增的酒店配置
           /* List<Integer> needAddChainIdList = Lists.newArrayList();
            // 前端传的酒店剔除所有已存在配置的酒店即是需要新增配置的酒店
            //requestRelatedChainIds.removeAll(existChainIdList);
            Safes.of(requestRelatedChainIds).stream().forEach(requestChainId ->{
                if(!existChainIdList.contains(requestChainId)){
                    needAddChainIdList.add(requestChainId);
                }
            });*/
            // 插入酒店关联配置
          /*  List<CleaningConfigChainMappingEntity> mappingEntityList = buildMappingEntityList(cleaningTimeConfigEntity, needAddChainIdList);
            if(CollectionUtils.isNotEmpty(mappingEntityList)){
                cleaningConfigChainMappingMapper.batchInsert(mappingEntityList);
            }*/
            operateType = CleanConfigOperateType.UPDATE.getCode();
        }
        // 写入操作日志
        CleaningTimeConfigOperationLogEntity cleaningTimeConfigOperationLogEntity = buildCleaningLogEntity(request, cleaningTimeConfigEntity.getId());
        cleaningTimeConfigOperationLogEntity.setOperateType(operateType);
        cleaningTimeConfigOperationLogMapper.insertSelective(cleaningTimeConfigOperationLogEntity);

        return CleanTimeConfigSaveResultDTO.builder().id(cleaningTimeConfigEntity.getId()).build();
    }

    private void checkParam(CleanTimeConfigSaveRequest request) {
        List<CleaningTimeConfigJson> cleaningTimeConfigJsonList = request.getCleaningTimeConfigJsonList();
        Safes.of(cleaningTimeConfigJsonList).stream().forEach(configJson -> {
            if(!Objects.equals(configJson.getLimitEndTime(), NumberUtils.INTEGER_ZERO)
                && !Objects.equals(configJson.getRemindEndTime(), NumberUtils.INTEGER_ZERO)){
                if(configJson.getRemindEndTime() <= configJson.getLimitEndTime()){
                    throw new BusinessException("结束提醒时长须大于限制结束时长");
                }
            }
        });
    }

    /**
     * 构建打扫记时配置log实体类
     * @param request
     * @param configId
     * @return
     */
    private CleaningTimeConfigOperationLogEntity buildCleaningLogEntity(CleanTimeConfigSaveRequest request, Long configId) {
        CleaningTimeConfigOperationLogEntity logEntity = new CleaningTimeConfigOperationLogEntity();
        logEntity.setConfigId(configId);
        logEntity.setConfigDetail(JSON.toJSONString(request.getCleaningTimeConfigJsonList()));
        logEntity.setFilterCondition(JSON.toJSONString(request.getFilterCondition()));
        logEntity.setRelatedChainId(JSON.toJSONString(request.getRelatedChainIds()));
        logEntity.setCreateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
        logEntity.setUpdateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
        logEntity.setTraceId(YaduoTraceContext.traceId());
        return logEntity;
    }

    /**
     * 构建酒店关联配置数据
     *
     * @param cleaningTimeConfigEntity
     * @param relatedChainIds
     * @return
     */
    private List<CleaningConfigChainMappingEntity> buildMappingEntityList(CleaningTimeConfigEntity cleaningTimeConfigEntity, List<Integer> relatedChainIds) {
        List<CleaningConfigChainMappingEntity> mappingEntityList = Lists.newArrayList();

        Safes.of(relatedChainIds).stream().forEach(chainId -> {
            CleaningConfigChainMappingEntity cleaningConfigChainMappingEntity = new CleaningConfigChainMappingEntity();
            cleaningConfigChainMappingEntity.setConfigId(cleaningTimeConfigEntity.getId());
            cleaningConfigChainMappingEntity.setChainId(chainId);
            cleaningConfigChainMappingEntity.setCreateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
            cleaningConfigChainMappingEntity.setUpdateUser(CommonParamsManager.get().getUserPermissionDTO().getHrId());
            cleaningConfigChainMappingEntity.setTraceId(YaduoTraceContext.traceId());
            mappingEntityList.add(cleaningConfigChainMappingEntity);
        });
        return mappingEntityList;
    }

    /**
     * 根据酒店维度查询对应的记时打扫配置
     * @param queryParam
     * @return
     */
    public CleanTimeConfigDetailDTO queryCleanTimeConfigDetailByChainId(){
      /*  if (Objects.isNull(queryParam) || Objects.isNull(queryParam.getChainId())){
            return null;
        }*/
      /*  CleaningConfigChainMappingQueryCondition condition = new CleaningConfigChainMappingQueryCondition();
        condition.setChainId(queryParam.getChainId());
        List<CleaningConfigChainMappingEntity> configChainMappingEntities = cleaningConfigChainMappingMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(configChainMappingEntities)){
            return null;
        }*/
       /* Long configId = configChainMappingEntities.get(0).getConfigId();
        queryParam.setId(configId);*/
        CleaningTimeConfigEntity cleaningTimeConfigEntity = cleaningTimeConfigMapper.selectOneByCondition(CleanTimeConfigQueryParam.builder().build());
        if (Objects.isNull(cleaningTimeConfigEntity)) {
            return null;
        }
        String configDetail = cleaningTimeConfigEntity.getConfigDetail();
        List<CleaningTimeConfigJson> cleaningTimeConfigJsonList = new ArrayList<>();
        if (StringUtils.isNotBlank(configDetail)) {
            cleaningTimeConfigJsonList = JSON.parseArray(configDetail, CleaningTimeConfigJson.class);
        }
        return CleanTimeConfigDetailDTO.builder()
                .id(cleaningTimeConfigEntity.getId())
                .cleaningTimeConfigJsonList(cleaningTimeConfigJsonList)
               // .releatedChainIdList(Lists.newArrayList(queryParam.getChainId()))
                .build();
    }

    /**
     * 房间结束打扫时检查用时是否满足打扫记时配置
     * @param roomCleanEndParam
     * @return
     */
    public CheckRoomCleanEndDTO checkCleanRoomTimeConsuming(RoomCleanEndParam roomCleanEndParam){
        CheckRoomCleanEndDTO cleanEndDTO = CheckRoomCleanEndDTO.builder().chainId(roomCleanEndParam.getChainId())
                .roomNo(roomCleanEndParam.getRoomNo()).tipCode(0).tipDesc("").build();
        if (newBusinessCodeConfBean.chainHasNewBusiness(roomCleanEndParam.getChainId(), SystemContants.CLEAN_ROOM_TIME_SETTING)) {
            Date currentDate = DateUtil.getCurrentDateTime();
            // 上下文信息
            RoomCleanContext context = RoomCleanContext.builder()
                    .chainId(roomCleanEndParam.getChainId())
                    .roomNo(roomCleanEndParam.getRoomNo())
                    .userId(String.valueOf(CommonParamsManager.getCurrUserId()))
                    .cleanNo(roomCleanEndParam.getCleanNo())
                    .clothCupsUpdaterList(roomCleanEndParam.getClothCupsUpdaterList())
                    .build();

            Boolean result = roomDomain.checkRoom(context, CleanStatusEnum.CLEAN);
            if (!result && !context.isSuccess()) {
                throw new BusinessException(context.getDesc());
            }

            if (result && Objects.isNull(context.getLastRoomClean())) {
                return cleanEndDTO;
            }
            CleanRoomEntity lastRoomClean = context.getLastRoomClean();
            Integer roomState = context.getRoomState();
            CleanTimeConfigDetailDTO configDetailDTO = queryCleanTimeConfigDetailByChainId();
            if (Objects.isNull(configDetailDTO) || CollectionUtils.isEmpty(configDetailDTO.getCleaningTimeConfigJsonList())) {
                return cleanEndDTO;
            }
            List<CleaningTimeConfigJson> configJsons = configDetailDTO.getCleaningTimeConfigJsonList();
            CleaningTimeConfigJson configJson = configJsons.stream().filter(c -> c.getRoomState().equals(roomState)).findFirst().orElse(null);
            if (Objects.isNull(configJson)) {
                return cleanEndDTO;
            }
            //打扫时长
            Minutes minutes = Minutes.minutesBetween(new DateTime(lastRoomClean.getStartTime().getTime()), new DateTime(currentDate.getTime()));
            int minute = minutes.getMinutes();

            // 限制结束时长与结束提醒时长均为0表示不做限制,直接返回
            if(Objects.equals(configJson.getLimitEndTime(), NumberUtils.INTEGER_ZERO) && Objects.equals(configJson.getRemindEndTime(), NumberUtils.INTEGER_ZERO)){
                return cleanEndDTO;
            }
            // 限制结束时长为0，结束提醒时长不为0
            if(Objects.equals(configJson.getLimitEndTime(), NumberUtils.INTEGER_ZERO) && !Objects.equals(configJson.getRemindEndTime(), NumberUtils.INTEGER_ZERO)){
                if(minute < configJson.getRemindEndTime()){
                    cleanEndDTO.setTipCode(1);
                    cleanEndDTO.setTipDesc("当前打扫时长为【" + minute + "】分钟" +
                            "不足【" + configJson.getRemindEndTime() + "】分钟，是否仍要结束打扫？");
                }
                return cleanEndDTO;
            }

            // 限制结束时长不为0，结束提醒时长为0
            if(!Objects.equals(configJson.getLimitEndTime(), NumberUtils.INTEGER_ZERO) && Objects.equals(configJson.getRemindEndTime(), NumberUtils.INTEGER_ZERO)){
                if (minute < configJson.getLimitEndTime()) {
                    cleanEndDTO.setTipCode(2);
                    cleanEndDTO.setTipDesc("当前打扫时长为【" + minute + "】分钟" +
                            "不足【" + configJson.getLimitEndTime() + "】分钟，请继续打扫？");
                }
                return cleanEndDTO;
            }

            // 限制结束时长、结束提醒时长都不为0
            if(minute < configJson.getLimitEndTime()){
                cleanEndDTO.setTipCode(2);
                cleanEndDTO.setTipDesc("当前打扫时长为【" + minute + "】分钟" +
                        "不足【" + configJson.getLimitEndTime() + "】分钟，请继续打扫？");
            }else if(minute < configJson.getRemindEndTime()){
                cleanEndDTO.setTipCode(1);
                cleanEndDTO.setTipDesc("当前打扫时长为【" + minute + "】分钟" +
                        "不足【" + configJson.getRemindEndTime() + "】分钟，是否仍要结束打扫？");
            }
            return cleanEndDTO;
        } else {
            return cleanEndDTO;
        }
    }
}
