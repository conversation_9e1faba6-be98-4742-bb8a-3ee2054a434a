package com.atour.hotel.module.dkf.web.report.controller;

import com.alibaba.excel.EasyExcel;
import com.atour.api.bean.PageResult;
import com.atour.api.bean.pmsapi.PmsApiPageResponse;
import com.atour.hotel.common.util.ExcelUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.dkf.web.report.request.CustomerGoodsConsumeReportParams;
import com.atour.hotel.module.dkf.web.report.service.CustomerGoodsConsumeReportService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/12
 * 客用品消耗报表-控制器
 */
@RestController
@RequestMapping(value = "/api/web/dkf/report", produces = {"application/json;charset=UTF-8"})
public class CustomerGoodsConsumeReportController {

    @Resource
    private CustomerGoodsConsumeReportService customerGoodsConsumeReportService;
    @Resource
    private ChainService chainService;

    @ResponseBody
    @PostMapping(value = "customerGoodsConsumeReport")
    public PmsApiPageResponse<List<String>> customerGoodsConsumeReport(@RequestBody @Valid CustomerGoodsConsumeReportParams params) {

        PageResult<List<String>> result = customerGoodsConsumeReportService.customerGoodsConsumeReport(params);
        return new PmsApiPageResponse<>(result.getData(), result.getPage());
    }

    @ResponseBody
    @RequestMapping(value = "customerGoodsConsumeReport/export", method = {RequestMethod.GET, RequestMethod.POST})
    public void customerGoodsConsumeReportExport(@RequestParam(required = true) String beginDate,
                                                 @RequestParam(required = true) String endDate,
                                                 @RequestParam(required = true) Integer chainId,
                                                 HttpServletResponse response) throws IOException {
        String chainName = chainService.getChainNameByChainId(chainId);
        if (chainName == null) {
            throw new BusinessException("酒店不存在");
        }
        CustomerGoodsConsumeReportParams params = new CustomerGoodsConsumeReportParams();
        params.setBeginDate(beginDate);
        params.setEndDate(endDate);
        params.setChainId(chainId);
        params.setPageNum(1);
        params.setPageSize(2000);
        PageResult<List<String>> result = customerGoodsConsumeReportService.customerGoodsConsumeReport(params);
        ExcelUtils.downLoadExcel(response, chainName + "消耗报表.xlsx",(t)->{
            EasyExcel.write(t).needHead(false).sheet("消耗报表").doWrite(result.getData());
        });
    }
}
