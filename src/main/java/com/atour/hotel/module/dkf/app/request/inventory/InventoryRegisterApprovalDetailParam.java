package com.atour.hotel.module.dkf.app.request.inventory;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 客用品详情
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InventoryRegisterApprovalDetailParam implements Serializable {

    /**
     * 客用品ID
     */
    @ApiModelProperty(value = "客用品ID")
    private Long supplyId;


    /**
     * 客用品名称
     */
    @ApiModelProperty(value = "使用数量")
    private Integer useCount;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer inventoryCount;


}
