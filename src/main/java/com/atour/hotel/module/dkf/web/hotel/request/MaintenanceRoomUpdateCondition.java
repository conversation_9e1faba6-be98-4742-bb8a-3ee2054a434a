package com.atour.hotel.module.dkf.web.hotel.request;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 维保房更新参数
 */
@Data
public class MaintenanceRoomUpdateCondition {

    /**
     * 主键id集合
     */
    private List<Long> idList;

    /**
     * 开始维保日期 yyyy-MM-dd
     */
    private LocalDate startDate;

    /**
     * 结束维保日期 yyyy-MM-dd
     */
    private LocalDate endDate;

    /**
     * 维保状态 1-维保中 2-结束维保
     */
    private Integer state;

    /**
     * 设置维保人
     */
    private String setupUser;

    /**
     * 结束维保人
     */
    private String endUser;

    /**
     * 维保原因
     */
    private String reason;

    private Integer deleteFlag;

    private String updateUser;

    private String traceId;
}
