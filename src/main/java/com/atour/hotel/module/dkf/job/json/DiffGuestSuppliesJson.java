package com.atour.hotel.module.dkf.job.json;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/11/14
 * 预警json
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiffGuestSuppliesJson {
    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * diff
     */
    private Double diffPercent;
    /**
     * 盘点数
     */
    private Double manageCount;
    /**
     * 做房数
     */
    private Double waiterCount;
    /**
     * 预警配置阀值
     */
    private Double percent;
}
