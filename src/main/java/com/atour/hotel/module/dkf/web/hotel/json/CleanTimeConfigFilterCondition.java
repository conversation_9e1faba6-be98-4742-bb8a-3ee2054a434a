package com.atour.hotel.module.dkf.web.hotel.json;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 打扫记时配置页面筛选条件
 */
@Data
public class CleanTimeConfigFilterCondition {

    /**
     * 酒店状态: 0全部，1开业 2筹建
     * @see com.atour.dicts.db.center.sys_chain.ChainStateEnum
     */
    @Schema(description = "酒店状态: 0全部，1开业 2筹建")
    private Integer hotelState;

    /**
     * 酒店类型: 0特许，1直营 2全部
     * @see com.atour.hotel.module.cost.enums.ChainTypeEnum
     */
    @Schema(description = "酒店类型: 0特许，1直营 2全部")
    private Integer hotelType;

    /**
     * 区域ids: -1代表全部
     */
    @Schema(description = "区域ids: -1代表全部")
    private List<Integer> areaIds;

}
