package com.atour.hotel.module.dkf.app.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 获取房间详情
 * @date 2024年12月21日17:39
 * @since JDK1.8
 */
@Data
public class RoomResultDTO implements Serializable {

    private static final long serialVersionUID = 7003012102390030276L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    private Integer chainId;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 房态
     */
    @ApiModelProperty(value = "房态(1:空隔夜 2:空脏房 3:在住脏换床单 4:在住脏房 5:空清洁房 6:空干净房 7:在住干净房 8:停售房 9:临时房)")
    private Integer roomState;

    /**
     * 打扫状态
     */
    @ApiModelProperty(value = "打扫状态(1:未打扫 2:打扫中 3:已打扫)")
    private Integer cleanState;

    /**
     * 普通打扫业务唯一编号
     */
    @ApiModelProperty(value = "打扫业务唯一编号")
    private String cleanNo;

    /**
     * 是否是大清房 0.否 1.是
     */
    private Integer isCleanRoom = 0;

    /**
     * 大清房查房id
     */
    private Long checkCleanStateRoomId;

    /**
     * 房型名称
     */
    @ApiModelProperty(value = "房型名称")
    private String roomTypeName;

    /**
     * 是否拥有朵客房经理权限 true：有；false：无
     */
    private Boolean hasDkfManageAuth = false;

    /**
     * 检查房标记 1-检查状态，无法入住 2-非检查状态，可以入住
     */
    private Integer checkRoomFlag;

    /**
     * 创建人名字
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否展示新版二级查房页面 true：展示；false:不展示
     */
    private boolean isShowNewSecRoundRoom = false;
}
