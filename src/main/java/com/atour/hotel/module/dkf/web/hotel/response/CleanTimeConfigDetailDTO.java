package com.atour.hotel.module.dkf.web.hotel.response;

import com.atour.hotel.module.dkf.web.hotel.json.CleanTimeConfigFilterCondition;
import com.atour.hotel.module.dkf.web.hotel.json.CleaningTimeConfigJson;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 打扫记时配置详情
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CleanTimeConfigDetailDTO {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 打扫时长配置
     */
    private List<CleaningTimeConfigJson> cleaningTimeConfigJsonList;

    /**
     * 配置关联的酒店信息
     */
    private List<Integer> releatedChainIdList;

    /**
     * 列表筛选条件
     */
    private CleanTimeConfigFilterCondition filterCondition;

}
