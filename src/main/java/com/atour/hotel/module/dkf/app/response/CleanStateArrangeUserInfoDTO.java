package com.atour.hotel.module.dkf.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 获取可排房人员信息
 * @date 2024年04月20日21:37
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CleanStateArrangeUserInfoDTO implements Serializable {
    private static final long serialVersionUID = 3327580651211097874L;

    /**
     * 排房人
     */
    @Schema(description = "排房人")
    private String arrangeUserName;

    /**
     * 排房人Id
     */
    @Schema(description = "排房人Id")
    private Integer arrangeUserId;

    /**
     * 已排大清数量
     */
    @Schema(description = "已排大清数量")
    private Integer arrangeRoomTotal = 0;

    /**
     * 是否是当前普通打扫排房人 0-不是 1-是
     */
    @Schema(description = "是否是当前普通打扫排房人 0-不是 1-是")
    private Integer isCurrentUser = 0;
}
