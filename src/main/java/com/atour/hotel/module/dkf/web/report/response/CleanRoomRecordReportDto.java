package com.atour.hotel.module.dkf.web.report.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/9
 * 做房记录表数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CleanRoomRecordReportDto implements Serializable {

    /**
     * 酒店ID
     */
    private Integer chainId;
    /**
     * 酒店名称
     */
    private String chainName;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 打扫开始时间
     */
    private String startTime;

    /**
     * 打扫结束时间
     */
    private String endTime;

    /**
     * 用时
     */
    private Integer cleanTime;

    /**
     * 营业日
     */
    private String accDate;

    /**
     * 打扫人姓名
     */
    private String sweepPeopleName;

    /**
     * 计件房态(打扫前)
     */
    private String pieceRoomState;

    /**
     * 打扫后房态
     */
    private String afterCleanRoomState;

    /**
     * 打扫编号
     */
    private String cleanRoomNo;

    /**
     * 二级查房结果
     */
    private SecRoundRoomResultDTO resultDTO;

    /**
     * 是否展示二级查房结果 true：展示；false:不展示
     */
    private boolean isShowSecRoundRoomResult = false;
}
