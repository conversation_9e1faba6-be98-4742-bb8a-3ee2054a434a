package com.atour.hotel.module.dkf.app.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年05月08日10:18
 * @since JDK1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SweepStandardInfoDTO implements Serializable {
    private static final long serialVersionUID = 6918692777108836251L;

    /**
     * 做房关键词ID
     */
    private Long sweepId;

    /**
     * 做房关键词
     */
    private String sweepKeyWord;

    /**
     * 做房对应步骤附件
     */
    private List<SweepStandardExtInfoDTO> extInfoDTOS;
}
