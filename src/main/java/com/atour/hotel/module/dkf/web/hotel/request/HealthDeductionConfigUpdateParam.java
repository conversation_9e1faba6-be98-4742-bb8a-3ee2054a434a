package com.atour.hotel.module.dkf.web.hotel.request;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * WEB层: 日常卫生问题扣分项配置更新参数
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@ApiModel(value = "HealthDeductionConfigUpdateParam", description = "日常卫生问题扣分项配置更新参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HealthDeductionConfigUpdateParam implements Serializable {

    private static final long serialVersionUID = 6794418446235962960L;
    /**
     * 主键Id(更新标识)
     */
    @NotNull(message = "更新标识Id不能为空")
    private Long id;

    /**
     * 扣分项名称
     */
    @NotBlank(message = "扣分项名称不能为空")
    private String deductionName;

    /**
     * 扣除分数
     */
    @NotNull(message = "扣除分数不能为空")
    @Min(value = 1, message = "分值不能小于1")
    private BigDecimal deductPoints;
}
