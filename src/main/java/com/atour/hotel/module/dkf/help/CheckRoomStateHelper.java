package com.atour.hotel.module.dkf.help;

import com.atour.hotel.module.dkf.enums.RoomStatus;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * 帮助类: 打扫后房态获取
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Service
public class CheckRoomStateHelper {

    /**
     * 项目进度控制集合
     */
    private Map<Integer, RoomStatus> checkRoomStateMap;

    /**
     * 初始化角色控制按钮信息
     */
    @PostConstruct
    private void init() {

        if (CollectionUtils.isEmpty(checkRoomStateMap)) {
            checkRoomStateMap = Maps.newHashMap();
        }

        checkRoomStateMap.put(RoomStatus.EMPTY_PURITY_ROOM.getCode(), RoomStatus.EMPTY_CLEAN_ROOM);
        checkRoomStateMap.put(RoomStatus.LIVING_CLEAN_ROOM.getCode(), RoomStatus.LIVING_CLEAN_ROOM);
        checkRoomStateMap.put(RoomStatus.EMPTY_CLEAN_ROOM.getCode(), RoomStatus.EMPTY_CLEAN_ROOM);
    }

    /**
     * 查询计件类型
     *
     * @param roomStatus 房态
     * @return 房态对应的计件类型
     */
    public RoomStatus checkRoomState(Integer roomStatus) {

        if (Objects.isNull(roomStatus)) {
            return null;
        }

        return checkRoomStateMap.get(roomStatus);
    }
}
