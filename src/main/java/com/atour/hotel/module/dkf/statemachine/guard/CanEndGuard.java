package com.atour.hotel.module.dkf.statemachine.guard;

import com.atour.db.redis.RedisLock;
import com.atour.hotel.common.lock.LockKeyFactory;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.enums.CleanStatusEnum;
import com.atour.hotel.module.dkf.statemachine.Constants;
import com.atour.hotel.module.dkf.statemachine.context.RoomCleanContext;
import com.atour.hotel.module.dkf.statemachine.event.RoomEvent;
import com.atour.hotel.module.dkf.statemachine.states.RoomStates;
import com.atour.hotel.module.dkf.web.hotel.service.RoomDomain;
import com.atour.monitor.AMonitor;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 前置校验: 结束打扫
 *
 * <AUTHOR>
 * @date 2019-04-11
 */
@Slf4j
@Service
public class CanEndGuard implements Guard<RoomStates, RoomEvent> {

    /**
     * 客房公共服务接口
     */
    @Autowired
    private RoomDomain roomDomain;

    @Resource
    RedisLock redisLock;

    /**
     * 结束打扫前置校验
     *
     * @param stateContext 上下文信息
     * @return 校验是否通过 true:校验通过 false:校验不通过
     */
    @Override
    public boolean evaluate(StateContext<RoomStates, RoomEvent> stateContext) {
        final RoomCleanContext cleanContext = (RoomCleanContext)stateContext.getMessageHeader(Constants.CLEAN_CONTEXT);

        try {
            String lockKey = LockKeyFactory.getRoomCleanLockKey(cleanContext.getChainId(), cleanContext.getRoomNo());
            RLock lock = redisLock.getLock(lockKey);
            if (Objects.isNull(lock) || !lock.tryLock()) {
                AMonitor.meter("arrange_room_clean_lock");
                throw new BusinessException("房间操作冲突，请刷新页面稍后再试");
            }
            try {
                return roomDomain.checkRoom(cleanContext, CleanStatusEnum.CLEAN);
            } finally {
                lock.unlock();
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            AMonitor.meter("APP.ROOM_STATUS.CanEndGuard.error");
            log.error("结束打扫前置校验出现未知异常", e);

        }
        return Boolean.FALSE;
    }
}
