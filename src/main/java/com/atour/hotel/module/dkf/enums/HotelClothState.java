package com.atour.hotel.module.dkf.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 枚举类: 布草杯具对应类型
 *
 * <AUTHOR>
 * @date 2019-08-20
 */
@AllArgsConstructor
@Getter
public enum HotelClothState {
    /**
     * 展开显示
     */
    VALIABLE(1, "开启"),
    /**
     * 折叠展示
     */
    INVALID(2, "关闭"),
    ;
    private Integer state;
    private String desc;

    /**
     * 根据ID获取状态，id不匹配默认关闭
     * @param id
     * @return
     */
    public static HotelClothState of(Integer id) {
        for (HotelClothState state : values()) {
            if (state.state.equals(id)) {
                return state;
            }
        }
        return INVALID;
    }

}
