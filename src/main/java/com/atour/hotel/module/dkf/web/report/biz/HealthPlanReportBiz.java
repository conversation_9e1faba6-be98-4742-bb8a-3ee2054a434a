package com.atour.hotel.module.dkf.web.report.biz;

import com.atour.chain.api.chain.dto.AtourChainInfoDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.galaxy.api.bean.room.RoomDTO;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.common.util.ExcelUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.CommonHelp;
import com.atour.hotel.module.dkf.app.dto.HealthPlanInfoDTO;
import com.atour.hotel.module.dkf.app.request.DkfHealthPlanMonthDetailParams;
import com.atour.hotel.module.dkf.app.request.DkfHealthPlanMonthParams;
import com.atour.hotel.module.dkf.app.response.DkfBaseHealthPlanCompleteDetailDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanMonthCompleteDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanMonthCompleteListDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanMonthDetailDTO;
import com.atour.hotel.module.dkf.app.response.DkfHealthPlanMonthStatisticsDTO;
import com.atour.hotel.module.dkf.enums.HealthFinishEnum;
import com.atour.hotel.module.dkf.web.hotel.request.HealthPlanMonthChainIdListParam;
import com.atour.hotel.module.dkf.web.hotel.response.HealthPlanMonthListDTO;
import com.atour.hotel.module.dkf.web.hotel.service.HealthPlanService;
import com.atour.hotel.module.dkf.web.hotel.service.RoomDomain;
import com.atour.hotel.module.dkf.web.report.request.HealthPlanMonthReportParams;
import com.atour.hotel.module.dkf.web.report.request.HealthPlanWeekReportParams;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanDetailExportReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanMonthExportReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanMonthReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanNameDetail;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekDetailListReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekDetailReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekExportReportDto;
import com.atour.hotel.module.dkf.web.report.response.HealthPlanWeekReportDto;
import com.atour.hotel.module.dkf.web.report.service.CommService;
import com.atour.hotel.module.hotel.utils.LocalDateUtil;
import com.atour.hotel.persistent.hotel.dao.HotelCommonDao;
import com.atour.hotel.persistent.hotel.dao.RoundsRecordDao;
import com.atour.hotel.persistent.hotel.entity.RoomEntityExt;
import com.atour.hotel.persistent.hotel.entity.RoundsRecordEntity;
import com.atour.hotel.persistent.hotel.param.RoomQueryParam;
import com.atour.hotel.persistent.hotel.param.RoundsRecordDateRangeQueryParam;
import com.atour.migrate.helper.dao.template.AbstractDaoProcessAction;
import com.atour.migrate.helper.dao.template.MergeDaoHandleTemplate;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务层: 全国计划卫生完成表
 *
 * <AUTHOR>
 * @date 2019-09-16
 */
@Component
public class HealthPlanReportBiz {

    /**
     * 数据层: 查房记录表
     */
    @Resource
    private RoundsRecordDao roundsRecordDao;

    /**
     * 公共服务: 公共服务
     */
    @Autowired
    private CommService commService;

    /**
     * 数据层: 酒店公共信息
     */
    @Resource
    private HotelCommonDao hotelCommonDao;

    /**
     * 业务层: 朵客房计划卫生
     */
    @Autowired
    private HealthPlanService healthPlanService;

    /**
     * chain远程服务
     */
    @Resource
    private ChainRemote chainRemote;

    @Resource
    private RoomDomain roomDomain;


    private static final Joiner JOINER = Joiner.on("/")
        .skipNulls();

    /**
     * 查询-周计划卫生报表
     *
     * @param weekReportParams 周计划卫生报表查询参数
     * @return 周计划卫生报表
     */
    public List<HealthPlanWeekReportDto> weekQuery(HealthPlanWeekReportParams weekReportParams) {

        Date beginDate;
        Date endDate;
        try {
            // 日期正确性校验
            beginDate = CommonUtils.convertStr2Date(weekReportParams.getBeginDate());
            endDate = CommonUtils.convertStr2Date(weekReportParams.getEndDate());
        } catch (Exception e) {
            throw new BusinessException("请确认完成日期是否合法 " + weekReportParams.getBeginDate() + "~" + weekReportParams.getEndDate());
        }

        //获取开始和结束只查
        long numOfDaysBetween =
            ChronoUnit.DAYS.between(LocalDate.parse(weekReportParams.getBeginDate()), LocalDate.parse(weekReportParams.getEndDate()));
        if (numOfDaysBetween > 30) {
            throw new BusinessException("查询数据跨度不能大于30天");
        }

        List<Range<LocalDate>> ranges = CommonHelp.listBetweenWeekDates(CommonHelp.date2LocalDate(beginDate), CommonHelp.date2LocalDate(endDate));

        List<HealthPlanWeekReportDto> healthPlanWeekReportDtos = Lists.newArrayList();

        if (CollectionUtils.isEmpty(weekReportParams.getChainIds())) {
            return healthPlanWeekReportDtos;
        }

        List<Integer> chainIds = weekReportParams.getChainIds();

        List<AtourChainInfoDTO> chainInfoDTOList = chainRemote.getChainInfoByIdList(Sets.newHashSet(chainIds))
            .getResult();
        Map<Integer, String> chainMap = Safes.of(chainInfoDTOList)
            .stream()
            .collect(Collectors.toMap(AtourChainInfoDTO::getChainId, AtourChainInfoDTO::getName));

        Map<String, String> deptNameMap = commService.getDeptNameMapByChainIdList(chainIds);

        for (List<Integer> chainIdList : Lists.partition(chainIds, 80)) {
            List<RoundsRecordEntity> roundsList = this.selectByParamDateRange(RoundsRecordDateRangeQueryParam.builder()
                .chainIdList(chainIdList)
                .startAccDate(beginDate)
                .endAccDate(endDate)
                .build());

            Map<Integer, List<RoundsRecordEntity>> roundRecordMap = Safes.of(roundsList)
                .stream()
                .collect(Collectors.groupingBy(RoundsRecordEntity::getChainId));

            List<RoomEntityExt> roomEntityExts = this.selectRoomNo(RoomQueryParam.builder()
                .chainIdList(chainIdList)
                .build());
            Map<Integer, List<RoomEntityExt>> roomMap = Safes.of(roomEntityExts)
                .stream()
                .collect(Collectors.groupingBy(RoomEntityExt::getChainId));

            for (Integer chainId : chainIdList) {
                for (Range<LocalDate> range : ranges) {
                    List<RoundsRecordEntity> roundsRecordEntities = Safes.of(roundRecordMap.get(chainId));

                    if (CollectionUtils.isEmpty(roundsRecordEntities)) {
                        continue;
                    }
                    List<Integer> roomList = Safes.of(roomMap.get(chainId))
                        .stream()
                        .map(RoomEntityExt::getRoomNo)
                        .collect(Collectors.toList());

                    // 组装周计划卫生完成明细
                    List<HealthPlanWeekDetailReportDto> details = getDetails(roundsRecordEntities, range, roomList);

                    // 明细中计划卫生展示列
                    List<String> healthPlanNames = Safes.of(details)
                        .stream()
                        .map(HealthPlanWeekDetailReportDto::getDetailList)
                        .flatMap(Collection::stream)
                        .map(HealthPlanWeekDetailListReportDto::getHealthPlanName)
                        .distinct()
                        .collect(Collectors.toList());

                    // 补充没有查房记录房间的计划卫生，全部为未完成
                    details.forEach(healthPlanWeekDetailReportDto -> {

                        List<HealthPlanWeekDetailListReportDto> detailList = Safes.of(healthPlanWeekDetailReportDto.getDetailList());
                        Map<String, HealthPlanWeekDetailListReportDto> planWeekDetailMap = detailList.stream()
                            .collect(Collectors.toMap(HealthPlanWeekDetailListReportDto::getHealthPlanName, Function.identity(), (k1, k2) -> k2));

                        healthPlanNames.forEach(healthPlanName -> {
                            HealthPlanWeekDetailListReportDto healthPlanWeekDetailListReportDto = planWeekDetailMap.get(healthPlanName);
                            if (Objects.isNull(healthPlanWeekDetailListReportDto)) {
                                detailList.add(HealthPlanWeekDetailListReportDto.builder()
                                    .healthPlanName(healthPlanName)
                                    .done(Boolean.FALSE)
                                    .build());
                            }
                        });

                    });

                    //新增展示月计划卫生项目完成占比
                    List<HealthPlanNameDetail> healthPlanNameDetails = getPlanNameDetail(healthPlanNames, details);
                    // 完成率最低项目
                    String lowHealthPlanName = getLowHealthPlanName(healthPlanNames, details);

                    // 周计划完成房间数量
                    int doneRoomNum = getDoneRoomNum(details);

                    // 周计划卫生完成率
                    BigDecimal done = new BigDecimal(String.valueOf(doneRoomNum)).divide(new BigDecimal(String.valueOf(roomList.size())), 4,
                        BigDecimal.ROUND_HALF_UP);

                    healthPlanWeekReportDtos.add(HealthPlanWeekReportDto.builder()
                        .chainName(chainMap.get(chainId))
                        .areaName(deptNameMap.get(chainId + ""))
                        .startDate(CommonHelp.localDate2Str(range.lowerEndpoint()))
                        .endDate(CommonHelp.localDate2Str(range.upperEndpoint()))
                        .roomNum(roomList.size())
                        .doneRate(Safes.of(done)
                            .doubleValue())
                        .lowHealthPlanName(lowHealthPlanName)
                        .healthPlanNames(healthPlanNames)
                        .healthPlanNameDisplay(healthPlanNameDetails)
                        .details(details)
                        .build());
                }
            }
            ;
        }
        return healthPlanWeekReportDtos;
    }

    private List<HealthPlanWeekDetailReportDto> getDetails(List<RoundsRecordEntity> roundsList, Range<LocalDate> range, List<Integer> roomNos) {

        List<HealthPlanWeekDetailReportDto> details = Lists.newArrayList();

        Map<String, List<RoundsRecordEntity>> roundsRecordMap = roundsList.stream()
            .filter(roundsRecordEntity -> Objects.nonNull(roundsRecordEntity.getAccDate()))
            .filter(roundsRecordEntity -> range.contains(Objects.requireNonNull(CommonHelp.date2LocalDate(roundsRecordEntity.getAccDate()))))
            .collect(Collectors.groupingBy(RoundsRecordEntity::getRoomNo));

        Safes.of(roomNos)
            .stream()
            .filter(Objects::nonNull)
            .forEach(roomNo -> {
                List<RoundsRecordEntity> roundRecords = roundsRecordMap.get(roomNo.toString());

                HealthPlanWeekDetailReportDto healthPlanWeekDetailReportDto = new HealthPlanWeekDetailReportDto();
                healthPlanWeekDetailReportDto.setRoomNo(roomNo.toString());

                List<HealthPlanWeekDetailListReportDto> detailList = Safes.of(roundRecords)
                    .stream()
                    .map(roundsEntity -> {
                        List<HealthPlanInfoDTO> weekHealthList = JsonUtils.parseList(roundsEntity.getWeekHealthJson(), HealthPlanInfoDTO.class);

                        return Safes.of(weekHealthList)
                            .stream()
                            .filter(Objects::nonNull)
                            .map(healthPlanInfoDTO -> HealthPlanWeekDetailListReportDto.builder()
                                .healthPlanName(healthPlanInfoDTO.getHealthName())
                                .done(Objects.equals(healthPlanInfoDTO.getStatus(), HealthFinishEnum.FINISH.getCode()))
                                .build())
                            .collect(Collectors.toList());
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

                List<HealthPlanWeekDetailListReportDto> weekDetailList = Lists.newArrayList();
                detailList.stream()
                    .collect(Collectors.groupingBy(HealthPlanWeekDetailListReportDto::getHealthPlanName))
                    .forEach((healthPlanName, weekDetails) -> {
                        HealthPlanWeekDetailListReportDto healthPlanWeekDetailListReportDto = new HealthPlanWeekDetailListReportDto();
                        healthPlanWeekDetailListReportDto.setHealthPlanName(healthPlanName);
                        boolean done = weekDetails.stream()
                            .filter(weekDetailListReportDto -> Objects.nonNull(weekDetailListReportDto.getDone()))
                            .anyMatch(HealthPlanWeekDetailListReportDto::getDone);
                        healthPlanWeekDetailListReportDto.setDone(done);
                        weekDetailList.add(healthPlanWeekDetailListReportDto);
                    });

                healthPlanWeekDetailReportDto.setDetailList(weekDetailList);
                details.add(healthPlanWeekDetailReportDto);
            });
        return details;
    }

    private int getDoneRoomNum(List<HealthPlanWeekDetailReportDto> details) {

        return Safes.of(details)
            .stream()
            .map(healthPlanWeekDetailReportDto -> {
                boolean allMatch = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                    .stream()
                    .allMatch(HealthPlanWeekDetailListReportDto::getDone);
                long count = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                    .stream()
                    .map(HealthPlanWeekDetailListReportDto::getHealthPlanName)
                    .distinct()
                    .count();

                // 全部周计划卫生合格且计划卫生数量>=7才算
                if (allMatch && count >= 7) {
                    return 1;
                }
                return 0;
            })
            .reduce(Integer::sum)
            .orElse(0);
    }

    private int getMonthDoneRoomNum(List<HealthPlanWeekDetailReportDto> details, List<String> healthPlanNames) {

        return Safes.of(details)
            .stream()
            .map(healthPlanWeekDetailReportDto -> {
                boolean allMatch = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                    .stream()
                    .allMatch(HealthPlanWeekDetailListReportDto::getDone);
                long count = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                    .stream()
                    .map(HealthPlanWeekDetailListReportDto::getHealthPlanName)
                    .distinct()
                    .count();

                // 全部周计划卫生合格且计划卫生数量>=7才算
                if (allMatch && count >= Safes.of(healthPlanNames)
                    .size()) {
                    return 1;
                }
                return 0;
            })
            .reduce(Integer::sum)
            .orElse(0);
    }

    private String getLowHealthPlanName(List<String> healthPlanNames, List<HealthPlanWeekDetailReportDto> details) {

        final String[] lowHealthPlanName = {StringUtils.EMPTY};
        final int[] reportListSize = {0};
        healthPlanNames.forEach(planName -> {
            Integer size = Safes.of(details)
                .stream()
                .map(healthPlanWeekDetailReportDto -> {
                    long count = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                        .stream()
                        .filter(
                            healthPlanWeekDetailListReportDto -> StringUtils.equals(planName, healthPlanWeekDetailListReportDto.getHealthPlanName()))
                        .filter(HealthPlanWeekDetailListReportDto::getDone)
                        .count();
                    return Long.valueOf(count)
                        .intValue();
                })
                .reduce(Integer::sum)
                .orElse(0);

            if (StringUtils.isEmpty(lowHealthPlanName[0])) {
                lowHealthPlanName[0] = planName;
                reportListSize[0] = size;
                return;
            }
            if (reportListSize[0] > size) {
                lowHealthPlanName[0] = planName;
                reportListSize[0] = size;
            } else if (reportListSize[0] == size) {
                lowHealthPlanName[0] = lowHealthPlanName[0] + "," + planName;
            }
        });
        return lowHealthPlanName[0];
    }

    /**
     * 查询-月计划卫生报表
     *
     * @param monthReportParams 月计划卫生报表查询参数
     * @return 月计划卫生报表
     */
    public List<HealthPlanMonthReportDto> monthQuery(HealthPlanMonthReportParams monthReportParams) {

        LocalDate beginDate = LocalDate.of(monthReportParams.getBeginYear(), monthReportParams.getBeginMonth(), 1);
        LocalDate date = LocalDate.of(monthReportParams.getEndYear(), monthReportParams.getEndMonth(), 1);
        LocalDate endDate = date.with(TemporalAdjusters.lastDayOfMonth());

        if (monthReportParams.getEndMonth() - monthReportParams.getBeginMonth() != 0
            || monthReportParams.getEndYear() - monthReportParams.getBeginYear() != 0) {
            throw new BusinessException("筛选条件时间范围不能大于一个月");
        }

        List<Integer> chainIds = Safes.of(monthReportParams.getChainIds());

        List<YearMonth> yearMonths = CommonHelp.getYearMonthBetweenDate(beginDate, endDate);

        List<HealthPlanMonthReportDto> healthPlanMonthReportDtos = Lists.newArrayList();

        // 酒店名称
        List<AtourChainInfoDTO> chainInfoDTOList = chainRemote.getChainInfoByIdList(Sets.newHashSet(chainIds))
            .getResult();
        Map<Integer, String> chainMap = Safes.of(chainInfoDTOList)
            .stream()
            .collect(Collectors.toMap(AtourChainInfoDTO::getChainId, AtourChainInfoDTO::getName));

        // 区域名称
        Map<String, String> deptNameMap = commService.getDeptNameMapByChainIdList(chainIds);

        for (List<Integer> chainIdList : Lists.partition(chainIds, 50)) {

            // 查询日期范围内的查房记录
            List<RoundsRecordEntity> bratchRoundsList = this.selectByParamDateRange(RoundsRecordDateRangeQueryParam.builder()
                .chainIdList(chainIdList)
                .startAccDate(CommonHelp.localDate2Date(beginDate))
                .endAccDate(CommonHelp.localDate2Date(endDate))
                .build());

            //按照门店id对查房记录进行分组
            Map<Integer, List<RoundsRecordEntity>> roundRecordMap = Safes.of(bratchRoundsList)
                .stream()
                .collect(Collectors.groupingBy(RoundsRecordEntity::getChainId));

            List<RoomEntityExt> roomEntityExts = this.selectRoomNo(RoomQueryParam.builder()
                .chainIdList(chainIdList)
                .build());
            Map<Integer, List<RoomEntityExt>> roomMap = Safes.of(roomEntityExts)
                .stream()
                .collect(Collectors.groupingBy(RoomEntityExt::getChainId));

            for (YearMonth yearMonth : yearMonths) {
                Range<LocalDate> monthDateRange = CommonHelp.buildDateRangeByMonth(yearMonth);

                // 查询月计划卫生
                Map<Integer, List<HealthPlanMonthListDTO>> healthPlanMap = healthPlanService.monthListByChainIdList(
                    HealthPlanMonthChainIdListParam.builder()
                        .chainIdList(chainIdList)
                        .year(yearMonth.getYear())
                        .month(yearMonth.getMonthValue())
                        .build());

                for (Integer chainId : chainIdList) {

                    List<RoundsRecordEntity> roundsList = roundRecordMap.get(chainId);

                    if (CollectionUtils.isEmpty(roundsList)) {
                        continue;
                    }

                    List<HealthPlanMonthListDTO> healthPlanMonthListDTOS = healthPlanMap.get(chainId);

                    List<Integer> roomNos = Safes.of(roomMap.get(chainId))
                        .stream()
                        .map(RoomEntityExt::getRoomNo)
                        .collect(Collectors.toList());

                    //根据完成明细组装展示name
                    List<String> healthPlanNames = Safes.of(healthPlanMonthListDTOS)
                        .stream()
                        .filter(Objects::nonNull)
                        .map(HealthPlanMonthListDTO::getHealthPlanName)
                        .filter(StringUtils::isNotEmpty)
                        .distinct()
                        .collect(Collectors.toList());

                    // 组装月计划卫生完成明细
                    List<HealthPlanWeekDetailReportDto> details = getMonthDetails(roundsList, monthDateRange, healthPlanNames, roomNos);
                    //新增展示月计划卫生项目完成占比
                    List<HealthPlanNameDetail> healthPlanNameDetails = getPlanNameDetail(healthPlanNames, details);
                    // 完成率最低项目
                    String lowHealthPlanName = getLowHealthPlanName(healthPlanNames, details);

                    // 月计划完成房间数量
                    int doneRoomNum = getMonthDoneRoomNum(details, healthPlanNames);

                    // 月计划卫生完成率
                    BigDecimal done = new BigDecimal(String.valueOf(doneRoomNum)).divide(new BigDecimal(String.valueOf(roomNos.size())), 2,
                        BigDecimal.ROUND_HALF_UP);

                    healthPlanMonthReportDtos.add(HealthPlanMonthReportDto.builder()
                        .areaName(deptNameMap.get(chainId + ""))
                        .chainName(chainMap.get(chainId))
                        .yearMonth(yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                        .roomNum(roomNos.size())
                        .doneRate(Safes.of(done)
                            .doubleValue())
                        .lowHealthPlanName(lowHealthPlanName)
                        .healthPlanNames(healthPlanNames)
                        .healthPlanNameDisplay(healthPlanNameDetails)
                        .details(details)
                        .build());
                }
            }
        }
        return healthPlanMonthReportDtos;
    }

    /**
     * 根据计划项目及项目完成情况统计各酒店各项目的完成进度
     * @param healthPlanNames
     * @param details
     * @return
     */
    private List<HealthPlanNameDetail> getPlanNameDetail(List<String> healthPlanNames, List<HealthPlanWeekDetailReportDto> details) {

        List<HealthPlanNameDetail> planNameDetail = Lists.newArrayList();
        healthPlanNames.forEach(e -> {
            int done = 0;
            int sum = 0;
            for (HealthPlanWeekDetailReportDto room : details) {
                Map<String, Boolean> nameToStatus = room.getDetailList()
                    .stream()
                    .collect(Collectors.toMap(HealthPlanWeekDetailListReportDto::getHealthPlanName, HealthPlanWeekDetailListReportDto::getDone,
                        (k1, k2) -> k2));
                Boolean completeStaus = nameToStatus.getOrDefault(e, false);
                sum++;
                if (completeStaus) {
                    done++;
                }
            }
            HealthPlanNameDetail detail = new HealthPlanNameDetail();
            detail.setHealthPlanName(e);
            detail.setHealthPlanDisplayValue(JOINER.join(done, sum, getCompleteRate(new BigDecimal(done), new BigDecimal(sum)) + "%"));
            planNameDetail.add(detail);
        });
        return planNameDetail;
    }

    /**
     * 计算完成率并将结果保留两位小数
     * @param done
     * @param sum
     * @return
     */
    private BigDecimal getCompleteRate(BigDecimal done, BigDecimal sum) {

        if (sum.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return done.multiply(new BigDecimal(100))
            .divide(sum, 2, BigDecimal.ROUND_HALF_UP);
    }

    private List<HealthPlanWeekDetailReportDto> getMonthDetails(List<RoundsRecordEntity> roundsList, Range<LocalDate> monthDateRange,
        List<String> healthPlanNames, List<Integer> roomNos) {

        List<HealthPlanWeekDetailReportDto> details = Lists.newArrayList();

        Map<String, List<RoundsRecordEntity>> roundsRecordMap = roundsList.stream()
            .filter(roundsRecordEntity -> Objects.nonNull(roundsRecordEntity.getAccDate()))
            .filter(roundsRecordEntity -> Objects.nonNull(roundsRecordEntity.getRoomNo()))
            .filter(roundsRecordEntity -> monthDateRange.contains(Objects.requireNonNull(CommonHelp.date2LocalDate(roundsRecordEntity.getAccDate()))))
            .collect(Collectors.groupingBy(RoundsRecordEntity::getRoomNo));

        Safes.of(roomNos)
            .stream()
            .filter(Objects::nonNull)
            .forEach(roomNo -> {
                List<RoundsRecordEntity> roundRecords = roundsRecordMap.get(roomNo.toString());

                HealthPlanWeekDetailReportDto healthPlanWeekDetailReportDto = new HealthPlanWeekDetailReportDto();
                healthPlanWeekDetailReportDto.setRoomNo(roomNo.toString());

                List<HealthPlanWeekDetailListReportDto> detailList = Safes.of(roundRecords)
                    .stream()
                    .map(roundsEntity -> {
                        List<HealthPlanInfoDTO> monthHealthList = JsonUtils.parseList(roundsEntity.getMonthHealthJson(), HealthPlanInfoDTO.class);

                        return Safes.of(monthHealthList)
                            .stream()
                            .filter(Objects::nonNull)
                            .map(healthPlanInfoDTO -> HealthPlanWeekDetailListReportDto.builder()
                                .healthPlanName(healthPlanInfoDTO.getHealthName())
                                .done(Objects.equals(healthPlanInfoDTO.getStatus(), HealthFinishEnum.FINISH.getCode()))
                                .build())
                            .collect(Collectors.toList());
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

                List<HealthPlanWeekDetailListReportDto> monthDetailList = Lists.newArrayList();
                detailList.stream()
                    .collect(Collectors.groupingBy(HealthPlanWeekDetailListReportDto::getHealthPlanName))
                    .forEach((healthPlanName, monthDetails) -> {
                        HealthPlanWeekDetailListReportDto healthPlanWeekDetailListReportDto = new HealthPlanWeekDetailListReportDto();
                        healthPlanWeekDetailListReportDto.setHealthPlanName(healthPlanName);
                        boolean done = monthDetails.stream()
                                .filter(weekDetailListReportDto -> Objects.nonNull(weekDetailListReportDto.getDone()))
                                .anyMatch(HealthPlanWeekDetailListReportDto::getDone);
                        healthPlanWeekDetailListReportDto.setDone(done);
                        monthDetailList.add(healthPlanWeekDetailListReportDto);
                    });

                List<String> healthPlanNameList = Safes.of(monthDetailList)
                    .stream()
                    .map(HealthPlanWeekDetailListReportDto::getHealthPlanName)
                    .collect(Collectors.toList());
                healthPlanNames.forEach(healthPlanName -> {
                    if (CollectionUtils.isNotEmpty(healthPlanNameList) && healthPlanNameList.contains(healthPlanName)) {
                        return;
                    }

                    HealthPlanWeekDetailListReportDto healthPlanWeekDetailListReportDto = new HealthPlanWeekDetailListReportDto();
                    healthPlanWeekDetailListReportDto.setHealthPlanName(healthPlanName);
                    healthPlanWeekDetailListReportDto.setDone(Boolean.FALSE);
                    monthDetailList.add(healthPlanWeekDetailListReportDto);

                });
                healthPlanWeekDetailReportDto.setDetailList(monthDetailList);
                details.add(healthPlanWeekDetailReportDto);
            });
        return details.stream()
            .sorted(Comparator.comparing(HealthPlanWeekDetailReportDto::getRoomNo))
            .collect(Collectors.toList());
    }

    /**
     * 导出-周计划卫生报表
     *
     * @param weekReportParams 周计划卫生报表查询参数
     * @param response         响应结果
     * @return 周计划卫生报表
     */
    public String weekExport(HealthPlanWeekReportParams weekReportParams, HttpServletResponse response) {

        // 查询周计划卫生报表
        List<HealthPlanWeekReportDto> healthPlanWeekReportDtos = weekQuery(weekReportParams);

        if (CollectionUtils.isEmpty(healthPlanWeekReportDtos)) {
            throw new BusinessException("未查询到报表数据,无法导出...");
        }

        List<HealthPlanWeekExportReportDto> exportDatas = healthPlanWeekReportDtos.stream()
            .map(healthPlanWeekReportDto -> {

                List<String> heands = new ArrayList<>(Collections.singletonList("房间号"));
                heands.addAll(healthPlanWeekReportDto.getHealthPlanNames());

                List<HealthPlanDetailExportReportDto> details = Safes.of(healthPlanWeekReportDto.getDetails())
                    .stream()
                    .map(healthPlanWeekDetailReportDto -> {

                        Map<String, Boolean> collect = Safes.of(healthPlanWeekDetailReportDto.getDetailList())
                            .stream()
                            .collect(
                                Collectors.toMap(HealthPlanWeekDetailListReportDto::getHealthPlanName, HealthPlanWeekDetailListReportDto::getDone,
                                    (k1, k2) -> k2));
                        Map<String, String> map = Maps.newHashMap();
                        for (String heand : healthPlanWeekReportDto.getHealthPlanNames()) {
                            Boolean aBoolean = collect.get(heand);
                            if (Objects.nonNull(aBoolean) && aBoolean) {
                                map.put(heand, "完成");
                            } else {
                                if (Objects.isNull(aBoolean)) {
                                    map.put(heand, " ");
                                } else {
                                    map.put(heand, "未完成");
                                }
                            }
                        }

                        return HealthPlanDetailExportReportDto.builder()
                            .roomNo(healthPlanWeekDetailReportDto.getRoomNo())
                            .mapValues(map)
                            .build();

                    })
                    .sorted(Comparator.comparing(HealthPlanDetailExportReportDto::getRoomNo))
                    .collect(Collectors.toList());
                return HealthPlanWeekExportReportDto.builder()
                    .areaName(healthPlanWeekReportDto.getAreaName())
                    .chainName(healthPlanWeekReportDto.getChainName())
                    .weekDate(healthPlanWeekReportDto.getStartDate() + "至" + healthPlanWeekReportDto.getEndDate())
                    .roomNum(healthPlanWeekReportDto.getRoomNum())
                    .doneRate(healthPlanWeekReportDto.getDoneRate())
                    .lowHealthPlanName(healthPlanWeekReportDto.getLowHealthPlanName())
                    .healthPlanNames(heands)
                    .details(details)
                    .build();
            })
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(exportDatas)) {
            throw new BusinessException("周计划卫生报表未查询到数据,无法导出...");
        }

        ExcelUtils<HealthPlanWeekExportReportDto> csv = new ExcelUtils<>();
        csv.exportCSV("周计划卫生报表", exportDatas, response);

        return "导出周计划卫生报表成功";
    }

    /**
     * 导出-月计划卫生报表
     *
     * @param monthReportParams 月计划卫生报表查询参数
     * @param response          响应结果
     * @return 月计划卫生报表
     */
    public String monthExport(HealthPlanMonthReportParams monthReportParams, HttpServletResponse response) {

        // 查询周计划卫生报表
        List<HealthPlanMonthReportDto> healthPlanMonthReportDtos = monthQuery(monthReportParams);

        if (CollectionUtils.isEmpty(healthPlanMonthReportDtos)) {
            throw new BusinessException("未查询到报表数据,无法导出...");
        }

        List<HealthPlanMonthExportReportDto> exportDatas = healthPlanMonthReportDtos.stream()
            .map(healthPlanMonthReportDto -> {

                List<String> heands = new ArrayList<>(Collections.singletonList("房间号"));
                heands.addAll(healthPlanMonthReportDto.getHealthPlanNames());

                List<HealthPlanDetailExportReportDto> details = Safes.of(healthPlanMonthReportDto.getDetails())
                    .stream()
                    .map(healthPlanMonthDetailReportDto -> {

                        Map<String, Boolean> collect = Safes.of(healthPlanMonthDetailReportDto.getDetailList())
                            .stream()
                            .collect(
                                Collectors.toMap(HealthPlanWeekDetailListReportDto::getHealthPlanName, HealthPlanWeekDetailListReportDto::getDone,
                                    (k1, k2) -> k2));
                        Map<String, String> map = Maps.newHashMap();
                        for (String heand : healthPlanMonthReportDto.getHealthPlanNames()) {
                            Boolean aBoolean = collect.get(heand);
                            if (Objects.nonNull(aBoolean) && aBoolean) {
                                map.put(heand, "完成");
                            } else {
                                if (Objects.isNull(aBoolean)) {
                                    map.put(heand, " ");
                                } else {
                                    map.put(heand, "未完成");
                                }
                            }
                        }

                        return HealthPlanDetailExportReportDto.builder()
                            .roomNo(healthPlanMonthDetailReportDto.getRoomNo())
                            .mapValues(map)
                            .build();

                    })
                    .sorted(Comparator.comparing(HealthPlanDetailExportReportDto::getRoomNo))
                    .collect(Collectors.toList());

                return HealthPlanMonthExportReportDto.builder()
                    .areaName(healthPlanMonthReportDto.getAreaName())
                    .chainName(healthPlanMonthReportDto.getChainName())
                    .yearMonth(healthPlanMonthReportDto.getYearMonth())
                    .roomNum(healthPlanMonthReportDto.getRoomNum())
                    .doneRate(healthPlanMonthReportDto.getDoneRate())
                    .lowHealthPlanName(healthPlanMonthReportDto.getLowHealthPlanName())
                    .healthPlanNames(heands)
                    .details(details)
                    .build();
            })
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(exportDatas)) {
            throw new BusinessException("月计划卫生报表未查询到数据,无法导出...");
        }

        ExcelUtils<HealthPlanMonthExportReportDto> csv = new ExcelUtils<>();
        csv.exportCSV("月计划卫生报表", exportDatas, response);

        return "导出月计划卫生报表成功";
    }

    /**
     * 查询日期范围内的查房记录
     *
     * @param roundsRecordDateRangeQueryParam
     * @return
     */
    private List<RoundsRecordEntity> selectByParamDateRange(RoundsRecordDateRangeQueryParam roundsRecordDateRangeQueryParam) {

        return MergeDaoHandleTemplate.execute(new AbstractDaoProcessAction<RoundsRecordDateRangeQueryParam, RoundsRecordEntity>() {

            @Override
            public String getMonitorKey() {

                return "national.selectByParamDateRange";
            }

            @Override
            public Collection<Integer> getAllNeedHandleChainIdList() {

                return roundsRecordDateRangeQueryParam.getChainIdList();
            }

            @Override
            public String getHandleTableName() {

                return "dkf_rounds_record";
            }

            @Override
            public RoundsRecordDateRangeQueryParam getNeedHandleDaoParam() {

                return roundsRecordDateRangeQueryParam;
            }

            @Override
            public List<RoundsRecordEntity> originalDaoLogic(RoundsRecordDateRangeQueryParam roundsRecordDateRangeQueryParam) {

                return roundsRecordDao.selectByParamDateRange(roundsRecordDateRangeQueryParam);
            }

            @Override
            public void setMergeChainId(RoundsRecordDateRangeQueryParam roundsRecordDateRangeQueryParam, Integer mergeChainId) {

                roundsRecordDateRangeQueryParam.setChainId(mergeChainId);
            }

            @Override
            public void setOldChainIdList(RoundsRecordDateRangeQueryParam roundsRecordDateRangeQueryParam, Collection<Integer> oldChainIdList) {

                roundsRecordDateRangeQueryParam.setChainIdList(oldChainIdList);
            }
        });
    }

    /**
     * 查询客房列表
     *
     * @param roomQueryParam
     * @return
     */
    private List<RoomEntityExt> selectRoomNo(RoomQueryParam roomQueryParam) {

        return MergeDaoHandleTemplate.execute(new AbstractDaoProcessAction<RoomQueryParam, RoomEntityExt>() {

            @Override
            public String getMonitorKey() {

                return "national.selectRoomNo";
            }

            @Override
            public Collection<Integer> getAllNeedHandleChainIdList() {

                return roomQueryParam.getChainIdList();
            }

            @Override
            public String getHandleTableName() {

                return "r_Room";
            }

            @Override
            public RoomQueryParam getNeedHandleDaoParam() {

                return roomQueryParam;
            }

            @Override
            public List<RoomEntityExt> originalDaoLogic(RoomQueryParam roomQueryParam) {

                return hotelCommonDao.selectRoomNoByChainIdList(roomQueryParam);
            }

            @Override
            public void setMergeChainId(RoomQueryParam roomQueryParam, Integer mergeChainId) {

                roomQueryParam.setChainId(mergeChainId);
            }

            @Override
            public void setOldChainIdList(RoomQueryParam roomQueryParam, Collection<Integer> oldChainIdList) {

                roomQueryParam.setChainIdList(oldChainIdList);
            }
        });
    }

    /**
     * 月计划卫生列表以及汇总统计
     * @param monthParams
     * @return
     */
    public DkfHealthPlanMonthCompleteDTO getDkfHealthPlanMonthStatistics(DkfHealthPlanMonthParams monthParams){
        LocalDate sDate = LocalDateUtil.convertStr2LocalDate(monthParams.getBeginDate());
        LocalDate eDate = LocalDateUtil.convertStr2LocalDate(monthParams.getEndDate());
        if (sDate.plusMonths(1L).isBefore(eDate)) {
            throw new BusinessException("查询日期间隔不能超过一个月");
        }

        DkfHealthPlanMonthCompleteDTO completeDTO = new DkfHealthPlanMonthCompleteDTO();
        DkfHealthPlanMonthStatisticsDTO statisticsDTO = new DkfHealthPlanMonthStatisticsDTO();
        List<RoomEntityExt> roomEntityExts = this.selectRoomNo(RoomQueryParam.builder()
                .chainIdList(Lists.newArrayList(monthParams.getChainId()))
                .build());
        if (CollectionUtils.isNotEmpty(roomEntityExts)){
            statisticsDTO.setTotalRoomNum(roomEntityExts.size());
            completeDTO.setStatisticsDTO(statisticsDTO);
        }
        List<YearMonth> yearMonths = CommonHelp.getYearMonthBetweenDate(sDate, eDate);
        YearMonth yearMonth = yearMonths.get(0);
        Range<LocalDate> monthDateRange = CommonHelp.buildDateRangeByMonth(yearMonth);
        // 查询月计划卫生
        Map<Integer, List<HealthPlanMonthListDTO>> healthPlanMap = healthPlanService.monthListByChainIdList(
                HealthPlanMonthChainIdListParam.builder()
                        .chainIdList(Lists.newArrayList(monthParams.getChainId()))
                        .year(yearMonth.getYear())
                        .month(yearMonth.getMonthValue())
                        .build());
        if (MapUtils.isEmpty(healthPlanMap)){
            return completeDTO;
        }

        List<HealthPlanMonthListDTO> healthPlanMonthListDTOS = healthPlanMap.get(monthParams.getChainId());
        //获取月度计划卫生具体是哪些配置项
        Integer planMonthDetailNum = healthPlanMonthListDTOS.stream().filter(h->Objects.nonNull(h.getHealthPlanId()))
                .collect(Collectors.groupingBy(HealthPlanMonthListDTO::getHealthPlanId)).size();

        List<Integer> roomNos = Safes.of(roomEntityExts)
                .stream()
                .map(RoomEntityExt::getRoomNo)
                .collect(Collectors.toList());

        // 查询日期范围内的查房记录
        List<RoundsRecordEntity> roundsList = this.selectByParamDateRange(RoundsRecordDateRangeQueryParam.builder()
                .chainIdList(Lists.newArrayList(monthParams.getChainId()))
                .startAccDate(DateUtil.parseDate(monthParams.getBeginDate()))
                .endAccDate(DateUtil.parseDate(monthParams.getEndDate()))
                .build());

       Map<String,Integer> healthPlanMonthMap = healthPlanMonthListDTOS.stream()
               .collect(Collectors.toMap(HealthPlanMonthListDTO::getHealthPlanName,HealthPlanMonthListDTO::getHealthPlanId,(v1,v2) ->v2));

        //根据完成明细组装展示name
        List<String> healthPlanNames = Safes.of(healthPlanMonthListDTOS)
                .stream()
                .filter(Objects::nonNull)
                .map(HealthPlanMonthListDTO::getHealthPlanName)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        // 组装月计划卫生完成明细
        List<HealthPlanWeekDetailReportDto> details = getMonthDetails(roundsList, monthDateRange, healthPlanNames, roomNos);
        //新增展示月计划卫生项目完成占比
        List<DkfHealthPlanMonthCompleteListDTO> healthPlanNameDetails = getDkfMonthPlanNameDetail(healthPlanNames, details,healthPlanMonthMap);
        // 月计划完成房间数量
        int doneRoomNum = getMonthDoneRoomNum(details, healthPlanNames);
        // 月计划卫生完成率
        BigDecimal totalCompleteRate = new BigDecimal(String.valueOf(doneRoomNum)).divide(new BigDecimal(String.valueOf(roomNos.size())), 2,
                BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        //计算月度计划卫生平均完成率
        statisticsDTO.setAvgCompleteRate(calAvgCompleteRate(roundsList, planMonthDetailNum, roomNos.size()));
        //总体完成情况
        statisticsDTO.setTotalCompleteRate(totalCompleteRate);
        completeDTO.setStatisticsDTO(statisticsDTO);
        completeDTO.setCompleteListDTOList(Safes.of(healthPlanNameDetails));
       return completeDTO;
    }


    /**
     * 计算月度计划卫生平均完成率
     * @param roundsList 查房信息
     * @param planMonthDetailNum 计划卫生检查项
     * @param roomSize 房间数量
     * @return
     */
    private BigDecimal calAvgCompleteRate(List<RoundsRecordEntity> roundsList, Integer planMonthDetailNum, Integer roomSize) {
        //统计出查房中有做月计划卫生的数据
        List<HealthPlanWeekDetailListReportDto> detailList = Safes.of(roundsList)
                .stream()
                .map(roundsEntity -> {
                    List<HealthPlanInfoDTO> monthHealthList = JsonUtils.parseList(roundsEntity.getMonthHealthJson(), HealthPlanInfoDTO.class);
                    return Safes.of(monthHealthList)
                            .stream()
                            .filter(Objects::nonNull)
                            .map(healthPlanInfoDTO -> HealthPlanWeekDetailListReportDto.builder()
                                    .healthPlanName(healthPlanInfoDTO.getHealthName())
                                    .done(Objects.equals(healthPlanInfoDTO.getStatus(), HealthFinishEnum.FINISH.getCode()))
                                    .build())
                            .collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        //统计周期内所有单项的已完成房间数总和
        Long doneRoomNum = detailList.stream().filter(Objects::nonNull)
                .filter(d -> d.getDone())
                .count();
        //月度平均分母 = 月度需要完成的计划卫生项数量 * 房间数
        Integer needDonePlanRoomNum = planMonthDetailNum * roomSize;
        //月度平均分子 = 统计周期内所有单项的已完成房间数总和
        //计算月度平均完成率
        BigDecimal avgCompleteRate = new BigDecimal(0);
        if (doneRoomNum > 0) {
            avgCompleteRate = new BigDecimal(doneRoomNum)
                    .divide(new BigDecimal(needDonePlanRoomNum), 2,
                            BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        }
        return avgCompleteRate;
    }

    /**
     * 根据计划项目及项目完成情况统计各酒店各项目的完成进度
     * @param healthPlanNames
     * @param details
     * @return
     */
    private List<DkfHealthPlanMonthCompleteListDTO> getDkfMonthPlanNameDetail(List<String> healthPlanNames, List<HealthPlanWeekDetailReportDto> details,Map<String,Integer> healthPlanMonthMap) {
        List<DkfHealthPlanMonthCompleteListDTO> planNameDetail = Lists.newArrayList();
        healthPlanNames.forEach(e -> {
            int done = 0;
            int sum = 0;
            for (HealthPlanWeekDetailReportDto room : details) {
                Map<String, Boolean> nameToStatus = room.getDetailList()
                        .stream()
                        .collect(Collectors.toMap(HealthPlanWeekDetailListReportDto::getHealthPlanName, HealthPlanWeekDetailListReportDto::getDone,
                                (k1, k2) -> k2));
                Boolean completeStaus = nameToStatus.getOrDefault(e, false);
                sum++;
                if (completeStaus) {
                    done++;
                }
            }
            DkfHealthPlanMonthCompleteListDTO detail = new DkfHealthPlanMonthCompleteListDTO();
            detail.setHealthPlanName(e);
            detail.setShouldCompleteNum(sum);
            detail.setCompleteNum(done);
            detail.setDoneRate(getCompleteRate(new BigDecimal(done),new BigDecimal(sum)));
            detail.setHealthPlanId(healthPlanMonthMap.getOrDefault(e,0));
            planNameDetail.add(detail);
        });
        return planNameDetail;
    }

    /**
     * 月计划卫生详情
     * @param monthDetailParams
     * @return
     */
   public DkfHealthPlanMonthDetailDTO queryDetail(DkfHealthPlanMonthDetailParams monthDetailParams){
       LocalDate sDate = LocalDateUtil.convertStr2LocalDate(monthDetailParams.getBeginDate());
       LocalDate eDate = LocalDateUtil.convertStr2LocalDate(monthDetailParams.getEndDate());
       if (sDate.plusMonths(1L).isBefore(eDate)) {
           throw new BusinessException("查询日期间隔不能超过一个月");
       }

       List<RoomDTO> roomDTOS = roomDomain.getRoomByChainId(monthDetailParams.getChainId());
       List<YearMonth> yearMonths = CommonHelp.getYearMonthBetweenDate(sDate, eDate);
       YearMonth yearMonth = yearMonths.get(0);
       Range<LocalDate> monthDateRange = CommonHelp.buildDateRangeByMonth(yearMonth);
       // 查询月计划卫生
       Map<Integer, List<HealthPlanMonthListDTO>> healthPlanMap = healthPlanService.monthListByChainIdList(
               HealthPlanMonthChainIdListParam.builder()
                       .chainIdList(Lists.newArrayList(monthDetailParams.getChainId()))
                       .year(yearMonth.getYear())
                       .month(yearMonth.getMonthValue())
                       .build());

       List<HealthPlanMonthListDTO> healthPlanMonthListDTOS = healthPlanMap.get(monthDetailParams.getChainId());

       List<Integer> roomNos = Safes.of(roomDTOS)
               .stream()
               .map(RoomDTO::getRoomNo)
               .map(Integer::valueOf)
               .collect(Collectors.toList());

       Map<String,String> roomTypeMap = roomDTOS.stream()
               .collect(Collectors.toMap(RoomDTO::getRoomNo,RoomDTO::getRoomTypeName,(v1,v2) ->v2));

       // 查询日期范围内的查房记录
       List<RoundsRecordEntity> roundsList = this.selectByParamDateRange(RoundsRecordDateRangeQueryParam.builder()
               .chainIdList(Lists.newArrayList(monthDetailParams.getChainId()))
               .startAccDate(DateUtil.parseDate(monthDetailParams.getBeginDate()))
               .endAccDate(DateUtil.parseDate(monthDetailParams.getEndDate()))
               .build());
       Map<String,Integer> healthPlanMonthMap = healthPlanMonthListDTOS.stream()
               .collect(Collectors.toMap(HealthPlanMonthListDTO::getHealthPlanName,HealthPlanMonthListDTO::getHealthPlanId,(v1,v2) ->v2));
       //根据完成明细组装展示name
       List<String> healthPlanNames = Safes.of(healthPlanMonthListDTOS)
               .stream()
               .filter(Objects::nonNull)
               .filter(h->StringUtils.isNotBlank(h.getHealthPlanName()) && Objects.equals(h.getHealthPlanName(),monthDetailParams.getHealthPlanName()))
               .map(HealthPlanMonthListDTO::getHealthPlanName)
               .filter(StringUtils::isNotEmpty)
               .distinct()
               .collect(Collectors.toList());
       // 组装月计划卫生完成明细
       List<HealthPlanWeekDetailReportDto> details = getMonthDetails(roundsList, monthDateRange, healthPlanNames, roomNos);
       List<DkfBaseHealthPlanCompleteDetailDTO> completeRoomDetailDTOS = new ArrayList<>();
       List<DkfBaseHealthPlanCompleteDetailDTO> finalCompleteRoomDetailDTOS = completeRoomDetailDTOS;
       Safes.of(details).forEach(d->{
           DkfBaseHealthPlanCompleteDetailDTO detailDTO = new DkfBaseHealthPlanCompleteDetailDTO();
           boolean allMatch = Safes.of(d.getDetailList())
                   .stream()
                   .allMatch(HealthPlanWeekDetailListReportDto::getDone);
           if (allMatch){
               detailDTO.setDone(Boolean.TRUE);
           }else {
               detailDTO.setDone(Boolean.FALSE);
           }
           detailDTO.setRoomTypeName(roomTypeMap.get(d.getRoomNo()));
           detailDTO.setRoomNo(d.getRoomNo());
           finalCompleteRoomDetailDTOS.add(detailDTO);
       });

       if (monthDetailParams.getShowDoneData() == 0){
           completeRoomDetailDTOS = completeRoomDetailDTOS.stream().filter(Objects::nonNull)
                   .filter(c->Objects.equals(Boolean.FALSE,c.getDone())).collect(Collectors.toList());
       }

       //新增展示月计划卫生项目完成占比
       List<DkfHealthPlanMonthCompleteListDTO> healthPlanNameDetails = getDkfMonthPlanNameDetail(healthPlanNames, details,healthPlanMonthMap);
       DkfHealthPlanMonthDetailDTO detailDTO = DkfHealthPlanMonthDetailDTO.builder().build();
       if (CollectionUtils.isNotEmpty(healthPlanNameDetails)){
           detailDTO.setMonthDetails(healthPlanNameDetails.get(0));
           detailDTO.setCompleteRoomDetailDTOS(completeRoomDetailDTOS);
       }
       return detailDTO;
   }
}
