package com.atour.hotel.module.dkf.app.request;

import com.atour.hotel.module.dkf.app.dto.DkfCheckRoomTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel(value = "DkfCheckRoomRectifyApproParam", description = "整改审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DkfCheckRoomRectifyApproParam implements Serializable {


    @ApiModelProperty(value = "整改ID")
    private Long rectifyId;

    @NotNull(message = "整改审批类型不能为空")
    @ApiModelProperty(value = "整改审批类型 0：通过 1：继续整改")
    private Integer rectifyType;

    @ApiModelProperty(value = "整改选中数据集合")
    private List<DkfCheckRoomTypeDTO> taskParamList;

    @Length(max = 300, message = "整改备不能超300字")
    @ApiModelProperty(value = "检查备注")
    private String checkRemrk;

    @ApiModelProperty(value = "上传附件")
    private List<String> ossFileKeys;
}
