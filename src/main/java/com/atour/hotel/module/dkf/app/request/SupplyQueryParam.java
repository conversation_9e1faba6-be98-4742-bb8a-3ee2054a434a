package com.atour.hotel.module.dkf.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 客用品查询参数
 * @date 2024年05月31日20:21
 * @since JDK1.8
 */
@Data
public class SupplyQueryParam implements Serializable {
    private static final long serialVersionUID = -4741298666932283091L;

    /**
     * 酒店ID
     */
    @Schema(description = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    /**
     * 营业日
     */
    @Schema(description = "营业日")
    private LocalDate accDate;

    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 酒店营业日 用于http 请求调用传参使用
     */
    private String chainAccDate;
}
