package com.atour.hotel.module.dkf.web.hotel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * WEB层: 日常卫生问题配置查询参数
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@ApiModel(value = "DayHealthProblemConfigParam", description = "日常卫生问题配置查询参数")
@Data
public class DayHealthProblemConfigParam implements Serializable {

    private static final long serialVersionUID = 4878852262577068631L;

    /**
     * 日常卫生问题名称
     */
    @ApiModelProperty(value = "日常卫生问题名称")
    private String healthProblemName;
}
