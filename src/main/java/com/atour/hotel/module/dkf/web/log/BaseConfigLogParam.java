package com.atour.hotel.module.dkf.web.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/9/20
 * 基础配置操作日志入参数据传输对象
 */
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class BaseConfigLogParam {
    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;
}
