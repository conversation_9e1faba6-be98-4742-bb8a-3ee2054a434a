package com.atour.hotel.module.dkf.web.hotel.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 破晓项目专用的
 *
 * <AUTHOR>
 * @date 2024/12/9 17:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuestsppliesCountResponse {
    /**
     * 数量
     */
    private BigDecimal count;

    /**
     * 含税单价
     */
    private BigDecimal priceInTax;
    /**
     * 单价不含税
     */
    private BigDecimal price;

}
