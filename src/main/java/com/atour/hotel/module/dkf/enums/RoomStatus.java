package com.atour.hotel.module.dkf.enums;

import com.atour.dicts.db.hotel.r_room.CheckInStateEnum;
import com.atour.dicts.db.hotel.r_room.CleanStateEnum;
import com.atour.dicts.db.hotel.r_room.HouseKeepStateEnum;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.CommonHelp;
import lombok.Getter;

import java.util.Date;
import java.util.Objects;

/**
 * 枚举类: 房态
 *
 * <AUTHOR>
 * @date 2019-04-01
 */
public enum RoomStatus {

    /**
     * 空隔夜(指前一晚的空隔夜房，每早需做空房抹尘)
     */
    EMPTY_OVERNIGHT(1, HouseKeepStateEnum.USABLE, CheckInStateEnum.VACANT, OvernightRoomEnum.OVERNIGHT, "空隔夜"),

    /**
     * 空脏房
     */
    EMPTY_DIRTY_ROOM(2, HouseKeepStateEnum.USABLE, CheckInStateEnum.VACANT, CleanStateEnum.DIRTY, "空脏房"),

    /**
     * 在住脏换床单
     */
    LIVING_DIRTY_SHEET_ROOM(3, HouseKeepStateEnum.USABLE, CheckInStateEnum.CHECK_IN, CleanStateEnum.DIRTY, "在住脏换床单"),

    /**
     * 在住脏房
     */
    LIVING_DIRTY_ROOM(4, HouseKeepStateEnum.USABLE, CheckInStateEnum.CHECK_IN, CleanStateEnum.DIRTY, "在住脏房"),

    /**
     * 空清洁房
     */
    EMPTY_PURITY_ROOM(5, HouseKeepStateEnum.USABLE, CheckInStateEnum.VACANT, CleanStateEnum.CLEAN_UNCHECK, "空清洁房"),

    /**
     * 空干净房
     */
    EMPTY_CLEAN_ROOM(6, HouseKeepStateEnum.USABLE, CheckInStateEnum.VACANT, CleanStateEnum.CLEAN_CHECKED, "空干净房"),

    /**
     * 在住干净房
     */
    LIVING_CLEAN_ROOM(7, HouseKeepStateEnum.USABLE, CheckInStateEnum.CHECK_IN, CleanStateEnum.CLEAN_CHECKED, "在住干净房"),

    /**
     * 停售房
     */
    DISABLE_ROOM(8, HouseKeepStateEnum.DISABLE, "停售房"),

    /**
     * 临时房
     */
    TEMPORARY_ROOM(9, TemporaryStateEnum.TEMPORARY, "临时房");

    @Getter
    private int code;
    @Getter
    private HouseKeepStateEnum houseKeepStateEnum;
    @Getter
    private CheckInStateEnum checkInStateEnum;
    @Getter
    private CleanStateEnum cleanStateEnum;
    @Getter
    private OvernightRoomEnum overnightRoomEnum;
    @Getter
    private String name;
    @Getter
    private TemporaryStateEnum temporaryStateEnum;

    RoomStatus(int code, HouseKeepStateEnum houseKeepStateEnum, CheckInStateEnum checkInStateEnum,
               OvernightRoomEnum overnightRoomEnum, String name) {
        this.code = code;
        this.houseKeepStateEnum = houseKeepStateEnum;
        this.checkInStateEnum = checkInStateEnum;
        this.overnightRoomEnum = overnightRoomEnum;
        this.name = name;
    }

    RoomStatus(int code, HouseKeepStateEnum houseKeepStateEnum, CheckInStateEnum checkInStateEnum,
               CleanStateEnum cleanStateEnum, String name) {
        this.code = code;
        this.houseKeepStateEnum = houseKeepStateEnum;
        this.checkInStateEnum = checkInStateEnum;
        this.cleanStateEnum = cleanStateEnum;
        this.name = name;
    }

    RoomStatus(int code, HouseKeepStateEnum houseKeepStateEnum, String name) {
        this.code = code;
        this.houseKeepStateEnum = houseKeepStateEnum;
        this.name = name;
    }

    RoomStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    RoomStatus(int code, TemporaryStateEnum temporaryStateEnum, String name) {
        this.code = code;
        this.temporaryStateEnum = temporaryStateEnum;
        this.name = name;
    }
    /**
     * 根据房间状态获取对应的房态
     *
     * @param houseKeepState 房间的可用状态
     * @param checkInState   房间的可用状态
     * @param overnightRoom  房间是否为隔夜房
     * @param cleanState     房间清洁状态
     * @param arrival        入住日期
     * @return 对应的房态
     */
    public static Integer value(Integer houseKeepState, Integer checkInState, Integer overnightRoom, Integer cleanState,
                                Date arrival, Date depAccDate) {
        for (RoomStatus b : RoomStatus.values()) {

            if (Objects.isNull(b.getCheckInStateEnum()) && Objects.isNull(b.getCleanStateEnum()) && Objects.isNull(
                    b.getOvernightRoomEnum())) {
                if (Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState)) {
                    return b.getCode();
                }
            } else if (Objects.isNull(b.getCleanStateEnum())) {
                if (Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState) && Objects.equals(b.getCheckInStateEnum()
                        .getValue(), checkInState) && Objects.equals(b.getOvernightRoomEnum()
                        .getValue(), overnightRoom)) {
                    return b.getCode();
                }
            } else if (Objects.isNull(b.getOvernightRoomEnum())) {
                if (Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState) && Objects.equals(b.getCheckInStateEnum()
                        .getValue(), checkInState) && Objects.equals(b.getCleanStateEnum()
                        .getValue(), cleanState)) {
                    if (Objects.isNull(arrival)) {
                        if (Objects.equals(checkInState, CheckInStateEnum.CHECK_IN.getValue()) && Objects.equals(cleanState, CleanStateEnum.DIRTY.getValue())) {
                            return RoomStatus.LIVING_DIRTY_ROOM.getCode();
                        }
                        return b.getCode();
                    }

                    // 在住房，当前日期减入住日为偶数，且当前日不是离店日的在住房单，需要换床单
                    int days = CommonHelp.daysBetweenByDate(new Date(), arrival);
                    int departDays = CommonHelp.daysBetweenByDate(depAccDate, new Date());
                    if (days > 0 && days % 2 == 0 && departDays > 0) {
                        return RoomStatus.LIVING_DIRTY_SHEET_ROOM.getCode();
                    } else {
                        if (Objects.equals(checkInState, CheckInStateEnum.CHECK_IN.getValue()) && Objects.equals(cleanState, CleanStateEnum.DIRTY.getValue())) {
                            return RoomStatus.LIVING_DIRTY_ROOM.getCode();
                        }
                        return b.getCode();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据房间状态获取对应的房态
     *
     * @param houseKeepState 房间的可用状态
     * @param checkInState   房间的可用状态
     * @param overnightRoom  房间是否为隔夜房
     * @param cleanState     房间清洁状态
     * @param arrival        入住日期
     * @return 对应的房态
     */
    public static Integer value(Integer houseKeepState, Integer checkInState, Integer overnightRoom, Integer cleanState,Integer temporaryState,
                                Date arrival, Date depAccDate) {
        for (RoomStatus b : RoomStatus.values()) {
            if ( Objects.nonNull(temporaryState) && Objects.equals(TemporaryStateEnum.TEMPORARY.getCode(),temporaryState)) {
                return RoomStatus.TEMPORARY_ROOM.getCode();
            }
            if (Objects.isNull(b.getCheckInStateEnum()) && Objects.isNull(b.getCleanStateEnum()) && Objects.isNull(
                    b.getOvernightRoomEnum())) {

                if (Objects.nonNull(b.getHouseKeepStateEnum()) && Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState)) {
                    return b.getCode();
                }
            } else if (Objects.isNull(b.getCleanStateEnum())) {
                if (Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState) && Objects.equals(b.getCheckInStateEnum()
                        .getValue(), checkInState) && Objects.equals(b.getOvernightRoomEnum()
                        .getValue(), overnightRoom)) {
                    return b.getCode();
                }
            } else if (Objects.isNull(b.getOvernightRoomEnum())) {
                if (Objects.equals(b.getHouseKeepStateEnum()
                        .getValue(), houseKeepState) && Objects.equals(b.getCheckInStateEnum()
                        .getValue(), checkInState) && Objects.equals(b.getCleanStateEnum()
                        .getValue(), cleanState)) {
                    if (Objects.isNull(arrival)) {
                        if (Objects.equals(checkInState, CheckInStateEnum.CHECK_IN.getValue()) && Objects.equals(cleanState, CleanStateEnum.DIRTY.getValue())) {
                            return RoomStatus.LIVING_DIRTY_ROOM.getCode();
                        }
                        return b.getCode();
                    }

                    // 在住房，当前日期减入住日为偶数，且当前日不是离店日的在住房单，需要换床单
                    int days = CommonUtils.daysBetweenByDate(new Date(), arrival);
                    int departDays = CommonUtils.daysBetweenByDate(depAccDate, new Date());
                    if (Objects.equals(checkInState, CheckInStateEnum.CHECK_IN.getValue()) && Objects.equals(cleanState,
                            CleanStateEnum.DIRTY.getValue()) && days > 0 && days % 2 == 0 && departDays > 0) {
                        return RoomStatus.LIVING_DIRTY_SHEET_ROOM.getCode();
                    } else {
                        if (Objects.equals(checkInState, CheckInStateEnum.CHECK_IN.getValue()) && Objects.equals(
                                cleanState, CleanStateEnum.DIRTY.getValue())) {
                            return RoomStatus.LIVING_DIRTY_ROOM.getCode();
                        }
                        return b.getCode();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据房态获取对应的房间状态
     *
     * @param code 值
     * @return 房间状态
     */
    public static RoomStatus status(int code) {
        for (RoomStatus b : RoomStatus.values()) {
            if (Objects.equals(b.getCode(), code)) {
                return b;
            }
        }
        throw new BusinessException("this enum key not exist");
    }

    /**
     * 统一房态名称
     * @param code
     * @return
     */
    public static String getStatusName(int code) {

        String name = "";
        switch (RoomStatus.status(code)) {
            case DISABLE_ROOM:
                name = "停售";
                break;
            case EMPTY_OVERNIGHT:
                name = "空隔夜";
                break;
            case EMPTY_DIRTY_ROOM:
                name = "空脏";
                break;
            case LIVING_DIRTY_SHEET_ROOM:
                name = "换床单";
                break;
            case LIVING_DIRTY_ROOM:
                name = "在住脏";
                break;
            case EMPTY_PURITY_ROOM:
                name = "空清洁";
                break;
            case EMPTY_CLEAN_ROOM:
                name = "空干净";
                break;
            case LIVING_CLEAN_ROOM:
                name = "住干净";
                break;
        }

        return name;

    }

}
