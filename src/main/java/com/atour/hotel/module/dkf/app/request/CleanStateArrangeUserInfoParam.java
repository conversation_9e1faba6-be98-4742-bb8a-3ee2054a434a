package com.atour.hotel.module.dkf.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年04月20日21:35
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CleanStateArrangeUserInfoParam {

    /**
     * 酒店id
     */
    @Schema(description = "酒店id")
    @NotNull(message = "酒店id不能为空")
    private Integer chainId;

    /**
     * 房间号
     */
    @Schema(description = "房间号")
    @NotBlank(message = "房间号不能为空")
    private String roomNo;
}
