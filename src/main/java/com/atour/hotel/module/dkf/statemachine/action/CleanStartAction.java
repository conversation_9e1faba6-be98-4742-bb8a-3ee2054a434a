/*
 *  * Copyright (c) 2019 yaduo.com. All Rights Reserved.
 */

package com.atour.hotel.module.dkf.statemachine.action;

import com.atour.galaxy.api.param.RoomUpdateParam;
import com.atour.galaxy.api.remote.GalaxyRoomRemote;
import com.atour.hotel.common.enums.FlagEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.dkf.app.response.RoomCleanStartDTO;
import com.atour.hotel.module.dkf.enums.CleanActionEnum;
import com.atour.hotel.module.dkf.enums.CleanStatusEnum;
import com.atour.hotel.module.dkf.statemachine.Constants;
import com.atour.hotel.module.dkf.statemachine.context.RoomCleanContext;
import com.atour.hotel.module.dkf.statemachine.event.RoomEvent;
import com.atour.hotel.module.dkf.statemachine.states.RoomStates;
import com.atour.hotel.module.dkf.web.hotel.service.RoomDomain;
import com.atour.hotel.persistent.hotel.dao.CleanRoomDao;
import com.atour.hotel.persistent.hotel.entity.CleanRoomEntity;
import com.atour.hotel.persistent.hotel.entity.RoomArrangementEntity;
import com.atour.utils.Safes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 开始打扫对应的action
 *
 * <AUTHOR>
 * @date 2019-03-29
 */
@Slf4j
@Service
public class CleanStartAction implements Action<RoomStates, RoomEvent> {

    /**
     * RPC: 房间相关
     */
    @Resource
    private GalaxyRoomRemote galaxyRoomRemote;

    /**
     * 数据库层: 打扫表
     */
    @Resource
    private CleanRoomDao cleanRoomDao;

    /**
     * 业务层: 扩展数据
     */
    @Resource
    private ParamService paramService;

    /**
     * 业务层: 房间业务模型
     */
    @Autowired
    private RoomDomain roomDomain;


    /**
     * 开始打扫
     *
     * @param context 上下文信息
     */
    @Override
    public void execute(StateContext<RoomStates, RoomEvent> context) {
        final RoomCleanContext cleanContext = (RoomCleanContext)context.getMessageHeader(Constants.CLEAN_CONTEXT);
        final RoomCleanStartDTO cleanStartDTO =
            (RoomCleanStartDTO)context.getMessageHeader(Constants.CLEAN_CONTEXT_RESULT_START);

        cleanStartDTO.setChainId(cleanContext.getChainId());
        cleanStartDTO.setRoomNo(cleanContext.getRoomNo());
        cleanStartDTO.setInitRoomState(cleanContext.getRoomState());

        log.info("【{}】酒店的【{}】房间开始打扫了--->状态由【{}】变更为 【{}】打扫人ID为【{}】", cleanContext.getChainId(), cleanContext.getRoomNo(),
            CleanStatusEnum.NOT_CLEAN.getName(), CleanStatusEnum.CLEAN.getName(), cleanContext.getUserId());

        try {

            // 更新房间状态
            boolean updateRoomStatus = updateRoomStatus(cleanContext);

            // 如果更新不成功,直接抛出
            if (!updateRoomStatus) {
                throw new BusinessException("当前房间正在被打扫,操作冲突,请刷新页面稍后再试");
            }

            // 记录房间打扫表
            insertCleanRoom(cleanContext, cleanStartDTO);
        } catch (BusinessException e) {
            log.error("【{}】酒店的【{}】房间开始打扫出现了业务异常 【{}】", cleanContext.getChainId(), cleanContext.getRoomNo(),
                e.getMessage(), e);
            cleanContext.setSuccess(Boolean.FALSE);
            cleanContext.setDesc(e.getMessage());
        } catch (Exception e) {
            log.error("【{}】酒店的【{}】房间开始打扫出现了未知异常", cleanContext.getChainId(), cleanContext.getRoomNo(), e);
            cleanContext.setSuccess(Boolean.FALSE);
            cleanContext.setDesc("房间开始打扫时出现未知异常，请联系技术人员...");
        }
    }

    private void insertCleanRoom(RoomCleanContext cleanContext, RoomCleanStartDTO cleanStartDTO) {

        // 查询排房信息
        RoomArrangementEntity arrangement =
            roomDomain.getRoomArrangementByRoomNo(cleanContext.getChainId(), cleanContext.getRoomNo());

        // 获取营业日
        Date currentAccDate = paramService.getCurrentAccDate(cleanContext.getChainId());

        CleanRoomEntity cleanRoomEntity = new CleanRoomEntity();
        cleanRoomEntity.setChainId(cleanContext.getChainId());
        cleanRoomEntity.setRoomNo(cleanContext.getRoomNo());
        cleanRoomEntity.setCleanRoomNo(
            roomDomain.getBizNoByChainIdAndRoomNo(cleanContext.getChainId(), cleanContext.getRoomNo()));
        cleanRoomEntity.setRoomArrangementNo(Safes.of(arrangement, new RoomArrangementEntity())
            .getRoomArrangementNo());

        cleanRoomEntity.setSweepPeopleId(String.valueOf(CommonParamsManager.getCurrUserId()));
        cleanRoomEntity.setPieceRoomState(cleanContext.getRoomState());
        cleanRoomEntity.setCleanStatus(CleanActionEnum.CLEAN_START.getCode());
        cleanRoomEntity.setStartTime(new Date());
        cleanRoomEntity.setAccDate(currentAccDate);
        cleanRoomEntity.setFlag(FlagEnum.INVALID.getValue());
        cleanRoomDao.insertSelective(cleanRoomEntity);

        cleanStartDTO.setCleanNo(cleanRoomEntity.getCleanRoomNo());
        cleanStartDTO.setCleanStatus(CleanStatusEnum.CLEAN.getCode());
        cleanStartDTO.setCleanStartDate(cleanRoomEntity.getStartTime());
    }

    private boolean updateRoomStatus(RoomCleanContext cleanContext) {
        Boolean aBoolean = galaxyRoomRemote.updateRoomWhenNotCleanStatus(RoomUpdateParam.builder()
            .chainId(cleanContext.getChainId())
            .roomNo(cleanContext.getRoomNo())
            .cleanStatus(CleanStatusEnum.CLEAN.getCode())
            .build());
        if (Objects.isNull(aBoolean)) {
            return false;
        }
        return aBoolean;
    }
}

