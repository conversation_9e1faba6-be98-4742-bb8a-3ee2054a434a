package com.atour.hotel.module.dkf.app.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 房态信息返回对象
 */
@ApiModel(value = "房态信息返回对象")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DkfRoomStatusDTO implements Serializable {

    /**
     * 房间状态
     */
    @ApiModelProperty(value = "房间状态")
    List<DkfRoomStatusMainDTO> roomStatusMainDTOS;

    /**
     * 房间状态数量
     */
    @ApiModelProperty(value = "房间状态数量")
    List<DkfRoomQueryPoolMeDTO> dkfRoomQueryPoolMeDTOS;

    /**
     * 房间总数量
     */
    @ApiModelProperty(value = "房间总数量")
    Integer roomCount;
}
