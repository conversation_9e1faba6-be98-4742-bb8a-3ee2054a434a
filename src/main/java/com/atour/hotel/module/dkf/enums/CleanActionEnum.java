package com.atour.hotel.module.dkf.enums;

import lombok.Getter;

/**
 * 枚举类: 打扫动作状态
 *
 * <AUTHOR>
 * @date 2019-04-15
 */
public enum CleanActionEnum {

    /**
     * 开始打扫
     */
    CLEAN_START(1, "开始打扫"),

    /**
     * 结束打扫
     */
    CLEAN_END(2, "结束打扫"),

    /**
     * 取消打扫
     */
    CLEAN_CANCEL(3, "取消打扫");

    @Getter
    private int code;
    @Getter
    private String name;

    CleanActionEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
