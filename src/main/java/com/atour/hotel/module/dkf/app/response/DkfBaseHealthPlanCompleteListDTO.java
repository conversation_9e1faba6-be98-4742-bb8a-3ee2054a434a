package com.atour.hotel.module.dkf.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 朵客房-计划卫生单项完成情况统计
 * @date 2024年12月17日11:48
 * @since JDK1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DkfBaseHealthPlanCompleteListDTO implements Serializable {
    private static final long serialVersionUID = -5248630382357691396L;
    /**
     * 计划卫生名称
     */
    @Schema(description = "计划卫生名称")
    private String healthPlanName;

    /**
     * 应完成数量
     */
    @Schema(description = "应完成数量")
    private Integer shouldCompleteNum;
    /**
     * 已完成数量
     */
    @Schema(description = "已完成数量")
    private Integer completeNum;

    /**
     * 计划卫生完成率
     */
    @Schema(description = "计划卫生完成率")
    private BigDecimal doneRate;

    /**
     * 计划卫生id
     */
    @Schema(description = "计划卫生id")
    private Integer healthPlanId;

}
