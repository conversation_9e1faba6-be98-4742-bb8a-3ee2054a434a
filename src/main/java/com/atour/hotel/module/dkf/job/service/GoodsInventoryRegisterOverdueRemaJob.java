package com.atour.hotel.module.dkf.job.service;

import com.atour.hotel.module.dkf.app.service.GoodsRegisterInventoryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 月登记审批单过期任务
 */
@Slf4j
@Component
@JobHandler(value = "goodsInventoryRegisterOverdueRemaJob")
public class GoodsInventoryRegisterOverdueRemaJob extends IJobHandler {

    @Resource
    private GoodsRegisterInventoryService goodsRegisterInventoryService;

    @Override
    public ReturnT<String> execute(String param) {
        goodsRegisterInventoryService.messageReminder();
        return ReturnT.SUCCESS;
    }




}
