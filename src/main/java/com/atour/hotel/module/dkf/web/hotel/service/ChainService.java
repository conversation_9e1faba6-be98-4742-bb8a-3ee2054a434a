package com.atour.hotel.module.dkf.web.hotel.service;

import com.atour.api.bean.ApiResult;
import com.atour.chain.api.chain.dto.AtourChainDTO;
import com.atour.chain.api.chain.dto.ChainDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.db.redis.annotation.Cacheable;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.web.hotel.response.PmsRoomStatusMainDTO;
import com.atour.hotel.module.dkf.web.hotel.response.RoomStatusListResultDTO;
import com.atour.pms.api.dto.room.RoomStatusMainDTO;
import com.atour.pms.api.dto.room.RoomStatusResultDTO;
import com.atour.pms.api.remote.PmsRoomRemote;
import com.atour.rbac.api.param.GetRegionBChainIdRequest;
import com.atour.rbac.api.remote.RbacRegionCityChainRemote;
import com.atour.rbac.api.response.RegionChainDto;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yaduo.chainbiz.service.api.dto.ChainInfoPageResponse;
import com.yaduo.chainbiz.service.api.dto.ChainQueryParam;
import com.yaduo.chainbiz.service.api.dto.EsChainDTO;
import com.yaduo.chainbiz.service.api.remote.EsChainRemote;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.logging.util.RPCContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChainService {

    @Resource
    private PmsRoomRemote pmsRoomRemote;
    @Resource
    private ChainRemote chainRemote;
    @Resource
    private RbacRegionCityChainRemote rbacRegionCityChainRemote;

    @Resource
    private EsChainRemote esChainRemote;
    /**
     * 根据酒店ID获取房间状态根据酒店ID获取房间状态
     *
     * @param chainId
     * @return
     */
    public RoomStatusListResultDTO getRoomStatusList(Integer chainId) {
        RoomStatusResultDTO roomStatusResultDTO = pmsRoomRemote.getRoomStatusList(chainId);
        return RoomStatusListResultDTO.builder()
                .adAllDebit(roomStatusResultDTO.getAdAllDebit())
                .adDepFolio(roomStatusResultDTO.getAdDepFolio())
                .adFolioCount(roomStatusResultDTO.getAdFolioCount())
                .roomStatusMain(this.convertToPmsRoomStatusDTO(roomStatusResultDTO.getRoomStatusMainDTOList()))
                .hasTemporary(roomStatusResultDTO.isHasTemporary())
                .build();
    }

    /**
     * 根据酒店id查询区域信息，缓存12h
     *
     * @param chainId 酒店id
     * @return 返回区域信息
     */
    @Cacheable(expires = 3600 * 12)
    public Optional<RegionChainDto> getChainAreaByChainId(Integer chainId) {
        ApiResult<List<RegionChainDto>> listApiResult = rbacRegionCityChainRemote.getRegionByChainId(GetRegionBChainIdRequest.builder().chainId(Lists.newArrayList(chainId)).isAll(true).build());
        if (listApiResult.getCode() == ApiResult.DEFAULT_SUCCEED_CODE) {
            if (CollectionUtils.isEmpty(listApiResult.getResult())) {
                log.warn("query chainArea by chainId:{} result is empty", chainId);
                return Optional.empty();
            }
            return Optional.of(listApiResult.getResult().iterator().next());
        }
        return Optional.empty();
    }

    /**
     * 根据酒店id查询区域信息
     *
     * @param chainIdList 酒店id
     * @return 返回区域信息
     */
    public Optional<List<RegionChainDto>> getChainAreaByChainIdList(List<Integer> chainIdList) {
        ApiResult<List<RegionChainDto>> listApiResult = rbacRegionCityChainRemote.getRegionByChainId(GetRegionBChainIdRequest.builder().chainId(Lists.newArrayList(chainIdList)).isAll(true).build());
        if (listApiResult.getCode() == ApiResult.DEFAULT_SUCCEED_CODE) {
            if (CollectionUtils.isEmpty(listApiResult.getResult())) {
                log.warn("query chainArea by chainId:{} result is empty", chainIdList);
                return Optional.empty();
            }
            return Optional.of(listApiResult.getResult());
        }
        return Optional.empty();
    }

    private List<PmsRoomStatusMainDTO> convertToPmsRoomStatusDTO(List<RoomStatusMainDTO> roomStatusMainDTOList) {
        return roomStatusMainDTOList.stream().map(dto -> convertPmsRoomStatusMainDTO(dto)).collect(Collectors.toList());
    }

    private static PmsRoomStatusMainDTO convertPmsRoomStatusMainDTO(RoomStatusMainDTO roomStatusMainDTO) {
        PmsRoomStatusMainDTO pmsRoomStatusMainDTO = new PmsRoomStatusMainDTO();
        pmsRoomStatusMainDTO.setBalance(roomStatusMainDTO.getBalance());
        pmsRoomStatusMainDTO.setArrAccDate(roomStatusMainDTO.getArrAccDate());
        pmsRoomStatusMainDTO.setArrDate(roomStatusMainDTO.getArrDate());
        pmsRoomStatusMainDTO.setArrangeFolioId(roomStatusMainDTO.getArrangeFolioId());
        pmsRoomStatusMainDTO.setCheckInState(roomStatusMainDTO.getCheckInState());
        pmsRoomStatusMainDTO.setCheckRoomFlag(roomStatusMainDTO.getCheckRoomFlag());
        pmsRoomStatusMainDTO.setChgtoId(roomStatusMainDTO.getChgtoId());
        pmsRoomStatusMainDTO.setCleanState(roomStatusMainDTO.getCleanState());
        pmsRoomStatusMainDTO.setDepAccDate(roomStatusMainDTO.getDepAccDate());
        pmsRoomStatusMainDTO.setDepart(roomStatusMainDTO.getDepart());
        pmsRoomStatusMainDTO.setFloor(roomStatusMainDTO.getFloor());
        pmsRoomStatusMainDTO.setFolioId(roomStatusMainDTO.getFolioId());
        pmsRoomStatusMainDTO.setGustName(roomStatusMainDTO.getGustName());
        pmsRoomStatusMainDTO.setHouseKeepFolioId(roomStatusMainDTO.getHouseKeepFolioId());
        pmsRoomStatusMainDTO.setHouseKeepState(roomStatusMainDTO.getHouseKeepState());
        pmsRoomStatusMainDTO.setIsArrival(roomStatusMainDTO.getIsArrival());
        pmsRoomStatusMainDTO.setIsBookInRoom(roomStatusMainDTO.getIsBookInRoom());
        pmsRoomStatusMainDTO.setIsDayRoom(roomStatusMainDTO.getIsDayRoom());
        pmsRoomStatusMainDTO.setIsDepart(roomStatusMainDTO.getIsDepart());
        pmsRoomStatusMainDTO.setIsFreeRoom(roomStatusMainDTO.getIsFreeRoom());
        pmsRoomStatusMainDTO.setIsInnerRoom(roomStatusMainDTO.getIsInnerRoom());
        pmsRoomStatusMainDTO.setIsReserve(roomStatusMainDTO.getIsReserve());
        pmsRoomStatusMainDTO.setIsTimeRoom(roomStatusMainDTO.getIsTimeRoom());
        pmsRoomStatusMainDTO.setMebTypeId(roomStatusMainDTO.getMebTypeId());
        pmsRoomStatusMainDTO.setRoomNo(roomStatusMainDTO.getRoomNo());
        pmsRoomStatusMainDTO.setRoomTypeCode(roomStatusMainDTO.getRoomTypeCode());
        pmsRoomStatusMainDTO.setRoomTypeId(roomStatusMainDTO.getRoomTypeId());
        pmsRoomStatusMainDTO.setRoomTypeName(roomStatusMainDTO.getRoomTypeName());
        pmsRoomStatusMainDTO.setSex(roomStatusMainDTO.getSex());
        pmsRoomStatusMainDTO.setSurreptitious(roomStatusMainDTO.getSurreptitious());
        pmsRoomStatusMainDTO.setVipTypeId(roomStatusMainDTO.getVipTypeId());
        pmsRoomStatusMainDTO.setIsOverNight(roomStatusMainDTO.getIsOverNight());
        pmsRoomStatusMainDTO.setIsChangeSheet(roomStatusMainDTO.getIsChangeSheet());
        pmsRoomStatusMainDTO.setArrangeRoomType(roomStatusMainDTO.getArrangeRoomType());
        pmsRoomStatusMainDTO.setTemporaryState(roomStatusMainDTO.getTemporaryState());
        pmsRoomStatusMainDTO.setIsDoNotDisturbTheRoom(roomStatusMainDTO.getIsDoNotDisturbTheRoom());
        return pmsRoomStatusMainDTO;
    }

    /**
     * 获取酒店信息
     *
     * @param chainId
     * @return
     */
    public ChainDTO getChainByChainId(Integer chainId) {
        ApiResult<ChainDTO> result = chainRemote.getPmsChainById(chainId);
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return null;
        }
        return result.getResult();
    }

    /**
     * 批量获取酒店信息
     *
     * @param chainIdList
     * @return
     */
    public List<ChainDTO> getChainByChainIdList(List<Integer> chainIdList) {
        ApiResult<List<ChainDTO>> result = chainRemote.getPmsChainByIdList(Sets.newHashSet(chainIdList));
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return null;
        }
        return result.getResult();
    }

    /**
     * 获取酒店名称
     *
     * @param chainId
     * @return
     */
    public String getChainNameByChainId(Integer chainId) {
        ChainDTO chain = this.getChainByChainId(chainId);
        if (Objects.nonNull(chain)) {
            return chain.getChainName();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据酒店id列表获取酒店名称
     *
     * @param chainIds
     * @return
     */
    public Map<Integer, String> getChainNamesByChainIds(Set<Integer> chainIds) {
        return this.getChainInfoByChainIds(chainIds)
                .stream()
                .collect(Collectors.toMap(AtourChainDTO::getChainId, AtourChainDTO::getName, (k1, k2) -> k1));

    }

    /**
     * 获取酒店信息
     *
     * @param
     * @return
     */
    public List<ChainDTO> getAllPmsChain() {
        ApiResult<List<ChainDTO>> allPmsChain = chainRemote.getAllPmsChain();
        if (Objects.nonNull(allPmsChain) && allPmsChain.getCode() == ApiResult.DEFAULT_SUCCEED_CODE) {
            return allPmsChain.getResult();
        }
        return Lists.newArrayList();
    }


    /**
     * 封装根据酒店id获取详情方法
     *
     * @param chainIds
     * @return
     */
    public List<AtourChainDTO> getChainInfoByChainIds(Set<Integer> chainIds) {
        ApiResult<List<AtourChainDTO>> chainByIdList = chainRemote.getChainByIdList(chainIds);
        if (Objects.isNull(chainByIdList) || !Objects.equals(chainByIdList.getCode(), ApiResult.DEFAULT_SUCCEED_CODE)
                || Objects.isNull(chainByIdList.getResult())) {
            return Lists.newArrayList();
        }
        return chainByIdList.getResult();
    }

    public List<AtourChainDTO> getChainByIdList(Set<Integer> chainIds) {
        ApiResult<List<AtourChainDTO>> result = chainRemote.getChainByIdList(chainIds);
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return null;
        }
        return result.getResult();
    }

    /**
     * 开业的pms酒店
     *
     * @return
     */
    public List<ChainDTO> getAllOpenedPmsChain() {
        ApiResult<List<ChainDTO>> result = chainRemote.getAllOpenedPmsChain();
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return Collections.emptyList();
        }
        return result.getResult();
    }

    /**
     * @param chainIds
     * @return
     */
    public List<EsChainDTO> queryChainDtoByEsRemote(List<Integer> chainIds) {
        chainIds = Safes.of(chainIds).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chainIds)) {
            return Collections.emptyList();
        }
        ChainQueryParam chainQueryParam = new ChainQueryParam();
        chainQueryParam.setChainIdList(chainIds);
        chainQueryParam.setPageSize(chainIds.size() + 1);
        AtourResponse<ChainInfoPageResponse> atourResponse = esChainRemote.queryPmsChainPageWithHLMA(RPCContext.createRequest(chainQueryParam));
        if(Objects.isNull(atourResponse) || !atourResponse.isSuccess()){
            throw new BusinessException("调用chain-biz queryPmsChainPageWithHLMA接口获取酒店信息失败");
        }
        return atourResponse.getData().getList();
    }

    /**
     * 获取酒店的ES信息
     * @param chainId
     * @return
     */
    public EsChainDTO queryChainEsInfo(Integer chainId) {
        List<EsChainDTO> esChainDTOS = queryChainDtoByEsRemote(Lists.newArrayList(chainId));
        return CollectionUtils.isNotEmpty(esChainDTOS) ? esChainDTOS.get(0) : null;
    }
}
