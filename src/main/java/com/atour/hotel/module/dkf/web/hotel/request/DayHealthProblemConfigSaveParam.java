package com.atour.hotel.module.dkf.web.hotel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * WEB层: 日常卫生问题配置保存参数
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@ApiModel(value = "DayHealthProblemConfigSaveParam", description = "日常卫生问题配置保存参数")
@Data
public class DayHealthProblemConfigSaveParam implements Serializable {

    private static final long serialVersionUID = 4878852262577068631L;

    /**
     * 日常卫生问题类型(A:A类 B:B类)
     */
    @ApiModelProperty(value = "日常卫生问题类型(A:A类 B:B类)")
    @NotNull(message = "日常卫生问题类型不能为空")
    private String problemType;

    /**
     * 日常卫生问题名称
     */
    @ApiModelProperty(value = "日常卫生问题名称")
    @NotBlank(message = "日常卫生问题名称不能为空")
    private String healthProblemName;
}
