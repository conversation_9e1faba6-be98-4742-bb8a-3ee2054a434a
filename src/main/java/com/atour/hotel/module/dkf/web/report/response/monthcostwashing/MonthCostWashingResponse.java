package com.atour.hotel.module.dkf.web.report.response.monthcostwashing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/11/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonthCostWashingResponse {
    /**
     * 区域
     */
    private String area;
    private String chainName;
    private Integer chainId;
    private String month;
    /**
     * 初始
     */
    private MonthCostWashingEntryTypeResponse init;
    /**
     * 补录
     */
    private MonthCostWashingEntryTypeResponse supplement;
}
