package com.atour.hotel.module.dkf.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年06月17日17:30
 * @since JDK1.8
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoomArrangmentDTO implements Serializable {
    private static final long serialVersionUID = 2109482875352961280L;

    @Schema(description = "楼层信息")
    private Integer floor;

    @Schema(description = "房间信息")
    private List<RoomArrangmentListDTO> roomArrangmentList;
}
