package com.atour.hotel.module.dkf.web.report.service;

import com.atour.api.bean.PageInfo;
import com.atour.api.bean.PageResult;
import com.atour.dicts.db.common.DeletedEnum;
import com.atour.dicts.enums.inventory.ConsumerGoodsRegisterTypeEnum;
import com.atour.hotel.framework.apllo.conf.BrokenMorningApolloConf;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.app.service.GoodsRegisterInventoryService;
import com.atour.hotel.module.dkf.web.hotel.baseconfig.service.GuestSupplyService;
import com.atour.hotel.module.dkf.web.report.request.CustomerGoodsConsumeReportParams;
import com.atour.hotel.persistent.hotel.dao.ConsumerGoodsConsumptionDao;
import com.atour.hotel.persistent.hotel.entity.ConsumerGoodsConsumptionEntity;
import com.atour.hotel.persistent.ops.dao.GuestSuppliesConfigDao;
import com.atour.hotel.persistent.ops.entity.GuestSuppliesConfigEntity;
import com.atour.hotel.persistent.pms.dao.ChainBaseConfigDao;
import com.atour.hotel.persistent.pms.entity.ChainBaseConfigEntity;
import com.atour.hotel.persistent.roommanage.dao.ChainBaseConfigNewDao;
import com.atour.hotel.persistent.roommanage.entity.ChainBaseConfigNewEntity;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/12
 * 客用品消耗报表处理层
 */
@Service
public class CustomerGoodsConsumeReportService {

    @Resource
    private ConsumerGoodsConsumptionDao consumerGoodsConsumptionDao;
    @Resource
    private ChainBaseConfigDao chainBaseConfigDao;

    @Resource
    private ChainBaseConfigNewDao chainBaseConfigNewDao;
    @Resource
    private GuestSuppliesConfigDao guestSuppliesConfigDao;
    @Resource
    private CommService commService;

    @Resource
    private BrokenMorningApolloConf brokenMorningApolloConf;
    @Resource
    private GoodsRegisterInventoryService goodsRegisterInventoryService;


    @Resource
    private GuestSupplyService guestSupplyService;




    public PageResult<List<String>> customerGoodsConsumeReport(CustomerGoodsConsumeReportParams params) {
        Date beginDate = DateUtil.parseDate(params.getBeginDate());
        Date endDate = DateUtil.parseDate(params.getEndDate());
        // 一年后的日期
        final Date addYearDate = DateUtil.addYears(beginDate, 1);
        if (DateUtil.dateEarlier(addYearDate, endDate)) {
            throw new BusinessException("日期查询区间不能超过一年");
        }

        PageResult<List<String>> pageResult = new PageResult<>();
        PageInfo pageInfo = new PageInfo(params.getPageNum(), params.getPageSize());
        pageResult.setPage(pageInfo);
        pageInfo.setTotalCount(0);

        //新客用品体系灰度酒店
        Boolean goodsConsumePreChainId = brokenMorningApolloConf.isPre(params.getChainId());


        ConsumerGoodsConsumptionEntity goodsConsumptionEntity = ConsumerGoodsConsumptionEntity.builder()
                .chainId(params.getChainId())
                .registerUserId(params.getUserId())
                .beginTime(beginDate)
                .endTime(endDate)
                .registerType(ConsumerGoodsRegisterTypeEnum.REGISTER.getType())
                .build();


        List<ConsumerGoodsConsumptionEntity> consumerGoodsConsumptionEntityList =null;
         if(!goodsConsumePreChainId){
             //首先按照分页查找出来基本数据
             //查询数据
              consumerGoodsConsumptionEntityList = consumerGoodsConsumptionDao.queryByParam(goodsConsumptionEntity);
         }else{
             consumerGoodsConsumptionEntityList =goodsRegisterInventoryService.customerGoodsConsumeReport(goodsConsumptionEntity);
         }

        List<Integer> consumerGoodsSupplyIdList = Safes.of(consumerGoodsConsumptionEntityList).stream().distinct().map(ConsumerGoodsConsumptionEntity::getSupplyId).collect(Collectors.toList());

        // 查询数据并按照有效无效排序
        List<GuestSuppliesConfigEntity> guestSuppliesConfigEntityList = this.selectSortByState( params.getChainId(),consumerGoodsSupplyIdList,  goodsConsumePreChainId);

        //需要展示的数据id
        List<Integer> idList = Safes.of(guestSuppliesConfigEntityList).stream().map(GuestSuppliesConfigEntity::getId).collect(Collectors.toList());
        //放置表头
        List<String> titleList = Lists.newArrayList("门店","姓名","使用日期");
        titleList.addAll(guestSuppliesConfigEntityList.stream().map(GuestSuppliesConfigEntity::getName).collect(Collectors.toList()));

       //按照人，SupplyId，营业日统计数量
        Map<String, Integer> consumerCountMap = new HashMap<>();
        Map<String, List<ConsumerGoodsConsumptionEntity>> consumerMap = Safes.of(consumerGoodsConsumptionEntityList).stream().collect(Collectors.groupingBy(s -> getConsumerKey(s)));
        consumerMap.forEach((k,v) ->
            consumerCountMap.put(k, v.stream().mapToInt(ConsumerGoodsConsumptionEntity::getUseCount).sum()));

        //按照SupplyId 分组统计
        Map<Integer, Integer> countMap = new HashMap<>();
        Map<Integer, List<ConsumerGoodsConsumptionEntity>> countGroupMap = Safes.of(consumerGoodsConsumptionEntityList).stream().collect(Collectors.groupingBy(ConsumerGoodsConsumptionEntity::getSupplyId));
        countGroupMap.forEach((k,v) ->
                countMap.put(k, v.stream().mapToInt(ConsumerGoodsConsumptionEntity::getUseCount).sum()));


        Map<String, List<String>> dateListMap = Maps.newHashMap();
        Set<Integer> sysUserIds = Safes.of(consumerGoodsConsumptionEntityList).stream().map(entity ->NumberUtils.toInt(entity.getRegisterUserId())).collect(Collectors.toSet());
        Map<Integer, String> userNameMap = commService.getUserList(Lists.newArrayList(sysUserIds));
        userNameMap.put(0,"自动出库");
        String chainName = commService.getChainName(params.getChainId());

        List<List<String>> mapDateList = Lists.newArrayList();

         for (Map.Entry<String, Integer> stringIntegerEntry : consumerCountMap.entrySet()) {
            String s =   stringIntegerEntry.getKey();
            String[] info = s.split(",");
            String userId = info[0];
            Integer supplyId = Integer.valueOf(info[1]);
            String accDate = info[2];
            //每日每人只展示一条数据
            String listKey = userId + StringUtils.EMPTY + accDate;
            if (!dateListMap.containsKey(listKey)) {
                String userName = userNameMap.getOrDefault(NumberUtils.toInt(userId), StringUtils.EMPTY);
                List<String> dateList = Lists.newArrayList(chainName,userName,accDate);
                dateListMap.put(listKey, dateList);
                mapDateList.add(dateList);
            }

            List<String> dateList = dateListMap.get(listKey);
            for (int i = 0; i < idList.size(); i++) {
                Integer id = idList.get(i);
                if (id.equals(supplyId)) {
                    addInfo(dateList,i+3,stringIntegerEntry.getValue() + StringUtils.EMPTY);
                    //i+3的原因是因为数据是从第三列开始动态拼接的，所以从4开始动态拼接
                } else if (dateList.size() <= (i + 3) || (dateList.size() > (i + 3) && dateList.get(i + 3) == null)) {
                    addInfo(dateList,i+3,StringUtils.EMPTY+ BigDecimal.ZERO.intValue());
                }
            }

        }

        //最终展示数据
        List<List<String>> mapList = Lists.newArrayList();
        //设置表头
        mapList.add(titleList);
        int start = (params.getPageNum() - 1) * params.getPageSize();
        int end = start + params.getPageSize();
        if (end > mapDateList.size()) {
            mapList.addAll(mapDateList.subList(start, mapDateList.size()));
        } else {
            mapList.addAll(mapDateList.subList(start, end));
        }
        //设置表尾
        List<String> countList  =Lists.newArrayList("合计",StringUtils.EMPTY,StringUtils.EMPTY);
        countList.addAll(idList.stream().map(id-> Objects.nonNull(countMap.get(id))?countMap.get(id)+"":"0").collect(Collectors.toList()));
        mapList.add(countList);
        pageResult.setData(mapList);
        return pageResult;
    }


    private void addInfo(List<String> dateList,int index,String value){
        if (dateList.size() <= index) {
            dateList.add(index, value);
        } else {
            dateList.set(index, value);
        }
    }
    private String getConsumerKey(ConsumerGoodsConsumptionEntity consumerGoodsConsumptionEntity){
        String accDate = DateUtil.formatDate(consumerGoodsConsumptionEntity.getAccDate(), DateUtil.DATE_FORMAT);
        return  consumerGoodsConsumptionEntity.getRegisterUserId() + "," + consumerGoodsConsumptionEntity.getSupplyId()
                        + "," + accDate;
    }

    private List<GuestSuppliesConfigEntity> selectSortByState(int chainid,List<Integer> consumerGoodsSupplyIdList, Boolean goodsConsumePreChainId) {

        List<GuestSuppliesConfigEntity> result = new ArrayList<>();

        if(goodsConsumePreChainId){
            List<GuestSuppliesConfigEntity> configNewEntityList = guestSupplyService.queryAllGuestSuppliesConfig(null);
            configNewEntityList = Safes.of(configNewEntityList).stream().filter(config -> config.getDeleteFlag().equals(DeletedEnum.UNDELETED.getCode()) ||
                    consumerGoodsSupplyIdList.contains(config.getId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(configNewEntityList)) {
                return  Lists.newArrayList();
            }

            //获取已配置的客用品数据
            List<ChainBaseConfigNewEntity> chainBaseConfigNewEntityList = chainBaseConfigNewDao.selectList(new LambdaQueryWrapper<ChainBaseConfigNewEntity>().eq(ChainBaseConfigNewEntity::getChainId, chainid)
                    .eq(ChainBaseConfigNewEntity::getDeleteFlag, DeletedEnum.UNDELETED.getCode()));

            // 转map
            Map<Long, Integer> chainBaseConfigMap = chainBaseConfigNewEntityList.stream()
                    .collect(Collectors.toMap(ChainBaseConfigNewEntity::getSupplyId, ChainBaseConfigNewEntity::getState,
                            (key1, key2) -> key2));
            // 有效
            List<GuestSuppliesConfigEntity> validList = configNewEntityList.stream()
                    .filter(c -> chainBaseConfigMap.containsKey(Long.valueOf(c.getId())) && Objects.equals(
                            chainBaseConfigMap.get(Long.valueOf(c.getId())), 0))
                    .collect(Collectors.toList());

            // 无效
            List<GuestSuppliesConfigEntity> invalidList = configNewEntityList.stream()
                    .filter(c -> chainBaseConfigMap.containsKey(Long.valueOf(c.getId())) && Objects.equals(
                            chainBaseConfigMap.get(Long.valueOf(c.getId())), 1))
                    .collect(Collectors.toList());

            result.addAll(validList);
            result.addAll(invalidList);

        }else{
            //客用品名称map
            List<GuestSuppliesConfigEntity> guestSuppliesConfigEntities = guestSuppliesConfigDao.getAllValidSupply(null);

            guestSuppliesConfigEntities = Safes.of(guestSuppliesConfigEntities).stream().filter(config -> config.getDeleteFlag().equals(DeletedEnum.UNDELETED.getCode()) ||
                    consumerGoodsSupplyIdList.contains(config.getId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(guestSuppliesConfigEntities)) {
                return Safes.of(guestSuppliesConfigEntities);
            }


            // 酒店配置信息
            List<ChainBaseConfigEntity> chainBaseConfigList = chainBaseConfigDao.queryChainBaseConfigByChainid(chainid);

            // 转map
            Map<Integer, Integer> chainBaseConfigMap = chainBaseConfigList.stream()
                    .collect(Collectors.toMap(ChainBaseConfigEntity::getSupplyId, ChainBaseConfigEntity::getState,
                            (key1, key2) -> key2));

            // 有效
            List<GuestSuppliesConfigEntity> validList = guestSuppliesConfigEntities.stream()
                    .filter(c -> chainBaseConfigMap.containsKey(c.getId()) && Objects.equals(
                            chainBaseConfigMap.get(c.getId()), 0))
                    .collect(Collectors.toList());

            // 无效
            List<GuestSuppliesConfigEntity> invalidList = guestSuppliesConfigEntities.stream()
                    .filter(c -> chainBaseConfigMap.containsKey(c.getId()) && Objects.equals(
                            chainBaseConfigMap.get(c.getId()), 1))
                    .collect(Collectors.toList());

            result.addAll(validList);
            result.addAll(invalidList);
        }

        return result;
    }
}
