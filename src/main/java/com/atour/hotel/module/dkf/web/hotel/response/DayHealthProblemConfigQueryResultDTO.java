package com.atour.hotel.module.dkf.web.hotel.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * WEB层: 日常卫生问题配置响应参数
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@ApiModel(value = "DayHealthProblemConfigQueryResultDTO", description = "日常卫生问题配置响应参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DayHealthProblemConfigQueryResultDTO implements Serializable {

    private static final long serialVersionUID = -1681327326903474805L;

    /**
     * Id
     */
    @ApiModelProperty(value = "Id")
    private Integer id;

    /**
     * 日常卫生问题名称
     */
    @ApiModelProperty(value = "日常卫生问题名称")
    private String healthProblemName;

    /**
     * 问题类型(A:A类 B:B类)
     */
    @ApiModelProperty(value = "问题类型(A:A类 B:B类)")
    private String problemType;
}
