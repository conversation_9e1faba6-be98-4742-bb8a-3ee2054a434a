package com.atour.hotel.module.dkf.web.group.baseconfig.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/9/18
 * 获取酒店布草列表
 */
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class GetGroupClothListParam {
    /**
     * ID
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
}
