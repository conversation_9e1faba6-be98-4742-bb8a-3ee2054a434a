package com.atour.hotel.module.dkf.web.report.response.monthcostwashing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonthCostWashingEntryTypeResponse {
    private Double soldNight;
    /**
     * 洗涤费用（元）
     * 去税
     */
    private BigDecimal washingRate;
    /**
     * 税率
     */
    private String rate;
    /**
     * 费率单位
     */
    private String rateUnit;
    /**
     * 单间夜成本（元）
     * 去税
     */
    private BigDecimal singleNightCost;
    /**
     * 录入类型
     */
    private String entryType;

    private List<MonthCostWashingCountResponse> guestList;

    /**
     * 发票集合
     */
    private List<MonthCostWashingInvoiceResponse> invoiceList;
}
