package com.atour.hotel.module.dkf.web.log;

import com.atour.api.bean.PageInfo;
import com.atour.api.bean.ResponseFactory;
import com.atour.api.bean.pmsapi.PmsApiPageResponse;
import com.atour.hotel.persistent.roommanage.dao.BaseConfigLogMapper;
import com.atour.hotel.persistent.roommanage.entity.BaseConfigLogEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/20
 * 基础配置逻辑服务层
 */
@Service
public class BaseConfigLogService {

    /**
     * 日志内容最大长度
     */
    private static final Integer MAX_REMARK = 1000;
    @Resource
    private BaseConfigLogMapper baseConfigLogMapper;

    /**
     * 新增日志
     * @param entity
     */
    public void addLog(BaseConfigLogEntity entity) {
        if (Objects.nonNull(entity.getRemark()) && entity.getRemark().length() > MAX_REMARK) {
            entity.setRemark(entity.getRemark().substring(0, entity.getRemark().length() - MAX_REMARK));
        }
        baseConfigLogMapper.insertSelective(entity);
    }

    /**
     * 批量新增日志
     * @param entities
     */
    public void batchAddLog(List<BaseConfigLogEntity> entities) {
        entities.forEach(entity -> {
            if (Objects.nonNull(entity.getRemark()) && entity.getRemark().length() > MAX_REMARK) {
                entity.setRemark(entity.getRemark().substring(0, entity.getRemark().length() - MAX_REMARK));
            }
        });
        baseConfigLogMapper.batchInsert(entities);
    }

    /**
     * 根据日志类型获取日志列表
     * @param logType 日志类型
     * @return
     */
    public List<BaseConfigLogDto> selectLogByType(BaseConfigLogEntity.LogType logType) {
        List<BaseConfigLogEntity> entityList = baseConfigLogMapper.selectBySelective(BaseConfigLogEntity.builder().logType(logType.getType()).build());
        return Optional.ofNullable(entityList).orElse(Collections.emptyList())
            .stream()
            .map(BaseConfigLogDto.Builder::build)
            .collect(Collectors.toList());
    }

    /**
     * 根据酒店ID和日志类型获取日志列表
     * @param logType 日志类型
     * @return
     */
    public PmsApiPageResponse<BaseConfigLogDto> selectLogByChainIdAndType(BaseConfigLogParam param, BaseConfigLogEntity.LogType logType) {
        BaseConfigLogEntity record = BaseConfigLogEntity.builder().chainId(param.getChainId()).logType(logType.getType()).build();
        Integer count = baseConfigLogMapper.selectCountBySelective(record);
        PageInfo pageInfo = new PageInfo(Objects.isNull(param.getPageNo()) ? 1:param.getPageNo(),
            Objects.isNull(param.getPageSize()) ? 20:param.getPageSize(), count = Objects.isNull(count) ? 0:count);
        if (count < 1 || count < (pageInfo.getPageNo() - 1) * pageInfo.getPageSize()) {
            return ResponseFactory.buildPmsApiPageResponse(Collections.emptyList(), pageInfo);
        }
        int beginNo = (pageInfo.getPageNo() - 1) * pageInfo.getPageSize();
        int size = pageInfo.getPageSize();
        List<BaseConfigLogEntity> entityList = baseConfigLogMapper.selectPageBySelective(record, beginNo, size);
        List<BaseConfigLogDto> result = Optional.ofNullable(entityList).orElse(Collections.emptyList())
            .stream()
            .map(BaseConfigLogDto.Builder::build)
            .collect(Collectors.toList());
        return ResponseFactory.buildPmsApiPageResponse(result, pageInfo);
    }
}
