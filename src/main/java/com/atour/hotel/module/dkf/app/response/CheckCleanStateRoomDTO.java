package com.atour.hotel.module.dkf.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 已查房列表返回数据对象
 * @date 2024年04月20日19:49
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckCleanStateRoomDTO implements Serializable {
    private static final long serialVersionUID = 2850752107744051932L;
    /**
     * 酒店id
     */
    @Schema(description = "酒店id")
    private Integer chainId;
    /**
     * 房间号
     */
    @Schema(description = "房间号")
    private String roomNo;

    /**
     * 房型
     */
    @Schema(description = "房型")
    private String roomTypeName;

    /**
     * 检查日期
     */
    @Schema(description = "检查日期")
    private String checkRoomDate;

    /**
     * 检查人
     */
    @Schema(description = "检查人")
    private String checkUserName;

    /**
     * 查房id
     */
    @Schema(description = "查房id")
    private Long checkRoomId;

    /**
     * 查房状态 0.待检查 1.未通过 2.通过
     */
    @Schema(description = "查房状态")
    private Integer checkRoomStatus;

    /**
     * 查房状态名称
     */
    @Schema(description = "查房状态名称")
    private String checkRoomStatusName;

    /**
     * 房态
     */
    @Schema(description = "房态(1:空隔夜 2:空脏房 3:在住脏换床单 4:在住脏房 5:空清洁房 6:空干净房 7:在住干净房 8:停售房)")
    private Integer roomState;
}
