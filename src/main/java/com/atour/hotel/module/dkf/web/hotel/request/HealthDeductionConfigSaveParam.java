package com.atour.hotel.module.dkf.web.hotel.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * WEB层: 日常卫生问题扣分项配置保存参数
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HealthDeductionConfigSaveParam implements Serializable {

    private static final long serialVersionUID = 3793616071109001379L;
    /**
     * 扣分项名称
     */
    @NotBlank(message = "扣分项名称不能为空")
    private String deductionName;

    /**
     * 扣分
     */
    @NotNull(message = "扣分分数不能为空")
    @Min(value = 1, message = "分值不能小于1")
    private BigDecimal deductPoints;
}
