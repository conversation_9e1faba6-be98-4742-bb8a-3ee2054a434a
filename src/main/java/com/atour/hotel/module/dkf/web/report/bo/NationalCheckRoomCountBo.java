package com.atour.hotel.module.dkf.web.report.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2019/9/17
 * 全国查房，sql按营业日分组的实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NationalCheckRoomCountBo {
    /**
     * 酒店id
     */
    private Integer chainId;
    private LocalDate accDate;
    private Integer count;
}
