package com.atour.hotel.module.dkf.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年04月23日15:49
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckCleanRoomRecordCondition {

    /**
     * 开始日期
     */
    private String beginDate;
    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 打扫人id
     */
    private String cleanUserId;

    /**
     * 酒店id
     */
    private Integer chainId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 检查人
     */
    private String checkUserName;

    /**
     * 批量房间号
     */
    private List<String> roomNos;

    /**
     * 检查状态 0.待检查 1.未通过 2.通过
     */
    private List<Integer> checkRoomStatusList;

    /**
     * 排序字段
     */
    private String orderByField;

    /**
     * 打扫人姓名
     */
    private String cleanUserName;
}
