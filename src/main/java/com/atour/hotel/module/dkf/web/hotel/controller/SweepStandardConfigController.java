package com.atour.hotel.module.dkf.web.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.dkf.web.hotel.request.SweepStandardConfigRequest;
import com.atour.hotel.module.dkf.web.hotel.response.SweepStandardConfigDTO;
import com.atour.hotel.module.dkf.web.hotel.service.SweepStandardConfigService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 做房九字诀配置控制层
 * @date 2025年04月21日20:57
 * @since JDK1.8
 */
@RestController
@RequestMapping(value = "/api/web/dkf/sweepStandardConfig", produces = {"application/json;charset=UTF-8"})
public class SweepStandardConfigController {

    @Resource
    private SweepStandardConfigService standardConfigService;

    /**
     * 做房九字诀规则配置
     * @param configRequest
     * @return
     */
    @PostMapping(value = "/addOrUpdateSweepStandardConfig")
    public ApiResult<Boolean> addOrUpdateSweepStandardConfig(@RequestBody @Valid SweepStandardConfigRequest configRequest){
        return ApiResult.success(standardConfigService.addOrUpdateSweepStandardConfig(configRequest));
    }

    /**
     * 查询做房九字诀规则配置
     * @param
     * @return
     */
    @PostMapping(value = "/getSweepStandardConfig")
    public ApiResult<List<SweepStandardConfigDTO>> getSweepStandardConfig(){
        return ApiResult.success(standardConfigService.getSweepStandardConfig());
    }
}
