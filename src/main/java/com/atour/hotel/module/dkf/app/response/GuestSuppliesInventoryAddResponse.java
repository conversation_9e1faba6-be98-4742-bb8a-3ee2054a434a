package com.atour.hotel.module.dkf.app.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客用品新增客月度盘点跳转响应参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GuestSuppliesInventoryAddResponse {

	/**
	 * 客用品ID
	 */
	private Long guestSuppliesId;
	/**
	 * 客用品名称
	 */
	private String guestSuppliesName;
	/**
	 * 数量
	 */
	private Integer guestSuppliesQuantity;

	/**
	 * 客用品分类：0:客用品，1:客用水
	 * @see com.atour.hotel.module.dkf.enums.SuppliesTypeEnum
	 */
	private Integer suppliesType;

	/**
	 * 商品类型 1-门店自采，2-总部商品
	 * @see com.atour.hotel.module.dkf.enums.SuppliesSourceEnum
	 */
	private Integer suppliesSource;
}
