package com.atour.hotel.module.dkf.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 大清计件请求入参
 * @date 2024年08月21日12:27
 * @since JDK1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CleanRoomPieceRecordParam implements Serializable {
    private static final long serialVersionUID = 2387423380734410812L;

    @Schema(description = "酒店id")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 开始日期 yyyy-mm-dd
     */
    @Schema(description = "开始日期")
    @NotBlank(message = "开始日期不能为空")
    private String beginDate;

    /**
     * 结束日期 yyyy-mm-dd
     */
    @Schema(description = "结束日期")
    @NotBlank(message = "结束日期不能为空")
    private String endDate;
}
