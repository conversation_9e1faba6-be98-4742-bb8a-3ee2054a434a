package com.atour.hotel.module.dkf.web.hotel.service;

import com.atour.api.bean.ApiResult;
import com.atour.db.redis.RedisLock;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.lock.LockKeyFactory;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.configuration.NewBusinessCodeConfBean;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.dkf.app.request.AddWorkConfirmParam;
import com.atour.hotel.module.dkf.app.request.AddWorkConfirmReq;
import com.atour.hotel.module.dkf.enums.WorkTypeEnum;
import com.atour.hotel.module.dkf.web.hotel.response.CopyListResponse;
import com.atour.hotel.module.dkf.web.hotel.response.ListForArrangementResponse;
import com.atour.hotel.module.dkf.web.hotel.response.RoomArrangeListForCopyResponse;
import com.atour.hotel.module.dkf.web.hotel.response.SysUserSimpleResponse;
import com.atour.hotel.module.dkf.web.hotel.response.UserListResponse;
import com.atour.hotel.module.dkf.web.hotel.response.UserSimpleInfoAndRoomResponse;
import com.atour.hotel.module.dkf.web.hotel.response.WorkConfirmCheckResponse;
import com.atour.hotel.module.dkf.web.hotel.response.WorkListForCopyResponse;
import com.atour.hotel.module.dkf.web.hotel.response.WorkListForRoomResponse;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.hotel.persistent.hotel.dao.RoomArrangementDao;
import com.atour.hotel.persistent.hotel.dao.WorkConfirmDao;
import com.atour.hotel.persistent.hotel.entity.RoomArrangementEntity;
import com.atour.hotel.persistent.hotel.entity.WorkConfirmEntity;
import com.atour.rbac.api.param.QueryJobByEmployeeIds;
import com.atour.rbac.api.remote.RbacJobRemote;
import com.atour.rbac.api.response.RbacJobDTO;
import com.atour.rbac.api.response.UserDTO;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 酒店分库业务逻辑类：排班业务
 *
 * <AUTHOR>
 * @date 2017年6月8日 下午4:46:33
 */
@Slf4j
@Service
public class WorkConfirmService {


    @Resource
    private ParamService paramService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private WorkConfirmDao workConfirmDao;

    @Resource
    private RoomArrangementService roomArrangementService;

    @Resource(name = "rbacJobRemote")
    private RbacJobRemote rbacJobRemote;

    @Resource
    private RedisLock redisLock;

    @Resource
    private CommentHelper commentHelper;

    @ApolloJsonValue("${roomArr.user.roleIds:[]}")
    private volatile List<Integer> roomArrUserRoleIds;

    @Resource
    private NewBusinessCodeConfBean newBusinessCodeConfBean;

    @Resource
    private RoomArrangementDao roomArrangementDao;

    @ApolloJsonValue("${dkf.work.job.id.sort:{}}")
    private volatile Map<String, Integer> dkfWorkJobIdSortMap;

    /**
     * 当班确认 - 查询
     *
     * @param chainId 酒店id
     */
    public UserListResponse getUserList(Integer chainId) {
        //首先查询出跟这家酒店有关系的人
        List<SysUserEntity> sysUserEntityList = sysUserService.getUserList(chainId);

        Date currentAccDate = paramService.getCurrentAccDate(chainId);
        List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmList(chainId, currentAccDate);

        List<RoomArrangementEntity> roomArrangementEntityList =
            roomArrangementService.getRoomArrangementList(chainId, DateUtil.printDate(currentAccDate));
        List<Integer> userIds = Safes.of(roomArrangementEntityList)
            .stream()
            .map(RoomArrangementEntity::getUserId)
            .collect(Collectors.toList());

        Map<Integer, String> workTypeMap = new HashMap<>(workConfirmEntityList.size());

        for (WorkConfirmEntity workConfirmEntity : workConfirmEntityList) {
            if (!workTypeMap.containsKey(workConfirmEntity.getUserId())) {
                workTypeMap.put(workConfirmEntity.getUserId(), "");
            }
            String workTypeStr = workTypeMap.get(workConfirmEntity.getUserId());
            workTypeStr += workConfirmEntity.getWorkType();
            workTypeStr += ",";
            workTypeMap.put(workConfirmEntity.getUserId(), workTypeStr);
        }
        Set<String> collect = sysUserEntityList.stream()
            .map(SysUserEntity::getHrId)
            .collect(Collectors.toSet());
        QueryJobByEmployeeIds queryJobByEmployeeIds = new QueryJobByEmployeeIds();
        queryJobByEmployeeIds.setEmployeeIds(collect);
        ApiResult<List<RbacJobDTO>> jobByEmployeeIds = rbacJobRemote.getJobByEmployeeIds(queryJobByEmployeeIds);
        Map<String, String> userJobMap = Safes.of(jobByEmployeeIds.getResult())
            .stream()
            .collect(Collectors.toMap(RbacJobDTO::getEmployeeId, RbacJobDTO::getName));

        //增加开关
        Map<String, UserDTO> userMap = new HashMap<>();
        if (newBusinessCodeConfBean.chainHasNewBusiness(chainId, SystemContants.ROOM_ARR_CONFIG)) {
            //获取岗位信息
            Set<String> employeeIdCollect = sysUserEntityList.stream()
                    .filter(s->StringUtils.isNotBlank(s.getHrId()))
                    .map(SysUserEntity::getHrId)
                    .collect(Collectors.toSet());
            List<UserDTO> userDTOS = commentHelper.getRbacUserDetail(employeeIdCollect);
            if (CollectionUtils.isEmpty(userDTOS)){
                return null;
            }
            userMap = userDTOS.stream().filter(user->StringUtils.isNotBlank(user.getEmployeeId()))
                    .collect(Collectors.toMap(UserDTO::getEmployeeId, each -> each, (value1, value2) -> value1));
        }

        //匹配角色名称
        List<SysUserSimpleResponse> waiterList = new ArrayList<>();
        List<SysUserSimpleResponse> otherList = new ArrayList<>();
        for (SysUserEntity sysUserEntity : sysUserEntityList) {
            SysUserSimpleResponse sysUserSimpleResponse = new SysUserSimpleResponse();
            sysUserSimpleResponse.setUserName(sysUserEntity.getUserName());
            sysUserSimpleResponse.setUserId(sysUserEntity.getUserID());
            sysUserSimpleResponse.setRoleName(userJobMap.get(sysUserEntity.getHrId()));
            String workTypeStr = workTypeMap.get(sysUserEntity.getUserID());
            if (StringUtils.isNotBlank(workTypeStr)) {
                workTypeStr = workTypeStr.substring(0, workTypeStr.length() - 1);
            }
            sysUserSimpleResponse.setWorkType(workTypeStr);
            sysUserSimpleResponse.setChainId(chainId);
            sysUserSimpleResponse.setRoomArrangeOfToday(userIds.contains(sysUserEntity.getUserID()));
            //如果是客房服务员，则放到客房服务员列表里
            if (newBusinessCodeConfBean.chainHasNewBusiness(chainId, SystemContants.ROOM_ARR_CONFIG) && MapUtils.isNotEmpty(userMap)) {
                UserDTO userDTO = userMap.get(sysUserEntity.getHrId());
                //根据排班伙伴的岗位信息进行岗位排序
                if (Objects.nonNull(userDTO) && StringUtils.isNotBlank(userDTO.getJobId())){
                    if (MapUtils.isNotEmpty(dkfWorkJobIdSortMap) && dkfWorkJobIdSortMap.containsKey(userDTO.getJobId())){
                        sysUserSimpleResponse.setSort(dkfWorkJobIdSortMap.getOrDefault(userDTO.getJobId(), 99));
                    }
                }

                if (Objects.nonNull(userDTO) && CollectionUtils.isNotEmpty(userDTO.getRoleIds())){
                    List<Integer> existRoleIds = userDTO.getRoleIds().stream().filter(roomArrUserRoleIds::contains).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(existRoleIds)){
                        waiterList.add(sysUserSimpleResponse);
                    }
                    else {
                        //如果是其他角色，则放到其他列表
                        otherList.add(sysUserSimpleResponse);
                    }
                }else {
                    //如果是其他角色，则放到其他列表
                    otherList.add(sysUserSimpleResponse);
                }
            }else {
                if (FileConfig.attendantRoleId.equals(sysUserEntity.getRuleRefUserID())) {
                    waiterList.add(sysUserSimpleResponse);
                } else {
                    //如果是其他角色，则放到其他列表
                    otherList.add(sysUserSimpleResponse);
                }
            }
        }
        //排序
        waiterList = waiterList.stream().sorted(Comparator.comparing(SysUserSimpleResponse::getSort)).collect(Collectors.toList());
        otherList = otherList.stream().sorted(Comparator.comparing(SysUserSimpleResponse::getSort)).collect(Collectors.toList());
//        Collections.sort(waiterList);
//        Collections.sort(otherList);
        UserListResponse userListResponse = new UserListResponse();
        userListResponse.setWaiterList(waiterList);
        userListResponse.setOtherList(otherList);
        return userListResponse;
    }

    /**
     * 复制历史当班
     */
    public CopyListResponse getCopyList(Integer chainId, String accDate) {
        if (Objects.isNull(chainId)){
            throw new BusinessException("酒店id不能为空");
        }

        if (StringUtils.isBlank(accDate)){
            throw new BusinessException("日期不能为空");
        }

        CopyListResponse copyListResponse = new CopyListResponse();

        if (Objects.isNull(chainId)) {
            copyListResponse.setRoomArrangeListForCopyResponseList(Lists.newArrayList());
            copyListResponse.setWorkListForCopyResponseList(Lists.newArrayList());
            return copyListResponse;
        }

        List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmList(chainId, DateUtil.parseDate(accDate));
        Map<Integer, Set<String>> workTypeMap = new HashMap<>(3);

        for (WorkConfirmEntity workConfirmEntity : workConfirmEntityList) {
            if (!workTypeMap.containsKey(workConfirmEntity.getWorkType())) {
                Set<String> userList = new HashSet<>();
                workTypeMap.put(workConfirmEntity.getWorkType(), userList);
            }
            String userName = sysUserService.getSysUserNameByUserId(workConfirmEntity.getUserId());
            workTypeMap.get(workConfirmEntity.getWorkType()).add(userName);

        }
        List<WorkListForCopyResponse> workListForCopyResponseList = new ArrayList<>(workTypeMap.keySet().size());
        for (Integer integer : workTypeMap.keySet()) {
            WorkListForCopyResponse workListForCopyResponse = new WorkListForCopyResponse();
            workListForCopyResponse.setWorkTypeName(WorkTypeEnum.desc(integer).getDesc());
            List<String> userNameList = new ArrayList<>(workTypeMap.get(integer));
            Collections.sort(userNameList);
            workListForCopyResponse.setUserNameList(userNameList);
            workListForCopyResponseList.add(workListForCopyResponse);
        }
        copyListResponse.setWorkListForCopyResponseList(workListForCopyResponseList);
        List<RoomArrangementEntity> roomArrangementEntityList =
            roomArrangementService.getRoomArrangementList(chainId, accDate);

        Map<String, Set<Integer>> roomMap = new HashMap<>();
        for (RoomArrangementEntity roomArrangementEntity : roomArrangementEntityList) {
            String userName = sysUserService.getSysUserNameByUserId(roomArrangementEntity.getUserId());
            if (!roomMap.containsKey(userName)) {
                Set<Integer> roomNo = new HashSet<>();
                roomMap.put(userName, roomNo);
            }
            roomMap.get(userName).add(Integer.valueOf(roomArrangementEntity.getRoomNo()));
        }

        List<RoomArrangeListForCopyResponse> roomArrangeListForCopyResponseList = new ArrayList<>();
        for (String key : roomMap.keySet()) {
            RoomArrangeListForCopyResponse roomArrangeListForCopyResponse = new RoomArrangeListForCopyResponse();
            roomArrangeListForCopyResponse.setUserName(key);
            List<Integer> roomNoList = new ArrayList<>(roomMap.get(key));
            Collections.sort(roomNoList);
            roomArrangeListForCopyResponse.setRoomNoList(roomNoList);
            roomArrangeListForCopyResponseList.add(roomArrangeListForCopyResponse);
        }
        Collections.sort(roomArrangeListForCopyResponseList);
        copyListResponse.setRoomArrangeListForCopyResponseList(roomArrangeListForCopyResponseList);
        return copyListResponse;
    }

    /**
     * 当班确认 - 排班
     */
    public void workConfirm(List<WorkConfirmEntity> workConfirmEntityList) {
        if (CollectionUtils.isEmpty(workConfirmEntityList)) {
            throw new BusinessException("数据为空，不能排班");
        }
        Integer chainId = workConfirmEntityList.get(0)
            .getChainId();
        Date currentAccDate = paramService.getCurrentAccDate(chainId);

        String lockKey = LockKeyFactory.getWorkConfirmKey(chainId);
        RLock lock = redisLock.getLock(lockKey);
        if (Objects.isNull(lock) || !lock.tryLock()) {
            log.warn("workConfirm lock FAIL, chainId: {}, lockKey: {}", chainId, lockKey);
            throw new BusinessException("操作冲突，请稍后重试");
        }
        try {
            //首次删除所有的排班
            WorkConfirmEntity param = new WorkConfirmEntity();
            param.setAccDate(currentAccDate);
            param.setChainId(chainId);
            log.info("workConfirmMapper_deleteByParam:{}", param);
            workConfirmDao.deleteByParam(param);

            workConfirmEntityList.removeIf(workConfirmEntity -> Objects.isNull(workConfirmEntity.getWorkType()));
            for (WorkConfirmEntity workConfirmEntity : workConfirmEntityList) {
                workConfirmEntity.setAccDate(currentAccDate);
                if (workConfirmEntity.getWorkType() != null && workConfirmEntity.getWorkType() > 3) {
                    throw new BusinessException("参数错误");
                }
            }

            // 复制数据中的员工 需要和 今日当班确认表中的员工,进行匹配
            UserListResponse userListResponse = getUserList(chainId);
            if (Objects.isNull(userListResponse)) {
                throw new BusinessException("复制排班排房失败,没有查询到今日当班确认员工数据");
            }

            List<SysUserSimpleResponse> otherList = Safes.of(userListResponse.getOtherList());
            List<SysUserSimpleResponse> waiterList = Safes.of(userListResponse.getWaiterList());
            List<Integer> userIds = Lists.newArrayListWithCapacity(otherList.size() + waiterList.size());
            userIds.addAll(otherList.stream()
                .map(SysUserSimpleResponse::getUserId)
                .collect(Collectors.toList()));
            userIds.addAll(waiterList.stream()
                .map(SysUserSimpleResponse::getUserId)
                .collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(userIds)) {
                throw new BusinessException("复制排班排房失败,没有查询到今日当班确认员工数据");
            }

            List<WorkConfirmEntity> collect = workConfirmEntityList.stream()
                .filter(workConfirmEntity -> userIds.contains(workConfirmEntity.getUserId()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return;
            }
            log.info("workConfirmMapper_insert:{}", collect);
            collect.forEach(workConfirmDao::insert);
        } finally {
            lock.unlock();
            log.info("workConfirm unlock success, chainId: {}, lockKey: {}", chainId, lockKey);
        }
    }

    /**
     * 复制到今天
     *
     * @param chainId 酒店ID
     * @param accDate 营业日
     */
    public void copyWorkConfirmAndRoonArrangement(Integer chainId, String accDate) {
        if (Objects.isNull(chainId) || Objects.isNull(accDate)) {
            throw new BusinessException("参数错误，酒店 或者 日期不能为空");
        }
        //排班锁
        String lockKeyWork = LockKeyFactory.getWorkConfirmKey(chainId);
        //排房锁
        String lockKeyRoom = LockKeyFactory.getRoomArrangemenLockKey(chainId);
        RLock lockWork = redisLock.getLock(lockKeyWork);
        RLock lockRoom = redisLock.getLock(lockKeyRoom);
        if (Objects.isNull(lockWork) || Objects.isNull(lockRoom) || !lockWork.tryLock()) {
            log.error("lockWork FAIL, chainId: {}, lockWork: {}, lockRoom={}", chainId, lockWork, lockRoom);
            throw new BusinessException("操作冲突，请稍后重试");
        }
        try {
            if (!lockRoom.tryLock()) {
                log.error("lockRoom FAIL, chainId: {}, lockWork: {}, lockRoom={}", chainId, lockWork, lockRoom);
                throw new BusinessException("操作冲突，请稍后重试");
            }
        } catch (Exception e) {
            //未拿到排房锁，则释放排班锁
            lockWork.unlock();
            log.error("unlock lockWork success, chainId: {}, lockWork: {}, lockRoom={}", chainId, lockWork, lockKeyRoom,
                e);
            throw new BusinessException(e.getMessage());
        }

        try {

            List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmList(chainId, DateUtil.parseDate(accDate));

            List<RoomArrangementEntity> roomArrangementEntityList =
                roomArrangementService.getRoomArrangementList(chainId, accDate);
            //复制排班
            workConfirmEntityList.removeIf(workConfirmEntity -> workConfirmEntity.getWorkType() == null);
            workConfirm(workConfirmEntityList);
            //复制排房
            if (CollectionUtils.isNotEmpty(roomArrangementEntityList)) {
                roomArrangementEntityList.removeIf(roomArrangementEntity -> roomArrangementEntity.getRoomNo() == null);
                roomArrangementService.roomArrangement(roomArrangementEntityList, chainId,
                    roomArrangementEntityList.get(0)
                        .getOperatorId(), Boolean.FALSE);
            }
        } finally {
            lockRoom.unlock();
            lockWork.unlock();
            log.info("copyWorkConfirmAndRoonArrangement unlock success, chainId: {}, lockWork: {}, lockRoom={}",
                chainId, lockWork, lockKeyRoom);
        }
    }

    /**
     *
     */
    public ListForArrangementResponse getWorkUserList(Integer chainId) {
        ListForArrangementResponse listForArrangementResponse = new ListForArrangementResponse();
        Date currentAccDate = paramService.getCurrentAccDate(chainId);
        List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmList(chainId, currentAccDate);

        Map<Integer, Set<UserSimpleInfoAndRoomResponse>> workTypeMap = new HashMap<>(3);
        List<RoomArrangementEntity> roomArrangementEntityList =
            roomArrangementService.getRoomArrangementList(chainId, DateUtil.printDate(currentAccDate));
        Map<Integer, Set<Integer>> roomMap = new HashMap<>();
        for (RoomArrangementEntity roomArrangementEntity : roomArrangementEntityList) {
            if (!roomMap.containsKey(roomArrangementEntity.getUserId())) {
                Set<Integer> roomNo = new HashSet<>();
                roomMap.put(roomArrangementEntity.getUserId(), roomNo);
            }
            roomMap.get(roomArrangementEntity.getUserId())
                .add(Integer.valueOf(roomArrangementEntity.getRoomNo()));
        }

        for (WorkConfirmEntity workConfirmEntity : workConfirmEntityList) {
            if (!workTypeMap.containsKey(workConfirmEntity.getWorkType())) {
                Set<UserSimpleInfoAndRoomResponse> userList = new HashSet<>();
                workTypeMap.put(workConfirmEntity.getWorkType(), userList);
            }
            String userName = sysUserService.getSysUserNameByUserId(workConfirmEntity.getUserId());
            UserSimpleInfoAndRoomResponse userSimpleInfoResponse = new UserSimpleInfoAndRoomResponse();
            userSimpleInfoResponse.setUserId(workConfirmEntity.getUserId());
            userSimpleInfoResponse.setUserName(userName);
            if (roomMap.containsKey(workConfirmEntity.getUserId())) {
                userSimpleInfoResponse.setRoomList(Safes.of(new ArrayList<>(roomMap.get(workConfirmEntity.getUserId()))));
            }
            workTypeMap.get(workConfirmEntity.getWorkType())
                .add(userSimpleInfoResponse);

        }
        List<WorkListForRoomResponse> workListForCopyResponseList = new ArrayList<>(3);
        for (Integer integer : workTypeMap.keySet()) {
            WorkListForRoomResponse workListForCopyResponse = new WorkListForRoomResponse();
            workListForCopyResponse.setWorkTypeName(WorkTypeEnum.desc(integer)
                .getDesc());
            workListForCopyResponse.setUserList(new ArrayList<>(workTypeMap.get(integer)));
            workListForCopyResponseList.add(workListForCopyResponse);
        }
        listForArrangementResponse.setWorkListForCopyResponseList(workListForCopyResponseList);
        return listForArrangementResponse;
    }

    /**
     * 校验是否排班
     *
     * @param userId 用户id
     * @return 排班列表
     */
    public List<WorkConfirmEntity> checkWork(Integer userId, int chainId, Date accDate) {
        WorkConfirmEntity workConfirmEntity = new WorkConfirmEntity();
        workConfirmEntity.setChainId(chainId);
        workConfirmEntity.setUserId(userId);
        workConfirmEntity.setAccDate(accDate);
        return workConfirmDao.select(workConfirmEntity);
    }

    /**
     * 获取排班列表
     *
     * @param chainId 酒店id
     * @param accDate 营业日
     * @return 排班列表
     */
    public List<WorkConfirmEntity> getWorkConfirmList(int chainId, Date accDate) {
        WorkConfirmEntity workConfirmEntity = new WorkConfirmEntity();
        workConfirmEntity.setChainId(chainId);
        workConfirmEntity.setAccDate(accDate);
        return workConfirmDao.select(workConfirmEntity);
    }

    /**
     * 新增排班校验
     * @param addWorkConfirmReq
     * @return
     */
    public WorkConfirmCheckResponse addWorkConfirmCheck(AddWorkConfirmReq addWorkConfirmReq){
        WorkConfirmCheckResponse workConfirmCheckResponse = new WorkConfirmCheckResponse();
        List<AddWorkConfirmParam> workConfirmParams = addWorkConfirmReq.getAddWorkConfirmParams();
        Integer chainId = workConfirmParams.get(0).getChainId();
        Date currentAccDate = paramService.getCurrentAccDate(chainId);
        //首先查询出跟这家酒店有关系的人
        List<SysUserEntity> sysUserEntityList = sysUserService.getUserList(chainId);
        Map<Integer, String> userMap = Safes.of(sysUserEntityList)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SysUserEntity::getUserID, SysUserEntity::getUserName));
        Set<Integer> currentUserIdSet = workConfirmParams.stream().map(AddWorkConfirmParam::getUserId).collect(Collectors.toSet());
        //1.先查出当前所有已排班的排班信息
        List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmList(chainId, currentAccDate);
        List<RoomArrangementEntity> arrangementEntityList = null;
        StringBuilder sbl = new StringBuilder();
        List<Integer> userIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workConfirmEntityList)){
            Set<Integer>  hasUserIdSet = workConfirmEntityList.stream().map(WorkConfirmEntity::getUserId).collect(Collectors.toSet());
            //已排班的人员跟新增排班的人员进行取差集
            List<Integer> reduceUserIdList = currentUserIdSet.stream().filter(item -> !hasUserIdSet.contains(item)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(reduceUserIdList)){
                arrangementEntityList = roomArrangementDao.selectByChainIdAndAccDateAndUserId(chainId, currentAccDate, reduceUserIdList);
            }
            if (CollectionUtils.isEmpty(reduceUserIdList) || CollectionUtils.isEmpty(arrangementEntityList)){
                reduceUserIdList = hasUserIdSet.stream().filter(item -> !currentUserIdSet.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reduceUserIdList)){
                    arrangementEntityList = roomArrangementDao.selectByChainIdAndAccDateAndUserId(chainId, currentAccDate, reduceUserIdList);
                }
            }
        }else {
            arrangementEntityList = roomArrangementDao.selectByChainIdAndAccDateAndUserId(chainId, currentAccDate, Lists.newArrayList(currentUserIdSet));
        }
        if (CollectionUtils.isNotEmpty(arrangementEntityList)) {
            Set<Integer> userIdSet = arrangementEntityList.stream().map(RoomArrangementEntity::getUserId).collect(Collectors.toSet());
            userIdSet.stream().forEach(a -> {
                String name = userMap.get(a);
                if (StringUtils.isNotBlank(name)) {
                    if (Objects.nonNull(sbl) && StringUtils.isNotBlank(sbl)) {
                        sbl.append(",");
                    }
                    sbl.append("[");
                    sbl.append(name);
                    sbl.append("]");
                    userIdList.add(a);
                }
            });
            if (Objects.nonNull(sbl)) {
                String failMsg = String.format(ResponseCodeEnum.DKF_HAS_ARR_ROOM_ERROR.getMessage(), sbl);
                workConfirmCheckResponse.setHasMsg(1);
                workConfirmCheckResponse.setHasArrRoomTostMsg(failMsg);
                workConfirmCheckResponse.setUserIdList(userIdList);
            }
        }
        return workConfirmCheckResponse;
    }

    /**
     * 查询当前排房人是否存在排班
     * @param chainId
     * @param userId
     */
    public void checkWorkConfirmForUserId(Integer chainId,Integer userId){
        Date currentAccDate = paramService.getCurrentAccDate(chainId);
        List<WorkConfirmEntity> workConfirmEntityList = getWorkConfirmListByUserId(chainId,currentAccDate,userId);
        if (CollectionUtils.isEmpty(workConfirmEntityList)){
            throw new BusinessException(ResponseCodeEnum.DKF_NO_HAS_WORK_ERROR.getMessage());
        }
    }

    /**
     * 获取排班列表
     *
     * @param chainId 酒店id
     * @param accDate 营业日
     * @return 排班列表
     */
    public List<WorkConfirmEntity> getWorkConfirmListByUserId(int chainId, Date accDate,Integer userId) {
        WorkConfirmEntity workConfirmEntity = new WorkConfirmEntity();
        workConfirmEntity.setChainId(chainId);
        workConfirmEntity.setAccDate(accDate);
        workConfirmEntity.setUserId(userId);
        return workConfirmDao.select(workConfirmEntity);
    }
}
