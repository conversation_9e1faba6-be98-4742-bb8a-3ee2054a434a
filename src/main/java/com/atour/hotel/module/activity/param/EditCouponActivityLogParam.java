package com.atour.hotel.module.activity.param;

import com.atour.user.api.market.dto.ActivityDetailDTO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/6/17
 */
@Getter
@ToString
@EqualsAndHashCode
@Builder
public class EditCouponActivityLogParam {

    /**
     * 更新参数
     */
    private EditCouponActivityParam editParam;

    /**
     * 活动数据
     */
    private ActivityDetailDTO activityDetailDTO;

    /**
     * 老的关联的券
     */
    private Set<Integer> oldPublishIds;

    /**
     * 老的选择的模板
     */
    private Set<Integer> oldTemplateIds;

}
