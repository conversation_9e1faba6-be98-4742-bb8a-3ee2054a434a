package com.atour.hotel.module.activity.service;

import com.atour.api.bean.ApiPageResult;
import com.atour.dicts.db.center.meb_DiscountPublishAudit.MebDiscountPublishAuditStatusEnum;
import com.atour.dicts.db.center.meb_DiscountPublishAudit.PopularStateEnum;
import com.atour.dicts.db.center.meb_DiscountPublishAudit.PublishTypeEnum;
import com.atour.dicts.db.center.meb_market.ActivityClassificationEnum;
import com.atour.dicts.db.center.meb_market.MarketStatusEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.framework.response.CommonPageDTO;
import com.atour.hotel.module.activity.dto.*;
import com.atour.hotel.module.activity.enums.FrontActivityStateEnum;
import com.atour.hotel.module.activity.param.*;
import com.atour.hotel.module.activity.wrapper.ActivityWrapper;
import com.atour.hotel.module.common.service.CommonOssService;
import com.atour.hotel.module.coupon.dto.SimpleCouponDTO;
import com.atour.hotel.module.coupon.service.CouponService;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.persistent.franchise.dao.ActivityMaterialDAO;
import com.atour.hotel.persistent.franchise.entity.CouponActivityMaterialEntity;
import com.atour.hotel.persistent.franchise.entity.CouponActivityMaterialExtEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.user.api.activity.dto.MarketDTO;
import com.atour.user.api.coupon.dto.MebDisCountRangeMapDTO;
import com.atour.user.api.coupon.dto.MebDiscountPublishAuditDTO;
import com.atour.user.api.coupon.param.CreateMarketParam;
import com.atour.user.api.coupon.param.QueryUserPublishAuditParam;
import com.atour.user.api.coupon.param.UpdateMarketParam;
import com.atour.user.api.coupon.remote.DiscountPublishAuditRemote;
import com.atour.user.api.coupon.remote.MebDiscountRangeMapRemote;
import com.atour.user.api.market.dto.ActivityDetailDTO;
import com.atour.user.api.market.dto.ActivityPageDTO;
import com.atour.user.api.market.enums.MarketPageStateEnum;
import com.atour.user.api.market.param.QueryMarketWithPageParam;
import com.atour.user.api.market.remote.MarketRemote;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.atour.utils.SimpleInstance;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.primitives.Ints;
import com.yaduo.resource.service.api.oss.dto.response.OssPreviewListDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Service
public class ChainMarketingActivityService {

    /**
     * 最大券数量
     */
    private static final int MAX_COUPON_COUNT = 2;

    @Resource
    private ActivityMaterialDAO activityMaterialDAO;

    @Resource
    private MaterialTemplateService materialTemplateService;

    @Resource
    private ChainMarketingActivityLogService chainMarketingActivityLogService;

    @Resource
    private CouponService couponService;

    @Resource
    private ChainService chainService;

    @Resource
    private MebDiscountRangeMapRemote mebDiscountRangeMapRemote;

    @Resource
    private DiscountPublishAuditRemote publishAuditRemote;

    @Resource
    private MarketRemote marketRemote;

    /*@Resource
    private OssRemote ossRemote;*/
    @Resource
    private CommonOssService commonOssService;

    /**
     * 活动详情
     *
     * @param activityId
     * @return
     */
    public ChainMarketingActivityDetailDTO detail(int activityId) {

        final ActivityDetailDTO activityDetailDTO = marketRemote.detail(activityId, null)
            .getResult();

        final ChainMarketingActivityDetailDTO chainMarketingActivityDetailDTO = ActivityWrapper.toCouponActivityDetailDTO(activityDetailDTO);
        if (Objects.isNull(chainMarketingActivityDetailDTO)) {
            return null;
        }

        //查询活动关联酒店名称
        String issuingChainName = chainService.getChainNameByChainId(activityDetailDTO.getIssuingChainId());
        chainMarketingActivityDetailDTO.setIssuingChainName(issuingChainName);

        final List<CouponActivityMaterialEntity> couponActivityMaterialEntities = activityMaterialDAO.selectByActivityId(activityId);
        final Set<Integer> templateIds = couponActivityMaterialEntities.stream()
            .map(CouponActivityMaterialEntity::getTemplateId)
            .collect(Collectors.toSet());
        chainMarketingActivityDetailDTO.setMaterialIds(templateIds);

        return chainMarketingActivityDetailDTO;
    }

    /**
     * 添加
     *
     * @param param
     * @return
     */
    public boolean add(AddCouponActivityParam param) {
        final Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> checkResult = checkAddChainMarketingActivityParam(param);

        final UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();
        final List<MebDiscountPublishAuditDTO> coupons = checkResult.getLeft();
        final Set<String> auditCodes = coupons.stream()
            .map(MebDiscountPublishAuditDTO::getRelAuditCode)
            .collect(toSet());
        final CreateMarketParam createMarketParam = ActivityWrapper.toCreateMarketParam(param, localUserHotel);
        createMarketParam.setAuditCodes(auditCodes);
        createMarketParam.setChainIds(checkResult.getRight());

        // 这里是活动关联的酒店id，跟上面的不一样，上面的是优惠券关联的酒店ID集合
        createMarketParam.setIssuingChainId(param.getIssuingChainId());

        final MarketDTO marketDTO = marketRemote.add(createMarketParam)
            .getResult();
        final Integer activityId = marketDTO.getMarketId();

        // 插入物料关联数据
        final Set<Integer> materialIds = param.getMaterialIds();
        final List<CouponActivityMaterialEntity> couponActivityMaterialEntities = materialIds.stream()
            .map(materialId -> {
                final CouponActivityMaterialEntity couponActivityMaterialEntity = new CouponActivityMaterialEntity();
                couponActivityMaterialEntity.setActivityId(activityId);
                couponActivityMaterialEntity.setTemplateId(materialId);
                return couponActivityMaterialEntity;
            })
            .collect(Collectors.toList());
        activityMaterialDAO.batchInsert(couponActivityMaterialEntities);

        chainMarketingActivityLogService.addActivityLog(marketDTO, localUserHotel);
        return true;
    }

    /**
     * 校验参数
     *
     * @param param
     * @return
     */
    private Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> checkAddChainMarketingActivityParam(AddCouponActivityParam param) {

        final Map<String, Map<String, String>> text = param.getText();
        if (MapUtils.isEmpty(text)) {
            throw new BusinessException("标题不能为空");
        }
        final Map<String, String> titleMap = text.get("title");
        if (MapUtils.isEmpty(titleMap)) {
            throw new BusinessException("标题不能为空");
        }

        final Date currentDate = DateUtil.getCurrentDate();

        final Date beginTime = param.getBeginTime();
        final Date endTime = param.getEndTime();
        if (DateUtil.compareDateOnly(beginTime, endTime) > 0) {
            throw new BusinessException("开始时间不能晚于结束时间");
        }

        if (beginTime.compareTo(currentDate) < 0) {
            throw new BusinessException("活动开始时间不能早于当前时间");
        }

        return checkCoupon(param, beginTime, endTime);

    }

    /**
     * 校验选择的券
     *
     * @param param
     * @param beginTime
     * @param endTime
     * @return
     */
    private Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> checkCoupon(AddCouponActivityParam param,
        Date beginTime, Date endTime) {

        final List<Integer> couponIds = param.getCouponIds();
        final Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> pair = checkCoupon(couponIds);

        final List<MebDiscountPublishAuditDTO> mebDiscountPublishAuditList = pair.getLeft();

        Date minBeginTime = null;
        Date maxEndTime = null;
        for (MebDiscountPublishAuditDTO mebDiscountPublishAuditDTO : mebDiscountPublishAuditList) {
            if (Objects.isNull(minBeginTime) || DateUtil.compareDateOnly(minBeginTime, mebDiscountPublishAuditDTO.getBeginDate()) > 0) {
                minBeginTime = DateUtil.getDateOnly(mebDiscountPublishAuditDTO.getBeginDate());
            }
            if (Objects.isNull(maxEndTime) || DateUtil.compareDateOnly(minBeginTime, mebDiscountPublishAuditDTO.getEndDate()) < 0) {
                maxEndTime = DateUtil.getDateOnly(mebDiscountPublishAuditDTO.getEndDate());
            }

        }

        if (DateUtil.compareDateOnly(beginTime, minBeginTime) < 0) {
            throw new BusinessException("活动的开始时间不能早于关联券的最早开始时间");
        }

        if (DateUtil.compareDateOnly(endTime, maxEndTime) > 0) {
            throw new BusinessException("活动的结束时间不能晚于关联券的最晚结束时间");
        }

        return pair;
    }

    /**
     * 校验选择的券
     *
     * @param couponIds
     * @return
     */
    private Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> checkCoupon(List<Integer> couponIds) {
        if (couponIds.size() > MAX_COUPON_COUNT) {
            throw new BusinessException(String.format("券数量不能超过%s张", MAX_COUPON_COUNT));
        }

        final List<MebDiscountPublishAuditDTO> mebDiscountPublishAuditList = publishAuditRemote.queryMultiDiscountPublishAudit(Sets.newHashSet(couponIds))
            .getResult();

        if (CollectionUtils.isEmpty(mebDiscountPublishAuditList)) {
            throw new BusinessException("选择的券不存在");
        }

        final Map<Integer, MebDiscountPublishAuditDTO> publishMap = mebDiscountPublishAuditList.stream()
            .collect(Collectors.toMap(MebDiscountPublishAuditDTO::getPAuditId, Function.identity()));

        if (mebDiscountPublishAuditList.size() != couponIds.size()) {
            final Set<Integer> validPublishIds = mebDiscountPublishAuditList.stream()
                .map(MebDiscountPublishAuditDTO::getPAuditId)
                .collect(Collectors.toSet());
            final Sets.SetView<Integer> invalidPublishIds = Sets.difference(Sets.newHashSet(couponIds), validPublishIds);
            throw new BusinessException(String.format("id 为【%s】的券无效", SimpleInstance.COMMA_JOINER.join(invalidPublishIds)));
        }

        final UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();
        final Integer userId = localUserHotel.getUserId();
        for (MebDiscountPublishAuditDTO mebDiscountPublishAuditDTO : mebDiscountPublishAuditList) {
            final Integer pAuditId = mebDiscountPublishAuditDTO.getPAuditId();
            if (!Objects.equals(mebDiscountPublishAuditDTO.getCreateUserId(), userId)) {
                final Map<Integer, String> disTypeMap = couponService.getDisTypeMap();
                throw new BusinessException(String.format("券【%s %s】不是您创建的, 不能添加", pAuditId,
                    disTypeMap.getOrDefault(mebDiscountPublishAuditDTO.getDisType(), StringUtils.EMPTY)));
            }
            final Integer publishType = mebDiscountPublishAuditDTO.getPublishType();
            if (publishType != PublishTypeEnum.QR_ACTIVITY.getCode()
                && publishType != PublishTypeEnum.QR_AND_CLIENT.getCode()) {
                throw new BusinessException("只能选择领取方式为【全部】、【扫码活动】的券");
            }

        }

        final Set<String> rangeCodes = mebDiscountPublishAuditList.stream()
            .map(MebDiscountPublishAuditDTO::getRangeCode)
            .collect(Collectors.toSet());
        final List<MebDisCountRangeMapDTO> rangeList = mebDiscountRangeMapRemote.queryRangeMapByMultiCode(rangeCodes)
            .getResult();
        if (CollectionUtils.isEmpty(rangeList)) {
            throw new BusinessException("选择的券未关联酒店");
        }

        final Map<String, Set<Integer>> relatedChains = rangeList.stream()
            .collect(Collectors.groupingBy(MebDisCountRangeMapDTO::getCode, mapping(MebDisCountRangeMapDTO::getChainid, toSet())));

        if (couponIds.size() == MAX_COUPON_COUNT) {

            final MebDiscountPublishAuditDTO firstPublishInfo = publishMap.get(couponIds.get(0));
            final MebDiscountPublishAuditDTO secondPublishInfo = publishMap.get(couponIds.get(1));

            final Set<Integer> firstRelatedChain = relatedChains.get(firstPublishInfo.getRangeCode());
            final Set<Integer> secondRelatedChain = relatedChains.get(secondPublishInfo.getRangeCode());
            if (CollectionUtils.isNotEmpty(Sets.difference(firstRelatedChain, secondRelatedChain))
            || CollectionUtils.isNotEmpty(Sets.difference(secondRelatedChain, firstRelatedChain))) {
                final Map<Integer, String> disTypeMap = couponService.getDisTypeMap();
                throw new BusinessException(String.format("【%s %s】与【%s %s】，适用门店不一致，无法添加！", firstPublishInfo.getPAuditId(),
                    disTypeMap.getOrDefault(firstPublishInfo.getDisType(), StringUtils.EMPTY), secondPublishInfo.getPAuditId(),
                    disTypeMap.getOrDefault(secondPublishInfo.getDisType(), StringUtils.EMPTY)));
            }

        }

        final Set<Integer> relatedChainIds = relatedChains.values()
            .stream()
            .flatMap(Collection::stream)
            .collect(toSet());

        return Pair.of(mebDiscountPublishAuditList, relatedChainIds);
    }

    /**
     * 编辑
     *
     * @param param
     * @return
     */
    public boolean edit(EditCouponActivityParam param) {

        final UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();

        final Integer activityId = param.getActivityId();
        final ActivityDetailDTO activityDetailDTO = marketRemote.detail(activityId, null)
            .getResult();
        if (Objects.isNull(activityDetailDTO)) {
            throw new BusinessException("活动不存在");
        }
        if (!Objects.equals(localUserHotel.getUserId(), activityDetailDTO.getCreateUserId())) {
            throw new BusinessException("您不是活动的创建人, 不能修改活动配置");
        }

        final UpdateMarketParam updateMarketParam = new UpdateMarketParam();
        updateMarketParam.setActivityId(activityId);
        updateMarketParam.setOperateUserId(localUserHotel.getUserId());
        updateMarketParam.setOperateUserName(localUserHotel.getUserName());

        // 状态
        if (param.getOnlineState() == FrontActivityStateEnum.ONLINE.getCode()
            && activityDetailDTO.getState() == MarketStatusEnum.FAILURE.getValue()) {
            updateMarketParam.setState(MarketStatusEnum.EFFECTIVE.getValue());
        } else if (param.getOnlineState() == FrontActivityStateEnum.OFFLINE.getCode()
            && activityDetailDTO.getState() == MarketStatusEnum.EFFECTIVE.getValue()) {
            updateMarketParam.setState(MarketStatusEnum.FAILURE.getValue());
        }

        // 关联的券
        final List<MebDiscountPublishAuditDTO> coupons = activityDetailDTO.getCoupons();
        final Set<Integer> publishIds = coupons.stream()
            .map(MebDiscountPublishAuditDTO::getPAuditId)
            .collect(toSet());

        final List<Integer> newPublishIds = param.getCouponIds();
        final Set<Integer> newPublishIdSet = Sets.newHashSet(newPublishIds);
        final Set<Integer> relatedCouponAdding = Sets.newHashSet(Sets.difference(newPublishIdSet, publishIds));
        if (CollectionUtils.isNotEmpty(relatedCouponAdding)) {
            final Date beginTime = activityDetailDTO.getBeginTime();
            final Date endTime = DateUtil.addDays(activityDetailDTO.getEndTime(), -1);
            final Pair<List<MebDiscountPublishAuditDTO>, Set<Integer>> checkResult = checkCoupon(param, beginTime, endTime);
            Set<Integer> newRelatedChainIds = checkResult.getRight();
            final Set<String> auditCodes = checkResult.getLeft()
                .stream()
                .map(MebDiscountPublishAuditDTO::getRelAuditCode)
                .collect(toSet());
            updateMarketParam.setAuditCodes(auditCodes);

            if (CollectionUtils.isNotEmpty(Sets.difference(newRelatedChainIds, activityDetailDTO.getChainIds()))) {
                updateMarketParam.setChainIds(newRelatedChainIds);
            }

        }

        final List<CouponActivityMaterialEntity> couponActivityMaterialEntities = activityMaterialDAO.selectByActivityId(activityId);
        final Set<Integer> templateIds = couponActivityMaterialEntities.stream()
            .map(CouponActivityMaterialEntity::getTemplateId)
            .collect(Collectors.toSet());

        final Set<Integer> newTemplateIds = param.getMaterialIds();
        final Set<Integer> templateDeleting = Sets.difference(templateIds, newTemplateIds);
        if (CollectionUtils.isNotEmpty(templateDeleting)) {
            throw new BusinessException("不能删除已选择的物料");
        }

        final Set<Integer> templateAdding = Sets.newHashSet(Sets.difference(newTemplateIds, templateIds));

        marketRemote.update(updateMarketParam);

        // 准备好数据后统一进行更新操作
        if (CollectionUtils.isNotEmpty(templateAdding)) {
            final List<CouponActivityMaterialEntity> materialAddingEntities = templateAdding.stream()
                .map(templateId -> {
                    final CouponActivityMaterialEntity couponActivityMaterialEntity = new CouponActivityMaterialEntity();
                    couponActivityMaterialEntity.setActivityId(activityId);
                    couponActivityMaterialEntity.setTemplateId(templateId);
                    return couponActivityMaterialEntity;
                })
                .collect(Collectors.toList());
            activityMaterialDAO.batchInsertOrUpdate(materialAddingEntities);
        }

        final EditCouponActivityLogParam logParam = EditCouponActivityLogParam.builder()
            .editParam(param)
            .activityDetailDTO(activityDetailDTO)
            .oldPublishIds(publishIds)
            .oldTemplateIds(templateIds)
            .build();
        chainMarketingActivityLogService.editActivityLog(logParam, localUserHotel);

        return true;
    }

    /**
     * 查询活动的物料
     *
     * @param activityId
     * @return
     */
    public List<ActivityMaterialDTO> queryActivityMaterial(int activityId) {

        final List<CouponActivityMaterialExtEntity> entityList = activityMaterialDAO.selectExtByActivityId(activityId);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }

        Map<String, Integer> templateTypeCounter = Maps.newHashMap();
        final List<ActivityMaterialDTO> result = entityList.stream()
            .filter(couponActivityMaterialExtEntity -> StringUtils.isNotEmpty(couponActivityMaterialExtEntity.getUrl()))
            .map(entity -> {
                final ActivityMaterialDTO activityMaterialDTO = new ActivityMaterialDTO();
                final String name = materialTemplateService.queryTemplateTypeName(entity.getType());
                Integer count = templateTypeCounter.computeIfAbsent(name, key -> 0);
                count += 1;
                templateTypeCounter.put(name, count);
                activityMaterialDTO.setType(String.format("%s-%s", name, count));
                String url = Safes.of(entity.getUrl());
                activityMaterialDTO.setUrl(url);
                if (StringUtils.isNotEmpty(url)) {
                    final List<OssPreviewListDTO> ossPreviewListDTOS = commonOssService.previewByOssKeyList(Lists.newArrayList(url), TimeUnit.DAYS.toSeconds(1));
                    final OssPreviewListDTO ossPreviewListDTO = ossPreviewListDTOS.stream().findFirst().orElse(null); 

                   /* final OssPreviewParam ossPreviewParam = new OssPreviewParam();
                    ossPreviewParam.setOssKey(url);
                    ossPreviewParam.setExpire(TimeUnit.DAYS.toSeconds(1));
                    final OssPreviewDTO preview = ossRemote.preview(ossPreviewParam);*/
                    activityMaterialDTO.setUrl(Objects.isNull(ossPreviewListDTO) ? null : ossPreviewListDTO.getShowUrl());
                }
                return activityMaterialDTO;
            })
            .collect(Collectors.toList());

        return result;
    }

    /**
     * 查询用于预览的数据
     *
     * @param activityId
     * @return
     */
    public ChainMarketingActivityPreviewDTO preview(int activityId) {

        final ChainMarketingActivityDetailDTO couponActivityDetail = detail(activityId);
        if (Objects.isNull(couponActivityDetail)) {
            throw new BusinessException("活动不存在");
        }

        final List<MaterialDTO> materialDTOS = materialTemplateService.queryMultiTemplate(couponActivityDetail.getMaterialIds());
        final ChainMarketingActivityPreviewDTO couponActivityPreviewDTO = new ChainMarketingActivityPreviewDTO();
        couponActivityPreviewDTO.setActivityInfo(couponActivityDetail);
        couponActivityPreviewDTO.setMaterialList(materialDTOS);

        return couponActivityPreviewDTO;
    }

    /**
     * 查询可以关联的券信息
     *
     * @return
     */
    public List<SimpleCouponDTO> coupons() {

        final UserPermissionDTO userInfo = CommonParamsManager.getLocalUserHotel();
        final QueryUserPublishAuditParam queryUserPublishAuditParam = new QueryUserPublishAuditParam();
        queryUserPublishAuditParam.setCreateUserId(userInfo.getUserId());
        queryUserPublishAuditParam.setEndDateLower(DateUtil.getCurrentDate());
        queryUserPublishAuditParam.setPopularStates(Sets.newHashSet(PopularStateEnum.CONTINUE.getCode()));
        queryUserPublishAuditParam.setPublishTypes(Sets.newHashSet(PublishTypeEnum.QR_ACTIVITY.getCode(), PublishTypeEnum.QR_AND_CLIENT.getCode()));
        queryUserPublishAuditParam.setStatusSet(Sets.newHashSet(MebDiscountPublishAuditStatusEnum.AUDIT_PASSED.getCode()));
        final List<MebDiscountPublishAuditDTO> dtoList = publishAuditRemote.queryUserPublishAudit(queryUserPublishAuditParam)
            .getResult();

        final Map<Integer, String> disTypeMap = couponService.getDisTypeMap();

        return Safes.of(dtoList)
            .stream()
            .map(mebDiscountPublishAuditDTO -> {
                final SimpleCouponDTO simpleCouponDTO = new SimpleCouponDTO();
                simpleCouponDTO.setCouponId(mebDiscountPublishAuditDTO.getPAuditId());
                final String disTypeName = disTypeMap.getOrDefault(mebDiscountPublishAuditDTO.getDisType(), StringUtils.EMPTY);
                simpleCouponDTO.setCouponName(simpleCouponDTO.getCouponId() + " " + disTypeName);
                simpleCouponDTO.setBeginTime(mebDiscountPublishAuditDTO.getBeginDate());
                simpleCouponDTO.setEndTime(mebDiscountPublishAuditDTO.getEndDate());
                return simpleCouponDTO;
            })
            .sorted((o1, o2) -> Ints.compare(o2.getCouponId(), o1.getCouponId()))
            .collect(Collectors.toList());

    }

    /**
     * 添加券的过程中校验是否可以添加
     *
     * @param checkCouponParam
     * @return
     */
    public CheckCouponDTO checkCoupons(CheckCouponParam checkCouponParam) {

        final CheckCouponDTO checkCouponDTO = new CheckCouponDTO();
        checkCouponDTO.setValid(true);
        checkCouponDTO.setMessage(StringUtils.EMPTY);

        final List<Integer> couponIds = checkCouponParam.getCouponIds();
        if (CollectionUtils.isEmpty(couponIds)) {
            return checkCouponDTO;
        }

        if (couponIds.size() == 1) {
            return checkCouponDTO;
        }

        try {
            checkCoupon(couponIds);
        } catch (Exception e) {
            if (!(e instanceof BusinessException)) {
                throw e;
            }
            checkCouponDTO.setValid(false);
            checkCouponDTO.setMessage(e.getMessage());

        }

        return checkCouponDTO;
    }

    /**
     * 活动列表
     *
     * @param param
     * @return
     */
    public CommonPageDTO<ChainMarketingActivityListDTO> list(ChainMarketingActivityListParam param) {

        final QueryMarketWithPageParam queryMarketWithPageParam = new QueryMarketWithPageParam();
        queryMarketWithPageParam.setBeginDate(param.getBeginDate());
        queryMarketWithPageParam.setEndDate(param.getEndDate());
        queryMarketWithPageParam.setPageNo(param.getPageNo());
        queryMarketWithPageParam.setPageSize(param.getPageSize());
        queryMarketWithPageParam.setTitle(param.getTitle());
        queryMarketWithPageParam.setState(MarketPageStateEnum.codeOf(Safes.of(param.getState(), 0)));
        queryMarketWithPageParam.setActivityClassify(ActivityClassificationEnum.CHAIN_MARKETING_ACTIVITY.getCode());

        final UserPermissionDTO localUserHotel = CommonParamsManager.getLocalUserHotel();
        if (Safes.of(param.getChainId(), 0) == 0) {
            queryMarketWithPageParam.setChainIds(null);
        } else {
            queryMarketWithPageParam.setChainIds(Sets.newHashSet(param.getChainId()));
        }

        // 设置查询活动关联的酒店id，如果是查询所有的酒店，需要过滤下，只查询用户关联的所有酒店
        queryMarketWithPageParam.setIssuingChainIds(new HashSet<>(param.getIssuingChainIds()));

        final ApiPageResult<List<ActivityPageDTO>> apiResult = marketRemote.page(queryMarketWithPageParam);

        final List<ActivityPageDTO> activityPageDTOList = apiResult.getResult();

        final Date currentDateTime = DateUtil.getCurrentDateTime();

        final Map<Integer, String> disTypeMap = couponService.getDisTypeMap();

        // 查询活动关联酒店ID对应的酒店名称
        Set<Integer> issuingChainIdSet = activityPageDTOList.stream().map(ActivityPageDTO::getIssuingChainId).collect(toSet());
        //final Map<Integer, String> issuingChainIdMap = chainService.getChainNamesByChainIds(issuingChainIdSet);
        final Map<Integer, String> issuingChainIdMap = couponService.getChainNames(issuingChainIdSet);

        final List<ChainMarketingActivityListDTO> list = activityPageDTOList.stream()
            .map(activityPageDTO -> ActivityWrapper.toActivityListDTO(activityPageDTO, currentDateTime, localUserHotel, disTypeMap, issuingChainIdMap))
            .sorted((o1, o2) -> Ints.compare(o2.getActivityId(), o1.getActivityId()))
            .collect(Collectors.toList());

        final CommonPageDTO<ChainMarketingActivityListDTO> result = new CommonPageDTO<>();
        result.setPage(apiResult.getPage());
        result.setData(list);

        return result;
    }



}
