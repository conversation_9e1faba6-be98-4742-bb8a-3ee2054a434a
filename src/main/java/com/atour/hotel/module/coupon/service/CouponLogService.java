package com.atour.hotel.module.coupon.service;

import com.atour.dicts.db.center.meb_DiscountPublishAudit.MebDiscountPublishAuditStatusEnum;
import com.atour.dicts.db.center.meb_DiscountPublishAudit.PopularStateEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.coupon.param.PublishPopularizeParam;
import com.atour.hotel.module.log.enums.OmsLogOperateTypeEnum;
import com.atour.hotel.module.log.enums.OmsLogTypeEnum;
import com.atour.hotel.module.log.service.OmsLogService;
import com.atour.hotel.persistent.franchise.entity.OmsOperateLogEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.user.api.coupon.dto.MebDiscountPublishAuditDTO;
import com.atour.user.api.coupon.dto.MebDiscountTypeDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2020/6/17
 */
@Service
public class CouponLogService {

    @Resource
    private OmsLogService omsLogService;

    @Resource
    private CouponTypeService couponTypeService;

    /**
     * 发行券日志
     *
     * @param mebDiscountPublishAuditDTO
     */
    public void addPublishLog(MebDiscountPublishAuditDTO mebDiscountPublishAuditDTO, UserPermissionDTO userInfo, String disTypeName) {

        if (Objects.isNull(mebDiscountPublishAuditDTO)) {
            return;
        }

        Supplier<OmsOperateLogEntity> supplier = () -> {
            final OmsOperateLogEntity omsOperateLogEntity = new OmsOperateLogEntity();
            omsOperateLogEntity.setBizId(mebDiscountPublishAuditDTO.getPAuditId());
            omsOperateLogEntity.setBizType(OmsLogTypeEnum.COUPON.getCode());
            omsOperateLogEntity.setContent(String.format("新建 【%s %s】", mebDiscountPublishAuditDTO.getPAuditId(), disTypeName));
            omsOperateLogEntity.setOperateUserId(userInfo.getUserId());
            omsOperateLogEntity.setOperateUsername(userInfo.getUserName());
            omsOperateLogEntity.setOperationType(OmsLogOperateTypeEnum.ADD.getCode());
            return omsOperateLogEntity;
        };


        omsLogService.addLog(supplier);
    }

    /**
     * 审核日志
     *
     * @param mebDiscountPublishAuditDTO
     */
    public void addAuditLog(MebDiscountPublishAuditDTO mebDiscountPublishAuditDTO, UserPermissionDTO userInfo) {

        if (Objects.isNull(mebDiscountPublishAuditDTO)) {
            return;
        }

        Supplier<OmsOperateLogEntity> supplier = () -> {
            final OmsOperateLogEntity omsOperateLogEntity = new OmsOperateLogEntity();
            omsOperateLogEntity.setBizId(mebDiscountPublishAuditDTO.getPAuditId());
            omsOperateLogEntity.setBizType(OmsLogTypeEnum.COUPON.getCode());

            String result = "通过";
            if (mebDiscountPublishAuditDTO.getStatus() == MebDiscountPublishAuditStatusEnum.AUDIT_NOT_PASS.getCode()) {
                result = "不通过";
            }
            final MebDiscountTypeDTO mebDiscountTypeDTO = couponTypeService.detail(mebDiscountPublishAuditDTO.getDisType());
            omsOperateLogEntity.setContent(String.format("%s 审核 【%s %s】%s", userInfo.getUserName(), mebDiscountPublishAuditDTO.getPAuditId(),
                mebDiscountTypeDTO.getDisTypeName(), result));
            omsOperateLogEntity.setOperateUserId(userInfo.getUserId());
            omsOperateLogEntity.setOperateUsername(userInfo.getUserName());
            omsOperateLogEntity.setOperationType(OmsLogOperateTypeEnum.EDIT.getCode());
            return omsOperateLogEntity;
        };


        omsLogService.addLog(supplier);
    }

    /**
     * 推广日志
     *
     * @param mebDiscountPublishAuditDTO
     */
    public void addPopularLog(PublishPopularizeParam publishPopularizeParam, MebDiscountPublishAuditDTO mebDiscountPublishAuditDTO,
        UserPermissionDTO userInfo) {

        if (Objects.isNull(mebDiscountPublishAuditDTO)) {
            return;
        }


        Supplier<OmsOperateLogEntity> supplier = () -> {
            final OmsOperateLogEntity omsOperateLogEntity = new OmsOperateLogEntity();
            omsOperateLogEntity.setBizId(mebDiscountPublishAuditDTO.getPAuditId());
            omsOperateLogEntity.setBizType(OmsLogTypeEnum.COUPON.getCode());

            String operation = "进行推广";
            if (publishPopularizeParam.getPopularizeResult() == PopularStateEnum.SUSPEND.getCode()) {
                operation = "暂停推广";
            }

            final MebDiscountTypeDTO mebDiscountTypeDTO = couponTypeService.detail(mebDiscountPublishAuditDTO.getDisType());

            omsOperateLogEntity.setContent(String.format("%s %s【%s %s】", userInfo.getUserName(), operation, mebDiscountPublishAuditDTO.getPAuditId(),
                mebDiscountTypeDTO.getDisTypeName()));
            omsOperateLogEntity.setOperateUserId(userInfo.getUserId());
            omsOperateLogEntity.setOperateUsername(userInfo.getUserName());
            omsOperateLogEntity.setOperationType(OmsLogOperateTypeEnum.EDIT.getCode());
            return omsOperateLogEntity;
        };

        omsLogService.addLog(supplier);
    }

}
