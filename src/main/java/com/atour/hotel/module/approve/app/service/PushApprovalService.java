package com.atour.hotel.module.approve.app.service;

import com.atour.activity.api.dto.ProcessActivityDTO;
import com.atour.activity.api.dto.ProcessDefinitionDTO;
import com.atour.activity.api.dto.ProcessInstanceDetailDTO;
import com.atour.activity.api.dto.StartProcessResultDTO;
import com.atour.activity.api.dto.TaskCompleteResultDTO;
import com.atour.activity.api.dto.TaskInfoQueryResultDTO;
import com.atour.activity.api.dto.TaskInstanceQueryResultDTO;
import com.atour.activity.api.dto.TaskQueryResultDTO;
import com.atour.activity.api.param.StartProcessParam;
import com.atour.activity.api.param.TaskCompleteParam;
import com.atour.activity.api.param.TaskQueryParam;
import com.atour.activity.api.param.WithdrawParam;
import com.atour.activity.api.remote.ProcessRemote;
import com.atour.activity.common.constant.ActivitiConstants;
import com.atour.activity.common.enums.ApproveProjectIndexEnum;
import com.atour.activity.common.enums.ApproveStateEnum;
import com.atour.activity.mq.TaskNotifyTodoUserMessage;
import com.atour.api.bean.PageInfo;
import com.atour.asso.api.dto.AssoUserInfoDTO;
import com.atour.asso.client.thread.AssoCommonParamsManager;
import com.atour.db.redis.RedisClient;
import com.atour.db.redis.RedisLock;
import com.atour.hotel.common.constants.RedisContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.lock.LockKeyFactory;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.constants.MetricConstants;
import com.atour.hotel.module.approve.app.condition.PushQueryPageCondition;
import com.atour.hotel.module.approve.app.dto.ApproveRecordDTO;
import com.atour.hotel.module.approve.app.dto.ApproveRecordOutDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalAudienceDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalDetailDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalEditDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalInfoDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalInitDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalPageDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalPartDTO;
import com.atour.hotel.module.approve.app.dto.PushApprovalPreviewDTO;
import com.atour.hotel.module.approve.app.request.push.ApprovalDetailParam;
import com.atour.hotel.module.approve.app.request.push.ApproveHandleParam;
import com.atour.hotel.module.approve.app.request.push.PushApprovalParam;
import com.atour.hotel.module.approve.app.request.push.PushQueryPageParam;
import com.atour.hotel.module.approve.app.wrapper.ApprovalWrapper;
import com.atour.hotel.module.approve.enums.PushApprovalOfAudienceEnum;
import com.atour.hotel.module.approve.enums.PushApprovalOfPushStateEnum;
import com.atour.hotel.module.approve.enums.PushApprovalOfSubmitTypeEnum;
import com.atour.hotel.module.approve.web.trans.service.ApproveFlowService;
import com.atour.hotel.module.common.service.CorpWechatNotifyService;
import com.atour.hotel.module.common.service.FileOperationService;
import com.atour.hotel.module.inner.folio.dto.InnerFolioTransModifyDTO;
import com.atour.hotel.module.inner.folio.wrapper.FolioTransModifyWrapper;
import com.atour.hotel.module.rbac.service.RbacService;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.hotel.persistent.pms.dao.PushApprovalRequestDao;
import com.atour.hotel.persistent.pms.entity.PushApprovalRequestEntity;
import com.atour.monitor.AMonitor;
import com.atour.rbac.api.response.UserDTO;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.atour.web.exception.BusinessException;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 推送申请 service
 *
 * <AUTHOR>
 * @date 2020年5月11日
 **/
@Slf4j
@Service
public class PushApprovalService {

    /**
     * 审批编号自增num默认位数
     */
    private final static int DEFAULT_APPROVAL_SIZE = 5;

    /**
     * 时间格式
     */
    private static final String DATE_FORMAT = "yyyy年MM月dd日 HH:mm";
    private static final String DATE_SECOND_FORMAT = "yyyy年MM月dd日 HH:mm:ss";

    @Resource
    private PushApprovalRequestDao pushApprovalRequestDao;
    @Resource
    private RedisLock lockRedis;
    @Resource
    private RedisClient redisClient;
    @Resource
    private ProcessRemote processRemote;

    @Resource
    private FileOperationService fileOperationService;
    @Resource
    private RbacService rbacService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private CorpWechatNotifyService corpWechatNotifyService;
    @Resource
    private FolioTransModifyService folioTransModifyService;
    @Resource
    private CommentHelper commentHelper;
    @Resource
    private PushPassApprovalService pushPassApprovalService;

    /**
     * 推送审批流程定义模板key
     */
    @Value("${push.process.definitionKey:push_approval}")
    private volatile String pushProcessDefinitionKey;

    /**
     * 推送审批受众对应关系
     */
    @ApolloJsonValue("${push.approval.audience:[]}")
    private volatile List<PushApprovalAudienceDTO> pushApprovalAudienceList;

    @Resource
    private ApproveFlowService approveFlowService;

    /**
     * 提交申请
     *
     * @param param
     * @return
     */
    public String submitApprove(PushApprovalParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        //校验申请推送管理的权限
        if (!rbacService.auditPushApprovalRequest(userInfo.getEmployeeId())) {
            throw new BusinessException(ResponseCodeEnum.FORBID_ACCESS.getMessage(),
                ResponseCodeEnum.FORBID_ACCESS.getCode());
        }
        String requestId = param.getRequestId();
        PushApprovalRequestEntity oldPushApprovalRequestEntity = null;
        if (StringUtils.isNotBlank(requestId)) {
            oldPushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
            if (Objects.isNull(oldPushApprovalRequestEntity)) {
                throw new BusinessException(ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getMessage(),
                    ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getCode());
            }
        }

        //调账审批锁
        String lockKey = LockKeyFactory.getPushApprovalLockKey(userInfo.getEmployeeId());
        RLock lock = lockRedis.getLock(lockKey);
        if (lock == null || !lock.tryLock()) {
            log.warn("PushApproval lock FAIL, employeeId={}", userInfo.getEmployeeId());
            AMonitor.meter(MetricConstants.PUSH_APPROVAL_CONFLICT);
            throw new BusinessException(ResponseCodeEnum.NO_PUSH_APPROVAL_LOCK.getMessage(),
                ResponseCodeEnum.NO_PUSH_APPROVAL_LOCK.getCode());
        }
        try {

            //保存
            PushApprovalRequestEntity.PushApprovalRequestEntityBuilder pushApprovalRequestEntity =
                PushApprovalRequestEntity.builder()
                    .background(param.getBackground())
                    .title(param.getTitle())
                    .content(param.getContent())
                    .employeeId(userInfo.getEmployeeId())
                    .fileUrls(param.getFileOssKeyList())
                    .pushUsers(param.getAudienceIdList())
                    .employeeId(userInfo.getEmployeeId())
                    .processState(ApproveStateEnum.WAIT.getCode())
                    .pushTime(param.getPushTime())
                    .processDefinitionKey(pushProcessDefinitionKey);
            switch (Objects.requireNonNull(PushApprovalOfSubmitTypeEnum.getEnumByCode(param.getSubmitType()))) {
                case SUBMIT_APPROVAL:
                    // 新增提交审批
                    if (StringUtils.isBlank(requestId)) {
                        // 审批编号生成 年月日+系统编号+4位自增数（每日自增数自0001开始计数）
                        requestId = generateRequestId();
                        // 开启审批流
                        String processInstanceId = this.start(param, userInfo, requestId);
                        pushApprovalRequestEntity.requestId(requestId);
                        pushApprovalRequestEntity.processId(processInstanceId);
                        pushApprovalRequestDao.insertSelective(pushApprovalRequestEntity.build());
                        //完成当前节点
                        this.completeCurrentNode(userInfo.getEmployeeId(), processInstanceId,requestId);
                    } else {
                        if (Objects.nonNull(oldPushApprovalRequestEntity) && (
                            Objects.equals(ApproveStateEnum.RECALL.getCode(),
                                oldPushApprovalRequestEntity.getProcessState()) || Objects.equals(
                                ApproveStateEnum.REFUSE.getCode(), oldPushApprovalRequestEntity.getProcessState()))) {
                            this.resubmitApproval(userInfo, oldPushApprovalRequestEntity, pushApprovalRequestEntity);

                            return requestId;
                        }
                        if (Objects.isNull(oldPushApprovalRequestEntity) || !Objects.equals(
                            ApproveStateEnum.WAIT_SUBMIT.getCode(), oldPushApprovalRequestEntity.getProcessState())) {
                            throw new BusinessException(
                                ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getMessage(),
                                ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getCode());
                        }
                        // 开启审批流
                        String processInstanceId = this.start(param, userInfo, requestId);
                        pushApprovalRequestEntity.processId(processInstanceId);
                        pushApprovalRequestDao.updateByPrimaryKeySelective(pushApprovalRequestEntity.build());
                        //完成当前节点
                        this.completeCurrentNode(userInfo.getEmployeeId(), processInstanceId,requestId);
                    }
                    break;
                case PREVIEW:
                    // 预览新增或更新
                    if (StringUtils.isBlank(requestId)) {
                        requestId = generateRequestId();
                        pushApprovalRequestEntity.requestId(requestId)
                            .processState(ApproveStateEnum.WAIT_SUBMIT.getCode());
                        pushApprovalRequestDao.insertSelective(pushApprovalRequestEntity.build());
                    } else {
                        //|| !Objects.equals(ApproveStateEnum.WAIT_SUBMIT.getCode(), oldPushApprovalRequestEntity.getProcessState())
                        if (Objects.isNull(oldPushApprovalRequestEntity)) {
                            throw new BusinessException(
                                ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getMessage(),
                                ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getCode());
                        }
                        pushApprovalRequestDao.updateByPrimaryKeySelective(pushApprovalRequestEntity.build());
                    }
                    break;
                case SUBMIT_APPROVAL_AGAIN:
                    resubmitApproval(userInfo, oldPushApprovalRequestEntity, pushApprovalRequestEntity);
                    break;
            }
        } finally {
            lock.unlock();
            log.info("submitApprove unlock, employeeId={}", userInfo.getEmployeeId());
        }
        return requestId;
    }

    /**
     * 重新提交审核
     *
     * @param userInfo
     * @param oldPushApprovalRequestEntity
     * @param pushApprovalRequestEntity
     */
    private void resubmitApproval(AssoUserInfoDTO userInfo, PushApprovalRequestEntity oldPushApprovalRequestEntity,
        PushApprovalRequestEntity.PushApprovalRequestEntityBuilder pushApprovalRequestEntity) {
        if (Objects.isNull(oldPushApprovalRequestEntity)) {
            throw new BusinessException(ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getMessage(),
                ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.getCode());
        }
        if (!Objects.equals(ApproveStateEnum.RECALL.getCode(), oldPushApprovalRequestEntity.getProcessState())
            && !Objects.equals(ApproveStateEnum.REFUSE.getCode(), oldPushApprovalRequestEntity.getProcessState())) {
            throw new BusinessException(ResponseCodeEnum.APPROVAL_STATE_ERROR.getMessage(),
                ResponseCodeEnum.APPROVAL_STATE_ERROR.getCode());
        }
        // 再次提交
        pushApprovalRequestEntity.requestId(oldPushApprovalRequestEntity.getRequestId());
        pushApprovalRequestDao.updateByPrimaryKeySelective(pushApprovalRequestEntity.build());
        this.completeCurrentNode(userInfo.getEmployeeId(), oldPushApprovalRequestEntity.getProcessId(),oldPushApprovalRequestEntity.getRequestId());
    }

    /**
     * 生成审批编号
     *
     * @return
     */
    private String generateRequestId() {
        // 审批编号生成 年月日+系统编号+4位自增数（每日自增数自0001开始计数）
        String formatDate = DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.FORMAT_DATE_NUMBER);
        String key = RedisContants.PUSH_ADJUST_KEY + formatDate;
        Long lastApprovalNum = redisClient.incryBy(key, NumberUtils.LONG_ONE);
        if (lastApprovalNum.equals(NumberUtils.LONG_ONE)) {
            redisClient.expire(key, RedisContants.FOLIO_TRANS_ADJUST_KEY_EXPIRE_TIME_IN_SECONDS);
        }
        return formatDate + ApproveProjectIndexEnum.OMS.getIndex() + CommonUtils.fill(lastApprovalNum,
            DEFAULT_APPROVAL_SIZE, DEFAULT_APPROVAL_SIZE);
    }

    /**
     * 启动审批流程
     *
     * @param param
     * @param userInfo
     * @param requestId
     * @return
     */
    private String start(PushApprovalParam param, AssoUserInfoDTO userInfo, String requestId) {
        //全局变量
        Map<String, Object> variables = Maps.newHashMap();
        // 发起流程申请
        StartProcessParam startProcessParam = StartProcessParam.builder()
            .userId(userInfo.getEmployeeId())
            .businessKey(requestId)
            .processDefinitionKey(pushProcessDefinitionKey)
            .variables(variables)
            .build();
        approveFlowService.setFeishuCallBack(startProcessParam,pushProcessDefinitionKey);
        StartProcessResultDTO startProcessResultDTO = processRemote.startProcess(startProcessParam);
        if (startProcessResultDTO == null || StringUtils.isEmpty(startProcessResultDTO.getProcessInstanceId())) {
            log.error("submitApprove start process error, param:{}", ObjectUtil.toJsonQuietly(param));
            throw new BusinessException(ResponseCodeEnum.START_PROCESS_FAILED.getMessage(),
                ResponseCodeEnum.START_PROCESS_FAILED.getCode());
        }
        return startProcessResultDTO.getProcessInstanceId();
    }

    /**
     * 完成当前节点
     *
     * @param employeeId
     * @param processInstanceId
     * @return
     */
    public String completeCurrentNode(String employeeId, String processInstanceId,String requestId) {
        // 完成当前节点
        List<TaskQueryResultDTO> taskQueryResultDTOS = processRemote.toDoList(TaskQueryParam.builder()
            .userId(employeeId)
            .processInstanceId(Lists.newArrayList(processInstanceId))
            .build());
        List<TaskInfoQueryResultDTO> taskInfos = taskQueryResultDTOS.get(0)
            .getTaskInstances()
            .get(0)
            .getTaskInfos();
        String taskId = taskInfos.get(0)
            .getTaskId();
        if (taskInfos.size() > 1) {
            log.warn("toDoList taskQueryResultDTOS=={}", ObjectUtil.toJsonQuietly(taskQueryResultDTOS));
        }
        // 设置流程变量
        Map<String, Object> variables = Maps.newHashMap();
        Map<String, Object> variablesLocal = Maps.newHashMap();

        // 区分拒绝与通过
        variables.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
            ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);
        variablesLocal.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
            ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);


        TaskCompleteParam taskCompleteParam = TaskCompleteParam.builder()
                .assignee(employeeId)
                .taskId(taskId)
                .variables(variables)
                .variablesLocal(variablesLocal)
                .approveAction(ApproveStateEnum.PASS.getCode())
                .processInstanceId(processInstanceId)
                .build();
        taskCompleteParam.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                FileConfig.approveAppUrl, pushProcessDefinitionKey, requestId, processInstanceId));
        taskCompleteParam.setDisplayType(2);
        approveFlowService.setFeishuCallBack(taskCompleteParam,pushProcessDefinitionKey);
        // 重新发起流程申请
        TaskCompleteResultDTO taskCompleteResultDTO = processRemote.complete(taskCompleteParam);
        if (taskCompleteResultDTO == null || StringUtils.isEmpty(taskCompleteResultDTO.getProcessInstanceId())) {
            log.error("completeCurrentNode process error, processInstanceId:{}", processInstanceId);
            throw new BusinessException(ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getMessage(),
                ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getCode());
        }
        return taskId;
    }

    /**
     * 推送审批分页
     *
     * @param pushQueryPageParam
     * @return
     */
    public PushApprovalPageDTO getPushApprovalPage(PushQueryPageParam pushQueryPageParam) {
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        PushApprovalPageDTO pushApprovalPageDTO = new PushApprovalPageDTO();
        PageInfo pageInfo = new PageInfo(pushQueryPageParam.getPageNo(), pushQueryPageParam.getPageSize());
        PushQueryPageCondition pushQueryPageCondition =
            ApprovalWrapper.wrapperPushQueryPageCondition(pushQueryPageParam, userInfo.getEmployeeId());
        // 总数
        Integer count = pushApprovalRequestDao.countPushApprovalByCondition(pushQueryPageCondition);
        if (count <= 0) {
            pushApprovalPageDTO.setPageInfo(pageInfo);
            pushApprovalPageDTO.setPushApprovalInfoDTOList(Collections.emptyList());
            return pushApprovalPageDTO;
        }
        // 分页条件
        pushQueryPageCondition.setPageNo((pageInfo.getPageNo() - 1) * pageInfo.getPageSize());
        pushQueryPageCondition.setPageSize(pageInfo.getPageSize());
        pageInfo.setTotalCount(count);
        pageInfo.compute(count);
        pushApprovalPageDTO.setPageInfo(pageInfo);
        // 集合数据
        pushApprovalPageDTO.setPushApprovalInfoDTOList(Safes.of(
            pushApprovalRequestDao.getPushApprovalByCondition(pushQueryPageCondition)
                .stream()
                .map(entity -> PushApprovalInfoDTO.builder()
                    .title(entity.getTitle())
                    .pushTime(DateUtil.formatDate(
                        Objects.equals(entity.getPushState(), PushApprovalOfPushStateEnum.PUSHED.getCode()) ?
                            entity.getUpdateTime() : entity.getPushTime(), DATE_SECOND_FORMAT))
                    .processStateName(ApproveStateEnum.getInstance(entity.getProcessState())
                        .getName())
                    .processState(entity.getProcessState())
                    .audienceIdList(Joiner.on("、")
                        .skipNulls()
                        .join(PushApprovalOfAudienceEnum.getAudienceListByCodes(entity.getPushUsers())))
                    .requestId(entity.getRequestId())
                    .showEditButton(isShowEditButton(entity))
                    .showViewNodeButton(isShowViewNodeButton(entity))
                    .showViewDetailButton(isShowViewDetailButton(entity))
                    .showWithdrawButton(isShowWithdrawButton(entity))
                    .build())
                .collect(Collectors.toList())));
        return pushApprovalPageDTO;
    }

    /**
     * 是否可编辑审批
     *
     * @param entity
     * @return
     */
    private boolean isShowWithdrawButton(PushApprovalRequestEntity entity) {
        return !(Objects.equals(ApproveStateEnum.WAIT_SUBMIT.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.RECALL.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.REFUSE.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.PASS.getCode(), entity.getProcessState()));
    }

    /**
     * 是否展示编辑
     *
     * @param entity
     * @return
     */
    private boolean isShowEditButton(PushApprovalRequestEntity entity) {
        return Objects.equals(ApproveStateEnum.WAIT_SUBMIT.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.RECALL.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.REFUSE.getCode(), entity.getProcessState());
    }

    /**
     * 是否展示查看详情按钮
     *
     * @param entity
     * @return
     */
    private boolean isShowViewDetailButton(PushApprovalRequestEntity entity) {
        return Boolean.TRUE;
    }

    /**
     * 是否展示查看节点按钮
     *
     * @param entity
     * @return
     */
    private boolean isShowViewNodeButton(PushApprovalRequestEntity entity) {
        return Objects.equals(ApproveStateEnum.WAIT.getCode(), entity.getProcessState()) || Objects.equals(
            ApproveStateEnum.REFUSE.getCode(), entity.getProcessState());
    }

    /**
     * 模糊查询
     *
     * @param searchValue
     * @return
     */
    public List<PushApprovalPartDTO> getPushApprovalByValue(String searchValue) {
        if (StringUtils.isBlank(searchValue)) {
            ResponseCodeEnum.PARAM_ERROR.throwException();
        }
        return null;
    }

    /**
     * 新增页面受众
     *
     * @return
     */
    public PushApprovalInitDTO initPushApprovalAudience() {
        return PushApprovalInitDTO.builder()
            .definitionKey(pushProcessDefinitionKey)
            .pushApprovalAudienceDTOList(pushApprovalAudienceList)
            .build();
    }

    /**
     * 预览详情
     *
     * @param requestId
     * @return
     */
    public PushApprovalPreviewDTO preview(String requestId) {
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        if (Objects.isNull(pushApprovalRequestEntity)) {
            log.error("preview approval is null, requestId={}", requestId);
            throw new BusinessException(ResponseCodeEnum.RESULT_IS_EMPTY.getMessage(),
                ResponseCodeEnum.RESULT_IS_EMPTY.getCode());
        }
        return PushApprovalPreviewDTO.builder()
            .requestId(requestId)
            .title(pushApprovalRequestEntity.getTitle())
            .content(pushApprovalRequestEntity.getContent())
            .fileList(fileOperationService.batchFilePreview(pushApprovalRequestEntity.getFileUrls()))
            .pushTime(DateUtil.formatDate(pushApprovalRequestEntity.getPushTime(), DATE_FORMAT))
            .build();
    }

    /**
     * 编辑详情
     *
     * @param requestId
     * @return
     */
    public PushApprovalEditDTO getPushApprovalDetail(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            ResponseCodeEnum.APPROVAL_REQUEST_NOT_EXIST_ERROR.throwException();
        }
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        if (Objects.isNull(pushApprovalRequestEntity)) {
            log.error("preview approval is null, requestId={}", requestId);
            throw new BusinessException(ResponseCodeEnum.RESULT_IS_EMPTY.getMessage(),
                ResponseCodeEnum.RESULT_IS_EMPTY.getCode());
        }
        return PushApprovalEditDTO.builder()
            .requestId(requestId)
            .title(pushApprovalRequestEntity.getTitle())
            .content(pushApprovalRequestEntity.getContent())
            .audienceIdList(pushApprovalRequestEntity.getPushUsers())
            .pushTime(DateUtil.formatDate(pushApprovalRequestEntity.getPushTime(), DateUtil.FORMAT_DATE_TIME))
            .showEditButton(isShowEditButton(pushApprovalRequestEntity))
            .showWithdrawButton(isShowWithdrawButton(pushApprovalRequestEntity))
            .background(pushApprovalRequestEntity.getBackground())
            .fileList(fileOperationService.batchFilePreview(pushApprovalRequestEntity.getFileUrls()))
            .build();
    }

    /**
     * 审批流程更新
     *
     * @param approveHandleParam
     * @param approveStateEnum
     * @return
     */
    public Boolean updateApproveFeishu(ApproveHandleParam approveHandleParam, ApproveStateEnum approveStateEnum) {
        //获取当前登录用户
        SysUserEntity userInfo = sysUserService.getByUserId(approveHandleParam.getUserId());

        String employeeId = userInfo.getHrId();
        String requestId = approveHandleParam.getRequestId();
        HashMap<String, Object> variables = Maps.newHashMap();
        variables.put("requestId", requestId);

        Map<String, Object> variablesLocal = Maps.newHashMap();
        variablesLocal.put("reason", approveHandleParam.getReason());
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        switch (approveStateEnum) {
            case RECALL:
                // 判断是否能撤回
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                        pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode()) || Objects.equals(
                        pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.PASS.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                variables.put("processState", approveStateEnum.getCode());
                WithdrawParam withdrawParam = WithdrawParam.builder()
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .userId(employeeId)
                        .variables(variables)
                        .build();
                withdrawParam.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                withdrawParam.setDisplayType(2);
                withdrawParam.setEndOnThisProcessInstance(Boolean.TRUE);
                processRemote.withdraw(withdrawParam);
                updateProcessState(approveStateEnum, requestId);
                break;
            case PASS:
                // 判断是否可以通过
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                        pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                // 调用流程
                variables.put("approve", NumberUtils.INTEGER_ONE);
                variablesLocal.put("approve", NumberUtils.INTEGER_ONE);
                TaskCompleteParam taskCompleteParam = TaskCompleteParam.builder()
                        .assignee(employeeId)
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .taskId(this.getCurrentTaskId(pushApprovalRequestEntity.getProcessId(), requestId, employeeId))
                        .variables(variables)
                        .variablesLocal(variablesLocal)
                        .approveAction(ApproveStateEnum.PASS.getCode())
                        .build();


                taskCompleteParam.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                taskCompleteParam.setDisplayType(2);

                approveFlowService.setFeishuCallBack(taskCompleteParam,pushProcessDefinitionKey);
                processRemote.complete(taskCompleteParam);
                break;
            case REFUSE:
                // 判断是否能拒绝
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                        pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.PASS.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                // 状态
                variables.put("processState", approveStateEnum.getCode());
                variables.put("approve", NumberUtils.INTEGER_ZERO);
                variablesLocal.put("approve", NumberUtils.INTEGER_ZERO);
                TaskCompleteParam taskCompleteParam1 = TaskCompleteParam.builder()
                        .assignee(employeeId)
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .taskId(this.getCurrentTaskId(pushApprovalRequestEntity.getProcessId(), requestId, employeeId))
                        .variables(variables)
                        .variablesLocal(variablesLocal)
                        .approveAction(ApproveStateEnum.REFUSE.getCode())
                        .build();
                taskCompleteParam1.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                taskCompleteParam1.setDisplayType(2);
                approveFlowService.setFeishuCallBack(taskCompleteParam1,pushProcessDefinitionKey);
                processRemote.complete(taskCompleteParam1);
                updateProcessState(approveStateEnum, requestId);
                break;
        }
        return Boolean.TRUE;
    }

    /**
     * 审批流程更新
     *
     * @param approveHandleParam
     * @param approveStateEnum
     * @return
     */
    public Boolean updateApprove(ApproveHandleParam approveHandleParam, ApproveStateEnum approveStateEnum) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        String employeeId = userInfo.getEmployeeId();
        String requestId = approveHandleParam.getRequestId();
        HashMap<String, Object> variables = Maps.newHashMap();
        variables.put("requestId", requestId);

        Map<String, Object> variablesLocal = Maps.newHashMap();
        variablesLocal.put("reason", approveHandleParam.getReason());
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        switch (approveStateEnum) {
            case RECALL:
                // 判断是否能撤回
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                    pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode()) || Objects.equals(
                    pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.PASS.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                variables.put("processState", approveStateEnum.getCode());
                WithdrawParam withdrawParam = WithdrawParam.builder()
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .userId(employeeId)
                        .variables(variables)
                        .build();
                withdrawParam.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                withdrawParam.setDisplayType(2);
                withdrawParam.setEndOnThisProcessInstance(Boolean.TRUE);
                processRemote.withdraw(withdrawParam);
                updateProcessState(approveStateEnum, requestId);
                break;
            case PASS:
                // 判断是否可以通过
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                    pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                // 调用流程
                variables.put("approve", NumberUtils.INTEGER_ONE);
                variablesLocal.put("approve", NumberUtils.INTEGER_ONE);
                TaskCompleteParam taskCompleteParam = TaskCompleteParam.builder()
                        .assignee(employeeId)
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .taskId(this.getCurrentTaskId(pushApprovalRequestEntity.getProcessId(), requestId, employeeId))
                        .variables(variables)
                        .variablesLocal(variablesLocal)
                        .approveAction(ApproveStateEnum.PASS.getCode())
                        .build();


                taskCompleteParam.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                taskCompleteParam.setDisplayType(2);

                approveFlowService.setFeishuCallBack(taskCompleteParam,pushProcessDefinitionKey);
                processRemote.complete(taskCompleteParam);
                break;
            case REFUSE:
                // 判断是否能拒绝
                if (Objects.isNull(pushApprovalRequestEntity) || Objects.equals(
                    pushApprovalRequestEntity.getProcessState(), ApproveStateEnum.PASS.getCode())) {
                    throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                }
                // 状态
                variables.put("processState", approveStateEnum.getCode());
                variables.put("approve", NumberUtils.INTEGER_ZERO);
                variablesLocal.put("approve", NumberUtils.INTEGER_ZERO);
                TaskCompleteParam taskCompleteParam1 = TaskCompleteParam.builder()
                        .assignee(employeeId)
                        .processInstanceId(pushApprovalRequestEntity.getProcessId())
                        .taskId(this.getCurrentTaskId(pushApprovalRequestEntity.getProcessId(), requestId, employeeId))
                        .variables(variables)
                        .variablesLocal(variablesLocal)
                        .approveAction(ApproveStateEnum.REFUSE.getCode())
                        .build();
                taskCompleteParam1.setMobileLink(String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                        FileConfig.approveAppUrl, pushProcessDefinitionKey, pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId()));
                taskCompleteParam1.setDisplayType(2);
                approveFlowService.setFeishuCallBack(taskCompleteParam1,pushProcessDefinitionKey);
                processRemote.complete(taskCompleteParam1);
                updateProcessState(approveStateEnum, requestId);
                break;
        }
        return Boolean.TRUE;
    }

    public void updateProcessState(ApproveStateEnum approveStateEnum, String requestId) {
        pushApprovalRequestDao.updateByPrimaryKeySelective(PushApprovalRequestEntity.builder()
            .requestId(requestId)
            .processState(approveStateEnum.getCode())
            .build());
    }

    public void updatePushState(PushApprovalOfPushStateEnum pushApprovalOfPushStateEnum, String requestId) {
        pushApprovalRequestDao.updateByPrimaryKeySelective(PushApprovalRequestEntity.builder()
            .requestId(requestId)
            .pushState(pushApprovalOfPushStateEnum.getCode())
            .build());
    }

    /**
     * 查询当前处理人对应审批实例的任务
     *
     * @param processId
     * @param requestId
     * @param employeeId
     * @return
     */
    private String getCurrentTaskId(String processId, String requestId, String employeeId) {
        // 完成当前节点
        List<TaskQueryResultDTO> taskQueryResultDTOS = processRemote.toDoList(TaskQueryParam.builder()
            .userId(employeeId)
            .processInstanceId(Lists.newArrayList(processId))
            .build());
        if (CollectionUtils.isEmpty(taskQueryResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getMessage(),
                ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getCode());
        }
        List<TaskInstanceQueryResultDTO> taskInstances = taskQueryResultDTOS.get(NumberUtils.INTEGER_ZERO)
            .getTaskInstances();
        if (CollectionUtils.isEmpty(taskInstances)) {
            throw new BusinessException(ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getMessage(),
                ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getCode());
        }
        TaskInstanceQueryResultDTO taskInstance = Safes.of(taskInstances)
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getBusinessKey(), requestId))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(taskInstance)) {
            throw new BusinessException(ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getMessage(),
                ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getCode());
        }
        List<TaskInfoQueryResultDTO> taskInfos = taskInstance.getTaskInfos();
        if (CollectionUtils.isEmpty(taskInfos)) {
            throw new BusinessException(ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getMessage(),
                ResponseCodeEnum.NO_VALID_TASK_TO_HANDLE.getCode());
        }
        if (taskInfos.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.DUPLICATE_TASK_ERROR.getMessage(),
                ResponseCodeEnum.DUPLICATE_TASK_ERROR.getCode());
        }
        return taskInfos.get(NumberUtils.INTEGER_ZERO)
            .getTaskId();
    }

    /**
     * 审批详情流程进度，节点信息
     *
     * @param requestId
     * @return
     */
    public ApproveRecordOutDTO getApproveDetailRecord(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            ResponseCodeEnum.APPROVAL_REQUEST_NOT_EXIST_ERROR.throwException();
        }
        // 审批详情
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        ProcessInstanceDetailDTO processInstanceDetailDTO =
            processRemote.queryProcessById(pushApprovalRequestEntity.getProcessId());
        List<ProcessActivityDTO> processActivityDTOS = processInstanceDetailDTO.getActivities();
        if (CollectionUtils.isEmpty(processActivityDTOS)) {
            ResponseCodeEnum.PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE.throwException();
        }
        // 流程进度
        ProcessDefinitionDTO processDefinition = processInstanceDetailDTO.getProcessDefinition();
        List<ProcessDefinitionDTO.TaskNode> taskNodes = processDefinition.getTaskNodes();
        List<String> employeeList = taskNodes.stream()
            .map(ProcessDefinitionDTO.TaskNode::getAssignees)
            .reduce(Lists.newArrayList(), (all, item) -> {
                all.addAll(item);
                return all;
            });
        employeeList.addAll(processActivityDTOS.stream()
            .map(e -> e.getAssigneeActivities()
                .stream()
                .map(ProcessActivityDTO.AssigneeActivity::getAssignee)
                .collect(Collectors.toList()))
            .reduce(Lists.newArrayList(), (all, item) -> {
                all.addAll(item);
                return all;
            }));
        Map<String, String> userNameMap = sysUserService.getUserListByHrids(Sets.newHashSet(employeeList))
            .stream()
            .collect(Collectors.toMap(SysUserEntity::getHrId, SysUserEntity::getUserName));

        return ApproveRecordOutDTO.builder()
            .approveProcessDTOList(taskNodes.stream()
                .map(e -> ApprovalWrapper.wrapperApproveProcessVO(e, userNameMap))
                .collect(Collectors.toList()))
            .approveRecordDTOList(processActivityDTOS.stream()
                .map(e -> {
                    ApproveRecordDTO approveRecordDTO = new ApproveRecordDTO();
                    approveRecordDTO.setApproveRecordUserList(e.getAssigneeActivities()
                        .stream()
                        .map(e1 -> ApprovalWrapper.wrapperApproveRecordUserDTO(e, e1, userNameMap))
                        .collect(Collectors.toList()));
                    return approveRecordDTO;
                })
                .collect(Collectors.toList()))
            .build();
    }

    /**
     * 通过流程定义Key判断是否属于推送审批
     *
     * @param processDefinitionKey 流程定义Key
     * @return 是否属于推送审批
     */
    public boolean supportProcessDefinitionKey(String processDefinitionKey) {
        return Objects.equals(pushProcessDefinitionKey, processDefinitionKey);
    }

    /**
     * 审核通过，完成消息推送
     *
     * @param requestId
     * @param processDefinitionKey
     */
    public void passPushApproval(String requestId, String processDefinitionKey) {
        if (!this.supportProcessDefinitionKey(processDefinitionKey)) {
            return;
        }
        PushApprovalRequestEntity pushApprovalRequestEntity = pushApprovalRequestDao.selectByRequestId(requestId);
        if (Objects.isNull(pushApprovalRequestEntity)) {
            log.error("passPushApproval approval is null, requestId={}", requestId);
            throw new BusinessException(ResponseCodeEnum.RESULT_IS_EMPTY.getMessage(),
                ResponseCodeEnum.RESULT_IS_EMPTY.getCode());
        }

        String url = String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
            FileConfig.approveAppUrl, pushApprovalRequestEntity.getProcessDefinitionKey(),
            pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId());
        // 通知发起人审批通过
        List<UserDTO> userDTOS =
            commentHelper.getRbacUserDetail(Sets.newHashSet(pushApprovalRequestEntity.getEmployeeId()));
        corpWechatNotifyService.sendCorpWechatMessage("几木里推送审批", "几木里推送审批完成", url, userDTOS.stream()
            .map(UserDTO::getEmail)
            .collect(Collectors.toList()));

        // 更新审批状态
        PushApprovalRequestEntity approvalRequestEntity = PushApprovalRequestEntity.builder()
            .requestId(requestId)
            .processState(ApproveStateEnum.PASS.getCode())
            .build();
        if (DateUtil.getCurrentDateTime()
            .after(pushApprovalRequestEntity.getPushTime())) {
            approvalRequestEntity.setPushState(PushApprovalOfPushStateEnum.PUSHED.getCode());
            pushPassApprovalService.pushPassApproval(pushApprovalRequestEntity);
        }
        pushApprovalRequestDao.updateByPrimaryKeySelective(approvalRequestEntity);
    }

    /**
     * 处理推送审批通知下个节点人
     *
     * @param taskNotifyTodoUserMessage
     */
    public void handlePushApproval(TaskNotifyTodoUserMessage taskNotifyTodoUserMessage) {
        if (!this.supportProcessDefinitionKey(taskNotifyTodoUserMessage.getProcessDefinitionKey())) {
            return;
        }
        log.info("推送审批通知下个节点人 taskNotifyTodoUserMessage={}", ObjectUtil.toJsonQuietly(taskNotifyTodoUserMessage));
        ApproveStateEnum approveStateEnum = ApproveStateEnum.getInstance(taskNotifyTodoUserMessage.getApproveAction());
        PushApprovalRequestEntity pushApprovalRequestEntity =
            pushApprovalRequestDao.selectByRequestId(taskNotifyTodoUserMessage.getBusinessKey());
        if (Objects.isNull(pushApprovalRequestEntity)) {
            log.error("推送审批通知下个节点人 审批信息不存在");
            return;
        }
        log.info("pushApprovalRequestEntity = {}", ObjectUtil.toJsonQuietly(pushApprovalRequestEntity));
        String title = "几木里推送审核";
        String content = "";
        // 通知发起人审批通过
        List<String> employeeIdList = taskNotifyTodoUserMessage.getEmployeeIdList();
        List<UserDTO> rbacUserList = Lists.newArrayList();

        switch (approveStateEnum) {
            case REFUSE:
                content = "您有花田推送内容审批被拒绝，请及时前往OMS处理";
                rbacUserList.addAll(
                    commentHelper.getRbacUserDetail(Sets.newHashSet(taskNotifyTodoUserMessage.getAssignee())));
                break;
            case PASS:
                content = "您收到一个新的几木里推送审批，请及时处理";
                rbacUserList.addAll(commentHelper.getRbacUserDetail(Sets.newHashSet(employeeIdList)));
                break;
            default:
                log.error("handlePushApproval state error", ObjectUtil.toJsonQuietly(taskNotifyTodoUserMessage));
                break;
        }

        if (CollectionUtils.isEmpty(rbacUserList)) {
            log.error("handlePushApproval User email error taskNotifyTodoUserMessage={}",
                ObjectUtil.toJsonQuietly(taskNotifyTodoUserMessage));
            return;
        }

        // https://qa-approve2.corp.at-our.com/pushDetail?processDefinitionKey=push_approval&businessKey=202008060300003
        String url = String.format("%s/pushDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
            FileConfig.approveAppUrl, pushApprovalRequestEntity.getProcessDefinitionKey(),
            pushApprovalRequestEntity.getRequestId(), pushApprovalRequestEntity.getProcessId());
        corpWechatNotifyService.sendCorpWechatMessage(title, content, url, Lists.newArrayList(rbacUserList.stream()
            .map(UserDTO::getEmail)
            .collect(Collectors.toList())));

    }

    /**
     * 申请详情
     *
     * @param param
     * @return
     */
    public PushApprovalDetailDTO detail(ApprovalDetailParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo)) {
            userInfo = folioTransModifyService.tryGetAssoUserInfoDTO(param.getUserId());
        }
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new com.atour.hotel.framework.exception.BusinessException("请先登录",
                ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        PushApprovalRequestEntity pushApprovalRequestEntity =
            pushApprovalRequestDao.selectByRequestId(param.getRequestId());
        if (Objects.isNull(pushApprovalRequestEntity)) {
            return null;
        }
        List<Integer> pushUsers = pushApprovalRequestEntity.getPushUsers();
        List<String> list = Safes.of(pushApprovalAudienceList)
            .stream()
            .filter(e -> pushUsers.contains(e.getId()))
            .map(PushApprovalAudienceDTO::getAudienceName)
            .collect(Collectors.toList());
        boolean showApprovalButton = false;
        if (!StringUtils.equalsIgnoreCase(pushApprovalRequestEntity.getEmployeeId(), userInfo.getEmployeeId())
            && !Objects.equals(ApproveStateEnum.PASS.getCode(), pushApprovalRequestEntity.getProcessState())
            && StringUtils.isNotBlank(
            getCurrentTaskId(pushApprovalRequestEntity.getProcessId(), pushApprovalRequestEntity.getRequestId(),
                pushApprovalRequestEntity.getProcessDefinitionKey(), userInfo.getEmployeeId()))) {
            showApprovalButton = Boolean.TRUE;
        }
        return PushApprovalDetailDTO.builder()
            .requestId(pushApprovalRequestEntity.getRequestId())
            .background(pushApprovalRequestEntity.getBackground())
            .title(pushApprovalRequestEntity.getTitle())
            .content(pushApprovalRequestEntity.getContent())
            .audience(Joiner.on("、")
                .skipNulls()
                .join(list))
            .fileList(fileOperationService.batchFilePreview(pushApprovalRequestEntity.getFileUrls()))
            .showApprovalButton(showApprovalButton)
            .pushTime(DateUtil.formatDate(pushApprovalRequestEntity.getPushTime(), DATE_SECOND_FORMAT))
            .processState(pushApprovalRequestEntity.getProcessState())
            .build();
    }

    private String getCurrentTaskId(String processId, String businessKey, String processDefinitionKey,
        String employeeId) {
        // 完成当前节点
        List<TaskQueryResultDTO> taskQueryResultDTOS = processRemote.toDoList(TaskQueryParam.builder()
            .userId(employeeId)
            .processInstanceId(Lists.newArrayList(processId))
            .build());
        TaskQueryResultDTO taskResultInfo = Safes.of(taskQueryResultDTOS)
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getProcessDefinitionKey(), processDefinitionKey))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(taskResultInfo)) {
            return null;
        }

        TaskInstanceQueryResultDTO taskInstance = Safes.of(taskResultInfo.getTaskInstances())
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getBusinessKey(), businessKey))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(taskInstance)) {
            return null;
        }
        List<TaskInfoQueryResultDTO> taskInfos = taskInstance.getTaskInfos();
        if (CollectionUtils.isEmpty(taskInfos)) {
            return null;
        }
        if (taskInfos.size() > 1) {
            return null;
        }
        return taskInfos.get(NumberUtils.INTEGER_ZERO)
            .getTaskId();
    }

    public List<InnerFolioTransModifyDTO> selectByRequestIdList(List<String> businessKeyList) {
        if (CollectionUtils.isEmpty(businessKeyList)) {
            return Collections.emptyList();
        }
        List<PushApprovalRequestEntity> pushApprovalRequestEntities =
            pushApprovalRequestDao.selectByRequestIdList(businessKeyList);
        if (CollectionUtils.isEmpty(pushApprovalRequestEntities)) {
            return Collections.emptyList();
        }
        return pushApprovalRequestEntities.stream()
            .map(FolioTransModifyWrapper::buildFolioTransModifyDTO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 未推送审核通过查询
     *
     * @param pushState
     * @param approvalState
     * @return
     */
    public List<PushApprovalRequestEntity> getPushApprovalPassAndNotPush(int pushState, Integer approvalState) {
        return pushApprovalRequestDao.getPushApprovalPassAndNotPush(pushState, approvalState);
    }
}