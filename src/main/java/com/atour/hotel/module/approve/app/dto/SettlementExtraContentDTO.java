package com.atour.hotel.module.approve.app.dto;

import com.atour.hotel.module.approve.app.request.ARTransInfo;
import com.atour.hotel.module.approve.app.request.ActivityInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 结算中心申请扩展对象
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "SettlementExtraContentDTO", description = "结算中心申请扩展对象")
public class SettlementExtraContentDTO implements Serializable {

    /**
     * 照片列表
     */
    @ApiModelProperty(value = "照片列表")
    private List<FilePreviewDTO> imageOssKeyList;

    /**
     * 对应的活动信息
     */
    @ApiModelProperty(value = "对应的活动信息")
    private List<ActivityInfo> activityInfos;

    /**
     * 账单id
     */
    @ApiModelProperty(value = "账单id")
    private Integer billId;
}
