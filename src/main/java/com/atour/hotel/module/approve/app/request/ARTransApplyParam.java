package com.atour.hotel.module.approve.app.request;

import com.atour.hotel.module.approve.app.dto.FilePreviewDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 调账请求参数
 *
 * <AUTHOR>
 * @date 2020/4/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "FolioTransApplyParam", description = "调账申请参数")
public class ARTransApplyParam implements Serializable {
    /**
     * AR转账信息
     */
    @ApiModelProperty(value = "AR转账信息")
    @NotNull(message = "AR转账信息不能为空")
    @Size(
            max = 1000
    )
    private List<ARTransInfo> arTransInfos;

    /**
     * 转出的会员id
     */
    @NotNull(message = "转出的会员id不能为空")
    @ApiModelProperty(value = "转出的会员id")
    private Long originMebId;

    /**
     * 转出的账户名称
     */
    @NotNull(message = "转出的账户名称不能为空")
    @ApiModelProperty(value = "转出的账户名称")
    private String originMebName;

    /**
     * 转入的会员id
     */
    @NotNull(message = "转入的会员id不能为空")
    @ApiModelProperty(value = "转入的会员id")
    private Long transferMebId;

    /**
     * 转入的账户名称
     */
    @NotNull(message = "转入的账户名称不能为空")
    @ApiModelProperty(value = "转入的账户名称")
    private String transferMebName;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Integer userId;
    /**
     * 调账申请ID
     */
    @ApiModelProperty(value = "调账申请ID")
    private String requestId;

    /**
     * 酒店id
     */
    @NotNull(message = "酒店id不能为空")
    @ApiModelProperty(value = "酒店id")
    private Integer chainId;

    /**
     * 房单id
     */
    @NotNull(message = "房单id不能为空")
    @ApiModelProperty(value = "房单id")
    private Long folioId;

    /**
     * 审批类目  1"重新开账" 2, "房费调整" 3, "特殊退款" 4, "冲减" 5, "转账" 6 悬单调账
     * @see com.atour.dicts.db.atour_pms.FolioTransApproveTypeEnum
     */
    @NotNull(message = "请选择审批类型")
    @ApiModelProperty(value = "调账审批类目")
    private Integer approveType;

    /**
     * 转移原因
     */
    @NotNull(message = "请选择转移原因")
    @ApiModelProperty(value = "转移原因")
    @Min(1)
    private Integer transferReason;

    /**
     * 申请原因
     */
    @NotBlank(message = "申请原因不能为空或超200字")
    @ApiModelProperty(value = "申请原因")
    @Length(max = 200,message = "申请原因不能为空或超200字")
    private String reason;

    /**
     * 照片列表
     */
    @NotNull(message = "至少上传一张图片")
    @ApiModelProperty(value = "照片列表")
    @Valid
    private List<FilePreviewDTO> imageOssKeyList;

    /**
     * 结算金额
     */
    @ApiModelProperty(value = "结算金额")
    @NotNull(message = "账单金额不能为空")
    private BigDecimal creditAmount;
}
