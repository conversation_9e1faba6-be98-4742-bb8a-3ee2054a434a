package com.atour.hotel.module.approve.app.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * AR审批完成后回调pms 参数
 * @author: junbo
 * @date: 2021年7月7日15:01:45
 */
@Valid
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalFinishTransferParam implements Serializable {

    /**
     * ar账务主键
     */
    @NotNull(message = "ar账务主键不能为空")
    private List<Integer> arTransIds;

    /**
     * 转入的会员id
     */
    @NotNull(message = "转入会员id不能为空")
    private Long transferMebId;

    @NotNull(message = "申请人")
    private String applyEmployeeId;

    @NotNull(message = "非法请求")
    private String token;

}
