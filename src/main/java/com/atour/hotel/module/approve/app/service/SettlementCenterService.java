package com.atour.hotel.module.approve.app.service;

import com.aliyun.openservices.ons.api.Message;
import com.atour.activity.api.dto.*;
import com.atour.activity.api.param.*;
import com.atour.activity.api.remote.CommonProcessRemote;
import com.atour.activity.api.remote.ProcessRemote;
import com.atour.activity.common.bean.ApproveLogEntity;
import com.atour.activity.common.constant.ActivitiConstants;
import com.atour.activity.common.enums.ApproveProjectIndexEnum;
import com.atour.activity.common.enums.ApproveStateEnum;
import com.atour.activity.mq.TaskNotifyTodoUserMessage;
import com.atour.api.bean.ApiResult;
import com.atour.asso.api.dto.AssoUserInfoDTO;
import com.atour.asso.client.thread.AssoCommonParamsManager;
import com.atour.chain.api.chain.dto.AtourChainInfoDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.db.redis.RedisClient;
import com.atour.db.redis.RedisLock;
import com.atour.dicts.db.atour_oms.SettlementApproveTypeEnum;
import com.atour.dicts.db.center.sys_message.MessageSourceEnum;
import com.atour.dicts.db.center.sys_message.SysMessageTypeEnum;
import com.atour.dicts.db.common.DeletedEnum;
import com.atour.dicts.enums.hotel_manage.MsgTypeEnum;
import com.atour.franchise.enums.ActivityTypeEnum;
import com.atour.franchise.remote.ActivityRemote;
import com.atour.hotel.common.constants.RedisContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.lock.LockKeyFactory;
import com.atour.hotel.common.page.Pager;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.approve.app.dto.FilePreviewDTO;
import com.atour.hotel.module.approve.app.dto.NextApproverDTO;
import com.atour.hotel.module.approve.app.dto.SettlementExtraContentDTO;
import com.atour.hotel.module.approve.app.enums.ActivityAuditStateEnum;
import com.atour.hotel.module.approve.app.request.*;
import com.atour.hotel.module.approve.app.response.FolioTransSubmitApprovalDTO;
import com.atour.hotel.module.approve.app.response.SettlementDetailDTO;
import com.atour.hotel.module.approve.app.response.SettlementModifyDTO;
import com.atour.hotel.module.approve.enums.ApproveQueryTypeEnum;
import com.atour.hotel.module.approve.enums.ArOperationTypeEnum;
import com.atour.hotel.module.approve.web.trans.service.ApproveFlowService;
import com.atour.hotel.module.common.service.*;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.dkf.web.report.service.CommService;
import com.atour.hotel.module.hotel.utils.LocalDateUtil;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.hotel.persistent.pms.dao.SettlementModifyRequestMapper;
import com.atour.hotel.persistent.pms.entity.SettlementModifyRequestEntity;
import com.atour.hotel.persistent.pms.param.SettlementListParam;
import com.atour.monitor.AMonitor;
import com.atour.notify.api.enums.JPushNotifyUrlTypeEnum;
import com.atour.notify.api.enums.JPushPlatformEnum;
import com.atour.notify.api.enums.JPushSceneTypeEnum;
import com.atour.notify.api.enums.JPushTypeEnum;
import com.atour.notify.api.params.AllChannelPushParam;
import com.atour.notify.api.params.JPushPersonalParam;
import com.atour.rbac.api.param.GetEmployeeByRoleIdParam;
import com.atour.rbac.api.remote.RbacUserRemote;
import com.atour.rbac.api.response.*;
import com.atour.security.Base64Util;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yaduo.resource.service.api.oss.dto.response.OssPreviewListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算中心申请 service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class SettlementCenterService {
    /**
     * 审批编号自增num默认位数
     */
    private final static int DEFAULT_APPROVAL_SIZE = 5;
    /**
     * 我的审批查询全部申请标识
     */
    private static final String REQUESTID_ALL = "-1";

    /**
     * 审批流程定义key
     */
    private static final List<String> approveProcessDefinitionKeys =
            ImmutableList.of(SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey());

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SettlementModifyRequestMapper settlementModifyRequestMapper;
    @Resource
    private RedisLock lockRedis;
    @Resource
    private RedisClient redisClient;
    @Resource
    private ProcessRemote processRemote;
    @Resource
    private ChainService chainService;
    @Resource
    private CommentHelper commentHelper;

    /*@Resource
    private OssRemote ossRemote;*/

    private CommonOssService commonOssService;

    @Resource
    private FolioTransModifyService folioTransModifyService;
    @Resource
    private CommonProcessRemote commonProcessRemote;
    @Resource
    private RegionChainService regionChainService;
    @Resource
    private ChainRemote chainRemote;
    @Resource
    private RbacUserRemote rbacUserRemote;
    @Resource
    private ActivityRemote activityRemote;
    @Resource
    private PushJpushService pushJpushService;
    @Resource
    private SysMessageService sysMessageService;
    @Resource
    private CorpWechatNotifyService corpWechatNotifyService;
    /**
     * 公共服务: 公共服务
     */
    @Autowired
    private CommService commService;
    //需要特殊处理的酒店id
    //动态增加抄送人
    @ApolloJsonValue("${ar.approve.supplement.copyUserList}")
    private List<String> copyUserList;

    @ApolloJsonValue("${supplement.copyUserList.role.set:[]}")
    private volatile Set<Integer> copyRoleSet;
    @Value("${oms.web.host}")
    private String omsWebHost;

    @Resource
    private ApproveFlowService approveFlowService;
    /**
     * 提交申请
     */
    public FolioTransSubmitApprovalDTO submitApprove(SettlementActivityParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = verifyUserInfo(param.getUserId());
        //校验申请条件
        this.verifyParam(param);

        //审批锁
        String lockKey = LockKeyFactory.getSettlementAddLockKey(param.getChainId(), param.getBillId());
        RLock lock = lockRedis.getLock(lockKey);
        if (lock == null || !lock.tryLock()) {
            log.info("--settlement submitApprove lock FAIL , chainId={},billNo={}", param.getChainId(), param.getBillNo());
            AMonitor.meter("settlement_submitApprove_conflict");
            throw new BusinessException(ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getMessage(),
                ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getCode());
        }
        try {
            // 审批编号生成 年月日+系统编号+4位自增数（每日自增数自0001开始计数）
            String formatDate = DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.FORMAT_DATE_NUMBER);
            String key = RedisContants.SETTLEMETN_CENTER_ADJUST_KEY + formatDate;
            Long lastApprovalNum = redisClient.incryBy(key, NumberUtils.LONG_ONE);
            if (lastApprovalNum.equals(NumberUtils.LONG_ONE)) {
                redisClient.expire(key, RedisContants.FOLIO_TRANS_ADJUST_KEY_EXPIRE_TIME_IN_SECONDS);
            }
            String requestId = formatDate + ApproveProjectIndexEnum.OMS.getIndex() + CommonUtils.fill(lastApprovalNum,
                DEFAULT_APPROVAL_SIZE, DEFAULT_APPROVAL_SIZE);
            //全局变量
            Map<String, Object> variables = Maps.newHashMap();
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.WAIT_SUBMIT.getName());
            variables.put(ActivitiConstants.VAR_NAME_PARAM_CHAIN_ID, param.getChainId());
            variables.put("reason", param.getReason());

            // 发起流程申请
            StartProcessParam startProcessParam = StartProcessParam.builder()
                .userId(userInfo.getEmployeeId())
                .businessKey(requestId)
                .processDefinitionKey(SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey())
                .variables(variables)
                .copyToUsers(this.getCopyUserList(param.getChainId(), userInfo.getEmployeeId()))
                .build();

            String url = omsWebHost+"/approvalManage/ctripApproval/ctripApprovalList";
            startProcessParam.setPcLink(url);
            startProcessParam.setDisplayType(2);
            LinkedHashMap<String, String> linkedHashMap = bulidFeishu(param.getChainId(),StringUtils.isNotEmpty(param.getBillNo())? param.getBillNo():"");
            if(linkedHashMap!=null && linkedHashMap.size()>0){
                startProcessParam.setFormData(linkedHashMap);
            }
            approveFlowService.setFeishuCallBack(startProcessParam,SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey());
            StartProcessResultDTO startProcessResultDTO = processRemote.startProcess(startProcessParam);
            if (startProcessResultDTO == null || StringUtils.isEmpty(startProcessResultDTO.getProcessInstanceId())) {
                log.error("settlement submitApprove start process error, 传入参数param:{}", ObjectUtil.toJsonQuietly(param));
                throw new BusinessException("提交申请失败，请重新提交", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
            }

            //保存
            SettlementModifyRequestEntity settlementModifyRequestEntity = SettlementModifyRequestEntity.convert(param, requestId, userInfo ,ApproveStateEnum.WAIT_SUBMIT.getCode(),
                    startProcessResultDTO.getProcessInstanceId(), SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey());
            settlementModifyRequestMapper.insertSelective(settlementModifyRequestEntity);

            settlementModifyRequestEntity.setProcessState(ApproveStateEnum.WAIT.getCode());
            //完成当前节点
            this.completeCurrentNode(settlementModifyRequestEntity, userInfo.getEmployeeId(), ApproveStateEnum.WAIT.getCode(),
                Boolean.TRUE);

            //修改流程状态->待审批
            settlementModifyRequestMapper.updateByPrimaryKeySelective(SettlementModifyRequestEntity.builder()
                .id(settlementModifyRequestEntity.getId())
                .processState(ApproveStateEnum.WAIT.getCode())
                .build());

            return FolioTransSubmitApprovalDTO.builder()
                .requestId(requestId)
                .build();
        } finally {
            lock.unlock();
            log.info("settlement submitApprove unlock, chainId={},billNo={},billDayStart={},billDayEnd={}", param.getChainId(), param.getBillNo(), param.getBillDayStart(), param.getBillDayEnd());
        }
    }

    private AssoUserInfoDTO verifyUserInfo(Integer userId){
        //获取当前登录用户
        AssoUserInfoDTO userInfo = null;
        //内部调用会传userId
        if (Objects.nonNull(userId)) {
            SysUserEntity sysUserEntity = sysUserService.getByUserId(userId);
            if (Objects.isNull(sysUserEntity)) {
                throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
            }
            userInfo = new AssoUserInfoDTO();
            userInfo.setUserId(sysUserEntity.getUserID());
            userInfo.setUserCode(sysUserEntity.getUserCode());
            userInfo.setUserName(sysUserEntity.getUserName());
            userInfo.setEmployeeId(sysUserEntity.getHrId());
        } else {
            userInfo = AssoCommonParamsManager.get();
        }

        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }

        return userInfo;
    }

    /**
     * 重新提交申请
     */
    public FolioTransSubmitApprovalDTO resubmitApprove(SettlementActivityParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = verifyUserInfo(param.getUserId());

        if (StringUtils.isBlank(param.getRequestId())) {
            throw new BusinessException("申请id不能为空", ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        SettlementModifyRequestEntity oldSettlementModifyRequestEntity = Safes.of(settlementModifyRequestMapper.selectBySelective(
                SettlementModifyRequestEntity.builder()
                        .requestId(param.getRequestId())
                        .build()))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(oldSettlementModifyRequestEntity)) {
            throw new BusinessException("未找到对应的申请信息", ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        param.setChainId(oldSettlementModifyRequestEntity.getChainId());
        //校验调账申请条件
        this.verifyParam(param);

        if (!StringUtils.equalsIgnoreCase(oldSettlementModifyRequestEntity.getEmployeeId(), userInfo.getEmployeeId())) {
            throw new BusinessException("只有申请本人可以重新提交调账申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        //非撤回状态不能重新提交
        if (!Objects.equals(oldSettlementModifyRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode())
                && !Objects.equals(oldSettlementModifyRequestEntity.getProcessState(), ApproveStateEnum.WAIT_SUBMIT.getCode())) {
            throw new BusinessException("当前状态不能重新提交申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //调账审批锁
        String lockKey = LockKeyFactory.getSettlementAddLockKey(param.getChainId(), param.getBillId());
        RLock lock = lockRedis.getLock(lockKey);
        if (lock == null || !lock.tryLock()) {
            log.info("settlement resubmitApprove lock FAIL, chainId={},billNo={}", param.getChainId(), param.getBillNo());
            AMonitor.meter("settlement resubmitApprove_conflict");
            throw new BusinessException(ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getMessage(),
                    ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getCode());
        }
        try {
            //保存
            SettlementModifyRequestEntity settlementModifyRequestEntity = SettlementModifyRequestEntity.builder()
                    .id(oldSettlementModifyRequestEntity.getId())
                    .processState(ApproveStateEnum.WAIT.getCode())
                    .reason(param.getReason())
                    .extraContent(JsonUtils.toJson(SettlementExtraContentDTO.builder()
                            .imageOssKeyList(param.getImageOssKeyList())
                            .activityInfos(param.getActivityInfos())
                            .build()))
                    .build();

            //修改申请信息
            settlementModifyRequestMapper.updateByPrimaryKeySelective(settlementModifyRequestEntity);
            oldSettlementModifyRequestEntity.setProcessState(ApproveStateEnum.WAIT.getCode());
            oldSettlementModifyRequestEntity.setReason(param.getReason());
            oldSettlementModifyRequestEntity.setRemark(param.getRemark());
            //完成当前节点
            this.completeCurrentNode(oldSettlementModifyRequestEntity, userInfo.getEmployeeId(), ApproveStateEnum.WAIT.getCode(),
                    Boolean.TRUE);

            return FolioTransSubmitApprovalDTO.builder()
                    .requestId(param.getRequestId())
                    .build();
        } finally {
            lock.unlock();
            log.info("settlement resubmitApprove unlock, chainId={},billNo={}", param.getChainId(), param.getBillNo());
        }
    }


    /**
     * 申请详情
     */
    public SettlementDetailDTO detail(FolioTransDetailParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo)) {
            userInfo = tryGetAssoUserInfoDTO(param.getUserId());
        }
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException("请先登录", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //查询结算中心申请详情
        SettlementModifyRequestEntity settlementModifyRequestEntity = Safes.of(settlementModifyRequestMapper.selectBySelective(
                SettlementModifyRequestEntity.builder()
                        .requestId(param.getRequestId())
                        .deleted(DeletedEnum.UNDELETED.getCode())
                        .build()))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(settlementModifyRequestEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //图片信息
        List<FilePreviewDTO> ossKeyList = Lists.newArrayList();
        List<ActivityInfo> activityInfos = Lists.newArrayList();
        if (StringUtils.isNotBlank(settlementModifyRequestEntity.getExtraContent())) {
            SettlementExtraContentDTO settlementExtraContentDTO =
                    JsonUtils.parseObject(settlementModifyRequestEntity.getExtraContent(), SettlementExtraContentDTO.class);

            activityInfos = settlementExtraContentDTO.getActivityInfos();

            if (CollectionUtils.isEmpty(activityInfos)){
                throw new BusinessException("活动信息不存在", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
            }

            List<FilePreviewDTO> imageOssKeyList = settlementExtraContentDTO.getImageOssKeyList();
            if (CollectionUtils.isNotEmpty(imageOssKeyList)) {
                /*OssBatchPreviewParam queryBuild = OssBatchPreviewParam.builder()
                        .ossKey(Safes.of(imageOssKeyList)
                                .stream()
                                .map(FilePreviewDTO::getOssKey)
                                .collect(Collectors.toList()))
                        .build();
                OssBatchPreviewDTO ossBatchPreviewDTO = ossRemote.batchPreview(queryBuild);*/
                // 获取图片预览url
                final List<OssPreviewListDTO> ossPreviewListDTOS = commonOssService.previewByOssKeyList(Safes.of(imageOssKeyList)
                        .stream()
                        .map(FilePreviewDTO::getOssKey)
                        .collect(Collectors.toList()), null);

                Map<String, String> filePreviewRespMap = Safes.of(ossPreviewListDTOS)
                        .stream()
                        .collect(Collectors.toMap(OssPreviewListDTO::getOssKey, OssPreviewListDTO::getShowUrl));

                Safes.of(imageOssKeyList)
                        .forEach(f -> {
                            f.setShowUrl(Objects.isNull(filePreviewRespMap.get(f.getOssKey())) ? StringUtils.EMPTY :
                                    filePreviewRespMap.get(f.getOssKey()));
                        });
            }
            ossKeyList.addAll(imageOssKeyList);
        }

        // 取用户花名
        SysUserEntity sysUserEntity = sysUserService.getUserByEmployeeId(settlementModifyRequestEntity.getEmployeeId());
        String flowerName = null;
        if (Objects.nonNull(sysUserEntity)) {
            flowerName = sysUserEntity.getUserName();
        }
        //操作类型 ： 0:查看 1: 有审核权限 2：撤回：3重新提交
        Integer operationType = ArOperationTypeEnum.READ.getValue();
        if (StringUtils.equalsIgnoreCase(settlementModifyRequestEntity.getEmployeeId(), userInfo.getEmployeeId())) {
            //可撤回
            if (Objects.equals(settlementModifyRequestEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())
                    && StringUtils.equalsIgnoreCase(settlementModifyRequestEntity.getLastApproveId(), userInfo.getEmployeeId())) {

                if (this.checkApproveState(settlementModifyRequestEntity, userInfo)) {
                    operationType = ArOperationTypeEnum.AUDIT_AND_WITHDRAW.getValue();
                }else {
                    operationType = ArOperationTypeEnum.WITHDRAW.getValue();
                }
            }
            if (this.checkApproveState(settlementModifyRequestEntity, userInfo)) {
                //可重新提交: 已撤回&&申请人本人
                if (Objects.equals(settlementModifyRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode())
                        && Objects.equals(userInfo.getUserId(), settlementModifyRequestEntity.getUserId())) {
                    operationType = ArOperationTypeEnum.RESUBMIT_APPROVE.getValue();
                }
            }
        } else {
            //有审核权限
            if (this.checkApproveState(settlementModifyRequestEntity, userInfo)) {
                operationType = ArOperationTypeEnum.AUDIT.getValue();
            }
        }
        return buildDetailInfo(settlementModifyRequestEntity,flowerName, ossKeyList, activityInfos, operationType);
    }

    /**
     * 我的审批列表
     *
     * @return
     */
    public Pager<SettlementModifyDTO> adjustList(SettlementActivityListParam param) {

        List<SettlementModifyDTO> resultList = Lists.newArrayList();
        //获取当前登录用户信息
        UserPermissionDTO userPermission = CommonParamsManager.getLocalUserHotel();
        if (Objects.isNull(userPermission)) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                    ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        List<Integer> chainIds = param.getChainIds();
        if (CollectionUtils.isEmpty(chainIds) && StringUtils.isNotBlank(param.getRegionId())) {
            chainIds = regionChainService.getChainIdsByDeptId(param.getRegionId());
        }
        //默认查询当前用户rbac所关联的所有门店
        if (CollectionUtils.isEmpty(chainIds)) {
            chainIds = userPermission.getChainList();
        }
        Pair<String, String> datePair = this.checkAdjustTransParam(param);
        String beginDate = datePair.getLeft();
        String endDate = datePair.getRight();

        //获取我的任务列表
        Set<String> myRequestIds;
        Set<String> myRequestIds2 = Sets.newHashSet();
        //我审批的列表特殊处理，返回待处理和已处理的信息
        if (ApproveQueryTypeEnum.MY_APPROVE_TODO.getValue().equals(param.getQueryType()) || ApproveQueryTypeEnum.MY_APPROVE_DONE.getValue().equals(param.getQueryType())){
            param.setQueryType(ApproveQueryTypeEnum.MY_APPROVE_TODO.getValue());
            myRequestIds = this.getMyCommonTask(param);
            param.setQueryType(ApproveQueryTypeEnum.MY_APPROVE_DONE.getValue());
            myRequestIds2 = this.getMyCommonTask(param);

            if (CollectionUtils.isNotEmpty(myRequestIds) && CollectionUtils.isNotEmpty(myRequestIds2)){
                myRequestIds2.removeAll(myRequestIds);
            }

            if (CollectionUtils.isEmpty(myRequestIds) && CollectionUtils.isEmpty(myRequestIds2)) {
                return Pager.builder(resultList)
                        .current(param.getPageNo())
                        .size(param.getPageSize())
                        .total(com.atour.common.versionlock.org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO)
                        .create();
            }
        } else {
            myRequestIds = this.getMyCommonTask(param);
            if (CollectionUtils.isEmpty(myRequestIds)) {
                return Pager.builder(resultList)
                        .current(param.getPageNo())
                        .size(param.getPageSize())
                        .total(com.atour.common.versionlock.org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO)
                        .create();
            }
        }

        Pair<Integer, Integer> pageParam = CommonUtils.getPageBetween(param.getPageNo(), param.getPageSize());
        SettlementListParam qureyParam = SettlementListParam.builder()
                .requestId(param.getRequestId())
                .chainIds(chainIds)
                .beginDate(beginDate)
                .endDate(endDate)
                .approveStates(param.getApproveStates())
                .pageStart(pageParam.getLeft())
                .pageEnd(pageParam.getRight())
                .billNo(param.getBillNo())
                .build();
        List<SettlementModifyRequestEntity> dataList = Lists.newArrayList();
        if (myRequestIds.contains(REQUESTID_ALL)) {
            //总条数
            int totalNum = settlementModifyRequestMapper.selectSettlementListCount(qureyParam);
            List<SettlementModifyRequestEntity> reqlist =
                    settlementModifyRequestMapper.selectSettlementList(qureyParam);
            if (CollectionUtils.isNotEmpty(reqlist)) {
                dataList.addAll(reqlist);
            }
            return Pager.builder(this.buildARTransModifyDTO(dataList))
                    .current(param.getPageNo())
                    .size(param.getPageSize())
                    .total(totalNum)
                    .create();
        } else {
            //分批查询
            qureyParam.setPageEnd(null);
            qureyParam.setPageStart(null);
            if (Objects.equals(param.getQueryType(), ApproveQueryTypeEnum.MY_APPROVE_TODO.getValue()) || Objects.equals(param.getQueryType(),ApproveQueryTypeEnum.MY_APPROVE_DONE.getValue())){
                if (CollectionUtils.isNotEmpty(myRequestIds)){
                    this.getDataListByPage(myRequestIds, dataList, qureyParam);
                }

                if (CollectionUtils.isNotEmpty(myRequestIds2)){
                    this.getDataListByPage(myRequestIds2, dataList, qureyParam);
                }
            } else {
                this.getDataListByPage(myRequestIds, dataList, qureyParam);
            }

            //逻辑分页
            List<SettlementModifyRequestEntity> pageDataList =
                    CommonUtils.pageLogic(dataList, param.getPageNo(), param.getPageSize());
            return Pager.builder(this.buildARTransModifyDTO(pageDataList))
                    .current(param.getPageNo())
                    .size(param.getPageSize())
                    .total(dataList.size())
                    .create();
        }

    }

    /**
     * 撤回审批
     */
    public void recallApprove(FolioTransApproveHandleParam param) {
        if (StringUtils.isBlank(param.getReason())) {
            throw new BusinessException("备注不能为空", ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        //获取当前登录用户
        AssoUserInfoDTO userInfo = verifyUserInfo(param.getUserId());
        //查询调账申请详情
        SettlementModifyRequestEntity settlementModifyRequestEntity = Safes.of(settlementModifyRequestMapper.selectBySelective(
                SettlementModifyRequestEntity.builder()
                        .requestId(param.getRequestId())
                        .deleted(DeletedEnum.UNDELETED.getCode())
                        .build()))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(settlementModifyRequestEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        if (!StringUtils.equalsIgnoreCase(userInfo.getEmployeeId(), settlementModifyRequestEntity.getEmployeeId())) {
            log.warn(ResponseCodeEnum.FOLIO_TRANS_NOT_SELF_ERROR.getMessage() + ", user.eid: {}, entity.eid: {}",
                    userInfo.getEmployeeId(), settlementModifyRequestEntity.getEmployeeId());
            throw new BusinessException(ResponseCodeEnum.FOLIO_TRANS_NOT_SELF_ERROR.getMessage(),
                    ResponseCodeEnum.FOLIO_TRANS_NOT_SELF_ERROR.getCode());
        }
        // 判断是否能撤回  审批拒绝、审批通过、审批过期状态 不可撤回
        if (Objects.equals(settlementModifyRequestEntity.getProcessState(), ApproveStateEnum.RECALL.getCode()) || Objects.equals(
                settlementModifyRequestEntity.getProcessState(), ApproveStateEnum.PASS.getCode())) {
            throw new BusinessException("当前状态不能撤回申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //保存申请信息
        SettlementModifyRequestEntity newSettlementEntity = SettlementModifyRequestEntity.builder()
                .id(settlementModifyRequestEntity.getId())
                .processState(ApproveStateEnum.RECALL.getCode())
                .build();

        // 设置变量
        Map<String, Object> variables = Maps.newHashMap();
        variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, newSettlementEntity.getProcessState());

        WithdrawParam withdrawParam = WithdrawParam.builder()
                .processInstanceId(settlementModifyRequestEntity.getProcessId())
                .userId(userInfo.getEmployeeId())
                .variables(variables)
                .approveLogEntity(new ApproveLogEntity(userInfo.getEmployeeId(),
                        Objects.nonNull(ApproveStateEnum.getInstance(newSettlementEntity.getProcessState())) ?
                                ApproveStateEnum.getInstance(newSettlementEntity.getProcessState())
                                        .getName() : StringUtils.EMPTY, param.getReason(),
                        DateUtil.printDateTime(DateUtil.getCurrentDateTime())))
                .approveAction(ApproveStateEnum.RECALL.getCode())
                .build();

        String url = String.format("%s/accountDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                FileConfig.approveAppUrl, SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey(), settlementModifyRequestEntity.getRequestId(),
                settlementModifyRequestEntity.getProcessId());
        withdrawParam.setMobileLink(url);
        withdrawParam.setDisplayType(2);
        LinkedHashMap<String, String> linkedHashMap = bulidFeishu(settlementModifyRequestEntity.getChainId(),StringUtils.isNotEmpty(settlementModifyRequestEntity.getBillNo())? settlementModifyRequestEntity.getBillNo():"");
        if(linkedHashMap!=null && linkedHashMap.size()>0){
            withdrawParam.setFormData(linkedHashMap);
        }
        withdrawParam.setEndOnThisProcessInstance(Boolean.TRUE);
        WithdrawResultDTO withdrawResultDTO = processRemote.withdraw(withdrawParam);

        //获取下一节点审批人
        if (Objects.nonNull(withdrawResultDTO) && Objects.nonNull(withdrawResultDTO.getStartTask())) {
            NextApproverDTO nextInfo = NextApproverDTO.builder()
                    .approverList(
                            getApproveUserList(settlementModifyRequestEntity.getProcessDefinitionKey(), settlementModifyRequestEntity.getProcessId(),
                                    settlementModifyRequestEntity.getRequestId()))
                    .taskName(withdrawResultDTO.getStartTask()
                            .getTaskName())
                    .build();
            newSettlementEntity.setNextApprover(ObjectUtil.toJsonQuietly(nextInfo));
        }
        //修改申请信息
        settlementModifyRequestMapper.updateByPrimaryKeySelective(newSettlementEntity);
        //通知结算中心
        this.callBackFanchise(settlementModifyRequestEntity.getRequestId(), ActivityAuditStateEnum.FAILED.getValue());
    }

    /**
     * 通过审批
     */
    public void passApprove(FolioTransApproveHandleParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo)) {
            userInfo = tryGetAssoUserInfoDTO(param.getUserId());
        }
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        //查询调账申请详情
        SettlementModifyRequestEntity settlementModifyRequestEntity = Safes.of(settlementModifyRequestMapper.selectBySelective(
                SettlementModifyRequestEntity.builder()
                        .requestId(param.getRequestId())
                        .deleted(DeletedEnum.UNDELETED.getCode())
                        .build()))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(settlementModifyRequestEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        if (!Objects.equals(settlementModifyRequestEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
            throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //保存审批通过信息
        SettlementModifyRequestEntity newSettlementEntity = SettlementModifyRequestEntity.builder()
                .id(settlementModifyRequestEntity.getId())
                .processState(ApproveStateEnum.WAIT.getCode())
                .build();

        //修改申请信息
        settlementModifyRequestMapper.updateByPrimaryKeySelective(newSettlementEntity);

        settlementModifyRequestEntity.setProcessState(ApproveStateEnum.PASS.getCode());
        settlementModifyRequestEntity.setRemark(param.getReason());

        //审批通过
        this.completeCurrentNode(settlementModifyRequestEntity, userInfo.getEmployeeId(), ApproveStateEnum.PASS.getCode(),
                Boolean.FALSE);

    }

    /**
     * 拒绝审批
     */
    public void refuseApprove(FolioTransApproveHandleParam param) {
        if (StringUtils.isBlank(param.getReason())) {
            throw new BusinessException("备注不能为空", ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();
        if (Objects.isNull(userInfo)) {
            userInfo = tryGetAssoUserInfoDTO(param.getUserId());
        }
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        //查询调账申请详情
        SettlementModifyRequestEntity settlementEntity = Safes.of(settlementModifyRequestMapper.selectBySelective(
                SettlementModifyRequestEntity.builder()
                        .requestId(param.getRequestId())
                        .deleted(DeletedEnum.UNDELETED.getCode())
                        .build()))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(settlementEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        if (!Objects.equals(settlementEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
            throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //保存申请信息
        SettlementModifyRequestEntity newSettlementEntity = SettlementModifyRequestEntity.builder()
                .id(settlementEntity.getId())
                .processState(ApproveStateEnum.REFUSE.getCode())
                .build();

        settlementEntity.setProcessState(ApproveStateEnum.REFUSE.getCode());
        settlementEntity.setRemark(param.getReason());
        //审批拒绝
        this.completeCurrentNode(settlementEntity, userInfo.getEmployeeId(), ApproveStateEnum.REFUSE.getCode(),
                Boolean.FALSE);
        //修改申请信息
        settlementModifyRequestMapper.updateByPrimaryKeySelective(newSettlementEntity);

        //通知结算中心
        this.callBackFanchise(settlementEntity.getRequestId(), ActivityAuditStateEnum.FAILED.getValue());

    }

    public void getDataListByPage(Set<String> myRequestIds, List<SettlementModifyRequestEntity> dataList, SettlementListParam qureyParam){
        List<List<String>> myRequestIdsBatch = Lists.partition(Lists.newArrayList(myRequestIds), 1000);
        qureyParam.setPageEnd(null);
        qureyParam.setPageStart(null);
        myRequestIdsBatch.forEach(b -> {
            qureyParam.setRequestIds(Sets.newHashSet(b));
            List<SettlementModifyRequestEntity> reqlist =
                    settlementModifyRequestMapper.selectSettlementList(qureyParam);
            if (CollectionUtils.isNotEmpty(reqlist)) {
                dataList.addAll(reqlist);
            }
        });
    }

    /**
     * 组装列表数据
     *
     * @param entityList
     * @return
     */
    public List<SettlementModifyDTO> buildARTransModifyDTO(List<SettlementModifyRequestEntity> entityList) {
        List<SettlementModifyDTO> resultList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(entityList)) {
            return resultList;
        }
        List<Integer> chainIds = entityList.stream()
                .map(SettlementModifyRequestEntity::getChainId)
                .collect(Collectors.toList());
        // 酒店名称
        List<AtourChainInfoDTO> chainInfoDTOList = chainRemote.getChainInfoByIdList(Sets.newHashSet(chainIds))
                .getResult();
        Map<Integer, String> chainMap = Safes.of(chainInfoDTOList)
                .stream()
                .collect(Collectors.toMap(AtourChainInfoDTO::getChainId, AtourChainInfoDTO::getName));
        // 区域名称
        Map<String, String> deptNameMap = commService.getDeptNameMapByChainIdList(chainIds);

        //申请人id
        List<String> employeeIds = entityList.stream()
                .map(SettlementModifyRequestEntity::getEmployeeId)
                .distinct()
                .collect(Collectors.toList());
        List<SysUserEntity> userList = Lists.newArrayList();
        List<List<String>> partitionEmployeeIds = Lists.partition(employeeIds, 1000);
        partitionEmployeeIds.forEach(ids -> {
            List<SysUserEntity> dataList = sysUserService.getUserListByHrids(Sets.newHashSet(ids));
            if (CollectionUtils.isNotEmpty(dataList)) {
                userList.addAll(dataList);
            }
        });

        Map<String, String> userNameMap = userList.stream()
                .collect(Collectors.toMap(SysUserEntity::getHrId, SysUserEntity::getUserName));

        Safes.of(entityList)
                .forEach(f -> {
                    // 酒店名称
                    String chainName = chainMap.get(f.getChainId());
                    // 区域名称
                    String region = deptNameMap.get(String.valueOf(f.getChainId()));
                    //审批状态
                    ApproveStateEnum approveStateEnum = ApproveStateEnum.getInstance(f.getProcessState());

                    chainName = "[" + f.getChainId() +"]" +(StringUtils.isBlank(chainName) ? "" : chainName);

                    SettlementModifyDTO dto = buildDTO(f, region, chainName, approveStateEnum, userNameMap);
                    //下一审批人
                    if (StringUtils.isNotBlank(f.getNextApprover())) {
                        NextApproverDTO nextApproverDTO =
                                ObjectUtil.fromJsonQuietly(f.getNextApprover(), NextApproverDTO.class);
                        if(StringUtils.isNotBlank(nextApproverDTO.getTaskName())){
                            dto.setNextApprover(String.format("%s(%s)", nextApproverDTO.getTaskName(),
                                    StringUtils.join(Safes.of(nextApproverDTO.getApproverList()), ",")));
                        }
                    }
                    resultList.add(dto);
                });

        return resultList;

    }

    /**
     * 封装SettlementModifyDTO
     * @param f
     * @param region
     * @param chainName
     * @param approveStateEnum
     * @param userNameMap
     * @return
     */
    private SettlementModifyDTO buildDTO(SettlementModifyRequestEntity f, String region, String chainName,
                                                      ApproveStateEnum approveStateEnum, Map<String, String> userNameMap){
        return SettlementModifyDTO.builder()
                .requestId(f.getRequestId())
                .regionName(region)
                .chainName(chainName)
                .amount(f.getAmount())
                .billNo(f.getBillNo())
                .approveState(f.getProcessState())
                .approveStateName(Objects.isNull(approveStateEnum) ? StringUtils.EMPTY : approveStateEnum.getName())
                .createUserName(userNameMap.get(f.getEmployeeId()))
                .createTime(DateUtil.formatDate(f.getCreateTime(), DateUtil.FORMAT_DATE_TIME))
                .build();

    }

    /**
     * 获取我的任务列表
     *
     * @return
     */
    public Set<String> getMyCommonTask(SettlementActivityListParam param) {
        //获取当前登录用户信息
        UserPermissionDTO userPermission = CommonParamsManager.getLocalUserHotel();
        if (Objects.isNull(userPermission)) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                    ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        ApproveQueryTypeEnum typeEnum = ApproveQueryTypeEnum.getInstance(param.getQueryType());
        if (Objects.isNull(typeEnum)) {
            throw new BusinessException("查询审批类型错误");
        }
        Set<String> requestIds = Sets.newHashSet();
        List<CommonTaskQueryResultDTO> taskList = Lists.newArrayList();
        CommonTaskQueryParam queryParam = CommonTaskQueryParam.builder()
                .userId(userPermission.getHrId())
                .processDefinitionKeyList(approveProcessDefinitionKeys)
                .beginDate(DateUtil.printDate(CommonUtils.LocalDateToDate(
                        CommonUtils.convertStr2LocalDate(param.getBeginDate())
                                .plusDays(-1))))
                .endDate(DateUtil.printDate(CommonUtils.LocalDateToDate(
                        CommonUtils.convertStr2LocalDate(param.getEndDate())
                                .plusDays(1))))
                .businessKey(param.getRequestId())
                .offset(com.atour.common.versionlock.org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO)
                .pageSize(5000)
                .build();
        switch (typeEnum) {
            case ALL:
                requestIds.add(REQUESTID_ALL);
                break;
            case MY_START:
                taskList = commonProcessRemote.startList(queryParam);
                break;
            case MY_APPROVE_TODO:
                taskList = commonProcessRemote.todoList(queryParam);
                break;
            case MY_APPROVE_DONE:
                taskList = commonProcessRemote.doneList(queryParam);
                break;
            case COPY_TO_ME:
                taskList = commonProcessRemote.copyToList(queryParam);
                break;
            default:
                break;
        }
        if (CollectionUtils.isEmpty(taskList)) {
            return requestIds;
        }
        requestIds.addAll(taskList.stream()
                .map(CommonTaskQueryResultDTO::getBusinessKey)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet()));
        return requestIds;
    }

    /**
     * 完成当前节点
     *
     * @param settlementModifyRequestEntity
     * @return
     */
    private String completeCurrentNode(SettlementModifyRequestEntity settlementModifyRequestEntity, String assignee,
        Integer approveAction, Boolean isFirstSubmit) {
        //获取流程当前的任务id
        String taskId = this.getCurrentTaskId(settlementModifyRequestEntity, assignee);
        Integer processState = settlementModifyRequestEntity.getProcessState();

        // 设置流程变量
        Map<String, Object> variables = Maps.newHashMap();
        Map<String, Object> variablesLocal = Maps.newHashMap();

        // 区分拒绝与通过
        ApproveStateEnum approveStateEnum = ApproveStateEnum.getInstance(processState);
        String approveStateName = StringUtils.EMPTY;
        if (Objects.equals(ApproveStateEnum.REFUSE, approveStateEnum)) {
            variables.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_REFUSE);
            variablesLocal.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_REFUSE);
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.REFUSE.getCode());
        } else {
            variables.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);
            variablesLocal.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.WAIT.getCode());
        }

        // 重新发起流程申请
        approveStateName = approveStateEnum.getName();
        //首次发起
        if (isFirstSubmit) {
            approveStateName = "提交";
        }

        TaskCompleteParam taskCompleteParam = TaskCompleteParam.builder()
                .assignee(assignee)
                .processInstanceId(settlementModifyRequestEntity.getProcessId())
                .taskId(taskId)
                .variables(variables)
                .variablesLocal(variablesLocal)
                .approveLogEntity(new ApproveLogEntity(assignee, approveStateName, settlementModifyRequestEntity.getRemark(),
                        DateUtil.printDateTime(DateUtil.getCurrentDateTime())))
                .approveAction(approveAction)
                .build();

        String url = String.format("%s/accountDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                FileConfig.approveAppUrl, SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey(), settlementModifyRequestEntity.getRequestId(),
                settlementModifyRequestEntity.getProcessId());
        taskCompleteParam.setMobileLink(url);
        taskCompleteParam.setDisplayType(2);
        LinkedHashMap<String, String> linkedHashMap = bulidFeishu(settlementModifyRequestEntity.getChainId(),StringUtils.isNotEmpty(settlementModifyRequestEntity.getBillNo())? settlementModifyRequestEntity.getBillNo():"");
        if(linkedHashMap!=null && linkedHashMap.size()>0){
            taskCompleteParam.setFormData(linkedHashMap);
        }
        approveFlowService.setFeishuCallBack(taskCompleteParam,SettlementApproveTypeEnum.XIECHENG.getProcessDefinitionKey());

        TaskCompleteResultDTO taskCompleteResultDTO = processRemote.complete(taskCompleteParam);
        if (taskCompleteResultDTO == null || StringUtils.isEmpty(taskCompleteResultDTO.getProcessInstanceId())) {
            log.error("--completeCurrentNode error, param:{}", ObjectUtil.toJsonQuietly(settlementModifyRequestEntity));
            throw new BusinessException("提交审批失败", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }

        log.info("completeCurrentNode approveAction={},taskCompleteResultDTO={}", approveAction,
            ObjectUtil.toJsonQuietly(taskCompleteResultDTO));

        if (Objects.nonNull(settlementModifyRequestEntity.getId())) {
            //获取下一节点审批人
            TaskInfoQueryResultDTO nextTask = Safes.of(taskCompleteResultDTO.getNextTaskInfos())
                .stream()
                .findFirst()
                .orElse(new TaskInfoQueryResultDTO());

            NextApproverDTO nextInfo = NextApproverDTO.builder()
                .approverList(
                    getApproveUserList(settlementModifyRequestEntity.getProcessDefinitionKey(), settlementModifyRequestEntity.getProcessId(),
                            settlementModifyRequestEntity.getRequestId()))
                .taskName(nextTask.getTaskName())
                .build();
            //保存下一节点审批人
            settlementModifyRequestMapper.updateByPrimaryKeySelective(SettlementModifyRequestEntity.builder()
                .id(settlementModifyRequestEntity.getId())
                .nextApprover(ObjectUtil.toJsonQuietly(nextInfo))
                .build());
        }

        return taskId;
    }

    /**
     * 获取流程当前的任务id
     *
     * @param settlementModifyRequestEntity
     * @return
     */
    private String getCurrentTaskId(SettlementModifyRequestEntity settlementModifyRequestEntity, String employeeId) {
        // 完成当前节点
        List<TaskQueryResultDTO> taskQueryResultDTOS = processRemote.toDoList(TaskQueryParam.builder()
            .userId(employeeId)
            .processInstanceId(Lists.newArrayList(settlementModifyRequestEntity.getProcessId()))
            .build());
        TaskQueryResultDTO taskResultInfo = Safes.of(taskQueryResultDTOS)
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getProcessDefinitionKey(),settlementModifyRequestEntity.getProcessDefinitionKey()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(taskResultInfo)) {
            throw new BusinessException("提交审批失败,无对应任务列表", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        TaskInstanceQueryResultDTO taskInstance = Safes.of(taskResultInfo.getTaskInstances())
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getBusinessKey(), settlementModifyRequestEntity.getRequestId()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(taskInstance)) {
            throw new BusinessException("提交审批失败,无对应流程实例信息", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        List<TaskInfoQueryResultDTO> taskInfos = taskInstance.getTaskInfos();
        if (CollectionUtils.isEmpty(taskInfos)) {
            throw new BusinessException("提交审批失败,无对应任务信息", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        if (taskInfos.size() > 1) {
            throw new BusinessException("提交审批失败,出现多个流程任务", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        return taskInfos.get(NumberUtils.INTEGER_ZERO)
            .getTaskId();
    }


    /**
     * 封装详情返回数据
     * @param settlementModifyRequestEntity
     * @param flowerName
     * @param ossKeyList
     * @param activityInfos
     * @param operationType
     * @return
     */
    private SettlementDetailDTO buildDetailInfo(SettlementModifyRequestEntity settlementModifyRequestEntity, String flowerName,
                                                List<FilePreviewDTO> ossKeyList,List<ActivityInfo> activityInfos,
                                                Integer operationType){
        return SettlementDetailDTO.builder()
                .requestId(settlementModifyRequestEntity.getRequestId())
                .amount(settlementModifyRequestEntity.getAmount())
                .processDefinitionKey(settlementModifyRequestEntity.getProcessDefinitionKey())
                .chainId(settlementModifyRequestEntity.getChainId())
                .chainName(chainService.getChainNameByChainId(settlementModifyRequestEntity.getChainId()))
                .approveState(settlementModifyRequestEntity.getProcessState())
                .approveStateName(Objects.nonNull(ApproveStateEnum.getInstance(settlementModifyRequestEntity.getProcessState())) ?
                        ApproveStateEnum.getInstance(settlementModifyRequestEntity.getProcessState())
                                .getName() : StringUtils.EMPTY)
                .userId(String.valueOf(settlementModifyRequestEntity.getUserId()))
                .userName(flowerName)
                .employeeId(settlementModifyRequestEntity.getEmployeeId())
                .reason(settlementModifyRequestEntity.getReason())
                .reasonName(ActivityTypeEnum.codeOf(settlementModifyRequestEntity.getReason()).getDesc())
                .remark(settlementModifyRequestEntity.getRemark())
                .imageOssKeyList(ossKeyList)
                .activityInfos(activityInfos)
                .operationType(operationType)
                .billDayStart(LocalDateUtil.convertLocalDate2Str(settlementModifyRequestEntity.getBillDateStart(), LocalDateUtil.YYYYMMDD))
                .billDayEnd(LocalDateUtil.convertLocalDate2Str(settlementModifyRequestEntity.getBillDateEnd(), LocalDateUtil.YYYYMMDD))
                .approveLogList(folioTransModifyService.getApproveLogList(settlementModifyRequestEntity.getProcessDefinitionKey(), settlementModifyRequestEntity.getProcessId(),
                                settlementModifyRequestEntity.getRequestId()))
                .createTime(Objects.isNull(settlementModifyRequestEntity.getCreateTime()) ? StringUtils.EMPTY :
                        DateUtil.printDateTime(settlementModifyRequestEntity.getCreateTime()))
                .build();
    }


    /**
     * 校验申请条件
     */
    public void verifyParam(SettlementActivityParam param) {
        if (Safes.of(param.getImageOssKeyList())
            .size() > 9) {
            throw new BusinessException("最多上传9张图片", ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        // 调用资源中心checkFile校验前端文件是否上传成功,未上传成功直接抛异常
        Safes.of(param.getImageOssKeyList()).stream()
                .forEach(filePreviewDTO -> {
                    commonOssService.checkFileExist(filePreviewDTO.getOssKey(),null);
                });
        if (CollectionUtils.isEmpty(param.getActivityInfos())){
            throw new BusinessException("对应的活动信息不存在", ResponseCodeEnum.PARAM_ERROR.getCode());
        }
    }

    /**
     * 获取抄送人员 employeeIds
     *
     * @param chainId
     * @return
     */
    public List<String> getCopyUserList(Integer chainId, String applyEmployeeId) {
        List<String> resultList = Lists.newArrayList();
        //申请人
        resultList.add(applyEmployeeId);
        HotelManagerDTO hotelManagerDto = commentHelper.getManagerByChainId(chainId);
        if (Objects.isNull(hotelManagerDto)) {
            return resultList;
        }
        //现长
        List<String> xianzhangList = Safes.of(hotelManagerDto.getXianzhangList())
            .stream()
            .map(HotelManagerInfoDTO::getEmployeeId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(xianzhangList)) {
            resultList.addAll(xianzhangList);
        }
        //省长
        List<String> shengzhangList = Safes.of(hotelManagerDto.getShengzhangList())
                .stream()
                .map(HotelManagerInfoDTO::getEmployeeId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shengzhangList)) {
            resultList.addAll(shengzhangList);
        }
        //城区销售经理
        List<String> chengquxiaoshoujingliList = Safes.of(hotelManagerDto.chengquxiaoshoujingliRoles)
                .stream()
                .map(HotelManagerInfoDTO::getEmployeeId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chengquxiaoshoujingliList)) {
            resultList.addAll(chengquxiaoshoujingliList);
        }
        //增加特许对账，船雪提供
        if (CollectionUtils.isNotEmpty(copyUserList)){
            resultList.addAll(copyUserList);
        }
        //获取门店财务
        if (CollectionUtils.isNotEmpty(copyRoleSet)){
            GetEmployeeByRoleIdParam build = GetEmployeeByRoleIdParam.builder()
                    .chainId(chainId)
                    .roleIds(copyRoleSet)
                    .build();
            List<RoleChainEmployeeDTO> emloyeeIds = Optional
                    .ofNullable(rbacUserRemote.getChainEmployeeByRoleIds(build))
                    .map(ApiResult::getResult)
                    .orElse(null);
            if (CollectionUtils.isNotEmpty(emloyeeIds)){
                resultList.addAll(emloyeeIds.stream().map(RoleChainEmployeeDTO::getEmployeeId).collect(Collectors.toList()));
            }
        }
        return resultList;
    }

    /**
     * 回调结算中心
     * @return
     */
    public boolean callBackFanchise(String requestId, Integer state){
        try {
            ApiResult<Boolean> booleanApiResult = activityRemote.auditResult(requestId, state);
            if (Objects.isNull(booleanApiResult)){
                log.error("调用franchise接口出现错误, result={}", ObjectUtil.toJsonQuietly(booleanApiResult));
                throw new com.atour.hotel.framework.exception.BusinessException("调用franchise接口出现错误");
            }

            if (!Objects.equals(booleanApiResult.getCode(), ApiResult.DEFAULT_SUCCEED_CODE)){
                log.error("通知franchise失败:requestId:{}", requestId);
                AMonitor.meter("remote_auditResult_result_error");
            }

        }catch (com.atour.hotel.framework.exception.BusinessException e) {
            log.error("调用franchise接口出现业务异常:requestId:{},e:{}",requestId,e);
            AMonitor.meter("remote_auditResult_bus_error");
        } catch (Exception e) {
            log.error("调用franchise接口出现异常:requestId:{},e:{}",requestId,e);
            AMonitor.meter("remote_auditResult_exception_error");
        }
        return Boolean.TRUE;
    }

    /**
     * 通过审批
     * @param settlementModifyRequestEntity
     */
    public void updateSettlement(SettlementModifyRequestEntity settlementModifyRequestEntity){
        //保存申请信息
        SettlementModifyRequestEntity settlementEntity = SettlementModifyRequestEntity.builder()
                .id(settlementModifyRequestEntity.getId())
                .processState(ApproveStateEnum.PASS.getCode())
                .build();

        settlementModifyRequestMapper.updateByPrimaryKeySelective(settlementEntity);
    }

    /**
     * 是否有权限审批/撤回/重新提交操作权限
     *
     * @param settlementModifyRequestEntity
     * @param userInfo
     * @return
     */
    public boolean checkApproveState( SettlementModifyRequestEntity settlementModifyRequestEntity,
        AssoUserInfoDTO userInfo) {
        try {
            String taskId = this.getCurrentTaskId(settlementModifyRequestEntity, userInfo.getEmployeeId());
            if (StringUtils.isNotBlank(taskId)) {
                return Boolean.TRUE;
            }
        } catch (BusinessException ignored) {
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }

    /**
     * 使用用户ID获取当前用户信息
     *
     * @param userId 用户Id
     * @return 用户信息
     */
    public AssoUserInfoDTO tryGetAssoUserInfoDTO(Integer userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        SysUserEntity sysUserEntity = sysUserService.getByUserId(userId);
        if (Objects.isNull(sysUserEntity)) {
            return null;
        }
        AssoUserInfoDTO userInfo = new AssoUserInfoDTO();
        userInfo.setUserId(sysUserEntity.getUserID());
        userInfo.setUserCode(sysUserEntity.getUserCode());
        userInfo.setUserName(sysUserEntity.getUserName());
        userInfo.setEmployeeId(sysUserEntity.getHrId());
        return userInfo;
    }

    /**
     * 获取下一节点审批人
     *
     * @return
     */
    public List<String> getApproveUserList(String processDefinitionKey, String processId, String requestId) {
        List<CommonLogInfoDTO> logList = commonProcessRemote.logList(CommonLogListParam.builder()
            .processDefinitionKey(processDefinitionKey)
            .businessKey(requestId)
            .processInstanceId(processId)
            .showNextTask(Boolean.TRUE)
            .build());
        CommonLogInfoDTO dto = Safes.of(logList)
            .stream()
            .filter(f -> StringUtils.equalsIgnoreCase(f.getOpType(), CommonLogInfoDTO.SPEC_OP_TYPE_NEXT_TASK))
            .findFirst()
            .orElse(new CommonLogInfoDTO());
        if (StringUtils.isNotBlank(dto.getRemark())) {
            return Lists.newArrayList(dto.getRemark()
                .split(","));
        }

        return Collections.emptyList();
    }

    /**
     * 校验时间
     *
     * @return Pair<开始时间 ， 结束时间>
     */
    public Pair<String, String> checkAdjustTransParam(SettlementActivityListParam param) {
        LocalDateTime startTime =
                LocalDateTime.of(CommonUtils.convertStr2LocalDate(param.getBeginDate()), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(CommonUtils.convertStr2LocalDate(param.getEndDate()), LocalTime.MAX);
        //计算相差天数
        int dayNum = CommonUtils.daysBetweenByDate(CommonUtils.LocalDateTimeToDate(endTime),
                CommonUtils.LocalDateTimeToDate(startTime));
        // 查全部限制180天，查其他限制61天
        if (Objects.equals(param.getQueryType(), ApproveQueryTypeEnum.ALL.getValue())) {
            if (dayNum > 180) {
                throw new BusinessException("查询时间跨度不能大于180天");
            }
        } else {
            if (dayNum > 61) {
                throw new BusinessException("查询时间跨度不能大于61天");
            }
        }
        String beginDate = CommonUtils.convertLocalDateTime2Str(startTime);
        String endDate = CommonUtils.convertLocalDateTime2Str(endTime);
        return Pair.of(beginDate, endDate);
    }


    /**
     * 推送审批消息
     */
    public void pushSettlementMessage(SettlementModifyRequestEntity settlementModifyRequestEntity, Integer processState,
                                      Set<String> receiveEmployeeIds) {
        log.info("--pushSettlementMessage processState={},receiveEmployeeIds={}", processState, ObjectUtil.toJsonQuietly(receiveEmployeeIds));
        //参数校验
        if (Objects.isNull(settlementModifyRequestEntity.getChainId()) || Objects.isNull(processState)
                || CollectionUtils.isEmpty(receiveEmployeeIds)) {
            log.warn("--pushSettlementMessage 参数缺失");
            return;
        }
        ApproveStateEnum approveStateEnum = ApproveStateEnum.getInstance(processState);
        if (Objects.isNull(approveStateEnum) || (!Objects.equals(approveStateEnum, ApproveStateEnum.WAIT)
                && !Objects.equals(approveStateEnum, ApproveStateEnum.PASS) && !Objects.equals(approveStateEnum,
                ApproveStateEnum.REFUSE))) {
            return;
        }
        String chainName = chainService.getChainNameByChainId(settlementModifyRequestEntity.getChainId());

        //获取员工id
        List<SysUserEntity> userList = sysUserService.getUserListByHrids(receiveEmployeeIds);
        List<UserDTO> rbacUserList = commentHelper.getRbacUserDetail(receiveEmployeeIds);
        Map<Integer, String> userMap = Safes.of(userList)
                .stream()
                .collect(Collectors.toMap(SysUserEntity::getUserID, SysUserEntity::getHrId, (k1, k2) -> k1));
        Map<String, String> rbacUserMap = Safes.of(rbacUserList)
                .stream()
                .collect(Collectors.toMap(UserDTO::getEmployeeId, UserDTO::getEmail, (k1, k2) -> k1));
        //userId和email map
        Map<Integer, String> userEmailMap = Maps.newHashMap();
        Safes.of(userMap)
                .forEach((k, v) -> {
                    userEmailMap.put(k, rbacUserMap.get(v));
                });
        Safes.of(userEmailMap)
                .forEach((userId, email) -> {
                    this.pushMessage(settlementModifyRequestEntity, approveStateEnum, userId, email, chainName);
                });

        //推送企业微信消息
        String url = String.format("%s/accountDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                FileConfig.approveAppUrl, settlementModifyRequestEntity.getProcessDefinitionKey(), settlementModifyRequestEntity.getRequestId(),
                settlementModifyRequestEntity.getProcessId());
        Pair<String, String> titelContenPair = this.buildPushConeten(settlementModifyRequestEntity, approveStateEnum, chainName);
        corpWechatNotifyService.sendCorpWechatMessage(titelContenPair.getRight(), titelContenPair.getLeft(), url,
                Lists.newArrayList(rbacUserMap.values()));
    }

    /**
     * 推送消息
     *
     * @param settlementModifyRequestEntity
     * @param approveStateEnum
     * @param userId
     * @param chainName
     */
    public void pushMessage(SettlementModifyRequestEntity settlementModifyRequestEntity, ApproveStateEnum approveStateEnum,
                            Integer userId, String email, String chainName) {
        try {
            Pair<String, String> titelContenPair = this.buildPushConeten(settlementModifyRequestEntity, approveStateEnum, chainName);

            Map<String, String> param = Maps.newHashMapWithExpectedSize(9);
            Map<String, String> notifyParam = Maps.newHashMapWithExpectedSize(3);
            param.put("chainId", String.valueOf(settlementModifyRequestEntity.getChainId()));
            param.put("userId", String.valueOf(userId));
            param.put("requestId", settlementModifyRequestEntity.getRequestId());
            param.put("content", titelContenPair.getRight());
            param.put("title", titelContenPair.getLeft());
            param.put("type", String.valueOf(MsgTypeEnum.ADJUST_TRANS.getCode()));
            param.put("processDefinitionKey", settlementModifyRequestEntity.getProcessDefinitionKey());
            param.put("processInstanceId", settlementModifyRequestEntity.getProcessId());

            String url = String.format("%s/ctripDetail?processDefinitionKey=%s&businessKey=%s&processInstanceId=%s",
                    FileConfig.approveAppUrl, settlementModifyRequestEntity.getProcessDefinitionKey(), settlementModifyRequestEntity.getRequestId(),
                    settlementModifyRequestEntity.getProcessId());
            notifyParam.put("title", Base64Util.encode(titelContenPair.getLeft()));
            notifyParam.put("type", Base64Util.encode(String.valueOf(JPushNotifyUrlTypeEnum.H5.getCode())));
            notifyParam.put("url", Base64Util.encode(url));
            //保存推送消息
            sysMessageService.saveMessage(settlementModifyRequestEntity.getChainId(), userId, ObjectUtil.toJson(param),
                    MessageSourceEnum.ADJUST_TRANS.getValue(), SysMessageTypeEnum.ADJUST_TRANS.getCode());
            //组装极光推送单播消息参数
            AllChannelPushParam allChannelPushParam =
                    pushJpushService.wrapperAdjustTransPushParam(settlementModifyRequestEntity.getChainId(), userId);

            //组装全渠道推送参数
            JPushPersonalParam jPushPersonalParam =
                    pushJpushService.buildCommonJPushParam(Lists.newArrayList(String.valueOf(userId)), notifyParam,
                            titelContenPair.getLeft(), JPushPlatformEnum.ALL.getCode(), JPushTypeEnum.NOTIFY.getCode(),
                            JPushSceneTypeEnum.ADJUST_TRANS_CODE.getCode());

            allChannelPushParam.setJPushPersonalParam(jPushPersonalParam);
            //调用notifyCenter 推送消息
            pushJpushService.pushJpushMessage(allChannelPushParam);

        } catch (Exception e) {
            log.error("--pushSettlementMessage chainId={},billNo={} 推送审批消息出现异常: ", settlementModifyRequestEntity.getChainId(),
                    settlementModifyRequestEntity.getBillNo(), e);
            AMonitor.meter("pushSettlementMessage");
        }
    }

    /**
     * 组装 标题和内容
     *
     * @param settlementModifyRequestEntity
     * @param approveStateEnum
     * @param chainName
     * @return
     */
    public Pair<String, String> buildPushConeten(SettlementModifyRequestEntity settlementModifyRequestEntity,
                                                 ApproveStateEnum approveStateEnum, String chainName) {
        String title = StringUtils.EMPTY;
        String content = StringUtils.EMPTY;
        switch (approveStateEnum) {
            case WAIT:
                title = "审批申请";
                content = String.format("申请门店：%s，账单号%s的活动明细确认", chainName, settlementModifyRequestEntity.getBillNo());
                break;
            case PASS:
                title = "审批通过";
                content = String.format("申请门店：%s，账单号%s的活动明细确认，申请已通过", chainName, settlementModifyRequestEntity.getBillNo());
                break;
            case REFUSE:
                title = "调账拒绝";
                content = String.format("申请门店：%s，账单号%s的活动明细确认，申请已拒绝", chainName, settlementModifyRequestEntity.getBillNo());
                break;
            default:
                break;
        }

        return Pair.of(title, content);
    }


    public LinkedHashMap<String,String>  bulidFeishu(Integer chainId,String billNo){
        if(Objects.isNull(chainId)){
            return null;
        }
        LinkedHashMap<String,String> formData =new LinkedHashMap<>();
        formData.put("申请门店",chainService.getChainNameByChainId(chainId));
        formData.put("账单号",StringUtils.isNotEmpty(billNo)?billNo:"");
        return formData;
    }

    public SettlementModifyRequestEntity getByRequestId(String requestId){
        if (StringUtils.isBlank(requestId)){
            return null;
        }

        return settlementModifyRequestMapper.getByRequestId(requestId);
    }

    /**
     * 完成当前节点时推送
     * @param message
     * @param taskNotifyTodoUserMessage
     */
    public void taskNoify(Message message, TaskNotifyTodoUserMessage taskNotifyTodoUserMessage){
        SettlementModifyRequestEntity byRequestId = this.getByRequestId(taskNotifyTodoUserMessage.getBusinessKey());
        if (Objects.isNull(byRequestId)) {
            log.error("通知待办的消息处理, 申请不存在. messageId={}, message: {}", message.getMsgID(),
                    JsonUtils.toJson(taskNotifyTodoUserMessage));
            AMonitor.meter("taskNotifyTodoUserListener_consume_exception");
        }

        // 更新最后审批人
        if (Objects.nonNull(taskNotifyTodoUserMessage.getAssignee())) {
            settlementModifyRequestMapper.updateByPrimaryKeySelective(SettlementModifyRequestEntity.builder()
                    .id(byRequestId.getId())
                    .lastApproveId(taskNotifyTodoUserMessage.getAssignee())
                    .build());
        }

        // 发送通知
        if (CollectionUtils.isNotEmpty(taskNotifyTodoUserMessage.getEmployeeIdList())) {
            this.pushSettlementMessage(byRequestId,
                    taskNotifyTodoUserMessage.getApproveAction(),
                    com.aliyun.openservices.shade.com.google.common.collect.Sets.newHashSet(taskNotifyTodoUserMessage.getEmployeeIdList()));
        }
    }

}
