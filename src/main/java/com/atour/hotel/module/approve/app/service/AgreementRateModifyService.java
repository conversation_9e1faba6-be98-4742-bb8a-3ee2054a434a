package com.atour.hotel.module.approve.app.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atour.activity.api.dto.CommonLogInfoDTO;
import com.atour.activity.api.dto.StartProcessResultDTO;
import com.atour.activity.api.dto.TaskCompleteResultDTO;
import com.atour.activity.api.dto.TaskInfoQueryResultDTO;
import com.atour.activity.api.dto.TaskInstanceQueryResultDTO;
import com.atour.activity.api.dto.TaskQueryResultDTO;
import com.atour.activity.api.param.*;
import com.atour.activity.api.remote.CommonProcessRemote;
import com.atour.activity.api.remote.ProcessRemote;
import com.atour.activity.common.bean.ApproveLogEntity;
import com.atour.activity.common.constant.ActivitiConstants;
import com.atour.activity.common.enums.ApproveProjectIndexEnum;
import com.atour.activity.common.enums.ApproveStateEnum;
import com.atour.api.bean.ApiResult;
import com.atour.api.bean.ApiSchemaResult;
import com.atour.asso.api.dto.AssoUserInfoDTO;
import com.atour.asso.client.thread.AssoCommonParamsManager;
import com.atour.chain.api.chain.dto.AtourChainDTO;
import com.atour.chain.api.chain.dto.ChainDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.db.redis.RedisClient;
import com.atour.db.redis.RedisLock;
import com.atour.dicts.db.atour_pms.AgreementApproveTypeEnum;
import com.atour.dicts.db.common.DeletedEnum;
import com.atour.galaxy.api.bean.rate_code.RoomRateCodeDTO;
import com.atour.galaxy.api.service.GalaxyRoomRateCodeService;
import com.atour.hotel.common.constants.RedisContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.lock.LockKeyFactory;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.approve.app.dto.AgreementApprovalExtraContentDTO;
import com.atour.hotel.module.approve.app.dto.AgreementAuditReferenceValueDTO;
import com.atour.hotel.module.approve.app.dto.FolioTransApproveLogListDTO;
import com.atour.hotel.module.approve.app.dto.NextApproverDTO;
import com.atour.hotel.module.approve.app.request.AgreementApplyParam;
import com.atour.hotel.module.approve.app.request.AgreementAutoApprovalRequest;
import com.atour.hotel.module.approve.app.request.CrsAgentMemberRequest;
import com.atour.hotel.module.approve.app.request.FolioTransApproveBatchParam;
import com.atour.hotel.module.approve.app.request.FolioTransApproveHandleParam;
import com.atour.hotel.module.approve.app.request.FolioTransDetailParam;
import com.atour.hotel.module.approve.app.request.MemberRateCodeRequest;
import com.atour.hotel.module.approve.app.request.QueryMebRoomRateDetailParam;
import com.atour.hotel.module.approve.app.response.AgreementAutoApprovalDTO;
import com.atour.hotel.module.approve.app.response.AgreementDetailDTO;
import com.atour.hotel.module.approve.app.response.FolioTransSubmitApprovalDTO;
import com.atour.hotel.module.approve.app.response.MebRoomRateDetailDTO;
import com.atour.hotel.module.approve.enums.AgreementPriceTypeEnum;
import com.atour.hotel.module.approve.enums.ApproveWayEnum;
import com.atour.hotel.module.approve.enums.ArOperationTypeEnum;
import com.atour.hotel.module.approve.enums.FeiShuApproveEnum;
import com.atour.hotel.module.approve.web.trans.service.ApproveFlowService;
import com.atour.hotel.module.common.service.CommonOssService;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.inner.folio.dto.InnerAgreementApprovalDTO;
import com.atour.hotel.module.inner.folio.wrapper.AgreementApprovalWrapper;
import com.atour.hotel.module.rbac.service.RbacUserService;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.hotel.persistent.hotel.dao.HotelCommonDao;
import com.atour.hotel.persistent.pms.dao.ApproveFlowBaseMapper;
import com.atour.hotel.persistent.pms.dao.ApproveMebRoomRateRequestExtMapper;
import com.atour.hotel.persistent.pms.entity.ApproveFlowBaseEntity;
import com.atour.hotel.persistent.pms.entity.ApproveMebRoomRateRequestExtEntity;
import com.atour.monitor.AMonitor;
import com.atour.security.Md5Util;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.atour.web.httpclient.AtourRestTemplate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 协议客户绑定房价代码审批 service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AgreementRateModifyService {
    /**
     * 审批编号自增num默认位数
     */
    private final static int DEFAULT_APPROVAL_SIZE = 5;

    @Autowired
    @Qualifier(value = "autoApproveAgreementRateExecutor")
    private ExecutorService autoApproveAgreementRateExecutor;

    @Resource
    private SysUserService sysUserService;


    @Resource
    private RedisLock lockRedis;
    @Resource
    private RedisClient redisClient;
    @Resource
    private ProcessRemote processRemote;
    @Resource
    private CommonProcessRemote commonProcessRemote;
    @Resource
    private ApproveFlowBaseMapper approveFlowBaseMapper;
    @Resource
    private ApproveMebRoomRateRequestExtMapper approveMebRoomRateRequestExtMapper;
    @Resource
    private ChainRemote chainRemote;
    @Resource
    private ChainService chainService;
    @Resource
    private HotelCommonDao hotelCommonDao;
    @Resource
    private GalaxyRoomRateCodeService galaxyRoomRateCodeService;
    @Resource(name = "redirectPmsHttpClient")
    private AtourRestTemplate redirectHttpClient;
    @Value("${crs-url.queryMebRoomRateDetailUrl}")
    private String queryMebRoomRateDetailUrl;

    @Value("${crs-url.checkCanAutoApproval}")
    private String checkCanAutoApprovalUrl;

    @Value("${crs-url.checkCanAutoFloatApproval}")
    private String checkCanAutoFloatApprovalUrl;


    @Value("${crm-url.checkRateCodeValidUrl}")
    private String checkRateCodeValidUrl;
    @Value("${crm-url.callBack}")
    private String callBackUrl;

    /**
     * 协议房价申请url
     */
    @Value("${agreement_room_rate_approve}")
    private String agreementRoomRateApproveUrl;

    @Resource
    private ApproveFlowService approveFlowService;

    @Resource
    private RbacUserService rbacUserService;

    @Resource
    private CommonOssService commonOssService;

    static final Integer TASK_UN_FINISHED = 2;

    private final static Integer NINE_MONTHS = 9;
    private final static Integer SIX_MONTHS = 6;
    private final static Integer THREE_MONTHS = 3;
    private final static Integer FIFTEEN_DAYS = 15;

    /**
     * 提交申请
     */

    public FolioTransSubmitApprovalDTO submitApprove(AgreementApplyParam param) {
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());
        checkRateCodeValid(param);

        // 默认人工审批
        Integer approveWay = ApproveWayEnum.HUMAN.getCode();
        String autoApproveReason = null;
        // 先看前端调用的参数是否传审批方式, 如果传的是自动审批，则调用crs接口判断是自动审批还是手动审批
        if (Objects.nonNull(param.getApproveWay())) {
            if (Objects.equals(param.getApproveWay(), ApproveWayEnum.AUTO.getCode())) {
                // 如果前端传值是自动审批，则需要调用crs接口二次确认是否可以走自动审批
                AgreementAutoApprovalDTO autoApprovalDTO = null;
                if (Objects.equals(param.getPriceType(), AgreementPriceTypeEnum.FIXED_PRICE.getCode())) {
                    autoApprovalDTO = checkCanAutoApprovalFromCrs(AgreementAutoApprovalRequest.builder()
                            .chainId(param.getChainId())
                            .roomRateCode(param.getRateCode())
                            .applyYear(param.getYear())
                            .applyDate(DateUtil.formatDate(new Date(), DateUtil.DATE_FORMAT))
                            .build(), checkCanAutoApprovalUrl);
                }
                // 浮动价格需要调用crs另一个接口判断是否可以自动审批
                if (Objects.equals(param.getPriceType(), AgreementPriceTypeEnum.FLOAT_PRICE.getCode())) {
                    autoApprovalDTO = checkCanAutoApprovalFromCrs(AgreementAutoApprovalRequest.builder()
                            .chainId(param.getChainId())
                            .roomRateCode(param.getRateCode())
                            .applyYear(param.getYear())
                            .applyDate(DateUtil.formatDate(new Date(), DateUtil.DATE_FORMAT))
                            .mebId(param.getMebId())
                            .discount(param.getDiscountRate())
                            .build(), checkCanAutoFloatApprovalUrl);
                }
                if (Objects.nonNull(autoApprovalDTO)) {
                    approveWay = autoApprovalDTO.isAutoApproval() ? ApproveWayEnum.AUTO.getCode() : ApproveWayEnum.HUMAN.getCode();
                    autoApproveReason = autoApprovalDTO.getAutoApprovalMsg();
                }
            }
        }
        // 修改最终判断出来的审批方式 防止下面插入数据库记录时审批方式approveWay错误
        param.setApproveWay(approveWay);

        ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = ApproveMebRoomRateRequestExtEntity.convert(null, param);

        // 需要调用crs接口获取提交申请时的审核参考值，存入数据库，因为审核参考值后续可能会变，需要提前将申请时的审核参考值记录在表中
        AgreementApprovalExtraContentDTO extraContentDTO = null;
        if (StringUtils.isNotBlank(approveMebRoomRateRequestExtEntity.getJsonValue())) {
            extraContentDTO =
                    JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getJsonValue(), AgreementApprovalExtraContentDTO.class);

        }
        // 调用crs获取审核参考值
        MebRoomRateDetailDTO referenceInfo = getReferenceInfo(approveMebRoomRateRequestExtEntity, null, extraContentDTO);
        AgreementAuditReferenceValueDTO auditReferenceValueDTO = AgreementAuditReferenceValueDTO.builder().build();
        BeanUtils.copyProperties(referenceInfo, auditReferenceValueDTO);
        approveMebRoomRateRequestExtEntity.setAuditReferenceValue(ObjectUtil.toJsonQuietly(auditReferenceValueDTO));


        //审批锁
        String lockKey = LockKeyFactory.getAgreementAddLockKey(param.getChainId(), param.getMebId());
        RLock lock = lockRedis.getLock(lockKey);
        if (lock == null || !lock.tryLock()) {
            log.info("--submitApprove lock FAIL , chainId={},mebId={}", param.getChainId(), param.getMebId());
            AMonitor.meter("submitApprove_conflict");
            throw new BusinessException(ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getMessage(),
                    ResponseCodeEnum.NO_FOLIO_TRANS_LOCK.getCode());
        }
        try {
            // 审批编号生成 年月日+系统编号+4位自增数（每日自增数自0001开始计数）
            String formatDate = DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.FORMAT_DATE_NUMBER);
            String key = RedisContants.AGREEMENT_HOUSE_PRICE_KEY + formatDate;
            Long lastApprovalNum = redisClient.incryBy(key, NumberUtils.LONG_ONE);
            if (lastApprovalNum.equals(NumberUtils.LONG_ONE)) {
                redisClient.expire(key, RedisContants.FOLIO_TRANS_ADJUST_KEY_EXPIRE_TIME_IN_SECONDS);
            }
            String requestId = formatDate + ApproveProjectIndexEnum.PMS.getIndex() + CommonUtils.fill(lastApprovalNum,
                    DEFAULT_APPROVAL_SIZE, DEFAULT_APPROVAL_SIZE);
            //全局变量
            Map<String, Object> variables = Maps.newHashMap();
            ChainDTO chainByChainId = chainService.getChainByChainId(param.getChainId());
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.WAIT_SUBMIT.getName());
            variables.put(ActivitiConstants.VAR_NAME_PARAM_CHAIN_ID, param.getChainId());
            variables.put("chainName", Objects.isNull(chainByChainId) ? "" : chainByChainId.getChainName());
            variables.put("unitName", param.getUnitName());
            variables.put("approveId", requestId);
            variables.put("chainIsSzZg", "Y");
            // 灰度判断要不要走省长减负的流程
            // 判定酒店是否是省长直管
            if (rbacUserService.szReduceWorkChainIds(param.getChainId(), AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey()) && !rbacUserService.checkChainIsSzZg(param.getChainId())) {
                variables.put("chainIsSzZg", "N");
            }

            // 发起流程申请
            StartProcessParam startProcessParam = StartProcessParam.builder()
                    .userId(userInfo.getEmployeeId())
                    .businessKey(requestId)
                    .processDefinitionKey(AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey())
                    .variables(variables)
                    .build();
            setFeishuUrl(startProcessParam, requestId, Objects.isNull(chainByChainId) ? "" : chainByChainId.getChainName(), param.getUnitName(), null);
            StartProcessResultDTO startProcessResultDTO = processRemote.startProcess(startProcessParam);
            if (startProcessResultDTO == null || StringUtils.isEmpty(startProcessResultDTO.getProcessInstanceId())) {
                log.error("submitApprove start process error, 传入参数param:{}", ObjectUtil.toJsonQuietly(param));
                throw new BusinessException("提交申请失败，请重新提交", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
            }
            //保存
            ApproveFlowBaseEntity approveFlowBaseEntity = ApproveFlowBaseEntity.convert(requestId, userInfo, startProcessResultDTO.getProcessInstanceId(), AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey(), "");
            approveFlowBaseMapper.insertSelective(approveFlowBaseEntity);
            approveMebRoomRateRequestExtEntity.setApproveBaseId(approveFlowBaseEntity.getId());


            approveMebRoomRateRequestExtMapper.insertSelective(approveMebRoomRateRequestExtEntity);

            approveMebRoomRateRequestExtEntity.setProcessState(ApproveStateEnum.WAIT.getCode());

            // 完成当前节点
            this.completeCurrentNode(approveFlowBaseEntity, approveWay, userInfo.getEmployeeId(), ApproveStateEnum.WAIT.getCode(),
                    Boolean.TRUE, ApproveStateEnum.WAIT.getCode(), startProcessParam.getFormData());

            // 如果是人工审批 修改流程状态->待审批
            if (approveWay == ApproveWayEnum.HUMAN.getCode()) {
                approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(ApproveMebRoomRateRequestExtEntity.convert(approveMebRoomRateRequestExtEntity.getId(), ApproveStateEnum.WAIT.getCode()));
            }
            // 如果是自动审批
            if (approveWay == ApproveWayEnum.AUTO.getCode()) {
                // 自动审批 需要将自动审批通过的原因记录下来,记录在扩展字段里
                AgreementApprovalExtraContentDTO agreementApprovalExtraContentDTO = JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getJsonValue(), AgreementApprovalExtraContentDTO.class);
                agreementApprovalExtraContentDTO.setAutoApproveReason(autoApproveReason);
                String jsonValue = JsonUtils.toJson(agreementApprovalExtraContentDTO);
                // 修改扩展字段 将自动审批原因添加到表记录
                approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(ApproveMebRoomRateRequestExtEntity.convert(approveMebRoomRateRequestExtEntity.getId(), jsonValue));
            }

            return FolioTransSubmitApprovalDTO.builder()
                    .requestId(requestId)
                    .build();
        } finally {
            lock.unlock();
            log.info("submitApprove unlock, chainId={},mebId={}", param.getChainId(), param.getMebId());
        }
    }

    /**
     * 调用crs查询是否可以自动审批
     */
    private AgreementAutoApprovalDTO checkCanAutoApprovalFromCrs(AgreementAutoApprovalRequest request, String url) {
        // 根据价格类型
        String uriString = UriComponentsBuilder.fromUriString(url)
                .build()
                .encode()
                .toUriString();
        long timestamp = System.currentTimeMillis();
        String data = "OMS" + JSONObject.toJSONString(request) + timestamp;
        String sign = Md5Util.md5UpperCase(data, "oms#2022@atour", "UTF-8");
        String requestUrl = uriString + "?sign=" + sign + "&timestamp=" + timestamp;
        ApiSchemaResult<AgreementAutoApprovalDTO> result = redirectHttpClient.postJsonForObject(requestUrl, request,
                new ParameterizedTypeReference<ApiSchemaResult<AgreementAutoApprovalDTO>>() {
                });
        log.info("checkCanAutoApprovalFromCrs 响应信息:result={}", ObjectUtil.toJsonQuietly(result));
        if (Objects.isNull(result)) {
            log.error("远程调用crs判断是否自动审批接口出现错误, result={}", ObjectUtil.toJsonQuietly(result));
            throw new com.atour.hotel.framework.exception.BusinessException("远程调用crs接口出现错误");
        }

        if (result.getCode() != ApiSchemaResult.DEFAULT_SUCCEED_CODE) {
            log.error("远程调用crs判断是否自动审批接口出现错误:request:{}", ObjectUtil.toJsonQuietly(request));
            AMonitor.meter("remote_checkCanAutoApprovalFromCrs_bus_error");
            throw new com.atour.hotel.framework.exception.BusinessException("远程调用crs接口出现错误");
        }
        return result.getResult();
    }

    /**
     * 通过审批
     */
    public void passApprove(FolioTransApproveHandleParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());
        //查询调账申请详情
        ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = Safes.of(approveFlowBaseMapper.selectAllBySelective(
                        ApproveMebRoomRateRequestExtEntity.convert(param.getRequestId(), DeletedEnum.UNDELETED.getCode())))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(approveMebRoomRateRequestExtEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        if (!Objects.equals(approveMebRoomRateRequestExtEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
            throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }


        //修改申请信息
        updateApproveExt(approveMebRoomRateRequestExtEntity.getExtId(), ApproveStateEnum.WAIT.getCode());
        approveMebRoomRateRequestExtEntity.setReason(param.getReason());
        approveMebRoomRateRequestExtEntity.setProcessState(ApproveStateEnum.PASS.getCode());

        ChainDTO chainByChainId = chainService.getChainByChainId(approveMebRoomRateRequestExtEntity.getChainId());
        LinkedHashMap<String, String> formData = new LinkedHashMap<>();
        formData.put("审批类型", " 协议价格审批");
        formData.put("申请酒店", Objects.isNull(chainByChainId) ? "" : chainByChainId.getChainName());
        formData.put("申请企业", approveMebRoomRateRequestExtEntity.getUnitName());
        formData.put("审批结果", ApproveStateEnum.PASS.getName());

        //审批通过
        this.completeCurrentNode(approveMebRoomRateRequestExtEntity, ApproveWayEnum.HUMAN.getCode(), userInfo.getEmployeeId(), ApproveStateEnum.PASS.getCode(),
                Boolean.FALSE, ApproveStateEnum.PASS.getCode(), formData);
    }

    /**
     * 拒绝审批
     */
    public void refuseApprove(FolioTransApproveHandleParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());
        //查询调账申请详情
        ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = Safes.of(approveFlowBaseMapper.selectAllBySelective(
                        ApproveMebRoomRateRequestExtEntity.convert(param.getRequestId(), DeletedEnum.UNDELETED.getCode())))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(approveMebRoomRateRequestExtEntity)) {
            throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        if (!Objects.equals(approveMebRoomRateRequestExtEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
            throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        //保存申请信息
        approveMebRoomRateRequestExtEntity.setProcessState(ApproveStateEnum.REFUSE.getCode());

        approveMebRoomRateRequestExtEntity.setReason(param.getReason());

        ChainDTO chainByChainId = chainService.getChainByChainId(approveMebRoomRateRequestExtEntity.getChainId());
        LinkedHashMap<String, String> formData = new LinkedHashMap<>();
        formData.put("审批类型", " 协议价格审批");
        formData.put("申请酒店", Objects.isNull(chainByChainId) ? "" : chainByChainId.getChainName());
        formData.put("申请企业", approveMebRoomRateRequestExtEntity.getUnitName());
        formData.put("审批结果", ApproveStateEnum.REFUSE.getName());
        //审批拒绝
        this.completeCurrentNode(approveMebRoomRateRequestExtEntity, ApproveWayEnum.HUMAN.getCode(), userInfo.getEmployeeId(), ApproveStateEnum.REFUSE.getCode(),
                Boolean.FALSE, ApproveStateEnum.REFUSE.getCode(), formData);
        //修改申请信息
        updateApproveExt(approveMebRoomRateRequestExtEntity.getExtId(), ApproveStateEnum.REFUSE.getCode());
    }

    /**
     * 申请详情
     */
    public AgreementDetailDTO detail(FolioTransDetailParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());

        //查询申请详情
        ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = Safes.of(approveFlowBaseMapper.selectAllBySelective(
                        ApproveMebRoomRateRequestExtEntity.convert(param.getRequestId(), DeletedEnum.UNDELETED.getCode())))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(approveMebRoomRateRequestExtEntity)) {
            throw new BusinessException("对应的审批申请信息不存在", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }
        //调账申请ID为空代表新增申请  返回房单账务信息
        if (StringUtils.isBlank(param.getRequestId())) {
            return new AgreementDetailDTO();
        }

        //合同信息
        AgreementApprovalExtraContentDTO extraContentDTO = null;
        if (StringUtils.isNotBlank(approveMebRoomRateRequestExtEntity.getJsonValue())) {
            extraContentDTO =
                    JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getJsonValue(), AgreementApprovalExtraContentDTO.class);

        }

        // 取用户花名
        SysUserEntity sysUserEntity = sysUserService.getUserByEmployeeId(approveMebRoomRateRequestExtEntity.getEmployeeId());
        String flowerName = null;
        if (Objects.nonNull(sysUserEntity)) {
            flowerName = sysUserEntity.getUserName();
        }
        //操作类型 ： 0:查看 1: 有审核权限 2：撤回：3重新提交 4：审核+撤回
        Integer operationType = NumberUtils.INTEGER_ZERO;
        //有审核权限
        if (this.checkApproveState(approveMebRoomRateRequestExtEntity, userInfo)) {
            operationType = ArOperationTypeEnum.AUDIT.getValue();
        }

        Date openDate = getOpenDate(approveMebRoomRateRequestExtEntity.getChainId());
        MebRoomRateDetailDTO referenceInfo = null;

        // 先判断数据库里面是否存有审核参考值信息, 没有则从crs获取,并更新到数据库,防止下次继续从crs获取
        if (StringUtils.isEmpty(approveMebRoomRateRequestExtEntity.getAuditReferenceValue())) {
            Integer statisticsMonth = getStatisticsMonth(openDate);
            referenceInfo = getReferenceInfo(approveMebRoomRateRequestExtEntity, statisticsMonth, extraContentDTO);
            AgreementAuditReferenceValueDTO auditReferenceValueDTO = AgreementAuditReferenceValueDTO.builder().build();
            BeanUtils.copyProperties(referenceInfo, auditReferenceValueDTO);
            approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(ApproveMebRoomRateRequestExtEntity.convert(approveMebRoomRateRequestExtEntity.getExtId(), auditReferenceValueDTO));
        } else {
            // 数据库存在审核参考值，则直接从数据库获取
            AgreementAuditReferenceValueDTO auditReferenceValueDTO = JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getAuditReferenceValue(), AgreementAuditReferenceValueDTO.class);
            referenceInfo = MebRoomRateDetailDTO.builder().build();
            BeanUtils.copyProperties(auditReferenceValueDTO, referenceInfo);
        }

        //int roomCount = hotelCommonDao.selectRoomCount(RoomQueryParam.builder().chainId(approveMebRoomRateRequestExtEntity.getChainId()).build());
        // 物理房量改成从crs审核参考值获取
        Integer roomCount = referenceInfo.getRoomCount();

        RoomRateCodeDTO rateCodeDto = galaxyRoomRateCodeService.getRateCode(approveMebRoomRateRequestExtEntity.getRoomRateCode());

        return AgreementDetailDTO.builder()
                .processDefinitionKey(approveMebRoomRateRequestExtEntity.getProcessDefinitionKey())
                .requestId(approveMebRoomRateRequestExtEntity.getRequestId())
                .bottomRoomRateCodePrice(referenceInfo.getBottomRoomRateCodePrice())
                .chainId(approveMebRoomRateRequestExtEntity.getChainId())
                .chainName(chainService.getChainNameByChainId(approveMebRoomRateRequestExtEntity.getChainId()))
                .userId(String.valueOf(approveMebRoomRateRequestExtEntity.getUserId()))
                .userName(flowerName)
                .employeeId(approveMebRoomRateRequestExtEntity.getEmployeeId())
                .approveState(approveMebRoomRateRequestExtEntity.getProcessState())
                .approveStateName(Objects.nonNull(ApproveStateEnum.getInstance(approveMebRoomRateRequestExtEntity.getProcessState())) ?
                        ApproveStateEnum.getInstance(approveMebRoomRateRequestExtEntity.getProcessState())
                                .getName() : StringUtils.EMPTY)
                .operationType(operationType)
                .mebId(approveMebRoomRateRequestExtEntity.getMebId())
                .mebCardNo(approveMebRoomRateRequestExtEntity.getMebCardNo())
                .unitName(approveMebRoomRateRequestExtEntity.getUnitName())
                .year(Objects.isNull(extraContentDTO) ? null : extraContentDTO.getYear())
                // 折扣率改回原有方式
                .discountRate(Objects.isNull(extraContentDTO) ? null : extraContentDTO.getDiscountRate())
                //.discountRate(new BigDecimal(referenceInfo.getCalculatedDiscountRate()))
                .breakfastType(approveMebRoomRateRequestExtEntity.getBreakFastType())
                .effectiveStartDate(DateUtil.formatDate(approveMebRoomRateRequestExtEntity.getEffectiveStartTime(), DateUtil.DATE_FORMAT))
                .effectiveEndDate(DateUtil.formatDate(approveMebRoomRateRequestExtEntity.getEffectiveEndTime(), DateUtil.DATE_FORMAT))
                .rateCode(approveMebRoomRateRequestExtEntity.getRoomRateCode())
                .targetNightCount(Objects.isNull(extraContentDTO) ? null : extraContentDTO.getTargetNightCount())
                .ossKeyList(Objects.isNull(extraContentDTO) ? null : extraContentDTO.getOssKeyList())
                .priceType(approveMebRoomRateRequestExtEntity.getPriceType())
                .approveLogList(this.getApproveLogList(approveMebRoomRateRequestExtEntity))
                .customerTypeName(referenceInfo.getCustomerTypeName())
                .roomNum(roomCount)
                .revpar(referenceInfo.getYearRevpar())
                .roomRate(referenceInfo.getCalculatedRoomPrice())
                .discount(referenceInfo.getCalculatedDiscountRate())
                .rentRate(referenceInfo.getAverageRentalRate())
                .averagePrice(referenceInfo.getCustomerUnitPrice())
                .openTime(DateUtil.formatDate(openDate, DateUtil.DATE_FORMAT))
                .rateName(Objects.isNull(rateCodeDto) ? "" : rateCodeDto.getRateCodeName())
                .averageRoomRate(referenceInfo.getAverageRoomRate())
                //.month(statisticsMonth)
                // 返回crs提供的过去x个月平均出租率对应的x月份数
                .month(referenceInfo.getAverageRentalRateMonth())
                //拼接申请时间字段
                .createTime(DateUtil.formatDate(approveMebRoomRateRequestExtEntity.getCreateTime(), DateUtil.DATE_FORMAT))
                .build();
    }

    /**
     * 批量通过审批
     */
    public void passApproveBatch(FolioTransApproveBatchParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());
        //校验
        Safes.of(param.getRequestIds())
                .forEach(f -> {
                    //查询调账申请详情
                    ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = Safes.of(
                                    approveFlowBaseMapper.selectAllBySelective(ApproveMebRoomRateRequestExtEntity.convert(f, DeletedEnum.UNDELETED.getCode())))
                            .stream()
                            .findFirst()
                            .orElse(null);
                    if (Objects.isNull(approveMebRoomRateRequestExtEntity)) {
                        throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                    }
                    if (!Objects.equals(approveMebRoomRateRequestExtEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
                        throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                    }
                });

        Safes.of(param.getRequestIds())
                .forEach(f -> {
                    //通过审批
                    this.passApprove(FolioTransApproveHandleParam.builder()
                            .requestId(f)
                            .reason(param.getReason())
                            .userId(param.getUserId())
                            .build());
                });
    }

    /**
     * 批量拒绝审批
     */
    public void refuseApproveBatch(FolioTransApproveBatchParam param) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = getUserInfo(param.getUserId());
        //校验
        Safes.of(param.getRequestIds())
                .forEach(f -> {
                    //查询调账申请详情
                    ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = Safes.of(
                                    approveFlowBaseMapper.selectAllBySelective(ApproveMebRoomRateRequestExtEntity.convert(f, DeletedEnum.UNDELETED.getCode())))
                            .stream()
                            .findFirst()
                            .orElse(null);
                    if (Objects.isNull(approveMebRoomRateRequestExtEntity)) {
                        throw new BusinessException("未找到对应的申请", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                    }
                    if (!Objects.equals(approveMebRoomRateRequestExtEntity.getProcessState(), ApproveStateEnum.WAIT.getCode())) {
                        throw new BusinessException("非待审批状态，不能操作审批", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
                    }

                });
        Safes.of(param.getRequestIds())
                .forEach(f -> {
                    //拒绝审批
                    this.refuseApprove(FolioTransApproveHandleParam.builder()
                            .requestId(f)
                            .reason(param.getReason())
                            .userId(param.getUserId())
                            .build());
                });
    }

    /**
     * 校验是否登录
     *
     * @param userId
     * @return
     */
    private AssoUserInfoDTO getUserInfo(Integer userId) {
        //获取当前登录用户
        AssoUserInfoDTO userInfo = AssoCommonParamsManager.get();

        if (Objects.nonNull(userInfo)) {
            return userInfo;
        }

        //内部调用会传userId
        if (Objects.nonNull(userId)) {
            SysUserEntity sysUserEntity = sysUserService.getByUserId(userId);
            if (Objects.isNull(sysUserEntity)) {
                throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
            }
            userInfo = new AssoUserInfoDTO();
            userInfo.setUserId(sysUserEntity.getUserID());
            userInfo.setUserCode(sysUserEntity.getUserCode());
            userInfo.setUserName(sysUserEntity.getUserName());
            userInfo.setEmployeeId(sysUserEntity.getHrId());
        } else {
            userInfo = AssoCommonParamsManager.get();
        }

        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getEmployeeId())) {
            throw new BusinessException("请先登录", ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        return userInfo;
    }

    /**
     * 完成当前节点
     *
     * @param approveWay 审批方式 1-自动审批 0-手动审批
     * @return
     */
    private String completeCurrentNode(ApproveFlowBaseEntity approveFlowBaseEntity, Integer approveWay, String assignee,
                                       Integer approveAction, Boolean isFirstSubmit, Integer processState, LinkedHashMap<String, String> formData) {

        // 设置流程变量
        Map<String, Object> variables = Maps.newHashMap();
        Map<String, Object> variablesLocal = Maps.newHashMap();

        //获取流程当前的任务id
        String taskId = this.getCurrentTaskId(approveFlowBaseEntity, assignee);
        // 区分拒绝与通过
        ApproveStateEnum approveStateEnum = ApproveStateEnum.getInstance(processState);
        String approveStateName = StringUtils.EMPTY;
        if (Objects.equals(ApproveStateEnum.REFUSE, approveStateEnum)) {
            variables.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                    ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_REFUSE);
            variablesLocal.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                    ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_REFUSE);
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.REFUSE.getCode());
        } else {
            variables.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                    ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);
            variablesLocal.put(ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE,
                    ActivitiConstants.BOTH_VAR_NAME_BPMN_APPROVE_RANGE_PASS);
            variables.put(ActivitiConstants.VAR_NAME_PROCESS_STATE, ApproveStateEnum.WAIT.getCode());
        }
        // 添加审批方式 自动 | 人工
        if (Objects.nonNull(approveWay)) {
            variables.put("approveWay", approveWay);
            variablesLocal.put("approveWay", approveWay);
        }

        // 重新发起流程申请
        approveStateName = approveStateEnum.getName();
        //首次发起
        if (isFirstSubmit) {
            approveStateName = "提交";
        }

        TaskCompleteParam taskCompleteParam = TaskCompleteParam.builder()
                .assignee(assignee)
                .processInstanceId(approveFlowBaseEntity.getProcessId())
                .taskId(taskId)
                .variables(variables)
                .variablesLocal(variablesLocal)
                .approveLogEntity(new ApproveLogEntity(assignee, approveStateName, approveFlowBaseEntity.getReason(),
                        DateUtil.printDateTime(DateUtil.getCurrentDateTime())))
                .approveAction(approveAction)
                .build();
        taskCompleteParam.setFormData(formData);
        taskCompleteParam.setDisplayType(2);
        taskCompleteParam.setMobileLink(agreementRoomRateApproveUrl + approveFlowBaseEntity.getRequestId() + "&processDefinitionKey=" + AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey());
        approveFlowService.setFeishuCallBack(taskCompleteParam, AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey());
        if(Objects.nonNull(approveWay) && ApproveWayEnum.AUTO.getCode() ==approveWay){
            taskCompleteParam.setSendMessage(Boolean.FALSE);
        }
        TaskCompleteResultDTO taskCompleteResultDTO = processRemote.complete(taskCompleteParam);
        if (taskCompleteResultDTO == null || StringUtils.isEmpty(taskCompleteResultDTO.getProcessInstanceId())) {
            log.error("--completeCurrentNode error, baseParam:{}", ObjectUtil.toJsonQuietly(approveFlowBaseEntity));
            throw new BusinessException("提交审批失败", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }

        log.info("completeCurrentNode approveAction={},taskCompleteResultDTO={}", approveAction,
                ObjectUtil.toJsonQuietly(taskCompleteResultDTO));

        if (Objects.nonNull(approveFlowBaseEntity.getId())) {
            //获取下一节点审批人
            TaskInfoQueryResultDTO nextTask = Safes.of(taskCompleteResultDTO.getNextTaskInfos())
                    .stream()
                    .findFirst()
                    .orElse(new TaskInfoQueryResultDTO());

            NextApproverDTO nextInfo = NextApproverDTO.builder()
                    .approverList(
                            getApproveUserList(approveFlowBaseEntity.getProcessDefinitionKey(), approveFlowBaseEntity.getProcessId(),
                                    approveFlowBaseEntity.getRequestId()))
                    .taskName(nextTask.getTaskName())
                    .build();
            //保存下一节点审批人
            approveFlowBaseMapper.updateByPrimaryKeySelective(ApproveFlowBaseEntity.convert(approveFlowBaseEntity.getId(), ObjectUtil.toJsonQuietly(nextInfo)));
        }

        return taskId;
    }

    /**
     * 获取流程当前的任务id
     *
     * @param approveFlowBaseEntity
     * @return
     */
    private String getCurrentTaskId(ApproveFlowBaseEntity approveFlowBaseEntity, String employeeId) {
        // 完成当前节点
        List<TaskQueryResultDTO> taskQueryResultDTOS = processRemote.toDoList(TaskQueryParam.builder()
                .userId(employeeId)
                .processInstanceId(Lists.newArrayList(approveFlowBaseEntity.getProcessId()))
                .build());
        TaskQueryResultDTO taskResultInfo = Safes.of(taskQueryResultDTOS)
                .stream()
                .filter(f -> StringUtils.equalsIgnoreCase(AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey(), f.getProcessDefinitionKey()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(taskResultInfo)) {
            throw new BusinessException("提交审批失败,无对应任务列表", ResponseCodeEnum.FOLIO_TRANS_AUTH_ERROR.getCode());
        }

        TaskInstanceQueryResultDTO taskInstance = Safes.of(taskResultInfo.getTaskInstances())
                .stream()
                .filter(f -> StringUtils.equalsIgnoreCase(f.getBusinessKey(), approveFlowBaseEntity.getRequestId()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(taskInstance)) {
            throw new BusinessException("提交审批失败,无对应流程实例信息", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        List<TaskInfoQueryResultDTO> taskInfos = taskInstance.getTaskInfos();
        if (CollectionUtils.isEmpty(taskInfos)) {
            throw new BusinessException("提交审批失败,无对应任务信息", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        if (taskInfos.size() > 1) {
            throw new BusinessException("提交审批失败,出现多个流程任务", ResponseCodeEnum.FOLIO_TRANS_ERROR.getCode());
        }
        return taskInfos.get(NumberUtils.INTEGER_ZERO)
                .getTaskId();
    }

    /**
     * 获取下一节点审批人
     *
     * @return
     */
    public List<String> getApproveUserList(String processDefinitionKey, String processId, String requestId) {
        List<CommonLogInfoDTO> logList = commonProcessRemote.logList(CommonLogListParam.builder()
                .processDefinitionKey(processDefinitionKey)
                .businessKey(requestId)
                .processInstanceId(processId)
                .showNextTask(Boolean.TRUE)
                .build());
        CommonLogInfoDTO dto = Safes.of(logList)
                .stream()
                .filter(f -> StringUtils.equalsIgnoreCase(f.getOpType(), CommonLogInfoDTO.SPEC_OP_TYPE_NEXT_TASK))
                .findFirst()
                .orElse(new CommonLogInfoDTO());
        if (StringUtils.isNotBlank(dto.getRemark())) {
            return Lists.newArrayList(dto.getRemark()
                    .split(","));
        }

        return Collections.emptyList();
    }

    private ApproveMebRoomRateRequestExtEntity updateApproveExt(Long id, Integer state) {
        //保存审批通过信息
        ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity = ApproveMebRoomRateRequestExtEntity.convert(id, state);

        approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(approveMebRoomRateRequestExtEntity);

        return approveMebRoomRateRequestExtEntity;
    }

    /**
     * 完成审批
     *
     * @param approveMebRoomRateRequestExtEntity
     */
    public void updateApprove(ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity) {
        //保存申请信息
        approveMebRoomRateRequestExtEntity = ApproveMebRoomRateRequestExtEntity.convert(approveMebRoomRateRequestExtEntity.getExtId(), ApproveStateEnum.PASS.getCode());

        approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(approveMebRoomRateRequestExtEntity);
    }

    /**
     * 通过requestId获取审批流信息
     *
     * @param requestId
     * @return
     */
    public ApproveMebRoomRateRequestExtEntity getByRequestId(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            return null;
        }

        return approveFlowBaseMapper.getByRequestId(requestId);
    }

    /**
     * 是否有权限审批/撤回/重新提交操作权限
     *
     * @param approveFlowBaseEntity
     * @param userInfo
     * @return
     */
    public boolean checkApproveState(ApproveFlowBaseEntity approveFlowBaseEntity,
                                     AssoUserInfoDTO userInfo) {
        try {
            String taskId = this.getCurrentTaskId(approveFlowBaseEntity, userInfo.getEmployeeId());
            if (StringUtils.isNotBlank(taskId)) {
                return Boolean.TRUE;
            }
        } catch (BusinessException ignored) {
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }

    private Date getOpenDate(Integer chainId) {
        ApiResult<AtourChainDTO> chainById = chainRemote.getChainById(chainId);
        if (Objects.isNull(chainById) || Objects.isNull(chainById.getResult())) {
            return null;
        }

        AtourChainDTO result = chainById.getResult();
        return result.getOpeningDate();
    }

    /**
     * 判断需要计算过去几个月的数据
     *
     * @param openingDate
     * @return
     */
    private Integer getStatisticsMonth(Date openingDate) {
        if (Objects.isNull(openingDate)) {
            return null;
        }
        int day = openingDate.getDay();
        Date date = new Date();
        String openStr = DateUtil.formatDate(openingDate, DateUtil.FORMAT_MONTH);
        String dateStr = DateUtil.formatDate(date, DateUtil.FORMAT_MONTH);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateUtil.FORMAT_MONTH);
        DateTime start = dateTimeFormatter.parseDateTime(openStr);
        DateTime now = dateTimeFormatter.parseDateTime(dateStr);
        int months = Months.monthsBetween(start, now).getMonths();
        if (day > FIFTEEN_DAYS) {
            months = months - 1;
        }

        if (months > THREE_MONTHS && months < NINE_MONTHS) {
            return THREE_MONTHS;
        }

        if (months >= NINE_MONTHS) {
            return SIX_MONTHS;
        }
        return null;
    }

    /**
     * 审批日志详情列表
     *
     * @return
     */
    public List<FolioTransApproveLogListDTO> getApproveLogList(ApproveMebRoomRateRequestExtEntity extEntity) {
        List<FolioTransApproveLogListDTO> resultList = Lists.newArrayList();
        // 如果是自动审批
        if (Objects.equals(extEntity.getApproveWay(), ApproveWayEnum.AUTO.getCode())) {
            if (Objects.nonNull(extEntity.getJsonValue())) {
                AgreementApprovalExtraContentDTO agreementApprovalExtraContentDTO = JsonUtils.parseObject(extEntity.getJsonValue(), AgreementApprovalExtraContentDTO.class);
                String autoApproveReason = agreementApprovalExtraContentDTO.getAutoApproveReason();
                if (Objects.isNull(autoApproveReason)) {
                    autoApproveReason = "自动审批";
                }
                resultList.add(FolioTransApproveLogListDTO.builder()
                        .employeeId(null)
                        .flowerName("系统")
                        .opType("审批通过")
                        .remark(autoApproveReason)
                        .taskId(extEntity.getProcessId())
                        .time(DateUtil.formatDate(extEntity.getUpdateTime(), DateUtil.FORMAT_DATE_TIME))
                        .build());
            }
        }

        List<CommonLogInfoDTO> logList = commonProcessRemote.logList(CommonLogListParam.builder()
                .processDefinitionKey(extEntity.getProcessDefinitionKey())
                .businessKey(extEntity.getRequestId())
                .processInstanceId(extEntity.getProcessId())
                .showNextTask(Boolean.TRUE)
                .build());
        Safes.of(logList)
                .forEach(f -> {
                    String flowerName = f.getFlowerName();
                    if (StringUtils.equalsIgnoreCase(f.getOpType(), CommonLogInfoDTO.SPEC_OP_TYPE_NEXT_TASK)) {
                        flowerName = "系统";
                    }
                    resultList.add(FolioTransApproveLogListDTO.builder()
                            .employeeId(f.getUserId())
                            .flowerName(flowerName)
                            .opType(f.getOpType())
                            .remark(f.getRemark())
                            .taskId(f.getTaskId())
                            .time(f.getTime())
                            .build());
                });

        return resultList;
    }

    /**
     * 回调crm
     *
     * @return
     */
    public void callBackCrm(ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity) {
        try {
            ArrayList<MemberRateCodeRequest> list = new ArrayList<>();
            MemberRateCodeRequest memberRateCodeRequest = new MemberRateCodeRequest();
            memberRateCodeRequest.setRateCode(approveMebRoomRateRequestExtEntity.getRoomRateCode());
            memberRateCodeRequest.setBeginDate(approveMebRoomRateRequestExtEntity.getEffectiveStartTime());
            memberRateCodeRequest.setEndDate(approveMebRoomRateRequestExtEntity.getEffectiveEndTime());
            list.add(memberRateCodeRequest);

            CrsAgentMemberRequest param = CrsAgentMemberRequest.builder()
                    .mebId(Math.toIntExact(approveMebRoomRateRequestExtEntity.getMebId()))
                    .rateCodes(list)
                    .build();

            String uriString = UriComponentsBuilder.fromUriString(callBackUrl)
                    .build()
                    .encode()
                    .toUriString();
            ApiSchemaResult result = redirectHttpClient.postJsonForObject(uriString, param,
                    new ParameterizedTypeReference<ApiSchemaResult>() {
                    });
            log.info("postCallBackCrm 响应信息:result={}", ObjectUtil.toJsonQuietly(result));
            if (Objects.isNull(result)) {
                log.warn("远程调用crm接口出现错误, result={}", ObjectUtil.toJsonQuietly(result));
                throw new com.atour.hotel.framework.exception.BusinessException("远程调用回调crm接口出现错误");
            }

            if (result.getCode() != ApiSchemaResult.DEFAULT_SUCCEED_CODE) {
                log.warn("回调crm失败:approveMebRoomRateRequestExtEntity:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity));
                AMonitor.meter("remote_callBackCrm_bus_error");
                throw new com.atour.hotel.framework.exception.BusinessException("远程调用回调crm接口出现错误");
            }

        } catch (com.atour.hotel.framework.exception.BusinessException e) {
            log.warn("远程调用回调crm接口出现业务异常:approveMebRoomRateRequestExtEntity:{},e:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity), e);
            AMonitor.meter("remote_callBackCrm_bus_error");
        } catch (Exception e) {
            log.warn("远程调用回调crm接口出现异常:approveMebRoomRateRequestExtEntity:{},e:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity), e);
            AMonitor.meter("remote_callBackCrm_exception_error");
        }

    }

    public void updateById(ApproveFlowBaseEntity approveFlowBaseEntity) {
        log.info("updateById, entity:{}", approveFlowBaseEntity);
        approveFlowBaseMapper.updateByPrimaryKeySelective(approveFlowBaseEntity);
    }

    /**
     * 从crs获取审核参考值信息
     *
     * @return
     */
    private MebRoomRateDetailDTO getReferenceInfo(ApproveMebRoomRateRequestExtEntity approveMebRoomRateRequestExtEntity, Integer month, AgreementApprovalExtraContentDTO extraContentDTO) {
        try {
            QueryMebRoomRateDetailParam param = QueryMebRoomRateDetailParam.builder()
                    .chainId(approveMebRoomRateRequestExtEntity.getChainId())
                    .mebId(approveMebRoomRateRequestExtEntity.getMebId())
                    .roomRateCode(approveMebRoomRateRequestExtEntity.getRoomRateCode())
                    .applyStartTime(DateUtil.formatDate(approveMebRoomRateRequestExtEntity.getEffectiveStartTime(), DateUtil.DATE_FORMAT))
                    .applyEndTime(DateUtil.formatDate(approveMebRoomRateRequestExtEntity.getEffectiveEndTime(), DateUtil.DATE_FORMAT))
                    .rentalRateMonthNumStartMonth(month)
                    .roomRatePriceType(approveMebRoomRateRequestExtEntity.getPriceType())
                    .discountRate(Objects.isNull(extraContentDTO) ? null : extraContentDTO.getDiscountRate())
                    .build();

            String uriString = UriComponentsBuilder.fromUriString(queryMebRoomRateDetailUrl)
                    .build()
                    .encode()
                    .toUriString();
            ApiSchemaResult<MebRoomRateDetailDTO> result = redirectHttpClient.postJsonForObject(uriString, param,
                    new ParameterizedTypeReference<ApiSchemaResult<MebRoomRateDetailDTO>>() {
                    });
            log.info("postGetReferenceInfo 响应信息:result={}", ObjectUtil.toJsonQuietly(result));
            if (Objects.isNull(result)) {
                log.error("远程调用获取审核参考值信息接口出现错误, result={}", ObjectUtil.toJsonQuietly(result));
                throw new com.atour.hotel.framework.exception.BusinessException("远程调用crs接口出现错误");
            }

            if (result.getCode() != ApiSchemaResult.DEFAULT_SUCCEED_CODE) {
                log.error("从crs获取审核参考值信息失败:approveMebRoomRateRequestExtEntity:{},month:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity), month);
//                AMonitor.meter("remote_getReferenceInfo_bus_error");
                throw new com.atour.hotel.framework.exception.BusinessException("远程调用crs接口出现错误");
            }

            return result.getResult();

        } catch (com.atour.hotel.framework.exception.BusinessException e) {
//            log.error("远程调用获取审核参考值信息接口出现业务异常:approveMebRoomRateRequestExtEntity:{},month:{},e:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity),month,e);
//            AMonitor.meter("remote_getReferenceInfo_bus_error");
            throw e;
        } catch (Exception e) {
            log.error("远程调用获取审核参考值信息接口出现异常:approveMebRoomRateRequestExtEntity:{},month:{},e:{}", ObjectUtil.toJsonQuietly(approveMebRoomRateRequestExtEntity), month, e);
//            AMonitor.meter("remote_getReferenceInfo_exception_error");
            throw new com.atour.hotel.framework.exception.BusinessException("远程调用crs接口出现错误");
        }
//        return new MebRoomRateDetailDTO();
    }

    /**
     * 校验申请是否有效
     *
     * @return
     */
    private void checkRateCodeValid(AgreementApplyParam agreementApplyParam) {
        // 先检查前端文件是否上传成功
        Safes.of(agreementApplyParam.getOssKeyList()).stream().forEach(fileUploadResp -> {
            commonOssService.checkFileExist(fileUploadResp.getOssKey(), fileUploadResp.getFileName());
        });

        ArrayList<MemberRateCodeRequest> list = new ArrayList<>();
        MemberRateCodeRequest memberRateCodeRequest = new MemberRateCodeRequest();
        memberRateCodeRequest.setRateCode(agreementApplyParam.getRateCode());
        memberRateCodeRequest.setBeginDate(DateUtil.parseDatetime(agreementApplyParam.getEffectiveStartDate(), DateUtil.DATE_FORMAT));
        memberRateCodeRequest.setEndDate(DateUtil.parseDatetime(agreementApplyParam.getEffectiveEndDate(), DateUtil.DATE_FORMAT));
        list.add(memberRateCodeRequest);

        CrsAgentMemberRequest param = CrsAgentMemberRequest.builder()
                .mebId(Math.toIntExact(agreementApplyParam.getMebId()))
                .rateCodes(list)
                .build();

        String uriString = UriComponentsBuilder.fromUriString(checkRateCodeValidUrl)
                .build()
                .encode()
                .toUriString();
        ApiSchemaResult result = redirectHttpClient.postJsonForObject(uriString, param,
                new ParameterizedTypeReference<ApiSchemaResult>() {
                });
        log.info("postCheckRateCodeValid 响应信息:result={}", ObjectUtil.toJsonQuietly(result));
        if (Objects.isNull(result)) {
            log.error("远程调用校验申请接口出现错误, result={}", ObjectUtil.toJsonQuietly(result));
            throw new com.atour.hotel.framework.exception.BusinessException(ResponseCodeEnum.SYSTEM_ERROR.getMessage());
        }

        if (result.getCode() != ApiSchemaResult.DEFAULT_SUCCEED_CODE) {
//            log.error("校验申请是否有效失败:agreementApplyParam:{}", ObjectUtil.toJsonQuietly(agreementApplyParam));
            AMonitor.meter("remote_checkRateCodeValid_bus_error");
            throw new com.atour.hotel.framework.exception.BusinessException(result.getMessage());
        }

        Boolean flag = JsonUtils.parseObject(result.getResult()
                .toString(), Boolean.class);


        if (!flag) {
            throw new BusinessException(result.getMessage(),
                    ResponseCodeEnum.APPROVE_ERROR.getCode());
        }

    }

    /**
     * 根据申请编号清单查询协议价格审批列表
     *
     * @param businessKeyList 申请编号
     * @return 协议价格审批列表
     */
    public List<InnerAgreementApprovalDTO> selectByRequestIdList(List<String> businessKeyList) {
        if (CollectionUtils.isEmpty(businessKeyList)) {
            return Collections.emptyList();
        }
        List<ApproveMebRoomRateRequestExtEntity> agreementApprovalRequestEntities =
                approveFlowBaseMapper.selectByRequestIdList(businessKeyList);

        if (CollectionUtils.isEmpty(agreementApprovalRequestEntities)) {
            return Collections.emptyList();
        }
        return agreementApprovalRequestEntities.stream()
                .map(AgreementApprovalWrapper::buildAgreementApprovalDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 去crs查询审核参考值并赋值
     *
     * @param nonAuditReferenceEntityList
     */
    public void getAuditReferenceOfEntityList(List<ApproveMebRoomRateRequestExtEntity> nonAuditReferenceEntityList) {
        if (CollectionUtils.isNotEmpty(nonAuditReferenceEntityList)) {

            nonAuditReferenceEntityList.stream().forEach(approveMebRoomRateRequestExtEntity -> {
                //合同信息
                AgreementApprovalExtraContentDTO extraContentDTO = null;
                if (StringUtils.isNotBlank(approveMebRoomRateRequestExtEntity.getJsonValue())) {
                    extraContentDTO =
                            JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getJsonValue(), AgreementApprovalExtraContentDTO.class);

                }
                Date openDate = getOpenDate(approveMebRoomRateRequestExtEntity.getChainId());
                MebRoomRateDetailDTO referenceInfo = null;

                // 先判断数据库里面是否存有审核参考值信息, 没有则从crs获取,并更新到数据库,防止下次继续从crs获取
                if (StringUtils.isEmpty(approveMebRoomRateRequestExtEntity.getAuditReferenceValue())) {
                    Integer statisticsMonth = getStatisticsMonth(openDate);
                    referenceInfo = getReferenceInfo(approveMebRoomRateRequestExtEntity, statisticsMonth, extraContentDTO);
                    if (Objects.nonNull(referenceInfo)) {
                        AgreementAuditReferenceValueDTO auditReferenceValueDTO = AgreementAuditReferenceValueDTO.builder().build();
                        BeanUtils.copyProperties(referenceInfo, auditReferenceValueDTO);
                        approveMebRoomRateRequestExtEntity.setAuditReferenceValue(JSON.toJSONString(auditReferenceValueDTO));

                        approveMebRoomRateRequestExtMapper.updateByPrimaryKeySelective(ApproveMebRoomRateRequestExtEntity.convert(approveMebRoomRateRequestExtEntity.getExtId(), auditReferenceValueDTO));
                    }

                }/*else{
                    // 数据库存在审核参考值，则直接从数据库获取
                    AgreementAuditReferenceValueDTO auditReferenceValueDTO = JsonUtils.parseObject(approveMebRoomRateRequestExtEntity.getAuditReferenceValue(), AgreementAuditReferenceValueDTO.class);
                    referenceInfo = MebRoomRateDetailDTO.builder().build();
                    BeanUtils.copyProperties(auditReferenceValueDTO, referenceInfo);
                }*/
            });
        }
    }


    private void setFeishuUrl(BaseProcessParam baseProcessParam, String requestId, String chainName, String unitName, ApproveStateEnum approveStateEnum) {

        baseProcessParam.setMobileLink(agreementRoomRateApproveUrl + requestId + "&processDefinitionKey=" + AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey());
        LinkedHashMap<String, String> formData = new LinkedHashMap<>();
        formData.put("审批类型", " 协议价格审批");
        formData.put("申请酒店", StringUtils.isNotEmpty(chainName) ? chainName : "");
        formData.put("申请企业", unitName);
        if (Objects.nonNull(approveStateEnum)) {
            formData.put("审批状态", approveStateEnum.getName());
        }
        baseProcessParam.setFormData(formData);
        baseProcessParam.setDisplayType(2);
        approveFlowService.setFeishuCallBack(baseProcessParam, AgreementApproveTypeEnum.ROOM_RATE.getProcessDefinitionKey());

    }


}
