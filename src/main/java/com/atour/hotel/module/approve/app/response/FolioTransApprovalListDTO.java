package com.atour.hotel.module.approve.app.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 调账申请列表的响应
 *
 * <AUTHOR>
 * @date 2020年4月22日11:42:01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "FolioTransApprovalListDTO", description = "调账申请列表的响应")
public class FolioTransApprovalListDTO implements Serializable {

    /**
     * 申请id
     **/
    private String requestId;

    /**
     * 员工编号
     **/
    private String employeeId;

    /**
     * 申请人id
     **/
    private Integer userId;

    /**
     * 申请人名称
     **/
    private String userName;

    /**
     * 房单id
     **/
    private Long folioId;

    /**
     * 酒店id
     **/
    private Integer chainId;
    /**
     * 酒店名
     **/
    private String chainName;

    /**
     * 审批类型 1-重新开账、2-房费调整、3-特殊退款、4-冲减、5-转账、0-其他
     * @see com.atour.dicts.db.atour_pms.FolioTransApproveTypeEnum
     **/
    private Integer approveType;

    /**
     * 审批类型名
     **/
    private String approveTypeName;

    /**
     * 审批状态枚举 0-其他 1-待审批 2-审批通过 3-审批拒绝 4-已撤回 5-审批过期
     *
     * @see com.atour.activity.common.enums.ApproveStateEnum
     */
    @ApiModelProperty(value = "审批状态枚举")
    private Integer approveState;

    /**
     * 审批状态枚举名
     *
     */
    @ApiModelProperty(value = "审批状态枚举")
    private String approveStateName;


    /**
     * 记录创建时间  yyyy-MM-dd HH:mm:ss
     **/
    private String createTime;

}
