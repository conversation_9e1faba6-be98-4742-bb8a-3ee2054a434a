package com.atour.hotel.module.approve.enums;

import java.util.Objects;

/**
 * 枚举: 调账报表 时间粒度
 *
 * <AUTHOR>
 * @date 2020年9月10日10:52:38
 */
public enum AdjustTransTimeTypeEnum {

    /**
     * 日
     */
    DAY("日", 1),

    /**
     * 周
     */
    WEEK("周", 2),

    /**
     * 月
     */
    MONTH("月", 3),

    /**
     * 年
     */
    YEAR("年", 4),

    ;

    private String desc;

    private Integer value;

    AdjustTransTimeTypeEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value
     * @return
     */
    public static AdjustTransTimeTypeEnum getInstance(Integer value) {

        for (AdjustTransTimeTypeEnum obj : AdjustTransTimeTypeEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }


    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }
}
