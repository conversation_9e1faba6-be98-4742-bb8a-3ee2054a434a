package com.atour.hotel.module.approve.app.controller;

import com.atour.api.bean.ApiResult;
import com.atour.api.bean.ApiResultFactory;
import com.atour.api.bean.ApiSchemaResult;
import com.atour.hotel.common.page.Pager;
import com.atour.hotel.framework.annotation.RoleFilterAnnotation;
import com.atour.hotel.module.approve.app.request.FolioTransApproveHandleParam;
import com.atour.hotel.module.approve.app.request.FolioTransDetailParam;
import com.atour.hotel.module.approve.app.request.SettlementActivityListParam;
import com.atour.hotel.module.approve.app.request.SettlementActivityParam;
import com.atour.hotel.module.approve.app.response.FolioTransSubmitApprovalDTO;
import com.atour.hotel.module.approve.app.response.SettlementDetailDTO;
import com.atour.hotel.module.approve.app.response.SettlementModifyDTO;
import com.atour.hotel.module.approve.app.service.SettlementCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Objects;

/**
 * 结账中心活动审批 Controller
 *
 * <AUTHOR>
 * @date 2020/4/16
 */
@Api(value = "结账中心活动审批")
@Slf4j
@RestController
@RequestMapping(value = "/api/web/approve/settlement/activity", produces = {"application/json;charset=UTF-8"})
public class SettlementCenterController {


    /**
     * 详情的data-schema定义。第一次请求时加载
     */
    private String detailSchemaContent;

    @Resource
    private SettlementCenterService settlementCenterService;

    /**
     * 提交申请
     *
     * @param param 请求参数
     */
    @ApiOperation(value = "提交申请", httpMethod = "POST")
    @RequestMapping(value = "/submitApprove", method = RequestMethod.POST)
    public ApiSchemaResult<FolioTransSubmitApprovalDTO> submitApprove(@Valid @RequestBody SettlementActivityParam param) {
        return ApiResultFactory.buildSuccessApiSchemaResult(settlementCenterService.submitApprove(param));
    }

    /**
     * 重新提交申请
     *
     * @param param 请求参数
     */
    @ApiOperation(value = "重新提交申请", httpMethod = "POST")
    @RequestMapping(value = "/resubmitApprove", method = RequestMethod.POST)
    public ApiSchemaResult<FolioTransSubmitApprovalDTO> resubmitApprove(
            @Valid @RequestBody SettlementActivityParam param) {
        return ApiResultFactory.buildSuccessApiSchemaResult(settlementCenterService.resubmitApprove(param));
    }

    /**
     * 申请详情
     *
     * @param param 申请详情
     */
    @ApiOperation(value = "申请详情", httpMethod = "POST")
    @PostMapping(value = "/detail")
    public ApiSchemaResult<SettlementDetailDTO> detail(@Valid @RequestBody FolioTransDetailParam param) {
        return ApiResultFactory.buildSuccessApiSchemaResult(settlementCenterService.detail(param), getSchemaOfDetail());
    }

    private String getSchemaOfDetail() {
        // 待抽取schema 启动时注解处理器加载(需要定义在mvc容器)
        if (Objects.isNull(detailSchemaContent)) {
            String detailSchemaFile = "schema/SettlementDetailDTO.json";
            ClassPathResource classPathResource = new ClassPathResource(detailSchemaFile);
            try {
                detailSchemaContent = IOUtils.toString(classPathResource.getInputStream());
            } catch (IOException e) {
                log.error("read schemaFile error " + detailSchemaFile, e);
                detailSchemaContent = StringUtils.EMPTY;
            }
            return detailSchemaContent;
        }

        return detailSchemaContent;
    }

    @GetMapping(value = "/list")
    @ApiOperation(value = "审批列表", httpMethod = "GET")
    @RoleFilterAnnotation(key = "ADJUST_TRANS_DETAIL_REPORT")
    public ApiResult<Pager<SettlementModifyDTO>> list(@Valid SettlementActivityListParam param) {
        return ApiResult.success(settlementCenterService.adjustList(param));
    }

    /**
     * 撤回审批
     *
     * @param param 撤回请求
     */
    @ApiOperation(value = "撤回审批", httpMethod = "POST")
    @RequestMapping(value = "/recallApprove", method = RequestMethod.POST)
    public ApiSchemaResult<Boolean> recallApprove(@Valid @RequestBody FolioTransApproveHandleParam param) {
        settlementCenterService.recallApprove(param);
        return ApiResultFactory.buildSuccessApiSchemaResult(Boolean.TRUE);
    }

    /**
     * 通过审批
     *
     * @param param 通过请求
     */
    @ApiOperation(value = "通过审批", httpMethod = "POST")
    @RequestMapping(value = "/passApprove", method = RequestMethod.POST)
    public ApiSchemaResult<Boolean> passApprove(@Valid @RequestBody FolioTransApproveHandleParam param) {
        settlementCenterService.passApprove(param);
        return ApiResultFactory.buildSuccessApiSchemaResult(Boolean.TRUE);
    }

    /**
     * 拒绝审批
     *
     * @param param 拒绝请求
     */
    @ApiOperation(value = "拒绝审批", httpMethod = "POST")
    @RequestMapping(value = "/refuseApprove", method = RequestMethod.POST)
    public ApiSchemaResult<Boolean> refuseApprove(@Valid @RequestBody FolioTransApproveHandleParam param) {
        settlementCenterService.refuseApprove(param);
        return ApiResultFactory.buildSuccessApiSchemaResult(Boolean.TRUE);
    }
}
