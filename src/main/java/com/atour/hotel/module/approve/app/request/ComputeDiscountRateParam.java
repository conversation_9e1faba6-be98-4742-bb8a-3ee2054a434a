package com.atour.hotel.module.approve.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 房费调整-折扣率计算 参数
 * @author: 万象
 * @date: 2020年8月28日15:01:45
 */
@Valid
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComputeDiscountRateParam implements Serializable {

    /**
     * 酒店id
     */
    @NotNull(message = "酒店id不能为空")
    @Positive(message = "酒店id不合法")
    @ApiModelProperty(value = "酒店id", dataType = "integer")
    private Integer chainId;

    /**
     * 房单id
     */
    @NotNull(message = "房单id不能为空")
    @Positive(message = "房单id不合法")
    @ApiModelProperty(value = "房单id", dataType = "Long")
    private Long folioId;

    /**
     * 申请金额
     */
    @NotNull(message = "申请金额不能为空")
    @Positive(message = "申请金额不合法")
    @ApiModelProperty(value = "申请金额", dataType = "BigDecimal")
    private BigDecimal applyAmount;

}
