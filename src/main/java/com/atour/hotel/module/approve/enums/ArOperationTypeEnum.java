package com.atour.hotel.module.approve.enums;

import java.util.Objects;

/**
 * 枚举: AR审批操作权限
 *
 * <AUTHOR>
 * @date 2021年7月8日10:52:43
 */
public enum ArOperationTypeEnum {

    /**
     * 查看权限
     */
    READ("查看权限", 0),

    /**
     * 审核权限
     */
    AUDIT("审核权限", 1),

    /**
     * 撤回权限
     */
    WITHDRAW("撤回权限", 2),
    /**
     * 重新提交
     */
    RESUBMIT_APPROVE("重新提交", 3),
    /**
     * 审核+撤回
     */
    AUDIT_AND_WITHDRAW("审核+撤回", 4),

    ;

    private String desc;

    private Integer value;

    ArOperationTypeEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value
     * @return
     */
    public static ArOperationTypeEnum getInstance(Integer value) {

        for (ArOperationTypeEnum obj : ArOperationTypeEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }
}
