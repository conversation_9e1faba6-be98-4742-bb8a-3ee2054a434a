package com.atour.hotel.module.approve.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 附件信息
 *
 * <AUTHOR>
 * @date 2020年5月13日
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "FileInfoDTO", description = "附件信息")
public class FileInfoDTO implements Serializable {
    /**
     * 文件唯一标识key
     */
    @ApiModelProperty(value = "文件唯一标识key", required = true)
    private String ossKey;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String fileName;

    /**
     * 文件类型
     */
    private String mimeType;

    /**
     * 大小(单位:字节)
     */
    private long size;

    /**
     * 文件大小（ x KB)
     */
    private String fileDisplaySize;

    /**
     * 预览地址
     * 注意:(私有空间的预览地址会有时效性问题)
     */
    @ApiModelProperty(value = "预览地址", required = true)
    private String showUrl;

}
