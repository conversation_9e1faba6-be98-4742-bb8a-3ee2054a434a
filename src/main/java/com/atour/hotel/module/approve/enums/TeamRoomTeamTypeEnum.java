package com.atour.hotel.module.approve.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 团队房其他费用类型
 */
@Getter
public enum TeamRoomTeamTypeEnum {
    OTHER(0, "其他"),
    BUSINESS_GROUP(1, "商务团"),
    TOURIST_GROUP(2, "旅游团"),
    REQUISITION(4, "征用"),
    ;

    /**
     * 英文code, 用于代码计算
     */
    private int code;

    /**
     * 中文名称
     */
    private String name;

    TeamRoomTeamTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TeamRoomTeamTypeEnum getEnumByCode(int code) {
        for (TeamRoomTeamTypeEnum value : TeamRoomTeamTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return TeamRoomTeamTypeEnum.OTHER;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
