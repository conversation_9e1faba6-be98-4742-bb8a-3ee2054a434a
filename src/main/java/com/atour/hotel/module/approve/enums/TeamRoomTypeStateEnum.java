package com.atour.hotel.module.approve.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 团队房订单状态
 * 审批状态 0,待审核，1,通过，2拒绝
 */
@Getter
public enum TeamRoomTypeStateEnum {
    SUBMIT(0, "待审批"),
    PASS(1, "审批通过"),
    REFUSE(2, "审批拒绝"),
    APPROVING(3, "审批中"),
    RECALL(4, "已撤回"),
    ;

    /**
     * 英文code, 用于代码计算
     */
    private int code;

    /**
     * 中文名称
     */
    private String name;

    TeamRoomTypeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TeamRoomTypeStateEnum getEnumByCode(int code) {
        for (TeamRoomTypeStateEnum value : TeamRoomTypeStateEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return SUBMIT;
    }
}
