package com.atour.hotel.module.approve.web.trans.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("入住房型信息保存参数")
public class TeamRoomTypeParam {
    private Long id;
    /**
     *
     */
    @ApiModelProperty(value = "订单id")
    private Long teamRoomId;
    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private Integer roomTypeId;
    /**
     * 房型名称
     */
    @ApiModelProperty(value = "房型名称")
    private String roomTypeName;
    /**
     * 折扣率
     */
    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;
    /**
     * 审批状态 0,待审核，1,通过，2拒绝, 3审批中
     */
    @ApiModelProperty(value = "审批状态 0,待审核，1,通过，2拒绝, 3审批中", allowableValues = "range[0,4]")
    private Integer approveState;
    /**
     * 顺序序号
     */
    @ApiModelProperty(value = "顺序序号")
    private Integer orderNum;

    /**
     * 时间范围数组
     */
    @ApiModelProperty(value = "时间范围数组")
    private List<TeamRoomTimeRangeParam> timeRangeArray;
    /**
     * 总收益
     */
    @ApiModelProperty(value = "总收益")
    private BigDecimal totalProfit;
    /**
     * 平均门市价
     */
    @ApiModelProperty(value = "平均门市价")
    private BigDecimal avgRoomPrice;

}
