package com.atour.hotel.module.approve.web.trans.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 调账数量分布报表-导出
 *
 * <AUTHOR>
 * @date 2020年10月11日15:37:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "FolioTransNumExportDTO", description = "调账数量分布报表-导出")
public class FolioTransNumExportDTO extends BaseRowModel implements Serializable {

    /**
     * 时间
     */
    @ExcelProperty(value = "时间")
    private String timeStr;

    /**
     * 区域名称
     */
    @ExcelProperty(value = "区域")
    private String regionName;

    /**
     * 门店名称
     */
    @ExcelProperty(value = "门店")
    private String chainName;

    /**
     * 城市名称
     */
    @ExcelProperty(value = "城市")
    private String cityName;

    /**
     * 待审批数
     */
    @ExcelProperty(value = "待审批数")
    private Integer waitNum;

    /**
     * 已撤回数
     */
    @ExcelProperty(value = "已撤回数")
    private Integer withdrawNum;

    /**
     * 审批通过数
     */
    @ExcelProperty(value = "审批通过数")
    private Integer passNum;

    /**
     * 审批拒绝数
     */
    @ExcelProperty(value = "审批拒绝数")
    private Integer refuseNum;

}
