package com.atour.hotel.module.approve.web.trans.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApproveStateDto implements Serializable {


    @ApiModelProperty(value = "审批编号")
    private String requestId;
    /**
     * 团队房审批状态 1-待审批、2-审批通过、3-审批拒绝、4-已撤回、5-待提交、6-已过期、7-住中入团申请 8-已上传合同 - 0-其他
     */
    @ApiModelProperty(value = "团队房审批状态 1-待审批、2-审批通过、3-审批拒绝、4-已撤回、5-待提交、6-已过期、7-住中入团申请 8-已上传合同 - 0-其他")
    private Integer approveState;
}
