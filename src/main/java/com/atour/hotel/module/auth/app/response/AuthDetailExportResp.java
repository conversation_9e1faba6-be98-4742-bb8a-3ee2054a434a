package com.atour.hotel.module.auth.app.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据层:全员授权明细导出返回实例
 * 
 * <AUTHOR>
 * @date 2020-03-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AuthDetailExportResp", description = "全员授权明细导出返回实例")
public class AuthDetailExportResp {
    /**
     * ID主键
     *
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 酒店id
     *
     */
    @ApiModelProperty(value = "酒店id")
    private Integer chainId;

    /**
     * 房单id
     *
     */
    @ApiModelProperty(value = "房单id")
    private Long folioId;

    /**
     * 房间号
     *
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 授权项目(1:伴手礼 2:纪念日 3:服务产品 4:水果盘 5:打车 6:邮寄费 7:停车费 8:OTC药品 9:送客就医 10:其他授权)
     *
     */
    @ApiModelProperty(value = "授权项目(1:伴手礼 2:纪念日 3:服务产品 4:水果盘 5:打车 6:邮寄费 7:停车费 8:OTC药品 9:送客就医 10:其他授权)")
    private Integer authProject;

    /**
     * 授权金额
     *
     */
    @ApiModelProperty(value = "授权金额,元单位")
    private BigDecimal authAmount;

    /**
     * 授权营业日
     *
     */
    @ApiModelProperty(value = "授权营业日")
    private Date authDate;

    /**
     * 授权人
     */

    /**
     * 授权客人类型(1:住店客人 2:非住店客人)
     *
     */
    @ApiModelProperty(value = "授权客人类型(1:住店客人 2:非住店客人)")
    private Integer authGuestType;


    /**
     * 会员类型(0:非会员 1:会员 2：非住店客人)
     *
     */
    @ApiModelProperty(value = "会员类型(0:非会员 1:会员)")
    private Integer mebType;


    /**
     * 删除标记 0:有效 1:无效
     *
     */
    @ApiModelProperty(value = "会员类型(0:有效 1:无效")
    private Integer deleteFlag;


    /**
     * 备注
     *
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 入住人姓名
     *
     */
    @ApiModelProperty(value = "入住人姓名")
    private String guestName;

    /**
     * 授权客人名称
     *
     */
    @ApiModelProperty(value = "授权客人名称")
    private String authGuestTypeName;

    /**
     * 会员名称 TODO
     *
     */
    @ApiModelProperty(value = "会员名称")
    private Integer mebTypeName;

    /**
     * 授权项目名称
     *
     */
    @ApiModelProperty(value = "授权项目名称")
    private String authProjectName;

    /**
     * 酒店名称
     *
     */
    @ApiModelProperty(value = "酒店名称")
    private String chainName;

    /**
     * 删除标记名称
     *
     */
    @ApiModelProperty(value = "会员类型(0:有效 1:无效")
    private String deleteFlagName;

}