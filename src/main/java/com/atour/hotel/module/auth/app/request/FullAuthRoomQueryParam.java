package com.atour.hotel.module.auth.app.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * WEB层: 根据营业日房号查询房单信息
 *
 * <AUTHOR>
 * @since 2020/03/03
 */
@ApiModel(value = "FullAuthRoomQueryParam", description = "根据营业日房号查询房单信息请求参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FullAuthRoomQueryParam implements Serializable {

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    /**
     * 查询营业日
     */
    @ApiModelProperty(value = "查询营业日")
    @NotBlank(message = "查询营业日不能为空")
    private String accDate;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    @NotNull(message = "房间号不能为空")
    private Integer roomNo;
}
