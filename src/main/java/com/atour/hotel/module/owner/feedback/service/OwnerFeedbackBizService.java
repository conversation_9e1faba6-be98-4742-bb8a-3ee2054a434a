package com.atour.hotel.module.owner.feedback.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atour.api.bean.PageInfo;
import com.atour.api.bean.PageResult;
import com.atour.dicts.db.center.sys_message.MessageSourceEnum;
import com.atour.dicts.db.center.sys_message.SysMessageTypeEnum;
import com.atour.dicts.enums.atourApp.PushBusinessEnum;
import com.atour.dicts.enums.rbac.UserOfTypeEnum;
import com.atour.hlm.dto.ExtendInfoObjDto;
import com.atour.hlm.dto.franchise.QueryPrincipalUserDTO;
import com.atour.hlm.enums.FeedbackStatusEnum;
import com.atour.hlm.franchise.newfeedback.dto.FeedbackDictDto;
import com.atour.hlm.remote.NewFeedbackRemote;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.framework.utils.FileExtUtil;
import com.atour.hotel.module.approve.app.dto.FileInfoDTO;
import com.atour.hotel.module.common.service.CommonOssService;
import com.atour.hotel.module.common.service.PushJpushService;
import com.atour.hotel.module.common.service.SysMessageService;
import com.atour.hotel.module.dkf.enums.MsgTypeEnum;
import com.atour.hotel.module.hotel.service.FranchiseUserService;
import com.atour.hotel.module.hotel.utils.UserUtil;
import com.atour.hotel.module.owner.feedback.OwnerUserIdUtil;
import com.atour.hotel.module.owner.feedback.enums.OwnerFeedbackStatusEnum;
import com.atour.hotel.module.owner.feedback.request.FeedbackAddParam;
import com.atour.hotel.module.owner.feedback.request.FeedbackConfirmParam;
import com.atour.hotel.module.owner.feedback.request.FeedbackQueryListParam;
import com.atour.hotel.module.owner.feedback.response.CategoryDto;
import com.atour.hotel.module.owner.feedback.response.FeedbackListResponse;
import com.atour.hotel.module.owner.feedback.response.FeedbackProcessResp;
import com.atour.hotel.module.school.response.MessageResponse;
import com.atour.hotel.module.school.wrapper.PushParamWrapper;
import com.atour.hotel.persistent.franchise.entity.FranchiseUserEntity;
import com.atour.notify.api.enums.JPushPlatformEnum;
import com.atour.notify.api.enums.JPushSceneTypeEnum;
import com.atour.notify.api.enums.JPushTypeEnum;
import com.atour.notify.api.params.AllChannelPushParam;
import com.atour.notify.api.params.JPushPersonalParam;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.security.Base64Util;
import com.atour.task.center.api.enums.TaskChannelEnum;
import com.atour.task.center.api.enums.UserPrefixEnum;
import com.atour.task.center.api.request.TaskInfoAuditReq;
import com.atour.task.center.api.request.TaskInfoCreateReq;
import com.atour.task.center.api.request.TaskInfoListReq;
import com.atour.task.center.api.request.TaskInfoUpdateReq;
import com.atour.task.center.api.request.attachment.AttachmentReqVO;
import com.atour.task.center.api.request.evaluation.TaskEvaluationSaveReqVO;
import com.atour.task.center.api.request.message.MessageReqVO;
import com.atour.task.center.api.request.process.TaskProcessQueryReqVO;
import com.atour.task.center.api.request.task.TaskInfoDetailReqVO;
import com.atour.task.center.api.response.TaskInfoDetailResp;
import com.atour.task.center.api.response.TaskInfoEvaluationResp;
import com.atour.task.center.api.response.TaskInfoListResp;
import com.atour.task.center.api.response.TaskInfoProcessResp;
import com.atour.task.center.api.response.attachment.AttachmentRespVO;
import com.atour.task.center.api.response.process.TaskProcessQueryRespVO;
import com.atour.task.center.api.service.ITaskDictService;
import com.atour.task.center.api.service.ITaskInfoEvaluationService;
import com.atour.task.center.api.service.ITaskInfoMessageService;
import com.atour.task.center.api.service.ITaskInfoService;
import com.atour.task.center.api.service.ITaskProcessService;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import com.yaduo.infras.core.base.bean.SortProperty;
import com.yaduo.infras.core.logging.util.RPCContext;
import com.yaduo.resource.service.api.oss.dto.response.OssFileInfoDTO;
import com.yaduo.resource.service.api.oss.dto.response.OssPreviewListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OwnerFeedbackBizService {

    @Resource
    private ITaskDictService taskDictServiceRemote;

    @Resource
    private ITaskInfoService taskInfoServiceRemote;
    @Resource
    private ITaskProcessService taskProcessServiceRemote;
    @Resource
    private ITaskInfoEvaluationService taskInfoEvaluationService;
    @Resource
    private CommonOssService commonOssService;
    @Resource
    private PushJpushService pushJpushService;
    @Resource
    private SysMessageService sysMessageService;
    @Resource
    private NewFeedbackRemote newFeedbackRemote;
    @Autowired
    private FranchiseUserService franchiseUserService;
    @Resource
    private ITaskInfoMessageService taskInfoMessageService;
    @Resource
    @Qualifier("feedbackPushExecutorService")
    private ExecutorService feedbackPushExecutorService;

    @Value("${jiguang.ownerAppKey}")
    private String ownerAppKey;

    @Value("${newFeedback.hlmUrl:}")
    private String newFeedbackUrl;

    private static final String TASK_CENTER_OWNER_FEEDBACK_SOURCE_CODE = "1";
    //反馈回复
    private static final String PROCESS_TYPE_REPLY = "1";

    public static final String TEMPLATE_COMMENT_STATUS_NO_SOLVE = "【反馈点评推送】 反馈 (%s) 业主已进行确认，确认结果为：未解决；原因：%s。请尽快跟进处理。网址链接：%s";
    public static final String TEMPLATE_COMMENT_STATUS_SOLVED = "【反馈点评推送】反馈 (%s) 业主已进行确认，确认结果为：已解决；打分：%s分；评价：%s。请知悉。网址链接：%s";

    public PageResult<FeedbackListResponse> queryList(FeedbackQueryListParam feedbackQueryListParam,
                                                      UserPermissionDTO userPermissionDTO) {


        TaskInfoListReq taskInfoListReq = new TaskInfoListReq();
        //只能查自己创建的反馈
        taskInfoListReq.setCreateUser(Arrays.asList(OwnerUserIdUtil.toTsUserId(userPermissionDTO)));
        taskInfoListReq.setTaskStatus(OwnerFeedbackStatusEnum.getTaskStatusSetByOwnerStatus(feedbackQueryListParam.getStatus()));
        taskInfoListReq.setTaskChannel(TaskChannelEnum.TALK_SHOW.getValue());
        taskInfoListReq.setSourceCode(TASK_CENTER_OWNER_FEEDBACK_SOURCE_CODE);

        SortProperty sortProperty = new SortProperty();
        sortProperty.setProperty("id");
        sortProperty.setDirection("desc");
        AtourRequest<RequestList<TaskInfoListReq>> atourRequest = RPCContext.createRequest(
                RequestList.create(taskInfoListReq,
                        feedbackQueryListParam.getPageInfo().getPageNo(), feedbackQueryListParam.getPageInfo().getPageSize(),
                        Arrays.asList(sortProperty)));

        AtourResponse<ResponseList<TaskInfoListResp>> atourResponse = taskInfoServiceRemote.queryList(atourRequest);
        if (!atourResponse.isSuccess()) {
            return new PageResult<>();
        }

        List<FeedbackListResponse> list = new ArrayList<>();
        PageResult<FeedbackListResponse> pageResult = new PageResult<>(feedbackQueryListParam.getPageInfo(), list);
        ResponseList<TaskInfoListResp> respResponseList = atourResponse.getData();
        List<TaskInfoListResp> taskInfoListRespList = respResponseList.getContent();
        taskInfoListRespList.forEach(taskInfo -> {
            FeedbackListResponse feedbackListResponse = buildFeedbackListResponse(taskInfo);
            list.add(feedbackListResponse);
        });
        pageResult.setPage(new PageInfo(respResponseList.getPage(), respResponseList.getSize(), respResponseList.getTotal()));
        return pageResult;
    }

    private FeedbackListResponse buildFeedbackListResponse(TaskInfoListResp taskInfo) {
        Date now = DateUtil.getCurrentDateTime();
        FeedbackListResponse feedbackListResponse = new FeedbackListResponse();
        feedbackListResponse.setId(taskInfo.getId());
        feedbackListResponse.setStatus(OwnerFeedbackStatusEnum.getCodeByFeedbackStatusEnum(FeedbackStatusEnum.codeOf(taskInfo.getTaskStatus())));
        feedbackListResponse.setType(taskInfo.getTaskType());
        feedbackListResponse.setDescription(taskInfo.getTaskDesc());
        if (taskInfo.getReplyTime() != null) {
            feedbackListResponse.setReplyTime(taskInfo.getReplyTime());
            long time = taskInfo.getReplyTime().getTime();
            feedbackListResponse.setAfterReplyHours(scaleHours(now, time));
        }
        feedbackListResponse.setScore(taskInfo.getTaskScore());
        feedbackListResponse.setCreateTime(taskInfo.getCreateTime());

        List<FeedbackProcessResp> feedbackProcessResps = getFeedbackProcessRespList(taskInfo.getId());
        feedbackListResponse.setFeedbackProcessRespList(feedbackProcessResps);
        return feedbackListResponse;
    }

    private static String scaleHours(Date now, long time) {
        double doubleHours = BigDecimal.valueOf((now.getTime() - time)).divide(BigDecimal.valueOf(1000 * 3600), 1, RoundingMode.CEILING).doubleValue();
        if (doubleHours < 1) {
            return "1";
        } else {
            return BigDecimal.valueOf(doubleHours).setScale(0, RoundingMode.CEILING).toString();
        }
    }

    /**
     * 查询任务处理过程列表
     *
     * @param taskId
     * @return
     */
    private List<FeedbackProcessResp> getFeedbackProcessRespList(Long taskId) {
        List<TaskProcessQueryRespVO> taskProcessQueryRespVOList = queryTaskProcessQueryRespVOS(taskId);
        List<FeedbackProcessResp> feedbackProcessResps = Safes.of(taskProcessQueryRespVOList)
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getProcessResult()))
                .sorted(Comparator.comparing(TaskProcessQueryRespVO::getId).reversed())
                .map(FeedbackProcessResp::buildFeedbackProcessResp)
                .collect(Collectors.toList());
        List<String> ossKeys = feedbackProcessResps.stream()
                .map(item -> item.getProcessAttachmentList().stream()
                        .map(FileInfoDTO::getOssKey).collect(Collectors.toList()))
                .flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, OssFileInfoDTO> ossKeyFileInfoMap = commonOssService.batchQueryOssFileInfoDtoMap(ossKeys);

        Map<String, OssPreviewListDTO> previewByOssKeyMap = commonOssService.getPreviewByOssKeyList(ossKeys);
        feedbackProcessResps.forEach(feedbackProcessRespItem -> {
            feedbackProcessRespItem.getProcessAttachmentList().forEach(attachmentItem -> {
                OssPreviewListDTO ossPreviewListDTO = previewByOssKeyMap.get(attachmentItem.getOssKey());
                if (ossPreviewListDTO != null) {
                    attachmentItem.setFileName(ossPreviewListDTO.getMimeName());
                    attachmentItem.setShowUrl(ossPreviewListDTO.getShowUrl());
                    attachmentItem.setMimeType(ossPreviewListDTO.getMimeType());
                }
                OssFileInfoDTO ossFileInfoDTO = ossKeyFileInfoMap.get(attachmentItem.getOssKey());
                if (ossFileInfoDTO != null) {
                    attachmentItem.setSize(ossFileInfoDTO.getSize());
                    attachmentItem.setFileDisplaySize(FileExtUtil.readableFileSize(ossFileInfoDTO.getSize()));
                }
            });
        });
        return feedbackProcessResps;
    }


    private List<TaskProcessQueryRespVO> queryTaskProcessQueryRespVOS(Long taskId) {
        List<TaskProcessQueryRespVO> taskProcessQueryRespVOList = Lists.newArrayList();
        AtourRequest<RequestList<TaskProcessQueryReqVO>> request = RPCContext.createRequest(RequestList.create(TaskProcessQueryReqVO.builder()
                .processType(PROCESS_TYPE_REPLY)
                .taskId(taskId)
                .build(), 1, 500));
        AtourResponse<ResponseList<TaskProcessQueryRespVO>> taskProcessList = taskProcessServiceRemote.getTaskProcessList(request);
        if (!taskProcessList.isSuccess()) {
            return Collections.emptyList();
        }
        ResponseList<TaskProcessQueryRespVO> responseList = taskProcessList.getData();
        taskProcessQueryRespVOList.addAll(responseList.getContent());
        return taskProcessQueryRespVOList;
    }

    /**
     * 新增反馈
     *
     * @param feedbackAddParam
     * @param userPermissionDTO
     * @return
     */
    public Long addFeedback(FeedbackAddParam feedbackAddParam, UserPermissionDTO userPermissionDTO) {
        QueryPrincipalUserDTO queryPrincipalUserDTO = new QueryPrincipalUserDTO();
        queryPrincipalUserDTO.setCategory(feedbackAddParam.getRootCategoryId());
        queryPrincipalUserDTO.setHotelId(String.valueOf(feedbackAddParam.getChainId()));
        com.atour.hlm.common.AtourResponse<String> atourResponse = newFeedbackRemote.getAutoDistributePrincipalUserInfo(queryPrincipalUserDTO);
        if (!atourResponse.isSuccess()) {
            throw new BusinessException("添加反馈失败，请稍后再试！");
        }
        FranchiseUserEntity franchiseUserEntity = franchiseUserService.selectUserById(userPermissionDTO.getUserId());

        TaskInfoCreateReq taskInfoCreateReq = new TaskInfoCreateReq();
        taskInfoCreateReq.setTaskChannel(TaskChannelEnum.TALK_SHOW.getValue());
        //业主端
        taskInfoCreateReq.setSourceCode(TASK_CENTER_OWNER_FEEDBACK_SOURCE_CODE);
        taskInfoCreateReq.setTaskType(null);
        taskInfoCreateReq.setTaskCategory(feedbackAddParam.getRootCategoryId());
        taskInfoCreateReq.setSubTaskCategory(null);
        //初始状态
        taskInfoCreateReq.setTaskStatus(FeedbackStatusEnum.NEW.getCode());
        taskInfoCreateReq.setTaskDesc(feedbackAddParam.getDescription());
        taskInfoCreateReq.setIsEmphasis(null);
        taskInfoCreateReq.setPriority(null);
//        taskInfoCreateReq.setPrincipalUser(atourResponse.getResult());
        taskInfoCreateReq.setPrincipalUserList(Collections.singletonList(atourResponse.getResult()));
        taskInfoCreateReq.setSolverUserList(Collections.singletonList(atourResponse.getResult()));
        taskInfoCreateReq.setCreateUser(OwnerUserIdUtil.toTsUserId(userPermissionDTO));
        taskInfoCreateReq.setAttachmentList(feedbackAddParam.getOssKeys().stream().map(ossKey -> AttachmentReqVO.builder().ossKey(ossKey).description("").build()).collect(Collectors.toList()));
        taskInfoCreateReq.setChainName(feedbackAddParam.getChainName());
        taskInfoCreateReq.setChainId(String.valueOf(feedbackAddParam.getChainId()));
        taskInfoCreateReq.setTag(null);
        ExtendInfoObjDto extendInfoObjDto = new ExtendInfoObjDto();
        extendInfoObjDto.setFeedBackPerson(userPermissionDTO.getUserName());
        extendInfoObjDto.setIdentity(String.valueOf(franchiseUserEntity.getRole()));
        extendInfoObjDto.setMobile(userPermissionDTO.getMobile());
        extendInfoObjDto.setResponseState(Boolean.FALSE);
        taskInfoCreateReq.setExtendInfo(JSON.toJSONString(extendInfoObjDto));
        AtourRequest<TaskInfoCreateReq> request = RPCContext.createRequest(taskInfoCreateReq);
        AtourResponse<Long> createTaskResp = taskInfoServiceRemote.createTask(request);
        if (createTaskResp.isSuccess()) {
            //调用接口异步发送企微消息
            newFeedbackRemote.sendNewFeedbackCopyToUsers(createTaskResp.getData());
            return createTaskResp.getData();
        } else {
            log.info("调用 task-center-biz 创建工单失败，error:{}", createTaskResp.getHeader().getExp());
            throw new BusinessException("提交失败，请稍后再试！");
        }
    }

    /**
     * 查询分类列表
     *
     * @return
     */
    public List<CategoryDto> queryCategoryList() {
        com.atour.hlm.common.AtourResponse<List<FeedbackDictDto>> response = newFeedbackRemote.getFeedbackDict("task_category", false, "add", null);
        if (!response.isSuccess()) {
            throw new BusinessException("请稍后再试！");
        }
        return response.getResult().stream()
                .map(item -> CategoryDto.builder()
                .dictKey(item.getDictKey())
                .dictValue(item.getDictValue())
                .build())
                .collect(Collectors.toList());
    }

    /**
     * 查询反馈详情
     *
     * @param detailId
     * @return
     */
    public FeedbackListResponse getDetailById(Long detailId) {
        TaskInfoDetailResp detailResp = getTaskInfoDetailResp(detailId, true, true);
        validateCreateUser(detailResp);
        return buildFeedbackListResponse(detailResp);
    }

    private static void validateCreateUser(TaskInfoDetailResp detailResp) {
        UserPermissionDTO userPermissionDTO = CommonParamsManager.getLocalUserHotel();
        if (!Objects.equals(OwnerUserIdUtil.fromTsUserId(detailResp.getCreateUser()), userPermissionDTO.getUserId())) {
            throw new BusinessException("您没有此反馈的查看权限噢");
        }
    }

    private TaskInfoDetailResp getTaskInfoDetailResp(Long detailId, boolean isQueryEvaluation, boolean isQueryProcess) {
        if (detailId == null) {
            return null;
        }
        TaskInfoDetailReqVO taskInfoDetailReqVO = new TaskInfoDetailReqVO();
        taskInfoDetailReqVO.setTaskId(detailId);
        taskInfoDetailReqVO.setQueryEvaluation(isQueryEvaluation);
        taskInfoDetailReqVO.setQueryProcess(isQueryProcess);
        AtourResponse<TaskInfoDetailResp> response = taskInfoServiceRemote.detail(RPCContext.createRequest(taskInfoDetailReqVO));
        if (!response.isSuccess()) {
            log.info("调用 task-center-biz 查询工单失败，error:{}", response.getHeader().getExp());
            throw new BusinessException("查询失败请稍后再试！");
        }
        return response.getData();
    }

    /**
     * Builds the feedback list response
     *
     * @param taskInfo
     * @return
     */
    private FeedbackListResponse buildFeedbackListResponse(TaskInfoDetailResp taskInfo) {
        Date now = DateUtil.getCurrentDateTime();
        FeedbackListResponse feedbackListResponse = new FeedbackListResponse();
        feedbackListResponse.setId(taskInfo.getId());
        feedbackListResponse.setStatus(OwnerFeedbackStatusEnum.getCodeByFeedbackStatusEnum(FeedbackStatusEnum.codeOf(taskInfo.getTaskStatus())));
        feedbackListResponse.setType(taskInfo.getTaskType());
        feedbackListResponse.setDescription(taskInfo.getTaskDesc());
        feedbackListResponse.setCreateTime(taskInfo.getCreateTime());
        if (taskInfo.getReplyTime() != null) {
            feedbackListResponse.setReplyTime(taskInfo.getReplyTime());
            long time = taskInfo.getReplyTime().getTime();
            feedbackListResponse.setAfterReplyHours(BigDecimal.valueOf(Math.round((now.getTime() - time) / 1000.0 / 3600.0)).setScale(1, RoundingMode.HALF_DOWN).toString());
        }
        feedbackListResponse.setScore(taskInfo.getTaskScore());
        List<FeedbackProcessResp> taskProcessQueryRespVOList = getFeedbackProcessRespList(taskInfo.getId());
        feedbackListResponse.setFeedbackProcessRespList(taskProcessQueryRespVOList);
//        String score = Safes.of(taskInfo.getEvaluationList()).stream().findFirst().map(TaskInfoEvaluationResp::getEvaluationScore).orElse("");
//        feedbackListResponse.setScore(score);
        List<String> ossKeys = Safes.of(taskInfo.getTaskAttachmentList()).stream().map(AttachmentRespVO::getOssKey).filter(Objects::nonNull).collect(Collectors.toList());
        List<OssFileInfoDTO> ossFileInfoDTOS = commonOssService.batchQueryOssFileInfoDto(ossKeys);
        Map<String, OssFileInfoDTO> ossFileInfoDTOMap = Safes.of(ossFileInfoDTOS).stream().collect(Collectors.toMap(OssFileInfoDTO::getOssKey, Function.identity()));
        Map<String, OssPreviewListDTO> previewByOssKeyMap = commonOssService.getPreviewByOssKeyList(ossKeys, null);
        List<FileInfoDTO> fileInfoDTOList = ossKeys.stream().map(ossKey -> {
            OssPreviewListDTO ossPreviewListDTO = previewByOssKeyMap.get(ossKey);
            OssFileInfoDTO ossFileInfoDTO = ossFileInfoDTOMap.get(ossKey);
            FileInfoDTO.FileInfoDTOBuilder builder = FileInfoDTO.builder();
            if (ossPreviewListDTO != null) {
                builder.fileName(ossPreviewListDTO.getMimeName())
                        .showUrl(ossPreviewListDTO.getShowUrl())
                        .ossKey(ossKey)
                        .mimeType(ossPreviewListDTO.getMimeType());
            }
            if (ossFileInfoDTO != null) {
                builder.size(ossFileInfoDTO.getSize());
                builder.fileDisplaySize(FileExtUtil.readableFileSize(ossFileInfoDTO.getSize()));
            }
            return builder.build();
        }).collect(Collectors.toList());
        feedbackListResponse.setFilesInfoList(fileInfoDTOList);
        return feedbackListResponse;
    }


    // 1.未解决 -> 工单状态：待确认 -> 已回复
    // 2.已解决 -> 工单状态：已回复 -> 已解决，取消标记 悬而未决，更新解决时间（最后一次的回复时间）
    public void confirm(FeedbackConfirmParam feedbackConfirmParam) {
        UserPermissionDTO userPermissionDTO = CommonParamsManager.getLocalUserHotel();
        if (feedbackConfirmParam.getResolved()) {
            if (feedbackConfirmParam.getScore() != null) {
                if (feedbackConfirmParam.getScore() <= 0 || feedbackConfirmParam.getScore() > 5) {
                    throw new BusinessException("评分1-5之间");
                }
            }
        }

        TaskInfoDetailResp taskInfoDetailResp = getTaskInfoDetailResp(feedbackConfirmParam.getId(), false, true);
        //校验是否是本人的反馈
        validateCreateUser(taskInfoDetailResp);

        if (!FeedbackStatusEnum.WAIT_CONFIRMED.getCode().equals(taskInfoDetailResp.getTaskStatus())) {
            throw new BusinessException("当前状态不能确认");
        }
        //更新任务信息
        updateTaskInfo(feedbackConfirmParam, taskInfoDetailResp);
        //点评信息
        createEvaluation(feedbackConfirmParam, userPermissionDTO);

        //发送未解决消息给负责人和解决人
        if (!feedbackConfirmParam.getResolved()) {
            sendComentStatusNoSolveToPrincipalUser(feedbackConfirmParam.getId(), feedbackConfirmParam.getRespId());
        }
        //发送已解决消息给负责人和解决人
        else {
            sendComentStatusSolvedToPrincipalUser(feedbackConfirmParam.getId(), feedbackConfirmParam.getRespId());
        }

    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(FeedbackConfirmParam feedbackConfirmParam, UserPermissionDTO userPermissionDTO, TaskInfoUpdateReq taskInfoUpdateReq) {
        TaskInfoAuditReq taskInfoAuditReq = new TaskInfoAuditReq();
        taskInfoAuditReq.setTaskId(feedbackConfirmParam.getId());
        taskInfoAuditReq.setNextStatus(taskInfoUpdateReq.getTaskStatus());
        taskInfoAuditReq.setUpdateUser(OwnerUserIdUtil.toTsUserId(userPermissionDTO));
        taskInfoServiceRemote.statusChange(RPCContext.createRequest(taskInfoAuditReq));
    }

    /**
     * 更新任务信息
     *
     * @param feedbackConfirmParam
     * @param taskInfoDetailResp
     * @return
     */
    private TaskInfoUpdateReq updateTaskInfo(FeedbackConfirmParam feedbackConfirmParam, TaskInfoDetailResp taskInfoDetailResp) {
        TaskInfoUpdateReq taskInfoUpdateReq = new TaskInfoUpdateReq();
        taskInfoUpdateReq.setTaskId(feedbackConfirmParam.getId());
        JSONObject extendInfoObj = taskInfoDetailResp.getExtendInfoObj();
        if (extendInfoObj == null) {
            extendInfoObj = new JSONObject();
        }
        ExtendInfoObjDto updateExtendInfoObj = extendInfoObj.toJavaObject(ExtendInfoObjDto.class);
        if (feedbackConfirmParam.getResolved()) {
            updateExtendInfoObj.setPendingIssueState(null);
            //处理点评设置为已解决
            updateExtendInfoObj.setProcessEvaluation("1");
            taskInfoUpdateReq.setTaskStatus(FeedbackStatusEnum.SOLVE.getCode());
            Date solveTime = Safes.of(taskInfoDetailResp.getProcessList()).stream()
                    .map(TaskInfoProcessResp::getCreateTime)
                    .filter(Objects::nonNull)
                    .max(Comparator.comparing(Date::getTime))
                    .orElse(new Date());
            taskInfoUpdateReq.setSolveTime(solveTime);
            if (Objects.nonNull(feedbackConfirmParam.getScore())) {
                taskInfoUpdateReq.setTaskScore(String.valueOf(feedbackConfirmParam.getScore()));
            }
        } else {
            //处理点评设置为未解决
            updateExtendInfoObj.setProcessEvaluation("0");
            taskInfoUpdateReq.setTaskStatus(FeedbackStatusEnum.REPLY.getCode());
        }
        taskInfoUpdateReq.setExtendInfo(JSON.toJSONString(updateExtendInfoObj));
        AtourResponse<Integer> response = taskInfoServiceRemote.updateTask(RPCContext.createRequest(taskInfoUpdateReq));
        if (!response.isSuccess()) {
            log.info("调用 task-center-biz 更新工单失败，error:{}", response.getHeader().getExp());
            throw new BusinessException("操作失败，请稍后再试! ");
        }
        return taskInfoUpdateReq;
    }

    /**
     * 创建点评信息
     *
     * @param feedbackConfirmParam
     * @param userPermissionDTO
     */
    private void createEvaluation(FeedbackConfirmParam feedbackConfirmParam, UserPermissionDTO userPermissionDTO) {
        TaskEvaluationSaveReqVO taskEvaluationSaveReqVO = TaskEvaluationSaveReqVO.builder().taskId(feedbackConfirmParam.getId())
                .createUser(OwnerUserIdUtil.toTsUserId(userPermissionDTO))
                .evaluationContent(feedbackConfirmParam.getRemark())
                .isSolve(feedbackConfirmParam.getResolved())
                .processId(feedbackConfirmParam.getRespId())
                .evaluationScore(feedbackConfirmParam.getScore() != null ? String.valueOf(feedbackConfirmParam.getScore()) : "")
                .build();

        AtourResponse<Long> createEvaluationResponse = taskInfoEvaluationService.create(RPCContext.createRequest(taskEvaluationSaveReqVO));
        if (!createEvaluationResponse.isSuccess()) {
            throw new BusinessException("操作失败，请稍后再试");
        }
    }

    /**
     * 查询业主待确认的反馈数量
     *
     * @param userPermissionDTO
     * @return
     */
    public Integer queryNotConfirmCount(UserPermissionDTO userPermissionDTO) {
        TaskInfoListReq taskInfoListReq = new TaskInfoListReq();
        //只能查自己创建的反馈
        taskInfoListReq.setCreateUser(Arrays.asList(OwnerUserIdUtil.toTsUserId(userPermissionDTO)));
        taskInfoListReq.setTaskStatus(OwnerFeedbackStatusEnum.getTaskStatusSetByOwnerStatus(OwnerFeedbackStatusEnum.CONFIRM_STATUS.getCode()));
        taskInfoListReq.setTaskChannel(TaskChannelEnum.TALK_SHOW.getValue());
        taskInfoListReq.setSourceCode(TASK_CENTER_OWNER_FEEDBACK_SOURCE_CODE);

        RequestList<TaskInfoListReq> taskInfoListReqRequestList = RequestList.create(taskInfoListReq, 1, 1);
        AtourResponse<ResponseList<TaskInfoListResp>> response = taskInfoServiceRemote.queryList(RPCContext.createRequest(taskInfoListReqRequestList));
        if (!response.isSuccess()) {
            log.info("调用 task-center-biz 查询工单失败，error:{}", response.getHeader().getExp());
            throw new BusinessException("请稍后再试! ");
        } else {
            ResponseList<TaskInfoListResp> responseList = response.getData();
            int total = responseList.getTotal();
            return total;
        }
    }


    /**
     * 发送极光推送和message
     *
     * @param taskId
     * @param messageTemplate
     */
    public void sendNotice(Long taskId, String messageTemplate) {
        TaskInfoDetailResp taskInfoDetailResp = getTaskInfoDetailResp(taskId, false, false);
        if (taskInfoDetailResp == null) {
            return;
        }
        Date date = new Date();
        if (StringUtils.startsWith(taskInfoDetailResp.getCreateUser(), UserPrefixEnum.FRANCHISE_USER.getValue())) {
            String userIdStr = StringUtils.removeStart(taskInfoDetailResp.getCreateUser(), UserPrefixEnum.FRANCHISE_USER.getValue() + "_");
            Integer userId = Integer.valueOf(userIdStr);
            String newReplyMsg = String.format(messageTemplate, taskInfoDetailResp.getId());
            doPushMessageToJiGuang(date, userId, newReplyMsg);
            MessageResponse messageResponse = new MessageResponse();
            messageResponse.setChainId("0");
            messageResponse.setTitle("意见与反馈");
            messageResponse.setContent(newReplyMsg);
            messageResponse.setType(String.valueOf(MsgTypeEnum.SYSTEM_MESSAGE.getCode()));
            messageResponse.setAccDate(DateUtil.printDate(DateUtil.getCurrentDate()));
            messageResponse.setUid(String.valueOf(userId));
            messageResponse.setUrl("/comment/detail?id=" + taskId);
            sysMessageService.saveMessage(0, userId, ObjectUtil.toJsonQuietly(messageResponse), MessageSourceEnum.OWNER_FEEDBACK_V2_MSG.getValue(), SysMessageTypeEnum.MESSAGE_NOTIFY.getCode());
        }
    }


    /**
     * 发送极光推送和message
     *
     * @param createUser
     * @param newReplyMsg
     */
    public void sendRemindNotice(String createUser, String newReplyMsg) {
        Date date = new Date();
        if (StringUtils.startsWith(createUser, UserPrefixEnum.FRANCHISE_USER.getValue())) {
            Integer userId = OwnerUserIdUtil.fromTsUserId(createUser);
            doPushMessageToJiGuang(date, userId, newReplyMsg);
            MessageResponse messageResponse = new MessageResponse();
            messageResponse.setChainId("0");
            messageResponse.setTitle("意见与反馈");
            messageResponse.setContent(newReplyMsg);
            messageResponse.setType(String.valueOf(MsgTypeEnum.SYSTEM_MESSAGE.getCode()));
            messageResponse.setAccDate(DateUtil.printDate(DateUtil.getCurrentDate()));
            messageResponse.setUid(String.valueOf(userId));
            messageResponse.setUrl("/comment/list");
            sysMessageService.saveMessage(0, userId, ObjectUtil.toJsonQuietly(messageResponse), MessageSourceEnum.OWNER_FEEDBACK_V2_MSG.getValue(), SysMessageTypeEnum.MESSAGE_NOTIFY.getCode());
        }
    }

    private void doPushMessageToJiGuang(Date date, Integer userId, String content) {
        AllChannelPushParam allChannelPushParam = new AllChannelPushParam();
        allChannelPushParam.setScene(PushBusinessEnum.OWNER_FEEDBACK.getBusinessCode());
        allChannelPushParam.setBusinessCode(UUID.randomUUID().toString());
        allChannelPushParam.setTopic(PushParamWrapper.OWNER_MESSAGE);
        allChannelPushParam.setMemberId(userId);

        JPushPersonalParam jPushPersonalParam = new JPushPersonalParam();
        allChannelPushParam.setJPushPersonalParam(jPushPersonalParam);
        jPushPersonalParam.setTitle("通知");
        jPushPersonalParam.setKeepExtras(true);
        jPushPersonalParam.setNotifyUrl("atouroms://page/h5/webView?path=comment/list");
        jPushPersonalParam.setNotifyType(JPushTypeEnum.ALL.getCode());
        final String jiGuangUser = UserUtil.jiGuangUser(UserOfTypeEnum.OWNER.getCode(), userId);
        jPushPersonalParam.setAppUserIdKeyList(Lists.newArrayList(jiGuangUser));
        jPushPersonalParam.setPlatform(JPushPlatformEnum.ALL.getCode());
        jPushPersonalParam.setContext(content);
        jPushPersonalParam.setSceneType(JPushSceneTypeEnum.OTHER_PUSH.getCode());
        jPushPersonalParam.setAppKey(ownerAppKey);

        Map<String, String> notifyParam = new HashMap<>(1);
        notifyParam.put("type", Base64Util.encode("2"));
        jPushPersonalParam.setNotifyExtras(notifyParam);

        final boolean result = pushJpushService.push(allChannelPushParam);
        log.info("pushJpushService result:{}", result);
    }


    /**
     * 点评结果为未解决推送消息
     *
     * @param taskId 工单id
     */
    public void sendComentStatusNoSolveToPrincipalUser(Long taskId, Long processId) {
        TaskInfoDetailReqVO taskInfoDetailReqVO = new TaskInfoDetailReqVO();
        taskInfoDetailReqVO.setTaskId(taskId);
        taskInfoDetailReqVO.setQueryProcess(false);
        taskInfoDetailReqVO.setQueryEvaluation(true);
        taskInfoDetailReqVO.setQueryStatusChangeLog(false);

        final AtourRequest<TaskInfoDetailReqVO> request = RPCContext.createRequest(taskInfoDetailReqVO);
        feedbackPushExecutorService.submit(RunnableWrapper.of(() -> {
            AtourResponse<TaskInfoDetailResp> detail = taskInfoServiceRemote.detail(request);
            if (detail.isSuccess() && detail.getData() != null) {
                TaskInfoDetailResp taskInfoDetailResp = detail.getData();
//                if (taskInfoDetailResp.getSolverUsers().contains(taskInfoDetailResp.getPrincipalUser())) {
//                    log.info("taskId:{} SolverUsers {} PrincipalUser:{} will not send message", taskId, JacksonUtil.writeValueAsString(taskInfoDetailResp.getSolverUsers()), JacksonUtil.writeValueAsString(taskInfoDetailResp.getPrincipalUser()));
//                    return;
//                }
                List<TaskInfoEvaluationResp> taskInfoEvaluationResps = taskInfoDetailResp.getEvaluationList();
                if (CollectionUtils.isEmpty(taskInfoEvaluationResps)) {
                    log.info("taskId:{} processId {} will not send message", taskId, processId);
                    return;
                }

                // 未解决
                Optional<TaskInfoEvaluationResp> optional = taskInfoEvaluationResps.stream().filter(t -> t.getProcessId().equals(processId) && !t.getIsSolve()).findFirst();
                if (!optional.isPresent()) {
                    log.info("===>taskId:{} processId {} will not send message", taskId, processId);
                    return;
                }
                TaskInfoEvaluationResp evaluationResp = optional.get();
                String finalMessage = String.format(TEMPLATE_COMMENT_STATUS_NO_SOLVE, taskInfoDetailResp.getId(), evaluationResp.getEvaluationContent(), newFeedbackUrl);
                MessageReqVO messageReqVO = new MessageReqVO();
                messageReqVO.setTaskId(taskId);
                messageReqVO.setWechatMessage(finalMessage);

                Set<String> sendUserList = new HashSet<>();
                sendUserList.addAll(Safes.of(taskInfoDetailResp.getSolverUsers()));
                if (CollectionUtils.isNotEmpty(taskInfoDetailResp.getPrincipalUserList())) {
                    sendUserList.addAll(taskInfoDetailResp.getPrincipalUserList());
                } else if (StringUtils.isNotEmpty(taskInfoDetailResp.getPrincipalUser())) {
                    sendUserList.add(taskInfoDetailResp.getPrincipalUser());
                }
                if (sendUserList.isEmpty()) {
                    return;
                }
                messageReqVO.setSendUserList(Lists.newArrayList(sendUserList));
                taskInfoMessageService.sendMessage(RPCContext.createRequest(messageReqVO));
            }
        }));
    }


    /**
     * 点评结果为已解决推送消息
     *
     * @param taskId 工单id
     */
    public void sendComentStatusSolvedToPrincipalUser(Long taskId, Long processId) {
        TaskInfoDetailReqVO taskInfoDetailReqVO = new TaskInfoDetailReqVO();
        taskInfoDetailReqVO.setTaskId(taskId);
        taskInfoDetailReqVO.setQueryProcess(false);
        taskInfoDetailReqVO.setQueryEvaluation(true);
        taskInfoDetailReqVO.setQueryStatusChangeLog(false);

        final AtourRequest<TaskInfoDetailReqVO> request = RPCContext.createRequest(taskInfoDetailReqVO);
        feedbackPushExecutorService.submit(RunnableWrapper.of(() -> {
            AtourResponse<TaskInfoDetailResp> detail = taskInfoServiceRemote.detail(request);
            if (detail.isSuccess() && detail.getData() != null) {
                TaskInfoDetailResp taskInfoDetailResp = detail.getData();
//                if (taskInfoDetailResp.getSolverUsers().contains(taskInfoDetailResp.getPrincipalUser())) {
//                    log.info("taskId:{} SolverUsers {} PrincipalUser:{} will not send message", taskId, JacksonUtil.writeValueAsString(taskInfoDetailResp.getSolverUsers()), JacksonUtil.writeValueAsString(taskInfoDetailResp.getPrincipalUser()));
//                    return;
//                }
                List<TaskInfoEvaluationResp> taskInfoEvaluationResps = taskInfoDetailResp.getEvaluationList();
                if (CollectionUtils.isEmpty(taskInfoEvaluationResps)) {
                    log.info("taskId:{} processId {} no evaluation will not send message", taskId, processId);
                    return;
                }
                //已解决
                Optional<TaskInfoEvaluationResp> optional = taskInfoEvaluationResps.stream().filter(t -> t.getProcessId().equals(processId) && t.getIsSolve()).findFirst();
                if (!optional.isPresent()) {
                    log.info("===>taskId:{} processId {} evaluation is no solve will not send message", taskId, processId);
                    return;
                }
                TaskInfoEvaluationResp evaluationResp = optional.get();
                String finalMessage = String.format(TEMPLATE_COMMENT_STATUS_SOLVED, taskInfoDetailResp.getId(), evaluationResp.getEvaluationScore(), evaluationResp.getEvaluationContent(), newFeedbackUrl);
                MessageReqVO messageReqVO = new MessageReqVO();
                messageReqVO.setTaskId(taskId);
                messageReqVO.setWechatMessage(finalMessage);

                Set<String> sendUserList = new HashSet<>();
                sendUserList.addAll(Safes.of(taskInfoDetailResp.getSolverUsers()));
                if (CollectionUtils.isNotEmpty(taskInfoDetailResp.getPrincipalUserList())) {
                    sendUserList.addAll(taskInfoDetailResp.getPrincipalUserList());
                } else if (StringUtils.isNotEmpty(taskInfoDetailResp.getPrincipalUser())) {
                    sendUserList.add(taskInfoDetailResp.getPrincipalUser());
                }
                if (sendUserList.isEmpty()) {
                    return;
                }
                messageReqVO.setSendUserList(Lists.newArrayList(sendUserList));
                taskInfoMessageService.sendMessage(RPCContext.createRequest(messageReqVO));
            }
        }));
    }


}
