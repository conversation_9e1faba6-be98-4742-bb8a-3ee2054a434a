package com.atour.hotel.module.collection.service;

import com.atour.hotel.dto.black.EmployeeAllDTO;
import com.atour.hotel.module.collection.dto.FinanceCollectionDTO;
import com.atour.hotel.param.conllection.*;
import com.yaduo.infras.core.base.bean.AtourRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @program: hotel-manage-app-api
 * @ClassName FinanceCollectionService
 * @description: 催收接口
 * @author: zhuyu
 * @create: 2024-11-19 16:44
 * @Version 1.0
 **/
public interface FinanceCollectionService {

    /**
     * 批量新增催收接口
     * @param params
     */
    void addCollectionInfoList(List<CollectionAddParam> params);

    /**
     * 查询催收详情
     * @param param
     * @return
     */
    FinanceCollectionDTO queryCollectionInfo(QueryCollectionListParam param);

    /**
     * 完成催收任务
     * @param paramList
     */
    void completeCollectionAmount(List<CompleteCollectionParam> paramList);

    /**
     * 转发催收任务
     * @param param
     */
    void transferCollection(List<TransferCollectionParam> paramList);

    /**
     * 接收催收任务
     * @param param
     */
    void reciveCollection(ReciveCollectionParam param);

    /**
     * 查询所有人
     * @param flowerName
     * @return
     */
    List<EmployeeAllDTO> employeeAll(String flowerName);
}
