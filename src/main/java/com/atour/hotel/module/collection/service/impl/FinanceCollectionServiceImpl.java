package com.atour.hotel.module.collection.service.impl;

import com.alibaba.fastjson.JSON;
import com.atour.api.bean.ApiResult;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.dto.black.EmployeeAllDTO;
import com.atour.hotel.framework.configuration.bean.FeishuMessConfigBean;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.collection.dto.FinanceCollectionDTO;
import com.atour.hotel.module.collection.dto.FinanceCollectionItemDetailDTO;
import com.atour.hotel.module.collection.dto.FinanceCollectionLogDTO;
import com.atour.hotel.module.collection.enums.FinanceCollectionStateEnum;
import com.atour.hotel.module.collection.service.FinanceCollectionService;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.feishu.request.FeiShuBaseRequest;
import com.atour.hotel.module.feishu.request.actions.FeishuActionsParam;
import com.atour.hotel.module.feishu.request.header.FeishuHeader;
import com.atour.hotel.module.feishu.service.BusSendMessService;
import com.atour.hotel.module.rbac.service.RbacUserService;
import com.atour.hotel.param.conllection.*;
import com.atour.hotel.persistent.franchise.dao.FinanceCollectionDao;
import com.atour.hotel.persistent.franchise.dao.FinanceCollectionLogDao;
import com.atour.hotel.persistent.franchise.entity.FinanceCollectionEntity;
import com.atour.hotel.persistent.franchise.entity.FinanceCollectionLogEntity;
import com.atour.rbac.api.param.QueryUserParam;
import com.atour.rbac.api.remote.RbacUserRemote;
import com.atour.rbac.api.response.RbacUserDTO;
import com.atour.rbac.api.response.UserRoleDTO;
import com.atour.utils.DateUtil;
import com.google.common.collect.Lists;
import com.yaduo.chainbiz.service.api.dto.EsChainDTO;
import com.yaduo.dcms.api.remote.IChainDebtPeriodController;
import com.yaduo.dcms.api.request.ChainDebtPeriodResultParam;
import com.yaduo.dcms.enums.DebtStatusEnum;
import com.yaduo.dcms.enums.RiskLevelEnum;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.exception.AtourBizValidException;
import com.yaduo.infras.message.api.dto.MessageBody;
import com.yaduo.infras.message.api.dto.MessageResp;
import com.yaduo.infras.message.api.dto.QiWeiAppModel;
import com.yaduo.infras.message.springmvc.client.MessageClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class FinanceCollectionServiceImpl implements FinanceCollectionService {

    @Resource
    private RbacUserRemote rbacUserRemote;
    @Resource
    private RbacUserService rbacUserService;
    @Resource
    private FinanceCollectionDao financeCollectionDao;
    @Resource
    private FinanceCollectionLogDao financeCollectionLogDao;
    @Resource
    private ParamService paramService;
    @Resource
    private MessageClient messageClient;
    @Resource
    private IChainDebtPeriodController chainDebtPeriodController;

    @Resource
    private BusSendMessService busSendMessService;

    @Resource
    private FeishuMessConfigBean feishuMessConfigBean;

    @Resource
    private ChainService chainService;

    @Value("${collection.push.appId}")
    private String appId;

    @Value("${collection.push.feishu.appId}")
    private String feishuAppId;


    @Value("${collection.amount.url}")
    private String collectionAmountUrl;

    @Override
    public void addCollectionInfoList(List<CollectionAddParam> params) {
        if(CollectionUtils.isEmpty(params) || params.size()>300){
            throw new BusinessException("催收列表不能为空并且不能大于300条");
        }
        //组装催收数据记录并发送消息
        Map<String,List<String>> sendMessageMap = new HashMap<>();
        try {
            sendMessageMap = extracted(params, sendMessageMap);
        } catch (Exception e) {
            log.error("添加催收消息失败",e);
            throw new BusinessException("添加催收消息失败，请重试");
        }
        //发送催收金额消息
            sendMessageMap.entrySet().stream().forEach(item->{
                String mapKey = item.getKey();
                String[] split = mapKey.split("-");
                String employeeId = split[0];
                String yearStr = split[1];
                String monthStr = split[2];
                String upUrl = collectionAmountUrl+"?employeeId="+employeeId+"&year="+yearStr+"&month="+monthStr;
                log.info("新增发送催收金额消息："+employeeId+"-"+yearStr+"-"+monthStr);
                sendCollectionAmountMessage(employeeId,item.getValue(),upUrl);
        });
    }

    @Transactional(transactionManager = "omsTransactionManager", rollbackFor = Exception.class)
    public Map<String, List<String>> extracted(List<CollectionAddParam> params, Map<String, List<String>> sendMessageMap) {

        try {
            params.stream().forEach(item->{
                FinanceCollectionEntity financeCollection = financeCollectionDao.queryByCollectionId(item.getCollectionId());
                if(financeCollection!=null){
                    financeCollection.setAmount(item.getAmount());
                    financeCollection.setRemainingArrears(item.getRemainingArrears());
                    financeCollection.setRiskLevel(item.getRiskLevel());
                    financeCollection.setRepairedAmount(item.getRepairedAmount());
                    financeCollection.setEmployeeId(item.getEmployeeId());
                    financeCollection.setEmployeeName(item.getEmployeeName());
                    financeCollection.setItemId(item.getItemId());
                    financeCollection.setItemName(item.getItemName());
                    financeCollection.setMonth(item.getMonth());
                    financeCollection.setYear(item.getYear());
                    financeCollection.setRedirectUrl(item.getRedirectUrl());
                    financeCollection.setUrgeFlag(item.getUrgeFlag());
                    financeCollection.setState(FinanceCollectionStateEnum.COLLECTION.getCode());
                    financeCollection.setUpdateTime(new Date());
                    financeCollection.setUpdateUserId(item.getEmployeeId());
                    financeCollection.setUpdateUserName(item.getEmployeeName());
                    financeCollectionDao.update(financeCollection);
                }else{
                    financeCollection = FinanceCollectionEntity.builder()
                            .amount(item.getAmount())
                            .remainingArrears(item.getRemainingArrears())
                            .repairedAmount(item.getRepairedAmount())
                            .riskLevel(item.getRiskLevel())
                            .collectionId(item.getCollectionId())
                            .employeeId(item.getEmployeeId())
                            .employeeName(item.getEmployeeName())
                            .hotelId(item.getHotelId())
                            .itemId(item.getItemId())
                            .itemName(item.getItemName())
                            .month(item.getMonth())
                            .redirectUrl(item.getRedirectUrl())
                            .year(item.getYear())
                            .urgeFlag(item.getUrgeFlag())
                            .state(FinanceCollectionStateEnum.COLLECTION.getCode())
                            .createUserId(item.getEmployeeId())
                            .createUserName(item.getEmployeeName())
                            .updateUserId(item.getEmployeeId())
                            .updateUserName(item.getEmployeeName())
                            .createTime(new Date())
                            .updateTime(new Date())
                            .build();
                    financeCollectionDao.insertSelective(financeCollection);
                    FinanceCollectionLogEntity financeCollectionLogEntity = FinanceCollectionLogEntity.builder()
                            .financeCollectionId(financeCollection.getId())
                            .employeeId(item.getEmployeeId())
                            .employeeName(item.getEmployeeName())
                            .remark(item.getEmployeeName()+"创建任务")
                            .state(FinanceCollectionStateEnum.COLLECTION.getCode())
                            .createUserId(item.getEmployeeId())
                            .createUserName(item.getEmployeeName())
                            .createTime(new Date())
                            .build();
                    financeCollectionLogDao.insert(financeCollectionLogEntity);
                }

                String mapKey = item.getEmployeeId()+"-"+item.getYear()+"-"+item.getMonth();
                if(!CollectionUtils.isEmpty(sendMessageMap.get(mapKey))){
                    List<String> itemNames = sendMessageMap.get(mapKey);
                    itemNames.add(item.getItemName());
                    sendMessageMap.put(mapKey,itemNames);
                }else{
                    List<String> itemNames = new ArrayList<>();
                    itemNames.add(item.getItemName());
                    sendMessageMap.put(mapKey,itemNames);
                }

            });
        } catch (Exception e) {
            log.info("新增催收任务失败："+e.getMessage());
            throw e;
        }
        return sendMessageMap;
    }

    @Override
    public FinanceCollectionDTO queryCollectionInfo(QueryCollectionListParam param) {
        List<FinanceCollectionEntity> financeCollectionEntityList = financeCollectionDao.queryCollectionInfoByCondition(param.getEmployeeId(), param.getMonth(), param.getYear());

        if(CollectionUtils.isEmpty(financeCollectionEntityList)){
            return null;
        }
        List<Long> financeCollectionIds = financeCollectionEntityList.stream().map(FinanceCollectionEntity::getId).collect(Collectors.toList());
        List<FinanceCollectionLogEntity> financeCollectionLogAllList = financeCollectionLogDao.queryAllByFinanceCollectionIdList(financeCollectionIds);

        Map<Long, List<FinanceCollectionLogEntity>> financeCollectionLogMap = new HashMap<>();
        financeCollectionLogAllList.stream().forEach(item->{
            if(financeCollectionLogMap.containsKey(item.getFinanceCollectionId())){
                List<FinanceCollectionLogEntity> list =financeCollectionLogMap.get(item.getFinanceCollectionId());
                list.add(item);
                financeCollectionLogMap.put(item.getFinanceCollectionId(),list);
            }else{
                List<FinanceCollectionLogEntity> financeCollectionLogEntities = new ArrayList<>();
                financeCollectionLogEntities.add(item);
                financeCollectionLogMap.put(item.getFinanceCollectionId(),financeCollectionLogEntities);
            }
        });

        List<FinanceCollectionItemDetailDTO> itemDetailDTOList = new ArrayList<>();
        //封装催收明细
        if(!CollectionUtils.isEmpty(financeCollectionEntityList)){
            FinanceCollectionDTO financeCollectionDTO = FinanceCollectionDTO.builder()
                    .employeeId(financeCollectionEntityList.get(0).getEmployeeId())
                    .employeeName(financeCollectionEntityList.get(0).getEmployeeName())
                    .year(financeCollectionEntityList.get(0).getYear())
                    .month(financeCollectionEntityList.get(0).getMonth()).build();

            List<Integer> partChainIds = financeCollectionEntityList.stream().map(FinanceCollectionEntity::getHotelId).collect(Collectors.toList());
            List<EsChainDTO> partEsChainDTOS = chainService.queryChainDtoByEsRemote(partChainIds);
            Map<Integer, String> chainNamesMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(partEsChainDTOS)){
                chainNamesMap = partEsChainDTOS.stream().collect(Collectors.toMap(EsChainDTO::getChainId, EsChainDTO::getChainName));
            }

            for (FinanceCollectionEntity item:  financeCollectionEntityList){
                FinanceCollectionItemDetailDTO financeCollectionItemDetailDTO = FinanceCollectionItemDetailDTO.builder()
                        .collectionAmount(item.getAmount())
                        .id(item.getId())
                        .hotelName(chainNamesMap.get(item.getHotelId()))
                        .hotelId(item.getHotelId())
                        .itemId(item.getItemId())
                        .collectionAmount(item.getAmount())
                        .itemName(item.getItemName())
                        .remainingArrears(item.getRemainingArrears())
                        .repairedAmount(item.getRepairedAmount())
                        .redirectUrl(item.getRedirectUrl())
                        .urgeFlag(item.getUrgeFlag())
                        .state(item.getState())
                        .stateDes(FinanceCollectionStateEnum.codeOf(item.getState()).getDescription())
                        .riskReason(item.getRiskReason())
                        .result(item.getResult())
                        .badDebtRisk(item.getBadDebtRisk())
                        .expectedPaymentDate(item.getExpectedPaymentDate())
                        .build();
                if(Objects.nonNull(item.getRiskLevel())){
                    financeCollectionItemDetailDTO.setRiskLevel(RiskLevelEnum.codeOf(item.getRiskLevel()).getDesc());
                }
                //查询催收日志
//                List<FinanceCollectionLogEntity> financeCollectionLogEntityList = financeCollectionLogDao.queryAllByFinanceCollectionId(item.getId());
                List<FinanceCollectionLogEntity> financeCollectionLogEntityList = financeCollectionLogMap.get(item.getId());
                if(CollectionUtils.isEmpty(financeCollectionLogEntityList)){
                    log.info("催收日志为空:{}",item.getId());
                    continue;
                }
                List<FinanceCollectionLogDTO> logList = new ArrayList<>();
                //组装日志
                financeCollectionLogEntityList.stream().forEach(logItem-> {
                    FinanceCollectionLogDTO financeCollectionLogDTO = FinanceCollectionLogDTO.builder()
                            .employeeId(logItem.getEmployeeId())
                            .employeeName(logItem.getEmployeeName())
                            .createTime(logItem.getCreateTime())
                            .state(FinanceCollectionStateEnum.codeOf(logItem.getState()).getDescription())
                            .operate(logItem.getEmployeeName()+","+FinanceCollectionStateEnum.codeOf(logItem.getState()).getDescription())
                            .remark(logItem.getRemark())
                            .build();
                    logList.add(financeCollectionLogDTO);
                });

                financeCollectionItemDetailDTO.setLogList(logList);
                itemDetailDTOList.add(financeCollectionItemDetailDTO);
            }

            //组装数据
            financeCollectionDTO.setItemDetailList(itemDetailDTOList);
            return financeCollectionDTO;
        }
        return null;
    }


    @Override
    public void completeCollectionAmount(List<CompleteCollectionParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            throw new BusinessException("请选择需要完成的项目", ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        List<ChainDebtPeriodResultParam> resultList = Lists.newArrayList();

        //校验坏账风险
        for(CompleteCollectionParam param:paramList) {
            if (Objects.equals(1, param.getBadDebtRisk()) && StringUtils.isEmpty(param.getRiskReason())) {
                throw new BusinessException("请输入坏账风险原因！", ResponseCodeEnum.PARAM_ERROR.getCode());
            }
            //校验预计还款时间
            if (!Objects.equals(1, param.getBadDebtRisk()) && (Objects.isNull(param.getExpectedPaymentDate()) || param.getExpectedPaymentDate().before(DateUtil.getCurrentDate()))) {
                throw new BusinessException("请输入正确的预计还款时间", ResponseCodeEnum.PARAM_ERROR.getCode());
            }

            //更新催收表
            FinanceCollectionEntity financeCollectionEntity = financeCollectionDao.queryById(param.getFinanceCollectionId());
            if (financeCollectionEntity == null || financeCollectionEntity.getState() == FinanceCollectionStateEnum.FINISH.getCode()) {
                log.info("催收任务不存在或者已处理完成：" + param.getFinanceCollectionId());
                continue;
            }
            financeCollectionEntity.setBadDebtRisk(param.getBadDebtRisk());
            financeCollectionEntity.setExpectedPaymentDate(param.getExpectedPaymentDate());
            financeCollectionEntity.setRiskReason(param.getRiskReason());
            financeCollectionEntity.setState(FinanceCollectionStateEnum.FINISH.getCode());
            financeCollectionEntity.setUpdateUserId(param.getEmployeeId());
            financeCollectionEntity.setUpdateUserName(getUserNameByEmployeeId(param.getEmployeeId()));
            financeCollectionEntity.setUpdateTime(new Date());
            financeCollectionDao.update(financeCollectionEntity);
            //添加催收日志
            FinanceCollectionLogEntity financeCollectionLogEntity = FinanceCollectionLogEntity.builder()
                    .financeCollectionId(param.getFinanceCollectionId())
                    .employeeId(param.getEmployeeId())
                    .employeeName(financeCollectionEntity.getEmployeeName())
                    .remark("催收员:" + getUserNameByEmployeeId(param.getEmployeeId()) + FinanceCollectionStateEnum.FINISH.getDescription())
                    .state(FinanceCollectionStateEnum.FINISH.getCode())
                    .createUserId(param.getEmployeeId())
                    .createUserName(getUserNameByEmployeeId(param.getEmployeeId()))
                    .createTime(new Date())
                    .build();
            financeCollectionLogDao.insert(financeCollectionLogEntity);

            //通知财务中心
            ChainDebtPeriodResultParam chainDebtPeriodResultParam = new ChainDebtPeriodResultParam();
            chainDebtPeriodResultParam.setId(financeCollectionEntity.getCollectionId());
            chainDebtPeriodResultParam.setDebtStatus(DebtStatusEnum.REPLAYED.getCode());
            chainDebtPeriodResultParam.setBadDebtRisk(Objects.equals(1, param.getBadDebtRisk()));
            chainDebtPeriodResultParam.setRiskReason(param.getRiskReason());
            chainDebtPeriodResultParam.setExpectedPaymentDate(param.getExpectedPaymentDate());
            resultList.add(chainDebtPeriodResultParam);

        }

        try {
            if(resultList.size()>0) {
                AtourRequest<List<ChainDebtPeriodResultParam>> request = new AtourRequest<>();
                request.setModel(resultList);
                log.info("催收完成通知财务中心入参：{}", JSON.toJSONString(request));
                AtourResponse<Void> atourResponse = chainDebtPeriodController.notifyDebtStatus(request);
                log.info("催收完成通知财务中心返回：{}", JSON.toJSONString(atourResponse));
                if (!atourResponse.isSuccess()) {
                    log.error("催收完成通知财务中心失败！");
                }
            }
        } catch (Exception e) {
            log.error("催收完成通知财务中心失败！", e);
        }

    }

    @Override
    public void transferCollection(List<TransferCollectionParam> param) {

        if (CollectionUtils.isEmpty(param)) {
            throw new BusinessException("请选择需要转办的催单", ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        List<FinanceCollectionLogEntity> logList = new ArrayList<>();
        List<FinanceCollectionEntity> batchlist = new ArrayList<>();
        List<ChainDebtPeriodResultParam> resultList = new ArrayList<>();
        Map<String,List<String>> sendMessageMap = new HashMap<>();

        for(TransferCollectionParam item:param) {

            if (Objects.isNull(item.getFinanceCollectionId())) {
                throw new BusinessException("请选择要转办的催收单！");
            }
            String employeeName = getUserNameByEmployeeId(item.getTransferEmployeeId());
            //更新催收表
            FinanceCollectionEntity financeCollectionEntity = financeCollectionDao.queryById(item.getFinanceCollectionId());

            if (financeCollectionEntity == null || financeCollectionEntity.getState() == FinanceCollectionStateEnum.FINISH.getCode()) {
                log.info("催收任务不存在或者已处理完成：" + item);
                continue;
            }

            //添加催收日志
            FinanceCollectionLogEntity financeCollectionLogEntity = FinanceCollectionLogEntity.builder()
                    .financeCollectionId(item.getFinanceCollectionId())
                    .employeeId(financeCollectionEntity.getEmployeeId())
                    .employeeName(financeCollectionEntity.getEmployeeName())
                    .remark(item.getRemark())
                    .state(FinanceCollectionStateEnum.TRANSFER.getCode())
                    .createUserId(financeCollectionEntity.getEmployeeId())
                    .createUserName(financeCollectionEntity.getEmployeeName())
                    .createTime(new Date())
                    .build();

            financeCollectionEntity.setTransferEmployeeId(financeCollectionEntity.getEmployeeId());
            financeCollectionEntity.setTransferName(financeCollectionEntity.getEmployeeName());
            financeCollectionEntity.setTransferTime(new Date());
            financeCollectionEntity.setUpdateUserId(item.getTransferEmployeeId());
            financeCollectionEntity.setUpdateUserName(employeeName);
            financeCollectionEntity.setEmployeeId(item.getTransferEmployeeId());
            financeCollectionEntity.setEmployeeName(employeeName);
            financeCollectionEntity.setRemark(item.getRemark());
            financeCollectionEntity.setState(FinanceCollectionStateEnum.TRANSFER.getCode());
            financeCollectionDao.update(financeCollectionEntity);
            batchlist.add(financeCollectionEntity);

            logList.add(financeCollectionLogEntity);
            //封装通知财务中心对象
            ChainDebtPeriodResultParam chainDebtPeriodResultParam = new ChainDebtPeriodResultParam();
            chainDebtPeriodResultParam.setId(financeCollectionEntity.getCollectionId());
            chainDebtPeriodResultParam.setHandleEmployeeId(item.getTransferEmployeeId());
            chainDebtPeriodResultParam.setHandleEmployeeName(getUserNameByEmployeeId(item.getTransferEmployeeId()));
            chainDebtPeriodResultParam.setDebtStatus(DebtStatusEnum.TRANSFERRED.getCode());
            resultList.add(chainDebtPeriodResultParam);

            String mapKey = financeCollectionEntity.getEmployeeId()+"-"+financeCollectionEntity.getYear()+"-"+financeCollectionEntity.getMonth();
            if(!CollectionUtils.isEmpty(sendMessageMap.get(mapKey))){
                List<String> itemNames = sendMessageMap.get(mapKey);
                itemNames.add(financeCollectionEntity.getItemName());
                sendMessageMap.put(mapKey,itemNames);
            }else{
                List<String> itemNames = new ArrayList<>();
                itemNames.add(financeCollectionEntity.getItemName());
                sendMessageMap.put(mapKey,itemNames);
            }
        }

        //批量添加催收日志
        if(logList.size()>0){
            financeCollectionLogDao.insertBatch(logList);
        }
        //通知财务中心
        if(resultList.size() > 0) {
            try {
                AtourRequest<List<ChainDebtPeriodResultParam>> request = new AtourRequest<>();
                request.setModel(resultList);
                AtourResponse<Void> atourResponse = chainDebtPeriodController.notifyDebtStatus(request);
                if (!atourResponse.isSuccess()) {
                    log.error("催收转办通知财务中心失败！");
                }
            } catch (Exception e) {
                log.error("催收转办通知财务中心异常！", e);
            }


            sendMessageMap.entrySet().stream().forEach(item->{
                String mapKey = item.getKey();
                String[] split = mapKey.split("-");
                String employeeId = split[0];
                String yearStr = split[1];
                String monthStr = split[2];
                String upUrl = collectionAmountUrl+"?employeeId="+employeeId+"&year="+yearStr+"&month="+monthStr;
                log.info("新增发送催收金额消息："+employeeId+"-"+yearStr+"-"+monthStr);
                sendCollectionAmountMessage(employeeId,item.getValue(),upUrl);
            });
        }


    }

    @Override
    public void reciveCollection(ReciveCollectionParam param) {
        if(CollectionUtils.isEmpty(param.getFinanceCollectionId())){
            throw new BusinessException("请选择要接收的催收单！");
        }
        List<FinanceCollectionLogEntity> logList = new ArrayList<>();
        List<ChainDebtPeriodResultParam> resultList = new ArrayList<>();
        for (Long item : param.getFinanceCollectionId()) {
            //更新催收表
            FinanceCollectionEntity financeCollectionEntity = financeCollectionDao.queryById(item);
            if(null == financeCollectionEntity || (financeCollectionEntity.getState()==FinanceCollectionStateEnum.RECIVE.getCode()||financeCollectionEntity.getState()==FinanceCollectionStateEnum.FINISH.getCode())){
                continue;
            }

            //添加催收日志
            FinanceCollectionLogEntity financeCollectionLogEntity = FinanceCollectionLogEntity.builder()
                    .financeCollectionId(item)
                    .employeeId(financeCollectionEntity.getEmployeeId())
                    .employeeName(financeCollectionEntity.getEmployeeName())
                    .state(FinanceCollectionStateEnum.RECIVE.getCode())
                    .remark(FinanceCollectionStateEnum.RECIVE.getDescription())
                    .createUserId(financeCollectionEntity.getEmployeeId())
                    .createUserName(financeCollectionEntity.getEmployeeName())
                    .createTime(new Date())
                    .build();

            financeCollectionEntity.setTransferEmployeeId(financeCollectionEntity.getEmployeeId());
            financeCollectionEntity.setTransferName(financeCollectionEntity.getEmployeeName());
            financeCollectionEntity.setState(FinanceCollectionStateEnum.RECIVE.getCode());
            financeCollectionDao.update(financeCollectionEntity);

            logList.add(financeCollectionLogEntity);
            //封装通知财务中心对象
            ChainDebtPeriodResultParam chainDebtPeriodResultParam = new ChainDebtPeriodResultParam();
            chainDebtPeriodResultParam.setId(financeCollectionEntity.getCollectionId());
            chainDebtPeriodResultParam.setDebtStatus(DebtStatusEnum.RECEIVED.getCode());
            resultList.add(chainDebtPeriodResultParam);
        }

        //批量添加催收日志
        if(logList.size() > 0){
            financeCollectionLogDao.insertBatch(logList);
        }
        //通知财务中心
        try {
            if(resultList.size() > 0) {
                AtourRequest<List<ChainDebtPeriodResultParam>> request = new AtourRequest<>();
                request.setModel(resultList);
                AtourResponse<Void> atourResponse = chainDebtPeriodController.notifyDebtStatus(request);
                if (!atourResponse.isSuccess()) {
                    log.error("催收接收通知财务中心失败！");
                }
            }
        } catch (Exception e) {
            log.error("催收接收通知财务中心异常！", e);
        }
    }

    public List<EmployeeAllDTO> employeeAll(String flowerName) {
        QueryUserParam queryUserParam = new QueryUserParam();
        if(StringUtils.isEmpty(flowerName)){
            throw new BusinessException("请输入要查询的花名！");
        }
        queryUserParam.setFlowerName(flowerName);
        queryUserParam.setEmail(null);
        //根据花名模糊查询用户信息
        ApiResult<List<UserRoleDTO>> userInfoDTOApiResult = rbacUserRemote.fuzzyMatchUserRoleByFlowerName(queryUserParam);
        if (userInfoDTOApiResult != null) {
            List<UserRoleDTO> userInfoDTOList = userInfoDTOApiResult.getResult();
            if (!CollectionUtils.isEmpty(userInfoDTOList)) {
                List<EmployeeAllDTO> result = new ArrayList<>();
                //封装返回对象
                userInfoDTOList.stream().forEach(item->{
                    EmployeeAllDTO employeeAllVo = new EmployeeAllDTO();
                    employeeAllVo.setFlowerName(item.getAlias());
                    employeeAllVo.setEmail(item.getEmail());
                    employeeAllVo.setEmployeeId(item.getEmployeeId());
                    result.add(employeeAllVo);
                });
                return result;
            }
        }
        return null;
    }


    /**
     * 根据员工编号获取花名(只获取花名)
     *
     * @param employeeId
     * @return
     */
    public String getUserNameByEmployeeId(String employeeId) {
        try {
            RbacUserDTO userDTODetail = rbacUserService.getUserByEmployeeOrUsername(employeeId);
            if (Objects.isNull(userDTODetail)) {
                return StringUtils.EMPTY;
            }
            return userDTODetail.getAlias();
        } catch (Exception e) {
            throw new BusinessException("获取花名异常");
        }

    }


    /**
     * 发送催收费用催办消息
     * @param employeeId
     * @param itemName
     */
    public void  sendCollectionAmountMessage(String employeeId, List<String> itemName,String upUrl) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("费项名称：%s \n", itemName.stream().collect(Collectors.joining(","))));
        content.append(String.format("请点击进行处理：\n"));
        sendMess(Lists.newArrayList(employeeId), content, "您有一条酒店费用应收，请立即处理", upUrl);
        sendMessFeishu(Lists.newArrayList(employeeId), content, "您有一条酒店费用应收，请立即处理", upUrl);
    }

    private void sendMess(List<String> employeeIdList, StringBuilder content, String title,String upUrl) {

        try {
            if(feishuMessConfigBean.getConfigSwitch(appId)){
                return;
            }
            MessageBody<QiWeiAppModel> messageBody = new MessageBody<>();
            QiWeiAppModel qiWeiAppModel = new QiWeiAppModel();
            qiWeiAppModel.setTitle(title);
            qiWeiAppModel.setDesc(content.toString());
            qiWeiAppModel.setMessageType(1);
            qiWeiAppModel.setEmployeeIdList(employeeIdList);
            qiWeiAppModel.setAgentId(appId);
            qiWeiAppModel.setUrl(upUrl);
            messageBody.setMsgType("qwApp");
            messageBody.setUnique(UUID.randomUUID().toString());
            messageBody.setContent(qiWeiAppModel);
            AtourRequest<List<MessageBody>> messageModel = new AtourRequest<>();
            messageModel.setModel(Lists.newArrayList(messageBody));
            AtourResponse<List<MessageResp>> listAtourResponse = messageClient.sendMulti(messageModel);

            if (!listAtourResponse.isSuccess()) {
                log.error(String.format("生成消息 标题%s,失败，详情信息 ：%s", title, JSON.toJSONString(listAtourResponse)));
                throw new AtourBizValidException("发送消息失败");
            }
        } catch (Exception e) {
            log.error("生成消息失败 e:" + e.getMessage());

        }

    }

    private void sendMessFeishu(List<String> employeeIdList, StringBuilder content, String title,String url){
        //判断是否开启飞书推送
        if(StringUtils.isEmpty(feishuAppId)){
            return;
        }
        //推送飞书
        FeishuHeader feishuHeader = FeiShuBaseRequest.bulidFeishuHeader("", FeiShuBaseRequest.bulidBaseContent(title, ""));

        FeishuActionsParam feishuActionsParam = FeishuActionsParam.builder().tag("button").url(url).type("primary").text(FeiShuBaseRequest.bulidBaseContent("查询详情", "plain_text")).build();

        busSendMessService.baseSendMessAction(employeeIdList,feishuHeader,FeiShuBaseRequest.bulidBaseContent(content.toString(), "lark_md"),feishuActionsParam,feishuAppId);
    }


   }
