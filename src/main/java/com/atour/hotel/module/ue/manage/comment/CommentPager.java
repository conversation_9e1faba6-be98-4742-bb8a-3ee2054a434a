package com.atour.hotel.module.ue.manage.comment;

import com.atour.hotel.common.page.Pager;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/11/21
 * 评论分页
 */
public class CommentPager<T, E> extends Pager<T> {

    @ApiModelProperty(value = "头部数据")
    @Getter
    @Setter
    E top;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    private int totalCount;


    public CommentPager() {
    }

    public CommentPager(PageData page, List<T> data, E top) {
        this.page = page;
        this.data = Lists.newArrayList();
        this.data.addAll(data);
        this.top = top;
    }
    public CommentPager(PageData page, List<T> data, E top, Integer totalCount) {
        this.page = page;
        this.data = Lists.newArrayList();
        this.data.addAll(data);
        this.top = top;
        this.totalCount = totalCount;
    }

    public static <T, E> Builder<T, E> builder(List<T> data, E top) {
        return new Builder<T, E>().data(data).top(top);
    }

    public static class Builder<T, E> {

        private List<T> data;

        private E top;

        private int curPage = 1;

        private int pageSize = 15;

        private int totalSize = 0;
        //慧评差评列表特殊字段
        private int totalCount = 0;

        private Builder() {
        }

        public Builder<T, E> current(int curPage) {
            this.curPage = curPage;
            return this;
        }

        public Builder<T, E> size(int pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder<T, E> total(int totalSize) {
            this.totalSize = totalSize;
            return this;
        }

        public Builder<T, E> totalCount(int totalCount) {
            this.totalCount = totalCount;
            return this;
        }

        public Builder<T, E> data(List<T> data) {
            this.data = data;
            return this;
        }

        public Builder<T, E> top(E top) {
            this.top = top;
            return this;
        }

        public CommentPager<T, E> create() {
            return new CommentPager<>(new PageData(this.curPage, this.pageSize, this.totalSize), data, top, totalCount);
        }
    }
}
