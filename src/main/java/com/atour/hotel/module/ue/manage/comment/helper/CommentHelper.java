package com.atour.hotel.module.ue.manage.comment.helper;

import com.atour.api.bean.ApiResult;
import com.atour.chain.api.chain.dto.RmRoomTypeDTO;
import com.atour.chain.api.chain.remote.RoomTypeRemote;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.util.CommonParamsDTO;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.service.RegionChainService;
import com.atour.hotel.module.rbac.service.RbacService;
import com.atour.hotel.module.ue.manage.comment.dto.MebSellerDepDto;
import com.atour.hotel.module.ue.manage.comment.dto.SimpleUserDto;
import com.atour.hotel.module.ue.manage.comment.dto.UserForInnerDTO;
import com.atour.hotel.module.ue.manage.comment.wrapper.SimpleUserInfoWrapper;
import com.atour.hotel.module.user.dto.SysUserDto;
import com.atour.hotel.persistent.cem.dao.UeNegativeCommentTypeMapper;
import com.atour.hotel.persistent.cem.entity.UeNegativeCommentTypeEntity;
import com.atour.hotel.persistent.center.dao.SysUserDao;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.order.api.dto.FolioDTO;
import com.atour.order.api.dto.GuestDTO;
import com.atour.order.api.remote.FolioRemote;
import com.atour.order.api.remote.GuestRemote;
import com.atour.rbac.api.param.GetRegionBChainIdRequest;
import com.atour.rbac.api.remote.RbacUserRemote;
import com.atour.rbac.api.response.HotelManagerDTO;
import com.atour.rbac.api.response.HotelManagerInfoDTO;
import com.atour.rbac.api.response.RbacUserDTO;
import com.atour.rbac.api.response.RegionChainDto;
import com.atour.rbac.api.response.RegionCityChainDto;
import com.atour.rbac.api.response.UserDTO;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.rbac.api.thread.RbacCommonParams;
import com.atour.user.api.member.dto.appuser.AppUserInfoDTO;
import com.atour.user.api.member.remote.appuser.AppUserQueryRemote;
import com.atour.user.api.member.remote.register.PersonalMemberRemote;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.atour.utils.http.HttpClientUtil;
import com.atour.utils.json.JsonUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/11/1
 * 查验管理评论帮助类
 */
@Component
@Slf4j
public class CommentHelper {

    @Resource
    private FolioRemote folioRemote;
    @Resource
    private UeNegativeCommentTypeMapper ueNegativeCommentTypeMapper;
    @Resource
    private RegionChainService regionChainService;
    @Resource
    private SysUserDao sysUserDao;
    @Resource
    private PersonalMemberRemote personalMemberRemote;
    @Resource
    private GuestRemote guestRemote;
    @Resource
    private AppUserQueryRemote appUserQueryRemote;
    @Resource
    private RoomTypeRemote roomTypeRemote;

    @Resource
    private RbacService rbacService;

    @Resource
    private RbacUserRemote rbacUserRemote;

    /**
     * 系统ID
     */
    @Value("${ue.systemId}")
    private Integer systemId;
    /**
     * 设为已查看权限
     */
    @Value("${ue.authority.set-rewiewed}")
    private Integer authoritySetrewiewed;
    /**
     * 设为未查看权限
     */
    @Value("${ue.authority.set-unreview}")
    private Integer authoritySetunreview;
    /**
     * 设为差评权限
     */
    @Value("${ue.authority.set-negative}")
    private Integer authoritySetnegative;
    /**
     * 差评管理报告提交权限
     */
    @Value("${ue.authority.negative-process-commit}")
    private Integer authorityNegativeProcessCommit;
    /**
     * 差评管理报告审批权限
     */
    @Value("${ue.authority.negative-process-approval}")
    private Integer authorityNegativeProcessApproval;
    /**
     * 根据权限获取用户url
     */
    @Value("${rbac-url.getUserByRuleIdUrl}")
    private String getUserByRuleIdUrl;

    /**
     * 获取酒店现长和兼店现长url
     */
    @Value("${rbac-url.getXianZhangAndPartTimeXianZhang}")
    private String getXianZhangAndPartTimeXianZhangUrl;

    /**
     * 项目名称
     */
    @Value("${spring.application.name}")
    private String applicationName;


    public SysUserDto getUserById(Integer userId) {
        return sysUserDao.getSysUserListByUserId(userId);
    }

    public FolioDTO getFolioDto (Integer chainId, Long folioId) {
        return folioRemote.getFolioDetail(chainId, folioId);
    }

    public RmRoomTypeDTO getRoomTypeById(Integer roomTypeId) {
        if (Objects.isNull(roomTypeId)) {
            return null;
        }
        return roomTypeRemote.getRoomTypeById(roomTypeId);
    }

    public GuestDTO getGuest(Integer chainId, Long guestId) {
        if (Objects.isNull(chainId) || Objects.isNull(guestId)) {
            return new GuestDTO();
        }
        return guestRemote.getGuestById(chainId, guestId);
    }

    /**
     * 获取app用户信息
     */
    public AppUserInfoDTO getAppUserInfoById(Integer appUserId) {
        if (Objects.isNull(appUserId)) {
            return new AppUserInfoDTO();
        }
        AppUserInfoDTO result = appUserQueryRemote.getUserInfoById(appUserId);
        return Objects.isNull(result) ? new AppUserInfoDTO():result;
    }

    /**
     * 获取level下的名称map
     * @param level
     * @return
     */
    public Map<Integer, String> getNegativeTypeNameMapByLevel(Integer level) {
        List<UeNegativeCommentTypeEntity> negativeTypeList = ueNegativeCommentTypeMapper.selectBySelective(UeNegativeCommentTypeEntity.builder().negativeLevel(level).build());
        return Safes.of(negativeTypeList).stream()
            .collect(Collectors.toMap(UeNegativeCommentTypeEntity::getId, UeNegativeCommentTypeEntity::getNegativeName));
    }

    /**
     *  根据主键获取
     * @param id
     * @return
     */
    public UeNegativeCommentTypeEntity getNegativeTypeById (Integer id) {
        return ueNegativeCommentTypeMapper.selectByPrimaryKey(id);
    }

    /**
     * 获取酒店区域map
     * @param user rbac登录信息
     * @return
     */
    public Map<Integer, String> getChainRegionMapByUser (RbacCommonParams user) {
        List<RegionCityChainDto>  rccDtoList = user.getRegionCityChainDtoList();
        if (CollectionUtils.isEmpty(rccDtoList)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> result = Maps.newHashMap();
        for (RegionCityChainDto rccDto : rccDtoList) {
            if (Objects.isNull(rccDto) || CollectionUtils.isEmpty(rccDto.getCityList())) {
                continue;
            }
            for (RegionCityChainDto.City city : rccDto.getCityList()) {
                if (CollectionUtils.isEmpty(city.getChainList())) {
                    continue;
                }
                for (RegionCityChainDto.City.SimpleChain chain : city.getChainList()) {
                    result.put(chain.getChainId(), rccDto.getRegionName());
                }
            }
        }
        return result;
    }

    /**
     * 获取酒店区域map
     * @param user 登录信息
     * @return
     */
    public Map<Integer, String> getChainRegionMapByUser (CommonParamsDTO user) {
        List<Integer> chainIdList = user.getUserPermissionDTO().getChainList();

        // 酒店区域信息
        List<RegionChainDto> regionChainDtos = regionChainService.getRegionByChainId(
            GetRegionBChainIdRequest.builder()
                .chainId(chainIdList)
                .build());
        if (CollectionUtils.isEmpty(regionChainDtos)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> result = Maps.newHashMap();
        for (RegionChainDto rcDto : regionChainDtos) {
            if (Objects.isNull(rcDto) || CollectionUtils.isEmpty(rcDto.getChains())) {
                continue;
            }
            for (RegionChainDto.RegionChainBo chain : rcDto.getChains()) {
                result.put(NumberUtils.toInt(chain.getChainId()), rcDto.getDeptName());
            }
        }
        return result;

    }

    /**
     * 根据区域ID获取用户有权限的酒店列表--rbac
     * @param user 用户信息
     * @param regionId 区域ID
     * @return
     */
    public List<Integer> getUserChanIdListByRegion(RbacCommonParams user, Integer regionId) {
        return getUserChanIdList(user, regionId);
    }

    /**
     * 根据区域ID获取用户有权限的酒店列表
     * @param user 用户信息
     * @return
     */
    public List<Integer> getUserChanIdList(RbacCommonParams user) {
        return getUserChanIdList(user, null);
    }


    private List<Integer> getUserChanIdList(RbacCommonParams user, Integer regionId) {
        List<RegionCityChainDto>  rccDtoList = user.getRegionCityChainDtoList();
        if (CollectionUtils.isEmpty(rccDtoList)) {
            return Collections.emptyList();
        }

        List<Integer> result = Lists.newArrayList();
        for (RegionCityChainDto rccDto : rccDtoList) {
            boolean regionFilter = Objects.isNull(regionId) || Objects.equals(rccDto.getRegionId(), regionId);
            if (!regionFilter || CollectionUtils.isEmpty(rccDto.getCityList())) {
                continue;
            }
            for (RegionCityChainDto.City city : rccDto.getCityList()) {
                if (CollectionUtils.isEmpty(city.getChainList())) {
                    continue;
                }
                for (RegionCityChainDto.City.SimpleChain chain : city.getChainList()) {
                    result.add(chain.getChainId());
                }
            }
        }
        return result;
    }

    /**
     * 获取用户信息
     */
    public UserForInnerDTO getUserDetail(String employeeId) {
        log.info("--getUserDetail 获取用户信息 employeeId={}", employeeId);
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getUserDetailUrl)
            .queryParam("employeeIds", employeeId)
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目获取用户信息响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取用户信息响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取用户信息,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目获取用户信息,result为空");
            return UserForInnerDTO.builder().build();
        }

        List<UserForInnerDTO> resultList = JsonUtils.parseList(response.getResult()
            .toString(), UserForInnerDTO.class);

        return Safes.of(resultList).stream().findAny().orElse(UserForInnerDTO.builder().build());
    }

    /**
     * 获取用户信息
     */
    public UserDetailDTO getUserDTODetail(String employeeId) {
        log.info("--getUserDetail 获取用户信息 employeeId={}", employeeId);
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getUserDetailDTOUrl)
            .queryParam("employeeId", employeeId)
            .queryParam("source", "RBAC")
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目获取用户信息响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取用户信息响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取用户信息,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目获取用户信息,result为空");
            return UserDetailDTO.builder().build();
        }

        return JsonUtils.parseObject(response.getResult()
            .toString(), UserDetailDTO.class);
    }

    /**
     * 获取用户信息
     */
    public UserForInnerDTO getUserDetailByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return UserForInnerDTO.builder().build();
        }

        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getUserDetailByUserCodeUrl)
            .queryParam("userCodes", userCode)
            .build()
            .encode()
            .toUriString();
        log.info("getUserDetailByUserCode uriString: {}", uriString);
        String rbacResult = HttpClientUtil.sendGet(uriString);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取用户信息响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取用户信息,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目获取用户信息,result为空");
            return UserForInnerDTO.builder().build();
        }

        List<UserForInnerDTO> resultList = JsonUtils.parseList(response.getResult()
            .toString(), UserForInnerDTO.class);
        UserForInnerDTO user = Safes.of(resultList).stream().findAny().orElse(UserForInnerDTO.builder().build());
        return user;
    }

    /**
     * 获取用户信息
     */
    public List<UserForInnerDTO> getUserDetailByUserCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Lists.newArrayList();
        }
        log.info("--getUserDetailByUserCode 获取用户信息 userCode={}", userCodes);
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getUserDetailByUserCodeUrl)
            .queryParam("userCodes", StringUtils.join(userCodes, ","))
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目getUserDetailByUserCode响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取用户信息响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取用户信息,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目获取用户信息,result为空");
            return Lists.newArrayList();
        }

        List<UserForInnerDTO> resultList = JsonUtils.parseList(response.getResult()
            .toString(), UserForInnerDTO.class);
        return resultList;
    }

    /**
     * 获取酒店的管理人员
     */
    public HotelManagerDTO getManagerByChainId(Integer chainId) {
        log.info("--getManagerByChainId 获取酒店的管理人员邮箱 chainIds={}", JsonUtils.toJson(Collections.singleton(chainId)));
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getManagerByChainIdUrl)
            .queryParam("chainIds", StringUtils.join(chainId, ","))
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目获取酒店的管理人员邮箱响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取酒店的管理人员邮箱响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取酒店的管理人员邮箱,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.error("调用RBAC项目获取酒店的管理人员邮箱,result为空");
            return HotelManagerDTO.builder().build();
        }

        List<HotelManagerDTO> resultList = JsonUtils.parseList(response.getResult()
            .toString(), HotelManagerDTO.class);

        return Safes.of(resultList).stream().findAny().orElse(HotelManagerDTO.builder().build());
    }

    /**
     * 获取酒店的管理人员
     */
    public List<HotelManagerDTO> getManagerByChainIds(List<Integer> chainIds) {
        log.info("--getManagerByChainId 获取酒店的管理人员邮箱 chainIds={}", JsonUtils.toJson(chainIds));
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getManagerByChainIdUrl)
            .queryParam("chainIds", StringUtils.join(chainIds, ","))
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目获取酒店的管理人员邮箱响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目获取酒店的管理人员邮箱响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目获取酒店的管理人员邮箱,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目获取酒店的管理人员邮箱,result为空");
            return Lists.newArrayList();
        }

        List<HotelManagerDTO> resultList = JsonUtils.parseList(response.getResult()
            .toString(), HotelManagerDTO.class);

        return resultList;
    }
    /**
     * 根据查看权限获取用户
     */
    public List<SimpleUserDto> getReviewer() {
        if (rbacService.isUseRbac()) {
            return getReviewerWithRbac();
        }
        List<Integer> ruleIdList = Lists.newArrayListWithExpectedSize(3);
        ruleIdList.add(authoritySetnegative);
        ruleIdList.add(authoritySetrewiewed);
        ruleIdList.add(authoritySetunreview);
        return getUserByRuleId(ruleIdList);
    }

    public List<SimpleUserDto> getReviewerWithRbac() {

        return rbacService.queryCommentReviewer()
            .stream()
            .map(SimpleUserInfoWrapper::fromUserInfo)
            .collect(Collectors.toList());
    }

    /**
     * 根据查看权限获取用户
     */
    public List<SimpleUserDto> getSetNegativeUserList() {
        List<Integer> ruleIdList = Lists.newArrayListWithExpectedSize(3);
        ruleIdList.add(authoritySetnegative);
        return getUserByRuleId(ruleIdList);
    }

    /**
     * 根据权限获取差评提交用户列表
     */
    public Map<String, SimpleUserDto> getNegativeDutyEmployeeMap() {
        if (rbacService.isUseRbac()) {
            return getNegativeDutyEmployeeMapWithRbac();
        }
        List<SimpleUserDto> simpleUserDtoList = getUserByRuleId(Collections.singletonList(authorityNegativeProcessCommit));
        return Safes.of(simpleUserDtoList)
            .stream()
            .collect(Collectors.toMap(SimpleUserDto::getUserCode, Function.identity(), (vq, v2) -> v2));
    }

    /**
     * 根据权限获取差评提交用户列表
     */
    public Map<String, SimpleUserDto> getNegativeDutyEmployeeMapWithRbac() {
        return rbacService.queryNegativeDutyEmployee()
            .stream()
            .map(SimpleUserInfoWrapper::fromUserInfo)
            .collect(Collectors.toMap(SimpleUserDto::getUserCode, Function.identity(), (vq, v2) -> v2));
    }

    /**
     * 根据权限获取差评提交用户列表
     */
    public Map<String, SimpleUserDto> getNegativeApprovalMap() {
        List<SimpleUserDto> simpleUserDtoList = getUserByRuleId(Collections.singletonList(authorityNegativeProcessApproval));
        return Safes.of(simpleUserDtoList)
            .stream()
            .collect(Collectors.toMap(SimpleUserDto::getUserCode, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 判断用户是否有差评提交权限，是否是差评负责人
     * @param userCode
     * @return
     */
    public Boolean isNegativeDutyEmployee(String userCode) {
        return Safes.of(getUserByRuleId(Collections.singletonList(authorityNegativeProcessCommit)))
            .stream()
            .anyMatch(user -> Objects.equals(user.getUserCode(), userCode));
    }

    /**
     * 根据权限获取用户
     */
    public List<SimpleUserDto> getUserByRuleId(List<Integer> ruleIdList) {
        log.info("--getUserByRuleId 根据权限获取用户 ruleIdList={}", JsonUtils.toJson(Collections.singleton(ruleIdList)));
        String uriString = UriComponentsBuilder.fromUriString(getUserByRuleIdUrl)
            .queryParam("ruleIds", StringUtils.join(ruleIdList, ","))
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目根据权限获取用户响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目根据权限获取用户响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目根据权限获取用户,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目根据权限获取用户,result为空");
            return Collections.emptyList();
        }

        return JsonUtils.parseList(response.getResult()
            .toString(), SimpleUserDto.class);

    }



    /**
     * 根据权限获取用户
     */
    public List<SimpleUserDto> getXianZhangAndPartTimeXianZhang(Integer chainId) {
        log.info("--getXianZhangAndPartTimeXianZhang 获取酒店现长和兼店现长 chainId={}", chainId);
        String uriString = UriComponentsBuilder.fromUriString(getXianZhangAndPartTimeXianZhangUrl)
            .queryParam("chainId", chainId)
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目根据权限获取酒店现长和兼店现长响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目根据权限获取酒店现长和兼店现长响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目根据权限获取酒店现长和兼店现长,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目根据权限获取酒店现长和兼店现长,result为空");
            return Collections.emptyList();
        }

        return JsonUtils.parseList(response.getResult()
            .toString(), SimpleUserDto.class);

    }

    /**
     * 根据地区获取地区所有的子集（包括所有层级的子集）
     */
    public List<MebSellerDepDto> getAreaSubDept(Integer areaId) {
        log.info("--getAreaSubDept 根据地区获取地区所有的子集 areaId={}", areaId);
        String uriString = UriComponentsBuilder.fromUriString(FileConfig.getAreaSubDeptUrl)
            .queryParam("areaId", areaId)
            .build()
            .encode()
            .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("调用RBAC项目根据地区获取地区所有的子集响应信息：{}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用RBAC项目根据地区获取地区所有的子集响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC项目根据地区获取地区所有的子集,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.warn("调用RBAC项目根据地区获取地区所有的子集,result为空");
            return Collections.emptyList();
        }

        return JsonUtils.parseList(response.getResult()
            .toString(), MebSellerDepDto.class);

    }


    /**
     * 根据酒店查酒店
     *
     * @param areaId
     * @return
     */
    public Set<Integer> queryChainByArea(Integer areaId) {

        final List<MebSellerDepDto> areaSubDept = Safes.of(getAreaSubDept(areaId));
        return areaSubDept.stream()
            .map(MebSellerDepDto::getDepId)
            .collect(Collectors.toSet());
    }

    /**
     * 获取会有手机号
     * @param operatorId
     * @param operatorName
     * @param visitedMemberId
     * @return
     */
    public String queryMebMobile(Integer operatorId, String operatorName, Integer visitedMemberId) {
        return personalMemberRemote.getMobile(operatorId, operatorName, visitedMemberId);
    }


    /**
     * 获取rbac用户信息
     */
    public List<UserDTO> getRbacUserDetail(Set<String> employeeIds) {
        log.info("--getRbacUserDetail 获取用户信息 employeeIds={}", ObjectUtil.toJsonQuietly(employeeIds));
        ApiResult<List<UserDTO>> userResult = rbacUserRemote.getUserListByEmployeeIds(
            Joiner.on(",").join(employeeIds),
            applicationName);
        if(Objects.isNull(userResult) || userResult.getCode()!=ResponseCodeEnum.SUCCESS.getCode()){
            log.warn("--getRbacUserDetail 获取用户信息失败");
            return Collections.emptyList();
        }
        return  userResult.getResult();
    }
    /**
     * 获取rbac用户信息
     */
    public List<com.atour.rbac.module.user.response.UserForInnerDTO> getUserDetailRpc(Set<String> employeeIds) {
        log.info("--getUserDetailRpc 获取用户信息 employeeIds={}", ObjectUtil.toJsonQuietly(employeeIds));
        ApiResult<List<com.atour.rbac.module.user.response.UserForInnerDTO>> userResult = rbacUserRemote.getUserDetailRpc(employeeIds);
        if(Objects.isNull(userResult) || userResult.getCode()!=ResponseCodeEnum.SUCCESS.getCode()){
            log.warn("--getUserDetailRpc 获取用户信息失败");
            return Collections.emptyList();
        }
        return  userResult.getResult();
    }





    /**
     * 获取用户所拥有的门店权限
     * @return
     */
    public List<Integer> getUserManageChainIdList(){
        UserPermissionDTO userPermission = CommonParamsManager.getLocalUserHotel();
        if (Objects.isNull(userPermission)) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                    ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        return userPermission.getChainList();
    }

    /**
     * 查询代理现长
     * @param chainId
     * @return
     */
    public List<SimpleUserDto> getProxyXianzhang(Integer chainId) {
        if (chainId == null) {
            return Collections.emptyList();
        }
        List<RbacUserDTO> rbacUserDTOList = rbacService.getRbacUserByJobIdAndDeptId(FileConfig.proxyXianZhangJobId, chainId.toString());
        if (CollectionUtils.isEmpty(rbacUserDTOList)) {
            return Collections.emptyList();
        }
        List<SysUserEntity> sysUserList = sysUserDao.getUserListByHrids(Safes.of(rbacUserDTOList).stream().map(item -> item.getEmployeeId()).collect(Collectors.toList()));
        return sysUserList.stream().map(item -> new SimpleUserDto(item.getUserID(), item.getUserCode(), item.getUserName())).collect(Collectors.toList());
    }


    /**
     * 获取现长登录名称集合
     * @param chainId
     * @return
     */
    public String getXianzhangListEmployeeId(Integer chainId){

        StringBuilder emailBulider =new StringBuilder();
        HotelManagerDTO manager = this.getManagerByChainId(chainId);
        if(CollectionUtils.isNotEmpty(manager.getXianzhangList())){

            final Set<String> employeeIdList = manager.getXianzhangList().stream().map(HotelManagerInfoDTO::getEmployeeId).collect(Collectors.toSet());

             List<com.atour.rbac.module.user.response.UserForInnerDTO> rbacUserDetail = getUserDetailRpc(employeeIdList);
             if(CollectionUtils.isNotEmpty(rbacUserDetail)){
                return rbacUserDetail.stream().map(com.atour.rbac.module.user.response.UserForInnerDTO::getUserName).collect(Collectors.joining(","));
             }
        }
        return emailBulider.toString();
    }

    /**
     * 获取登录名称
     * @param employeeId
     * @return
     */
    public String getUserLoginName(String employeeId){
        if(StringUtils.isEmpty(employeeId)){
            return null;
        }
        Set<String> stringSet = new HashSet<>();
        stringSet.add(employeeId);
        List<com.atour.rbac.module.user.response.UserForInnerDTO> rbacUserDetail = getUserDetailRpc(stringSet);
        if(CollectionUtils.isNotEmpty(rbacUserDetail)){
                return rbacUserDetail.get(0).getUserName();
        }
        return null;
    }








}
