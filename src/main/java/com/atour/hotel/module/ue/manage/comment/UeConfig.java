package com.atour.hotel.module.ue.manage.comment;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 配置项
 *
 * <AUTHOR>
 * @date 2022-05-09 20:25
 */
@Configuration
public class UeConfig {
    /**
     * 开启日志输出
     */
    public static volatile boolean logEnabled = false;

    /**
     * 每次更新的时间范围
     */
    public static volatile int historyMins = 40;

    /**
     * 使用更新时间来刷新ue comment 更准确高效
     */
    public static volatile boolean historyByUpdateTime = true;

    @Value("${ue.huipingReview.logEnabled:true}")
    public void setLogEnabled(boolean logEnabled) {
        UeConfig.logEnabled = logEnabled;
    }

    @Value("${ue.huipingReview.historyMins:1440}")
    public void setHistoryMins(int historyMins) {
        UeConfig.historyMins = historyMins;
    }

    @Value("${ue.huipingReview.historyByUpdateTime:false}")
    public void setHistoryByUpdateTime(boolean historyByUpdateTime) {
        UeConfig.historyByUpdateTime = historyByUpdateTime;
    }
}

