package com.atour.hotel.module.ue.manage.comment.statemachine.action;

import com.atour.hotel.module.ue.manage.comment.statemachine.events.NegativeEvent;
import com.atour.hotel.module.ue.manage.comment.statemachine.states.NegativeState;
import lombok.Data;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;

/**
 * <AUTHOR>
 * @date 2020/8/7
 */
@Data
public class NegativeCommentErrorAction implements Action<NegativeState, NegativeEvent> {

    private Exception exception;

    /**
     * Execute action with a {@link StateContext}.
     *
     * @param context the state context
     */
    @Override
    public void execute(StateContext<NegativeState, NegativeEvent> context) {

        setException(context.getException());
    }
}
