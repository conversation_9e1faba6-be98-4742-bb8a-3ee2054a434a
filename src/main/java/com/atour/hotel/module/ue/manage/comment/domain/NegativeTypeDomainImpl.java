package com.atour.hotel.module.ue.manage.comment.domain;

import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.ue.manage.comment.dto.NegativeTypeListDto;
import com.atour.hotel.persistent.cem.dao.UeCommentMapper;
import com.atour.hotel.persistent.cem.dao.UeNegativeCommentTypeMapper;
import com.atour.hotel.persistent.cem.dao.UeNegativeTypeRelationMapper;
import com.atour.hotel.persistent.cem.entity.UeCommentEntity;
import com.atour.hotel.persistent.cem.entity.UeNegativeCommentTypeEntity;
import com.atour.hotel.persistent.cem.entity.UeNegativeTypeRelationEntity;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/11/8
 * 差评类型领域
 */
@Service
public class NegativeTypeDomainImpl implements NegativeTypeDomain{
    @Resource
    private UeNegativeCommentTypeMapper ueNegativeCommentTypeMapper;
    @Resource
    private UeNegativeTypeRelationMapper ueNegativeTypeRelationMapper;
    @Resource
    private UeCommentMapper ueCommentMapper;

    public List<UeNegativeCommentTypeEntity> getAll() {
        return ueNegativeCommentTypeMapper.selectBySelective(null);
    }

    /**
     * 构建层级结构(三层)
     * @return
     */
    @Override
    public List<NegativeTypeListDto> buildLevelStructrue() {
        List<UeNegativeCommentTypeEntity>  entityList = getAll();
        Map<Integer, List<UeNegativeCommentTypeEntity>> groupByLevel = Safes.of(entityList).stream()
            .collect(Collectors.groupingBy(UeNegativeCommentTypeEntity::getNegativeLevel));
        Map<Integer, List<UeNegativeCommentTypeEntity>> levelOneMap = Safes.of(groupByLevel.getOrDefault(1, Collections.emptyList()))
            .stream()
            .collect(Collectors.groupingBy(UeNegativeCommentTypeEntity::getPid));
        Map<Integer, List<UeNegativeCommentTypeEntity>> levelTwoMap = Safes.of(groupByLevel.getOrDefault(2, Collections.emptyList()))
            .stream()
            .collect(Collectors.groupingBy(UeNegativeCommentTypeEntity::getPid));
        Map<Integer, List<UeNegativeCommentTypeEntity>> levelThreeMap = Safes.of(groupByLevel.getOrDefault(3, Collections.emptyList()))
            .stream()
            .collect(Collectors.groupingBy(UeNegativeCommentTypeEntity::getPid));

        return Safes.of(levelOneMap.values()).stream()
            .map(levelOneList -> Safes.of(levelOneList)
                .stream()
                .map(levelOne -> {
                    List<NegativeTypeListDto> levelTwoChildren = Safes.of(levelTwoMap.get(levelOne.getId()))
                        .stream()
                        .map(levelTwo -> {
                            List<NegativeTypeListDto> levelThreeChildren = Safes.of(levelThreeMap.get(levelTwo.getId()))
                                .stream()
                                .map(levelThree -> NegativeTypeListDto.builder().id(levelThree.getId()).name(levelThree.getNegativeName()).build())
                                .collect(Collectors.toList());
                            return NegativeTypeListDto.builder().id(levelTwo.getId()).name(levelTwo.getNegativeName()).children(levelThreeChildren).build();
                        })
                        .collect(Collectors.toList());
                    return NegativeTypeListDto.builder().id(levelOne.getId()).name(levelOne.getNegativeName()).children(levelTwoChildren).build();
                })
                .collect(Collectors.toList())
            )
            .reduce(Lists.newArrayList(), (all, item) -> {all.addAll(item); return all;});
    }

    @Override
    public int replaceNegativeTypeRelation(Long commentId, List<Integer> negativeTypeList) {
        if (Objects.isNull(commentId) || CollectionUtils.isEmpty(negativeTypeList)) {
            return 0;
        }
        UeCommentEntity ueCommentEntity = ueCommentMapper.selectByPrimaryKey(commentId);
        if (Objects.isNull(ueCommentEntity)) {
           throw new BusinessException(String.format("评论ID[%s]不存在！", commentId));
        }
        List<UeNegativeTypeRelationEntity> recordList = ueNegativeTypeRelationMapper.selectBySelective(UeNegativeTypeRelationEntity.builder().commentId(commentId).build());

        if (CollectionUtils.isNotEmpty(recordList)) {
            ueNegativeTypeRelationMapper.deleteByCommentId(commentId);
        }
        negativeTypeList.forEach(negativeType -> {
            UeNegativeTypeRelationEntity entity = UeNegativeTypeRelationEntity
                .builder()
                .chainId(ueCommentEntity.getChainId())
                .commentId(commentId)
                .sourceType(ueCommentEntity.getSourceType())
                .typeId(negativeType)
                .negativeTime(ueCommentEntity.getNegativeTime())
                .build();
            ueNegativeTypeRelationMapper.insertSelective(entity);
        });

        return negativeTypeList.size();
    }
}
