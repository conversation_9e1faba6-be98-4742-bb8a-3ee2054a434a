package com.atour.hotel.module.ue.manage.comment.sql.param;

import com.atour.hotel.module.ue.manage.comment.statemachine.states.NegativeState;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/11/1
 * 差评列表数据层入参
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NegativeListQueryParam {
    /**
     * 评论时间起始时间点
     */
    private LocalDate beginTime;
    /**
     * 评论时间结束时间点
     */
    private LocalDate endTime;
    /**
     * 酒店列表
     */
    private List<Integer> chainIdList;
    /**
     * 来源类型
     */
    private List<Integer> sourceTypeList;

    /**
     * 差评类型
     */
    private List<Integer> negativeTypeList;
    /**
     * 差评状态
     */
    private List<Integer> negativeStateList;
    /**
     * 只看超时
     */
    private Boolean negativeProcessTimeoutFlag;
    /**
     * 页码
     */
    private Integer beginNo;

    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 排序语句
     */
    private String orderByPhrase;

    @Getter
    @AllArgsConstructor
    public enum ProcessState {
        /**
         * 未知
         */
        UNKNONW(0, "未知"),
        /**
         * 待处理
         */
        PEDING_PROCESS(1, "待处理"),
        /**
         * 待反馈
         */
        PENDING_FEEDBACK(2, "待反馈"),
        /**
         * 待审核
         */
        PENDING_APPROVAL(3, "待审核"),
        /**
         * 整改完成
         */
        REFORM_FINISH(4, "整改完成"),
        /**
         * 不计入差评
         */
        NOT_NEGATIVE(5, "申诉通过"),
        ;
        private Integer state;
        private String desc;
        public static ProcessState ofState(Integer state) {
            for (ProcessState item : values()) {
                if (Objects.equals(item.state, state)) {
                    return item;
                }
            }
            return UNKNONW;
        }
    }

    public static List<Integer> getPendingProcess() {
        List<Integer> result = Lists.newArrayList();
        result.add(NegativeState.UN_PROCESS.getState());
        result.add(NegativeState.APPEAL_NOT_PASS.getState());
        result.add(NegativeState.REFORM_NOT_PASS.getState());
        return result;
    }

    /**
     * 将入参的差评处理状态，转为实际差评状态
     * @param processState
     * @param negativeState
     * @return
     */
    public static List<Integer> cvNegativeState(Integer processState, Integer negativeState) {
        List<Integer> result = Lists.newArrayList();
        //如果negativeState不为空直接返回
        if (Objects.nonNull(negativeState)) return Lists.newArrayList(negativeState);
        //如果处理状态为空，返回所有差评状态
        if (Objects.isNull(processState)) {
            result.add(NegativeState.UN_PROCESS.getState());
            result.add(NegativeState.APPEAL_NOT_PASS.getState());
            result.add(NegativeState.REFORM_NOT_PASS.getState());
            result.add(NegativeState.REFORM_PASS_AND_NEED_FEEDBACK.getState());
            result.add(NegativeState.FEEDBACK_NOT_PASS.getState());
            result.add(NegativeState.REFORMING.getState());
            result.add(NegativeState.APPEALING.getState());
            result.add(NegativeState.FEEDBACKING.getState());
            result.add(NegativeState.REFORM_PASS_AND_FINISH.getState());
            result.add(NegativeState.FEEDBACK_PASS.getState());
            result.add(NegativeState.NOT_COUNTED_INTO_NEGATIVE.getState());
            result.add(NegativeState.APPEAL_PASS.getState());
            //如果差评状态不为空，直接返回此状态
            if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                result = Lists.newArrayList(negativeState);
                return result;
            }
            return result;
        }

        switch (ProcessState.ofState(processState)) {
            case PEDING_PROCESS: {
                result.add(NegativeState.UN_PROCESS.getState());
                result.add(NegativeState.APPEAL_NOT_PASS.getState());
                result.add(NegativeState.REFORM_NOT_PASS.getState());
                //如果差评状态不为空，直接返回此状态
                if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                    result = Lists.newArrayList(negativeState);
                    return result;
                }
                break;
            }
            case PENDING_FEEDBACK: {
                result.add(NegativeState.REFORM_PASS_AND_NEED_FEEDBACK.getState());
                result.add(NegativeState.FEEDBACK_NOT_PASS.getState());
                //如果差评状态不为空，直接返回此状态
                if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                    result = Lists.newArrayList(negativeState);
                    return result;
                }
                break;
            }
            case PENDING_APPROVAL: {
                result.add(NegativeState.REFORMING.getState());
                result.add(NegativeState.APPEALING.getState());
                result.add(NegativeState.FEEDBACKING.getState());
                //如果差评状态不为空，直接返回此状态
                if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                    result = Lists.newArrayList(negativeState);
                    return result;
                }
                break;
            }
            case REFORM_FINISH: {
                result.add(NegativeState.REFORM_PASS_AND_FINISH.getState());
                result.add(NegativeState.FEEDBACK_PASS.getState());
                //如果差评状态不为空，直接返回此状态
                if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                    result = Lists.newArrayList(negativeState);
                    return result;
                }
                break;
            }
            case NOT_NEGATIVE: {
                result.add(NegativeState.NOT_COUNTED_INTO_NEGATIVE.getState());
                result.add(NegativeState.APPEAL_PASS.getState());
                //如果差评状态不为空，直接返回此状态
                if (Objects.nonNull(negativeState) && result.contains(negativeState)) {
                    result = Lists.newArrayList(negativeState);
                    return result;
                }
                break;
            }
            default:
        }
        return result;
    }

    /**
     * 将实际差评状态，转为处理状态状态
     * @param negativeState
     * @return
     */
    public static ProcessState cvNegativeStateDto(NegativeState negativeState) {
        ProcessState result = ProcessState.UNKNONW;
        switch (negativeState) {
            case UN_PROCESS:
            case APPEAL_NOT_PASS:
            case REFORM_NOT_PASS: {
                result = ProcessState.PEDING_PROCESS;
                break;
            }
            case REFORM_PASS_AND_NEED_FEEDBACK:
            case FEEDBACK_NOT_PASS: {
                result = ProcessState.PENDING_FEEDBACK;
                break;
            }
            case REFORMING:
            case APPEALING:
            case FEEDBACKING: {
                result = ProcessState.PENDING_APPROVAL;
                break;
            }
            case REFORM_PASS_AND_FINISH:
            case FEEDBACK_PASS: {
                result = ProcessState.REFORM_FINISH;
                break;
            }
            case NOT_COUNTED_INTO_NEGATIVE:
            case APPEAL_PASS:{
                result = ProcessState.NOT_NEGATIVE;
                break;
            }
            default:
        }
        return result;
    }
}
