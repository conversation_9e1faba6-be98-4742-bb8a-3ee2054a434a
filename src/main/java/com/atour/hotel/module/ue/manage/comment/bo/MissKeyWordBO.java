package com.atour.hotel.module.ue.manage.comment.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 观点遗漏
 *
 * <AUTHOR>
 * @date 2020/7/31
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MissKeyWordBO extends AdditionalInformationBO {

    /**
     * 遗漏的的情感观点
     */
    private List<KeyWordBO> missKeywords;

}
