package com.atour.hotel.module.ue.manage.comment.enums;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

/**
 * 慧评渠道枚举
 */
@Getter
@AllArgsConstructor
public enum HuiPingSourceTypeEnum {
    CTRIP(1, "携程"),
    ELONG(2, "艺龙"),
    OWL(3, "猫途鹰"),
    DIANPING(5, "大众点评"),
    QUNAR(6, "去哪儿"),
    TRIP_ADVISOR(9, "TripAdvisor"),
    FLY_PIG(10, "飞猪"),
    BOOKING(11, "Booking"),
    AGODA(12, "Agoda"),
    EXPEDIA(16, "Expedia"),
    ORBITZ(18, "Orbitz"),
    HOTELS(19, "Hotels.com"),
    LIVE(21, "住哪网"),
    MEI_TUAN(26, "美团"),
    ATOUR_FEED(10015, "随手拍"),
    CUSTOMER_COMPLAINT(10056, "400客诉"),
    COMPLAINT_INFO(10057, "一键吐槽"),
    APP_MEMBER(10071, "亚朵APP-会员"),
    APP_STORE(10072, "亚朵APP-门店协议"),
    APP_GROUP(10073, "亚朵APP-集团协议"),
    WECHAT_MEMBER(10074, "亚朵微信-会员"),
    WECHAT_STORE(10075, "亚朵微信-门店协议"),
    WECHAT_GROUP(10076, "亚朵微信-集团协议"),
    ALI_MEMBER(10077, "亚朵支付宝-会员"),
    ALI_STORE(10078, "亚朵支付宝-门店协议"),
    ALI_GROUP(10079, "亚朵支付宝-集团协议"),
    H5_MEMBER(10080, "亚朵H5-会员"),
    H5_STORE(10081, "亚朵H5-门店协议"),
    H5_GROUP(10082, "亚朵H5-集团协议"),
    H5_GROUP_EXTERNAL(10096, "亚朵H5-集团协议(外部)"),
    ATOUR_APP(10014, "APP（旧）"),
    ATOUR_WECHAT(10016, "微信（旧）"),
    ;

    //亚朵渠道的点评
    public static final Set<Integer> AtourSourceType = Sets.newHashSet(ATOUR_APP.getTypeId(), ATOUR_WECHAT.getTypeId(), ATOUR_FEED.getTypeId(), H5_GROUP.getTypeId(), H5_STORE.getTypeId(), H5_MEMBER.getTypeId(), ALI_GROUP.getTypeId(), ALI_STORE.getTypeId(),
            ALI_MEMBER.getTypeId(), WECHAT_GROUP.getTypeId(), WECHAT_STORE.getTypeId(), WECHAT_MEMBER.getTypeId(), APP_GROUP.getTypeId(), APP_STORE.getTypeId(), APP_MEMBER.getTypeId(), H5_GROUP_EXTERNAL.getTypeId());

    private static final Map<Integer, HuiPingSourceTypeEnum> map;

    static {
        HuiPingSourceTypeEnum[] values = values();
        map = Maps.newHashMapWithExpectedSize(values.length);
        for (HuiPingSourceTypeEnum value : values) {
            map.put(value.typeId, value);
        }
    }

    private int typeId;

    private String typeName;

    public static HuiPingSourceTypeEnum codeOf(int typeId) {
        return map.get(typeId);
    }


    public static String getValue(int code) {
        for (HuiPingSourceTypeEnum typeEnum : values()) {
            if (typeEnum.typeId == code) {
                return typeEnum.getTypeName();
            }
        }
        return null;
    }
}
