package com.atour.hotel.module.ue.web.comment.controller;

import com.atour.api.bean.ResponseFactory;
import com.atour.api.bean.pmsapi.PmsApiResponse;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.ue.manage.comment.param.FeedbackApprovalParam;
import com.atour.hotel.module.ue.manage.comment.param.FeedbackCommitParam;
import com.atour.hotel.module.ue.manage.comment.param.UpdateFeedbackApprovalResultParam;
import com.atour.hotel.module.ue.manage.comment.service.NegativeManageService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/10/29
 * 差评反馈流程控制机器
 */
@RestController
@RequestMapping(value = "/api/web/ue/feedback", produces = {"application/json;charset=UTF-8"})
public class NegativeFeedbackController {

    @Resource
    private NegativeManageService negativeManageService;

    @PostMapping(value = "/commit")
    @ResponseBody
    public PmsApiResponse commit(@RequestBody FeedbackCommitParam param) throws Exception {
        negativeManageService.feedbackCommit(CommonParamsManager.get(), param);
        return ResponseFactory.buildPmsApiSuccessReponse();
    }

    @PostMapping(value = "/approval")
    @ResponseBody
    public PmsApiResponse pass(@RequestBody FeedbackApprovalParam param) throws Exception {
        negativeManageService.feedbackApproval(CommonParamsManager.get(), param);
        return ResponseFactory.buildPmsApiSuccessReponse();
    }

    @PostMapping(value = "/change/approval")
    @ResponseBody
    public PmsApiResponse changeApproval(@RequestBody UpdateFeedbackApprovalResultParam param) throws Exception {
        negativeManageService.updateFeedbackApprovalResultApproval(CommonParamsManager.get(), param);
        return ResponseFactory.buildPmsApiSuccessReponse();
    }
}
