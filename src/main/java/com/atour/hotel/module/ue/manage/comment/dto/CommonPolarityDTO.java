package com.atour.hotel.module.ue.manage.comment.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 语义分析结果
 *
 * <AUTHOR>
 * @date 2021-11-30 16:16
 */
@Data
public class CommonPolarityDTO {
    /**
     * 字段起点
     */
    @JSONField(name = "start_index")
    private Integer startIndex;
    /**
     * 字段内容
     */
    @JSONField(name = "audit_prop_name")
    private String auditPropName;
    /**
     * 字段终点
     */
    @JSONField(name = "end_index")
    private Integer endIndex;
    /**
     * 情感倾向 大于0为好评，小于0为差评，不返回片段信息则为无情感
     */
    @JSONField(name = "polarity")
    private String polarity;
}
