package com.atour.hotel.module.ue.manage.comment.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/7/31
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AnalysisErrorFeedbackParam extends AnalysisFeedbackParam {

    @NotNull(message = "点评 id 不能为空")
    @ApiModelProperty(value = "点评 id")
    private Long commentId;

}
