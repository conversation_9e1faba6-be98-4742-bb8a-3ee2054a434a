package com.atour.hotel.module.ue.manage.comment.job;

import com.atour.hotel.module.ue.manage.comment.enums.NegativeStateSet;
import com.atour.hotel.persistent.cem.dao.UeCommentMapper;
import com.atour.hotel.persistent.cem.entity.UeCommentEntity;
import com.atour.utils.Safes;
import com.atour.utils.xxljob.XxlJobParamUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 清洗审核时间
 *
 * <AUTHOR>
 * @date 2020/9/24
 */
@Slf4j
@Component
@JobHandler(value = "approvalTImeJob")
public class ApprovalTimeJob extends IJobHandler {

    @Resource
    private UeCommentMapper ueCommentMapper;

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER_T = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");

    private static final DateTimeFormatter DATE_TIME_FORMATTER_T_1 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * execute handler, invoked when executor receives a scheduling request
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) {

        final Map<String, String> paramMap = XxlJobParamUtil.parseParam(param);
        log.info("清洗点评数据的审核时间");
        long id = Long.parseLong(paramMap.getOrDefault("id", "0"));
        int limit = Integer.parseInt(paramMap.getOrDefault("limit", "1000"));
        while (true) {
            final List<UeCommentEntity> ueCommentEntities = ueCommentMapper.selectByIdLimit(id, limit);
            if (CollectionUtils.isEmpty(ueCommentEntities)) {
                break;
            }
            for (UeCommentEntity ueCommentEntity : ueCommentEntities) {
                final String comment = ueCommentEntity.getNegativeProcessFlow();
                log.info("清洗点评数据的审核时间。 id={}", ueCommentEntity.getId());
                final List<UeCommentEntity.NegativeProcessFlow> negativeProcessFlowFromJson =
                    Safes.of(ueCommentEntity.getNegativeProcessFlowFromJson());
                if (StringUtils.isBlank(comment) || CollectionUtils.isEmpty(negativeProcessFlowFromJson)) {
                    continue;
                }
                List<LocalDateTime> dates = negativeProcessFlowFromJson.stream()
                    .filter(flow -> NegativeStateSet.NEGATIVE_STATE_SET.contains(flow.getNegativeState()) && StringUtils.isNotBlank(
                        flow.getProcessTime()))
                    .map(flow -> {
                        LocalDateTime dateTime;
                        String processTime = flow.getProcessTime();
                        if(processTime.contains("T")){
                            if(flow.getProcessTime().length()>20) {
                                dateTime = LocalDateTime.parse(flow.getProcessTime(), DATE_TIME_FORMATTER_T);
                            }else{
                                dateTime = LocalDateTime.parse(flow.getProcessTime(), DATE_TIME_FORMATTER_T_1);
                            }
                        }else{
                            dateTime = LocalDateTime.parse(flow.getProcessTime(), DATE_TIME_FORMATTER);
                        }
                        return dateTime;
                        })
                    .collect(Collectors.toList());
                if(CollectionUtils.isEmpty(dates)){
                    continue;
                }
                LocalDateTime time = Collections.max(dates);
                UeCommentEntity updateRecord = new UeCommentEntity().setId(ueCommentEntity.getId())
                    .setApprovalTime(time);
                ueCommentMapper.updateByPrimaryKeySelective(updateRecord);

                log.info("清洗点评数据的审核时间。 id={}", ueCommentEntity.getId());
            }
            id = ueCommentEntities.get(ueCommentEntities.size() - 1)
                .getId();
        }

        log.info("清洗点评数据的审核时间");

        return ReturnT.SUCCESS;
    }
}
