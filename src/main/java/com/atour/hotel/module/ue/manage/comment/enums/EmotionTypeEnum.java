package com.atour.hotel.module.ue.manage.comment.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 情感类型
 *
 * <AUTHOR>
 * @date 2020/7/30
 */
@Getter
@AllArgsConstructor
public enum EmotionTypeEnum {

    /**
     * 好评
     */
    POSITIVE(1, "好评"),

    NEGATIVE(2, "差评"),

    // 这个是为了和 data-center 的数据结构对上, 业务上是没有的
    NORMAL(3, "中评"),

    ;

    private static final Map<Integer, EmotionTypeEnum> map;

    static {
        EmotionTypeEnum[] values = values();
        map = Maps.newHashMapWithExpectedSize(values.length);
        for (EmotionTypeEnum value : values) {
            map.put(value.code, value);
        }
    }


    private int code;

    private String description;

    public static EmotionTypeEnum codeOf(int code) {
        return map.get(code);
    }

}
