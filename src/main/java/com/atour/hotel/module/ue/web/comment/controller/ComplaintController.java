package com.atour.hotel.module.ue.web.comment.controller;

import com.atour.hotel.module.ue.manage.comment.service.ComplaintService;
import com.atour.hotel.param.complaint.ComplaintDTO;
import com.atour.hotel.param.complaint.ComplaintParam;
import com.atour.hotel.remote.ComplaintRemote;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 一键吐槽查询服务
 * @date 2025年01月02日22:18
 * @since JDK1.8
 */
@Api(value = "一键吐槽查询列表")
@RestController
@RequestMapping("/inner/api/web/complaint")
public class ComplaintController implements ComplaintRemote {

    @Resource
    private ComplaintService complaintService;

    /**
     * 一键吐槽列表
     *
     * @param request
     * @return
     */
    @Override
    @PostMapping("/page")
    public AtourResponse<ResponseList<ComplaintDTO>> pageList(@Validated @RequestBody AtourRequest<RequestList<ComplaintParam>> request) {
        try {
            return AtourResponse.successResponse(complaintService.pageList(request));
        } catch (Exception e) {
            return AtourResponse.errorResponse(e.getMessage());
        }
    }
}
