package com.atour.hotel.module.ue.web.comment.controller;

import com.atour.api.bean.ApiResult;
import com.atour.api.bean.ResponseFactory;
import com.atour.api.bean.pmsapi.PmsApiResponse;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.ue.manage.comment.CommentPager;
import com.atour.hotel.module.ue.manage.comment.dto.NegativeDutyEmployeeListDto;
import com.atour.hotel.module.ue.manage.comment.dto.NegativeFlowInfoDto;
import com.atour.hotel.module.ue.manage.comment.dto.NegativeListDto;
import com.atour.hotel.module.ue.manage.comment.dto.NegativeListTopDto;
import com.atour.hotel.module.ue.manage.comment.param.CancelNegativeParam;
import com.atour.hotel.module.ue.manage.comment.param.NegativeListParam;
import com.atour.hotel.module.ue.manage.comment.param.UpdateCommitParam;
import com.atour.hotel.module.ue.manage.comment.service.CommentService;
import com.atour.hotel.module.ue.manage.comment.service.NegativeManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 差评相关逻辑
 * <AUTHOR>
 * @date 2020/8/3
 */
@Validated
@Api(value = "差评相关", tags = {"差评相关"})
@RestController
@RequestMapping(value = "/api/web/ue/comment/negative", produces = {"application/json;charset=UTF-8"})
public class NegativeCommentController {

    @Resource
    private CommentService commentService;

    @Resource
    private NegativeManageService negativeManageService;

    /**
     * 获取差评列表
     * @return
     */
    @PostMapping(value = "/list")
    @ResponseBody
    @ApiOperation(value = "获取差评列表", httpMethod = "POST", produces = "application/json")
    public ApiResult<CommentPager<NegativeListDto, NegativeListTopDto>> queryNegativeList(@RequestBody NegativeListParam param) {

        return ApiResult.success(commentService.queryPageNegative(CommonParamsManager.get(), param));
    }

    /**
     * 导出差评列表
     * @return
     */
    @GetMapping(value = "/export")
    @ResponseBody
    @ApiImplicitParams(value = {
        @ApiImplicitParam(name = "negativeTypeList", value = "差评类型", dataType = "array"),
        @ApiImplicitParam(name = "processState", value = "处理状态", dataType = "int"),
        @ApiImplicitParam(name = "negativeState", value = "差评状态", dataType = "int"),
        @ApiImplicitParam(name = "negativeProcessTimeoutFlag", value = "只看超时标志", dataType = "boolean"),
        @ApiImplicitParam(name = "beginTime", value = "评论开始时间", dataType = "string"),
        @ApiImplicitParam(name = "endTime", value = "评论结束时间", dataType = "string"),
        @ApiImplicitParam(name = "chainIdList", value = "门店", dataType = "array"),
        @ApiImplicitParam(name = "sourceType", value = "来源类型", dataType = "int"),
        @ApiImplicitParam(name = "deptId", value = "区域ID", dataType = "int"),
       })
    @ApiOperation(value = "导出差评列表", httpMethod = "GET", produces = "application/json")
    public void exportNegativeList(NegativeListParam param, HttpServletResponse response) {

        commentService.exportPageNegative(CommonParamsManager.get(), param, response);
    }


    /**
     *  差评处理详情
     * @param commentId
     * @return
     */
    @GetMapping(value = "/flow/info")
    @ResponseBody
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "commentId", value = "评论 id", dataType = "java.lang.Long", required = true)})
    @ApiOperation(value = "差评处理详情", httpMethod = "GET", produces = "application/json")
    public ApiResult<List<NegativeFlowInfoDto>> negativeFlowInfo(@RequestParam Long commentId) {

        return ApiResult.success(commentService.negativeFlowInfo(CommonParamsManager.get(), commentId));
    }


    /**
     *  差评负责人
     * @param chainId
     * @return
     */
    @GetMapping(value = "/dutyEmployeeList")
    @ResponseBody
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "chainId", value = "酒店id", dataType = "java.lang.Integer", required = true)})
    @ApiOperation(value = "差评负责人", httpMethod = "GET", produces = "application/json")
    public ApiResult<List<NegativeDutyEmployeeListDto>> getNegativeDutyEmployeeList(@RequestParam Integer chainId) {

        return ApiResult.success(commentService.getNegativeDutyEmployeeList(chainId));
    }


    /**
     * 修改申诉
     *
     *
     * @param param
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/change/commit")
    @ResponseBody
    @ApiOperation(value = "修改差评申诉", httpMethod = "POST", produces = "application/json")
    public PmsApiResponse<Void> changeCommit(@RequestBody UpdateCommitParam param) throws Exception {

        negativeManageService.updateCommit(CommonParamsManager.get(), param);
        return ResponseFactory.buildPmsApiSuccessReponse();
    }

    @RequestMapping("/cancel")
    @ApiOperation(value = "取消差评", httpMethod = "POST", produces = "application/json")
    public ApiResult<Boolean> cancelNegative(@Valid @RequestBody CancelNegativeParam param) {

        return ApiResult.success(commentService.cancelNegative(param));
    }

}
