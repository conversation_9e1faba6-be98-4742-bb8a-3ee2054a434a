package com.atour.hotel.module.ue.manage.comment.param;

import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.ue.manage.comment.enums.AppealApprovalTypeEnum;
import com.atour.hotel.module.ue.manage.comment.statemachine.events.NegativeEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/10/29
 * 申诉审批数据传输对象
 */
@Data
public class AppealApprovalParam {
    /**
     * 评论ID
     */
    @ApiModelProperty(value = "评论ID")
    private Long commentId;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容, 审核意见")
    private String content;
    /**
     * 申诉审批类型，1：通过；2：不通过
     */
    @ApiModelProperty(value = "申诉审批类型，1：通过；2：不通过, 3: 评测异常，取消差评, 4: 评测异常，仍计差评, 5: 评测正常，仍计差评, 6: 评测正常，取消差评")
    private Integer appealApprovalType;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    public NegativeEvent gainNegativeEvent() {

        if (Objects.equals(AppealApprovalTypeEnum.PASS.getType(), appealApprovalType)
            || Objects.equals(AppealApprovalTypeEnum.ANALYSIS_ERROR_CANCEL_NEGATIVE.getType(), appealApprovalType)
            || Objects.equals(AppealApprovalTypeEnum.ANALYSIS_CORRECT_CANCEL_NEGATIVE.getType(), appealApprovalType)) {
            return NegativeEvent.APPEAL_PASS;
        } else if (Objects.equals(AppealApprovalTypeEnum.NOT_PASS.getType(), appealApprovalType)
            || Objects.equals(AppealApprovalTypeEnum.ANALYSIS_ERROR_KEEP_NEGATIVE.getType(), appealApprovalType)
            || Objects.equals(AppealApprovalTypeEnum.ANALYSIS_CORRECT_KEEP_NEGATIVE.getType(), appealApprovalType)) {
            return NegativeEvent.APPEAL_NOT_PASS;
        } else {
            throw new BusinessException(String.format("意外的审批类型[%s]", appealApprovalType));
        }
    }
}
