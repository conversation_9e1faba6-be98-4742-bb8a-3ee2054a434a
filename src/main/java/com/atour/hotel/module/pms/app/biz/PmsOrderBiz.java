package com.atour.hotel.module.pms.app.biz;

import com.atour.api.bean.pmsapi.PmsApiPageResponse;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.template.BaseBizTemplate;
import com.atour.hotel.module.pms.app.request.PmsBookOrderQueryParam;
import com.atour.hotel.module.pms.app.response.PmsBookOrderListResultDTO;
import com.atour.web.httpclient.AtourRestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 业务聚合层: 能耗管理-公共业务
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@Slf4j
@Component
public class PmsOrderBiz {

    /**
     * PMS查询预订单
     */
    @Value("${pms-url.bookingOrder}")
    private String pmsBookingOrderUrl;

    @Resource(name = "redirectPmsHttpClient")
    private AtourRestTemplate redirectHttpClient;
    /**
     * 查询PMS预订单列表
     *
     * @param pmsBookOrderQueryParam 预订单列表查询参数
     * @return 今日抵店预订单列表
     */
    public List<PmsBookOrderListResultDTO> orderBookList(PmsBookOrderQueryParam pmsBookOrderQueryParam) {
        return new BaseBizTemplate<List<PmsBookOrderListResultDTO>>("PmsOrderBiz.orderBookList") {
            @Override
            protected void checkParams() {
            }

            @Override
            protected List<PmsBookOrderListResultDTO> process() {

                // 查询预订单列表
                Integer currUserId = CommonParamsManager.getCurrUserId();
                String uriString = UriComponentsBuilder.fromUriString(pmsBookingOrderUrl)
                    .queryParam("chainid", pmsBookOrderQueryParam.getChainId())
                    .queryParam("state", 1)
                    .queryParam("querystate", 1)
                    .queryParam("pageNo", pmsBookOrderQueryParam.getPageNo())
                    .queryParam("pageSize", pmsBookOrderQueryParam.getPageSize())
                    .queryParam("userid", currUserId)
                    .queryParam("querydata", pmsBookOrderQueryParam.getQueryData())
                    .build()
                    .encode()
                    .toUriString();
//                String result = HttpClientUtil.sendGet(uriString);
                PmsApiPageResponse<PmsBookOrderListResultDTO> pageResponse = redirectHttpClient.getForObject(uriString, new ParameterizedTypeReference<PmsApiPageResponse<PmsBookOrderListResultDTO>>() {
                });
                try {
                    if (pageResponse.getCode() != 0) {
//                        AMonitor.meter("pms_book_order_search_fail");
                        log.error("调用PMS查询今日抵店预订单列表,出现错误, chainId={} queryData={} code:{}, msg:{}",
                                pmsBookOrderQueryParam.getChainId(), pmsBookOrderQueryParam.getQueryData(),
                                pageResponse.getCode(), pageResponse.getMsg());
                        throw new BusinessException("朵儿不在服务内,请稍后再试");
                    }
                    if (Objects.isNull(pageResponse.getResult())) {
//                        AMonitor.meter("pms_book_order_search_isNull");
                        log.error("调用PMS查询今日抵店预订单列表信息为空, chainId={} queryData={}", pmsBookOrderQueryParam.getChainId(), pmsBookOrderQueryParam.getQueryData());
                        throw new BusinessException("没有查询结果");
                    }
                    return pageResponse.getResult();
                } catch (BusinessException e) {
                    throw e;
                } catch (Exception e) {
                    throw new BusinessException("朵儿不在服务内,请稍后再试");
                }
            }
        }.execute();
    }
}
