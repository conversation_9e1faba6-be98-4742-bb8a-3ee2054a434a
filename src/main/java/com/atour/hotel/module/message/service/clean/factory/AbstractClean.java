package com.atour.hotel.module.message.service.clean.factory;

import com.alibaba.fastjson.JSONObject;
import com.atour.corp.api.dto.MebMainDTO;
import com.atour.dicts.db.center.sys_message.MessageSourceEnum;
import com.atour.dicts.db.center.sys_message.RelatedTypeEnum;
import com.atour.dicts.db.center.sys_message.SysMessageTypeEnum;
import com.atour.hotel.module.common.service.PushJpushService;
import com.atour.hotel.module.dkf.enums.MsgTypeEnum;
import com.atour.hotel.module.meb.service.MebService;
import com.atour.hotel.module.message.enums.CleanTitleEnum;
import com.atour.hotel.module.message.service.AppointmentCleanService;
import com.atour.hotel.persistent.center.dao.SysMessageDao;
import com.atour.hotel.persistent.center.entity.SysMessageEntity;
import com.atour.notify.api.enums.JPushPlatformEnum;
import com.atour.notify.api.enums.JPushSceneTypeEnum;
import com.atour.notify.api.enums.JPushTypeEnum;
import com.atour.notify.api.params.AllChannelPushParam;
import com.atour.notify.api.params.JPushPersonalParam;
import com.atour.order.api.dto.AppointmentCleanOrderDTO;
import com.atour.order.api.enums.AppointmentCleanStateEnum;
import com.atour.security.Base64Util;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author：子烁
 * @date：2021/7/20 下午7:18
 */
public abstract class AbstractClean {
    @Resource
    private PushJpushService pushJpushService;

    @Resource
    private SysMessageDao sysMessageDao;
    @Resource
    private AppointmentCleanService appointmentCleanService;


    @Resource
    private MebService mebService;

    /**
     * 参数构建
     *
     * @param appointmentCleanOrderDTO
     * @return
     */
    private SysMessageEntity buildParam(AppointmentCleanOrderDTO appointmentCleanOrderDTO) {
        SysMessageEntity sysMessageEntity = new SysMessageEntity();
        SysMessageEntity.ContentExt contentExt = new SysMessageEntity.ContentExt();
        contentExt.setContent(appointmentCleanOrderDTO.getRemark());
        contentExt.setChainId(String.valueOf(appointmentCleanOrderDTO.getChainId()));
        contentExt.setUid(String.valueOf(appointmentCleanOrderDTO.getMemberId()));
        contentExt.setType(appointmentCleanOrderDTO.getState() + "_" + appointmentCleanOrderDTO.getAppointmentType());
        contentExt.setAccDate(DateUtil.formatDate(appointmentCleanOrderDTO.getCleanDate())+"_"+appointmentCleanOrderDTO.getTimeSlot());
        if (Objects.equals(appointmentCleanOrderDTO.getState(), AppointmentCleanStateEnum.APPOINTMENT.code())) {
            contentExt.setTitle(CleanTitleEnum.getInstance(appointmentCleanOrderDTO.getAppointmentType()).getDesc());
        } else if (Objects.equals(appointmentCleanOrderDTO.getState(), AppointmentCleanStateEnum.CANCEL.code())) {
            contentExt.setTitle(CleanTitleEnum.getInstance(appointmentCleanOrderDTO.getAppointmentType()).getDesc());
        }
        sysMessageEntity.setContent(JSONObject.toJSONString(contentExt));
        sysMessageEntity.setCreateTime(new Date());
        sysMessageEntity.setDelayedTime(new Date());
        sysMessageEntity.setUpdateTime(new Date());
        sysMessageEntity.setRoomNo(appointmentCleanOrderDTO.getRoomNo()+"_"+appointmentCleanOrderDTO.getId());
        sysMessageEntity.setHotelId(appointmentCleanOrderDTO.getChainId());
        sysMessageEntity.setMessageType(SysMessageTypeEnum.APPOINT_CLEAN.getCode());
        sysMessageEntity.setMessageSource(MessageSourceEnum.APPOINT_CLEAN.getValue());
        sysMessageEntity.setState(appointmentCleanOrderDTO.getState());
        sysMessageEntity.setRelateId(appointmentCleanOrderDTO.getMemberId());
        sysMessageEntity.setRelateType(RelatedTypeEnum.DEFAULT.getValue());

        return sysMessageEntity;
    }

    /**
     * @param userId
     * @param chainId
     * @param status
     * @param type
     */
    private void sendMessage(Integer userId, Integer chainId, Integer status, Integer type) {
        Map<String, String> notifyParam = Maps.newHashMap();
        String title = null;
        if (Objects.equals(status, AppointmentCleanStateEnum.APPOINTMENT.code())) {
            title = CleanTitleEnum.getInstance(type).getDesc();
        } else if (Objects.equals(status, AppointmentCleanStateEnum.CANCEL.code())) {
            title = CleanTitleEnum.CANCEL_CLEAN.getDesc();
        }


        notifyParam.put("title", Base64Util.encode(title));
        notifyParam.put("type", Base64Util.encode(String.valueOf(MsgTypeEnum.APPOINTMENT_CLEAN.getCode())));

        MebMainDTO mebInfo = mebService.getMebInfo(userId);
        if (Objects.nonNull(mebInfo)){
            //组装极光推送单播消息参数
            AllChannelPushParam allChannelPushParam =
                    pushJpushService.wrapperTaskCenterPushParam(chainId, userId);

            //组装全渠道推送参数
            JPushPersonalParam jPushPersonalParam =
                    pushJpushService.buildCommonJPushParam(Lists.newArrayList(String.valueOf(mebInfo.getModifyUserId())), notifyParam, title,
                            JPushPlatformEnum.ALL.getCode(), JPushTypeEnum.NOTIFY.getCode(),
                            JPushSceneTypeEnum.APPOINTMENT_CLEAN.getCode());
            allChannelPushParam.setJPushPersonalParam(jPushPersonalParam);
            pushJpushService.pushJpushMessage(allChannelPushParam);
        }


    }

    /**
     * @param ids
     */
    public void saveMessage(Set<Long> ids) {
        List<AppointmentCleanOrderDTO> query = appointmentCleanService.query(ids);
        Safes.of(query).stream().forEach(item -> {
            SysMessageEntity sysMessageEntity = buildParam(item);
            sysMessageDao.insertSelective(sysMessageEntity);
            CompletableFuture.runAsync(() -> {
                sendMessage(item.getMemberId(), item.getChainId(), item.getState(), item.getAppointmentType());
            });
        });
    }


}
