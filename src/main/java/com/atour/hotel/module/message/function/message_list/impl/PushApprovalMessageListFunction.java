package com.atour.hotel.module.message.function.message_list.impl;

import com.atour.dicts.db.center.sys_message.MessageSourceEnum;
import com.atour.hotel.module.approve.app.dto.PushApprovalPreviewDTO;
import com.atour.hotel.module.message.function.message_list.AbstractSysMessageListFunction;
import com.atour.hotel.module.message.param.QueryMessageListParam;
import com.atour.hotel.module.message.response.MessageListDTO;
import com.atour.hotel.module.message.wrapper.MessageWrapper;
import com.atour.hotel.persistent.center.entity.SysMessageEntity;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Component
public class PushApprovalMessageListFunction extends AbstractSysMessageListFunction {

    /**
     * 是否使用当前查询请求
     *
     * @param queryMessageListParam
     * @return
     */
    @Override
    public boolean match(QueryMessageListParam queryMessageListParam) {

        return MessageSourceEnum.MESSAGE_NOTIFY_OMS_APPROVAL == queryMessageListParam.getMessageSource();
    }

    /**
     * 转换数据
     *
     * @param data
     * @return
     */
    @Override
    @SneakyThrows
    protected MessageListDTO convertData(SysMessageEntity data, QueryMessageListParam queryMessageListParam) {
        PushApprovalPreviewDTO pushApprovalPreviewDTO =
            Safes.of(ObjectUtil.fromJson(data.getContent(), PushApprovalPreviewDTO.class),
                new PushApprovalPreviewDTO());
        String url = "/home/<USER>" + pushApprovalPreviewDTO.getRequestId();

        return MessageWrapper.toPushMessageListDTO(data, pushApprovalPreviewDTO, url);
    }

    /**
     * 是否需要将 chainId 作为查询参数
     *
     * @return
     */
    @Override
    protected boolean needChainId() {

        return false;
    }
}
