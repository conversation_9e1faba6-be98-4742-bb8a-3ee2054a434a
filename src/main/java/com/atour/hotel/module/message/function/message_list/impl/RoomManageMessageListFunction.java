package com.atour.hotel.module.message.function.message_list.impl;

import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.center.sys_message.MessageSourceEnum;
import com.atour.hotel.module.message.service.MessageConfigService;
import com.atour.hotel.module.message.function.message_list.AbstractSysMessageListFunction;
import com.atour.hotel.module.message.param.QueryMessageListParam;
import com.atour.hotel.module.message.response.MessageListDTO;
import com.atour.hotel.module.message.wrapper.MessageWrapper;
import com.atour.hotel.module.rbac.enums.UserChainJobEnum;
import com.atour.hotel.module.rbac.service.RbacService;
import com.atour.hotel.persistent.center.entity.SysMessageEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.utils.Safes;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Component
public class RoomManageMessageListFunction extends AbstractSysMessageListFunction {

    @Resource
    private RbacService rbacService;

    /**
     * 是否使用当前查询请求
     *
     * @param queryMessageListParam
     * @return
     */
    @Override
    public boolean match(QueryMessageListParam queryMessageListParam) {

        return queryMessageListParam.getMessageSource() == MessageSourceEnum.ROOM_MANAGE;
    }

    /**
     * Applies this function to the given argument.
     *
     * @param queryMessageListParam the function argument
     * @return the function result
     */
    @Override
    public Pair<List<MessageListDTO>, PageInfo> apply(QueryMessageListParam queryMessageListParam) {

        final UserPermissionDTO userInfo = queryMessageListParam.getUserInfo();
        final UserChainJobEnum userChainJobEnum = rbacService.queryUserChainJob(userInfo.getHrId());
        final Pair<List<SysMessageEntity>, PageInfo> pair = queryData(queryMessageListParam);

        final List<MessageListDTO> messageList = Safes.of(pair.getKey())
            .stream()
            .filter(Objects::nonNull)
            .map(sysMessageEntity ->  {
                final MessageListDTO messageListDTO = MessageWrapper.fromDKFMessage(sysMessageEntity);
                String url = StringUtils.EMPTY;
                if (userChainJobEnum == UserChainJobEnum.MANAGER) {
                    url = String.format("/rooms/checkRoom?roomNo=%s", messageListDTO.getRoomNo());
                } else if (userChainJobEnum == UserChainJobEnum.WAITER) {
                    url = String.format("/rooms/roomCleanDetail?roomNo=%s", messageListDTO.getRoomNo());
                }
                messageListDTO.setUrl(url);
                return messageListDTO;
            })
            .collect(Collectors.toList());

        return Pair.of(messageList, pair.getRight());
    }

    /**
     * 转换数据
     *
     * @param data
     * @return
     */
    @Override
    protected MessageListDTO convertData(SysMessageEntity data, QueryMessageListParam queryMessageListParam) {

        return null;
    }
}
