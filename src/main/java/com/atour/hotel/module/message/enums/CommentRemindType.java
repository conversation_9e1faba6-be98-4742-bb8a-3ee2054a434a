package com.atour.hotel.module.message.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;


/**
 * <AUTHOR> ren
 * @date 2021-04-27
 * @desc 评论消息提醒类型
 */
@Getter
public enum CommentRemindType {
    /**
     * 1-差评整改提醒
     */
    BAD_COMMENT_IMPROVE(1, "差评整改提醒", false),

    /**
     * 2-差评超时提醒
     */
    BAD_COMMENT_TIMEOUT(2, "差评超时提醒", false);

    private final int code;

    private final String title;

    /**
     * 是否有详情页
     */
    private final boolean hasDetail;

    private static final Map<Integer, CommentRemindType> map;

    static {
        final CommentRemindType[] values = CommentRemindType.values();
        map = Maps.newHashMapWithExpectedSize(values.length);
        for (CommentRemindType value : values) {
            map.put(value.code, value);
        }
    }

    CommentRemindType(int code, String title, boolean hasDetail) {

        this.code = code;
        this.title = title;
        this.hasDetail = hasDetail;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value value
     * @return CommentRemindType
     */
    public static CommentRemindType getInstance(int value) {
        return map.get(value);
    }
}
