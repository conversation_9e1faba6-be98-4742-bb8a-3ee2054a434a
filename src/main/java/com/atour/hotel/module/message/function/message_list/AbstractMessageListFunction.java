package com.atour.hotel.module.message.function.message_list;

import com.atour.api.bean.PageInfo;
import com.atour.hotel.module.message.param.QueryMessageListParam;
import com.atour.hotel.module.message.response.MessageListDTO;
import com.atour.utils.Safes;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
public abstract class AbstractMessageListFunction<T> implements Function<QueryMessageListParam, Pair<List<MessageListDTO>, PageInfo>> {

    /**
     * 是否使用当前查询请求
     *
     * @param queryMessageListParam
     * @return
     */
    public abstract boolean match(QueryMessageListParam queryMessageListParam);

    /**
     * Applies this function to the given argument.
     *
     * @param queryMessageListParam the function argument
     * @return the function result
     */
    @Override
    public Pair<List<MessageListDTO>, PageInfo> apply(QueryMessageListParam queryMessageListParam) {

        final Pair<List<T>, PageInfo> pair = queryData(queryMessageListParam);

        final List<MessageListDTO> messageList = Safes.of(pair.getKey())
            .stream()
            .map(t -> convertData(t, queryMessageListParam))
            .collect(Collectors.toList());

        return Pair.of(messageList, pair.getRight());
    }

    /**
     * 查询数据
     *
     * @param queryMessageListParam
     * @return
     */
    protected abstract Pair<List<T>, PageInfo> queryData(QueryMessageListParam queryMessageListParam);

    /**
     * 转换数据
     *
     * @param data
     * @return
     */
    protected abstract MessageListDTO convertData(T data, QueryMessageListParam queryMessageListParam);

}
