package com.atour.hotel.module.message.enums;

import com.atour.order.api.enums.AppointmentCleanTypeEnum;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @author：子烁
 * @date：2021/7/20 下午8:31
 */
@Getter
public enum  CleanTitleEnum {

    CLEAN(1, "需要打扫"),
    NOTHING(2, "不需要打扫"),
    CANCEL_CLEAN(3, "取消打扫");

    private Integer value;
    private String desc;

    CleanTitleEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value 值
     * @return
     */
    public static CleanTitleEnum getInstance(Integer value) {
        for (CleanTitleEnum obj : CleanTitleEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }

}
