package com.atour.hotel.module.log.service;

import com.alibaba.fastjson.JSON;
import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.atour_crs.LogOperationTypeEnum;
import com.atour.hotel.common.util.BeanDiffUtils;
import com.atour.hotel.framework.response.CommonPageDTO;
import com.atour.hotel.module.log.dto.LogListDTO;
import com.atour.hotel.module.log.enums.OmsLogOperateTypeEnum;
import com.atour.hotel.module.log.param.LogListParam;
import com.atour.hotel.persistent.franchise.condition.OmsOperateLogListCondition;
import com.atour.hotel.persistent.franchise.dao.OmsOperateLogDAO;
import com.atour.hotel.persistent.franchise.entity.OmsOperateLogEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.utils.Safes;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/16
 */
@Service
public class OmsLogService {

    @Resource
    private ExecutorService omsLogExecutor;

    @Resource
    private OmsOperateLogDAO omsOperateLogDAO;
    /**
     * 线程池
     */
    @Autowired
    private ExecutorService diffLogExecutor;

    /**
     * 日志列表
     *
     * @param param
     * @return
     */
    public CommonPageDTO<LogListDTO> list(LogListParam param) {

        final PageInfo pageInfo = new PageInfo(param.getPageNo(), param.getPageSize());
        final CommonPageDTO<LogListDTO> result = new CommonPageDTO<>();
        result.setPage(pageInfo);
        result.setData(Collections.emptyList());

        final OmsOperateLogListCondition omsOperateLogListCondition = new OmsOperateLogListCondition();
        omsOperateLogListCondition.setBizId(param.getBizId());
        omsOperateLogListCondition.setType(param.getType());
        omsOperateLogListCondition.setOffset((param.getPageNo() - 1) * pageInfo.getPageSize());
        omsOperateLogListCondition.setLimit(pageInfo.getPageSize());

        final int count = omsOperateLogDAO.countByCondition(omsOperateLogListCondition);
        pageInfo.compute(count);

        if (count == 0) {
            return result;
        }

        final List<OmsOperateLogEntity> omsOperateLogEntities =
            omsOperateLogDAO.selectByCondition(omsOperateLogListCondition);
        final List<LogListDTO> data = Safes.of(omsOperateLogEntities)
            .stream()
            .map(entity -> {
                final LogListDTO logListDTO = new LogListDTO();
                logListDTO.setId(entity.getId());
                logListDTO.setContent(entity.getContent());
                logListDTO.setOperateTime(entity.getCreateTime());
                logListDTO.setOperateUserName(entity.getOperateUsername());
                return logListDTO;
            })
            .collect(Collectors.toList());

        result.setData(data);

        return result;
    }

    /**
     * 添加日志
     */
    public void addLog(Supplier<OmsOperateLogEntity> supplier) {
        omsLogExecutor.execute(RunnableWrapper.of(() -> {
            final OmsOperateLogEntity omsOperateLogEntity = supplier.get();
            if (Objects.isNull(omsOperateLogEntity)) {
                return;
            }
            omsOperateLogDAO.insert(omsOperateLogEntity);
        }));
    }

    /**
     * 记录操作日志
     *
     * @param operateType       com.atour.hotel.module.log.enums.OmsLogOperateTypeEnum
     * @param logType
     * @param entityId
     * @param oldJson
     * @param newJson
     * @param userPermissionDTO
     * @param diffFlag          日志内容是否要记录
     * @param keyWord
     */
    public void log(Integer operateType, Integer logType, Integer entityId, Object oldJson, Object newJson,
        UserPermissionDTO userPermissionDTO, boolean diffFlag, String keyWord) {
        diffLogExecutor.execute(RunnableWrapper.of(() -> {
            if (Objects.isNull(oldJson)) {
                // 记录新增操作日志
                log(logType, entityId, newJson, userPermissionDTO, diffFlag, keyWord);
            } else {
                // 记录DIFF更新操作日志
                updateLog(operateType, logType, entityId, oldJson, newJson, userPermissionDTO, diffFlag, keyWord);
            }
        }));
    }

    /**
     * 记录日志-新增
     *
     * @param logType
     * @param entityId
     * @param newJson
     * @param userPermissionDTO
     * @return
     */
    private void log(Integer logType, Integer entityId, Object newJson, UserPermissionDTO userPermissionDTO,
        boolean diffFlag, String keyWord) {
        Supplier<OmsOperateLogEntity> supplier = () -> {
            OmsOperateLogEntity omsOperateLogEntity = new OmsOperateLogEntity();
            omsOperateLogEntity.setBizType(logType);
            omsOperateLogEntity.setBizId(entityId);
            omsOperateLogEntity.setOperateUsername(userPermissionDTO.getUserName());
            omsOperateLogEntity.setOperateUserId(userPermissionDTO.getUserId());
            omsOperateLogEntity.setOperationType(OmsLogOperateTypeEnum.ADD.getCode());

            StringBuilder builder = new StringBuilder();
            builder.append(LogOperationTypeEnum.ADD.getDescription());
            builder.append(" 内容 ：");
            if (diffFlag) {
                if (newJson instanceof String) {
                    builder.append(newJson);
                } else {
                    builder.append(JSON.toJSONString(newJson));
                }
            } else {
                builder.append(keyWord);
            }
            omsOperateLogEntity.setContent(builder.toString());
            return omsOperateLogEntity;
        };
        addLog(supplier);
    }

    /**
     * 记录日志-更新
     *
     * @param logType
     * @param entityId
     * @param oldJson
     * @param newJson
     * @param userPermissionDTO
     * @return
     */
    private void updateLog(Integer operateType, Integer logType, Integer entityId, Object oldJson, Object newJson,
        UserPermissionDTO userPermissionDTO, boolean diffFlag, String keyWord) {
        Supplier<OmsOperateLogEntity> supplier = () -> {
            OmsOperateLogEntity omsOperateLogEntity = new OmsOperateLogEntity();
            omsOperateLogEntity.setBizType(logType);
            omsOperateLogEntity.setBizId(entityId);
            omsOperateLogEntity.setOperateUsername(userPermissionDTO.getUserName());
            omsOperateLogEntity.setOperateUserId(userPermissionDTO.getUserId());
            omsOperateLogEntity.setOperationType(OmsLogOperateTypeEnum.EDIT.getCode());

            StringBuilder builder = new StringBuilder();
            builder.append(OmsLogOperateTypeEnum.codeOf(operateType)
                .getDescription());
            builder.append(" 内容 ：");
            if (diffFlag) {
                builder.append(BeanDiffUtils.beanDiff(oldJson, newJson, new StringBuilder()));
            } else {
                builder.append(keyWord);
            }
            omsOperateLogEntity.setContent(builder.toString());
            return omsOperateLogEntity;
        };
        addLog(supplier);
    }

}
