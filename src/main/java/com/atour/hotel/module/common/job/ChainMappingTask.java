package com.atour.hotel.module.common.job;

import com.atour.hotel.module.common.service.ChainMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 门店、区域、城市 关系映射
 * <AUTHOR>
 * @date 2021/3/19
 */
@Slf4j
@Component
@JobHandler(value = "chainMappingTask")
public class ChainMappingTask extends IJobHandler {

    @Resource
    private ChainMappingService chainMappingService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        chainMappingService.synChainMappingInfo();
        return ReturnT.SUCCESS;
    }
}
