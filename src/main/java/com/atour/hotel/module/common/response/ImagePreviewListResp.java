package com.atour.hotel.module.common.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImagePreviewListResp {
	/**
	 * 有效时间戳
	 * 超出此时间，则URL过期
	 */
	private Long expire;

	/**
	 * key
	 */
	private String ossKey;

	/**
	 * 预览地址
	 * 注意:(私有空间的预览地址会有时效性问题)
	 */
	private String showUrl;
}
