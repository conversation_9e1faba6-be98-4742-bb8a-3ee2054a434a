package com.atour.hotel.module.common.request;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.TimeUnit;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FilePreviewReq {
	@NotBlank(message = "文件唯一标示ossKey不能为空")
	private String ossKey;

	/**
	 * 过期时间
	 * */
	private Long expire = TimeUnit.DAYS.toSeconds(1);
}
