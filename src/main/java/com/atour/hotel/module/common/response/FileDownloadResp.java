package com.atour.hotel.module.common.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileDownloadResp {
	/**
	 * 大小(单位:字节)
	 */
	private long size;

	/**
	 * 文件类型
	 */
	private String mimeType;

	/**
	 * 文件名称
	 */
	private String mimeName;

	/**
	 * 下载的文件信息(data:contentType;base64,XXX逗号分隔)
	 * 例: data:image/jpeg;base64,XXX
	 */
	private String fileBase64;
}
