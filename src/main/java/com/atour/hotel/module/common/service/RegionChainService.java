package com.atour.hotel.module.common.service;

import com.alibaba.excel.util.CollectionUtils;
import com.atour.api.bean.ApiResult;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.dto.ChainListByAreaDTO;
import com.atour.rbac.api.param.GetRegionBChainIdRequest;
import com.atour.rbac.api.response.AreaChainInfoDTO;
import com.atour.rbac.api.response.RegionChainDto;
import com.atour.rbac.api.response.RegionCityChainDto;
import com.atour.utils.Safes;
import com.atour.utils.http.HttpClientUtil;
import com.atour.utils.json.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/22
 */
@Log4j2
@Service
public class RegionChainService {

    /**
     * rbac 获取酒店区域列表
     */
    @Value("${rbac-url.getRegionByChainIdUrl}")
    private String getRegionBChainIdUrl;

    /**
     * rbac 获取酒店区域列表
     * 需将二级城区过滤并将二级城区下属酒店关联到一级城区下
     */
    @Value("${rbac-url.getRegionByChainIdNewUrl}")
    private String getRegionByChainIdNewUrl;

    /**
     * rbac 根据区域获得酒店列表
     */
    @Value("${rbac-url.getChainListByDeptIdUrl}")
    private String getChainListByDeptIdUrl;

    /**
     * rbac 根据区域获得酒店列表
     */
    @Value("${rbac-url.getChainListByDeptIdNewUrl}")
    private String getChainListByDeptIdNewUrl;
    /**
     * rbac 根据chainIds获取关系树
     */
    @Value("${rbac-url.regionCityChainTreeByChainIds}")
    private String regionCityChainTreeByChainIds;

    /**
     * 调用Rbac获取门店关联的系统
     * <p>
     * 需将二级城区过滤并将二级城区下属酒店关联到一级城区下
     */
    public List<RegionChainDto> getRegionByChainId(GetRegionBChainIdRequest request) {
        List<Integer> chainId = request.getChainId();
        chainId = chainId.stream().distinct().collect(Collectors.toList());
        request.setChainId(chainId);
        log.info("--getRegionByChainId 开始调用Rbac获取酒店区域列表 request={}", request.toString());
        String rbacResult = HttpClientUtil.sendPostJson(getRegionByChainIdNewUrl, JsonUtils.toJson(request));
        log.debug("--getRegionByChainId 调用Rbac获取酒店区域列表 rbacResult={}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("getRegionByChainId调用Rbac获取酒店区域列表,响应信息为空");
            throw new com.atour.hotel.framework.exception.BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                    ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("getRegionByChainId调用RBAC获取酒店区域列表,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new com.atour.hotel.framework.exception.BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.error("getRegionByChainId调用RBAC获取酒店区域列表,result为空");
            throw new BusinessException(ResponseCodeEnum.RESULT_IS_EMPTY.getMessage(),
                    ResponseCodeEnum.RESULT_IS_EMPTY.getCode());
        }
        return JsonUtils.parseList(JsonUtils.toJson(response.getResult()), RegionChainDto.class);
    }

    /**
     * 调用Rbac获取区域城市门店结构树列表
     * <p>
     */
    public List<RegionCityChainDto> getRegionTreeByChainId(List<Integer> chainIdList) {
        log.info("--regionCityChainTreeByChainIds 开始调用Rbac获取区域城市门店结构树列表 request={}", chainIdList);
        if (CollectionUtils.isEmpty(chainIdList)) {
            return Collections.emptyList();
        }
        String chainIds = chainIdList
                .stream().map(String::valueOf).collect(Collectors.joining(","));
        String uriString = UriComponentsBuilder.fromUriString(regionCityChainTreeByChainIds)
                .queryParam("chainIds", chainIds)
                .queryParam("isAll", "true")
                .build()
                .encode()
                .toUriString();
        String rbacResult = HttpClientUtil.sendGet(uriString);
        log.debug("--regionCityChainTreeByChainIds 调用Rbac获取区域城市门店结构树列表 rbacResult={}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("regionCityChainTreeByChainIds,响应信息为空");
            throw new com.atour.hotel.framework.exception.BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                    ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("regionCityChainTreeByChainIds 调用Rbac获取区域城市门店结构树列表,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new com.atour.hotel.framework.exception.BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.error("regionCityChainTreeByChainIds,result为空");
            throw new BusinessException(ResponseCodeEnum.RESULT_IS_EMPTY.getMessage(),
                    ResponseCodeEnum.RESULT_IS_EMPTY.getCode());
        }
        return JsonUtils.parseList(JsonUtils.toJson(response.getResult()), RegionCityChainDto.class);
    }


    /**
     * 调用Rbac根据区域获得酒店列表
     *
     * @param deptId 区域id
     */
    public List<ChainListByAreaDTO> getChainListByDeptId(String deptId) {
        log.info("--getRegionBChainId 开始调用Rbac根据区域获得酒店列表 deptId={}", deptId);
        if (StringUtils.isBlank(deptId)) {
            return Collections.emptyList();
        }
        String rbacResult = HttpClientUtil.sendGet(getChainListByDeptIdNewUrl + "?deptId=" + deptId);
        log.debug("--getRegionBChainId 调用Rbac根据区域获得酒店列表 rbacResult={}", rbacResult);
        ApiResult response = JsonUtils.parseObject(rbacResult, ApiResult.class);
        if (Objects.isNull(response)) {
            log.error("调用Rbac根据区域获得酒店列表,响应信息为空");
            throw new BusinessException(ResponseCodeEnum.USER_TIMEOUT.getMessage(),
                    ResponseCodeEnum.USER_TIMEOUT.getCode());
        }
        if (response.getCode() != 0) {
            log.error("调用RBAC根据区域获得酒店列表,出现错误, code:{}, msg:{}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getMessage(), response.getCode());
        }

        if (Objects.isNull(response.getResult())) {
            log.error("调用RBAC根据区域获得酒店列表,result为空");
            return Collections.emptyList();
        }
        List<AreaChainInfoDTO> dataList =
                Safes.of(JsonUtils.parseList(JsonUtils.toJson(response.getResult()), AreaChainInfoDTO.class));
        List<ChainListByAreaDTO> resultList = Lists.newArrayListWithExpectedSize(dataList.size());
        dataList.forEach(f -> {
            resultList.add(ChainListByAreaDTO.builder()
                    .chainId(f.getChainId())
                    .chainName(f.getChainName())
                    .areaId(f.getParentDeptId())
                    .areaName(f.getParentDeptName())
                    .build());
        });
        return resultList;
    }

    /**
     * 根据区域获得酒店ids
     *
     * @param deptId 区域id
     */
    public List<Integer> getChainIdsByDeptId(String deptId) {
        List<ChainListByAreaDTO> list = this.getChainListByDeptId(deptId);
        return Safes.of(list)
                .stream()
                .map(ChainListByAreaDTO::getChainId)
                .collect(Collectors.toList());

    }


    /**
     * 对酒店区分处理
     */
    public Map<String, List<Integer>> getMapChainIdsByDeptId(List<Integer> chainList) {
        if (CollectionUtils.isEmpty(chainList)) {
            return Maps.newHashMap();
        }
        GetRegionBChainIdRequest request = new GetRegionBChainIdRequest();
        request.setChainId(chainList);
        List<RegionChainDto> list = this.getRegionByChainId(request);
        Map<String, List<Integer>> collect = Safes.of(list).stream()
                .collect(Collectors.toMap(RegionChainDto::getDeptId, item ->
                        item.getChains().stream().map(chain -> Integer.parseInt(chain.getChainId()))
                                .collect(Collectors.toList())));
        return collect;
    }
}
