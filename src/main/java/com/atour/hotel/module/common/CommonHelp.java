package com.atour.hotel.module.common;

import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 帮助类: 工具
 *
 * <AUTHOR>
 * @date 2019-07-25
 */
public class CommonHelp {

    /**
     * 星期
     */
    private final static String[] WEEKS = new String[] {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};

    /**
     * 日期格式 yyyy-MM-dd
     */
    private static final String YYYY_MM_DD = "yyyy-MM-dd";

    public final static String IMAGE_BASE_PATH = "QN/";

    private static final int TODAY = 0;

    /**
     * 常量: 字符串长度
     */
    private static final int STRING_LENGTH = 10;

    /**
     * 根据传入日期计算当月在传入日期前的所有天数
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 天数集合
     */
    public static List<String> rangeByDate(LocalDate start, LocalDate end) {
        // 用起始时间作为流的源头，按照每次加一天的方式创建一个无限流
        return Stream.iterate(start, localDate -> localDate.plusDays(1))
            // 截断无限流，长度为起始时间和结束时间的差+1个
            .limit(ChronoUnit.DAYS.between(start, end) + 1)
            // 由于最后要的是字符串，所以map转换一下
            .map(LocalDate::toString)
            // 把流收集为List
            .collect(Collectors.toList());
    }

    /**
     * 获取最大日期
     *
     * @param date 日期集合
     * @return 最大日期
     */
    public static LocalDate maxDate(LocalDate... date) {
        return Arrays.stream(date)
            .max(LocalDate::compareTo)
            .orElse(LocalDate.now());
    }

    /**
     * LocalDate转String
     *
     * @param date 日期
     * @return 字符串日期
     */
    public static String localDate2Str(LocalDate date) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(fmt);
    }



    /**
     * 获取指定日期前几天或后几天
     *
     * @param date 日期
     * @param days 时间格式(相差的天数,可以为正负数。 负数:指定日期前,正数:指定日期后)
     * @return 最大日期
     */
    public static LocalDate getDateByDays(LocalDate date, int days) {
        return date.plusDays(days);
    }

    /**
     * 获取规定时间 几天前或者几天后的时间
     *
     * @param date 字符串时间
     * @param day  时间格式(相差的天数,可以为正负数。 负数:指定日期前,正数:指定日期后)
     * @return Date格式的时间
     */
    public static Date getDateByDays(Date date, int day) {

        if (Objects.isNull(date)) {
            return null;
        }

        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    /**
     * 获取最小日期
     *
     * @param date 日期集合
     * @return 最大日期
     */
    public static LocalDate minDate(LocalDate... date) {
        return Arrays.stream(date)
            .min(LocalDate::compareTo)
            .orElse(LocalDate.now());
    }

    /**
     * 获取最小日期
     *
     * @param date 日期集合
     * @return 最大日期
     */
    public static LocalDate date2LocalDate(Date date) {

        if (Objects.isNull(date)) {
            return null;
        }

        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();

        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        return instant.atZone(zoneId)
            .toLocalDate();
    }

    /**
     * LocalDate转Date
     *
     * @param localDate 时间
     * @return Date格式时间
     */
    public static Date localDate2Date(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            localDate = LocalDate.now();
        }
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 获得某个日期下的 月份的天数
     *
     * @param date 时间字符串
     * @return 该月份一共多少天
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 严格格式化一个字符串形式[yyyy-MM-dd]的日期为Date
     *
     * @param str 日期字符串
     * @return 格式化后的日期
     * @throws ParseException 转化异常
     */
    public static Date parseDateStrictly(String str) throws ParseException {
        return DateUtils.parseDateStrictly(str, YYYY_MM_DD);
    }

    /**
     * 获得该日期下的星期
     *
     * @param dateStr 日期字符串(一个字符串形式[yyyy-MM-dd]的日期)
     * @return 星期几
     */
    public static String strDateToWeek(String dateStr) throws ParseException {
        Date date = parseDateStrictly(dateStr);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekIndex < 0) {
            weekIndex = 0;
        }
        return WEEKS[weekIndex];
    }

    /**
     * 列出给定日期区间的整周日期(周一~周日)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 整周日期范围
     */
    public static List<Range<LocalDate>> listBetweenWeekDates(LocalDate beginDate, LocalDate endDate) {
        if (Objects.isNull(beginDate) || Objects.isNull(endDate) || beginDate.isAfter(endDate)) {
            return Collections.emptyList();
        }
        if (!DayOfWeek.MONDAY.equals(DayOfWeek.of(beginDate.get(ChronoField.DAY_OF_WEEK))) || !DayOfWeek.SUNDAY.equals(
            DayOfWeek.of(endDate.get(ChronoField.DAY_OF_WEEK)))) {
            throw new BusinessException("日期区间必须为1个或多个整周");
        }

        // 按照起始日期，每次递增一天的方式生成一个Stream
        Set<LocalDate> monDaySet = Stream.iterate(beginDate, localDate -> localDate.plusDays(1))
            // 按照要求的时间间隔中的实际间隔天数截断Stream
            .limit(ChronoUnit.DAYS.between(beginDate, endDate) + 1)
            // 过滤为周一的日期
            .filter(localDate -> DayOfWeek.MONDAY.equals(DayOfWeek.of(localDate.get(ChronoField.DAY_OF_WEEK))))
            .collect(Collectors.toSet());

        List<Range<LocalDate>> ranges = Lists.newArrayList();
        monDaySet.forEach(monDay -> ranges.add(Range.closed(monDay, monDay.plusDays(6))));

        return ranges;
    }

    /**
     * 返回二个日期之间所有月份(yyyy-MM)
     */
    public static List<YearMonth> getYearMonthBetweenDate(LocalDate beginDate, LocalDate endDate) {
        if (Objects.isNull(beginDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }
        if (beginDate.isAfter(endDate)) {
            return Collections.emptyList();
        }
        YearMonth beginYearMonth = YearMonth.from(beginDate);
        YearMonth endYearMonth = YearMonth.from(endDate);
        long numOfMonthsBetween = ChronoUnit.MONTHS.between(beginYearMonth, endYearMonth);
        return IntStream.iterate(0, i -> i + 1).limit(numOfMonthsBetween + 1).mapToObj(beginYearMonth::plusMonths)
            .collect(Collectors.toList());
    }

    /**
     * 根据传入的月份获取当月第一天和最后一天
     *
     * @param yearMonth 月份信息
     * @return 当月月初和月末信息
     */
    public static Range<LocalDate> buildDateRangeByMonth(YearMonth yearMonth) {

        LocalDate beginMonth = LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), 1);

        LocalDate endMonth = beginMonth.with(TemporalAdjusters.lastDayOfMonth());
        return Range.closed(beginMonth, endMonth);
    }

    /**
     * 构建图片路径
     * 此方向做了更新为了兼容新老api的图片地址
     * 新api使用七牛服务
     * 老api使用本地服务
     * @param img
     * @return
     */
    public static String buildImg(String img) {
        if (StringUtils.isNotEmpty(img)) {
            if (img.contains(IMAGE_BASE_PATH)) {
                // 七牛服务
                return buildImg(FileConfig.imageQiniuServer, img);
            } else {
                return buildImg(FileConfig.imageServer, img);
            }
        } else {
            return "";
        }
    }

    /**
     * 构建图片路径
     *
     * @param prefix
     * @param img
     * @return
     */
    public static String buildImg(String prefix, String img) {
        if (StringUtils.isBlank(img)) {
            return "";
        }
        if (img.startsWith("http") || img.startsWith("https")) {
            return img;
        }
        return prefix + img;
    }

    /**
     * 计算相差天数
     * <p>时间格式 : yyyy-MM-dd || yyyy-MM-dd hh:mm:ss</p>
     *
     * @param oDate 开始时间
     * @param fDate 结束时间
     * @return 相差天数(oTime - fTime)
     */
    public static int daysBetweenByDate(Date oDate, Date fDate) {

        String startDate = DateUtil.formatDate(oDate);

        String endDate = DateUtil.formatDate(fDate);

        if (startDate.length() < STRING_LENGTH || endDate.length() < STRING_LENGTH) {
            return BigDecimal.ZERO.intValue();
        }

        LocalDate startLocalDate = LocalDate.parse(startDate.substring(0, 10));
        LocalDate endLocalDate = LocalDate.parse(endDate.substring(0, 10));
        return BigDecimal.valueOf(startLocalDate.toEpochDay() - endLocalDate.toEpochDay()).intValue();
    }

    /**
     * 全部
     */
    public static final int ALL = -99;

}
