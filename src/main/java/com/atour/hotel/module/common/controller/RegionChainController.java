package com.atour.hotel.module.common.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.common.service.CommonChainService;
import com.atour.hotel.module.common.service.RegionChainService;
import com.atour.rbac.api.param.GetRegionBChainIdRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2019/7/25
 */
@Controller
@RequestMapping("api/web/region/chain")
public class RegionChainController {
    @Resource
    private RegionChainService regionChainService;
    @Autowired
    private CommonChainService commonChainService;

    /**
     * 根据酒店获得区域
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getRegionBChainId", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public ApiResult getRegionBChainId(@Valid @RequestBody GetRegionBChainIdRequest request) {
        return ApiResult.success(regionChainService.getRegionByChainId(request));
    }

    /**
     * 获取当前用户所有酒店
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getCurrentUserChains", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public ApiResult getRegionBChainId() {
        return ApiResult.success(commonChainService.getCurrentUserChains());
    }

}
