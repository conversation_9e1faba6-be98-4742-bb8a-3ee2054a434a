package com.atour.hotel.module.safety.enums;

import com.atour.hotel.framework.mq.enums.OrderSuccessEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @author：子烁
 * @date：2021/9/23 下午1:00
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum  PlanDetailStatusEnum {

    WAIT_FINISHED(0, "待完成"),

    HAVE_FINISHED(1, "已完成"),

    EXPIRED(2, "已过期");

    private int code;
    private String msg;


    public static PlanDetailStatusEnum getInstance(int code){
        PlanDetailStatusEnum[] values = PlanDetailStatusEnum.values();
        for (PlanDetailStatusEnum value : values) {
            if (value.getCode() == code){
                return value;
            }
        }
        return null;
    }
}
