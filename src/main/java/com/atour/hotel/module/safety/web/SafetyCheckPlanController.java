package com.atour.hotel.module.safety.web;

import com.atour.api.bean.ApiPageResult;
import com.atour.api.bean.ApiResult;
import com.atour.api.bean.PageInfo;
import com.atour.hotel.module.safety.request.OmsPlanListRequest;
import com.atour.hotel.module.safety.request.PlanAddRequest;
import com.atour.hotel.module.safety.request.PlanModifyRequest;
import com.atour.hotel.module.safety.request.PlanUpdateRequest;
import com.atour.hotel.module.safety.response.BaseConfigGetResponse;
import com.atour.hotel.module.safety.response.OmsPlanListResponse;
import com.atour.hotel.module.safety.service.SafetyPlanService;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author：子烁
 * @date：2021/9/24 下午5:32
 */
@Api(value = "oms安全巡检")
@RestController
@RequestMapping(value = "api/web/safety/plan", produces = {"application/json;charset=UTF-8"})
public class SafetyCheckPlanController {

    @Resource
    private SafetyPlanService safetyPlanService;

    /**
     * 巡检计划管理
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public ApiPageResult<List<OmsPlanListResponse>> planList(@RequestBody @Valid OmsPlanListRequest request) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNo(request.getPageNo());
        pageInfo.setPageSize(request.getPageSize());
        ApiPageResult<List<OmsPlanListResponse>> result = new ApiPageResult<>();
        Pair<List<OmsPlanListResponse>, PageInfo> list = safetyPlanService.omsPlanList(request, pageInfo);
        result.setPage(list.getRight());
        result.setResult(list.getLeft());
        result.setCode(ApiResult.DEFAULT_SUCCEED_CODE);
        return result;
    }

    /**
     * 查看 配置规则
     *
     * @param planId
     * @return
     */
    @GetMapping("/base/config/get")
    public ApiResult<BaseConfigGetResponse> baseConfigGet(@RequestParam(value = "planId") @Valid @NotNull(message = "planId不能为空") Long planId) {

        return ApiResult.success(safetyPlanService.baseConfigGet(planId));
    }


    /**
     * 更新状态
     *
     * @param request
     * @return
     */
    @PostMapping("/update")
    public ApiResult<Void> update(@RequestBody @Valid PlanUpdateRequest request) {
        safetyPlanService.update(request);
        return ApiResult.success(null);
    }


    /**
     * 修订
     *
     * @param request
     * @return
     */
    @PostMapping("/modify")
    public ApiResult<Void> modify(@RequestBody @Valid PlanModifyRequest request) {
        safetyPlanService.modify(request);
        return ApiResult.success(null);
    }


    /**
     * 新增计划
     *
     * @param request
     * @return
     */
    @PostMapping("/add")
    public ApiResult<Void> add(@RequestBody @Valid PlanAddRequest request) {
        safetyPlanService.add(request);
        return ApiResult.success(null);
    }


}
