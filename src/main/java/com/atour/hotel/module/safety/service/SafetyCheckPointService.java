package com.atour.hotel.module.safety.service;

import com.atour.chain.api.chain.dto.ChainDTO;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.safety.request.PointAddRequest;
import com.atour.hotel.module.safety.request.PointUpdateRequest;
import com.atour.hotel.module.safety.response.PointListResponse;
import com.atour.hotel.module.safety.wrapper.SafetyCheckPointWrapper;
import com.atour.hotel.persistent.safety.dao.SafetyCheckPlanExtDao;
import com.atour.hotel.persistent.safety.dao.SafetyCheckPointDao;
import com.atour.hotel.persistent.safety.entity.SafetyCheckPlanDetailEntity;
import com.atour.hotel.persistent.safety.entity.SafetyCheckPlanExtEntity;
import com.atour.hotel.persistent.safety.entity.SafetyCheckPointEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author：子烁
 * @date：2021/9/24 上午10:44
 */
@Service
@Slf4j
public class SafetyCheckPointService {

    private final static Integer CHECK_POINT_MAX_COUNT = 200;
    @Resource
    private SafetyCheckPointDao safetyCheckPointDao;
    @Resource
    private ChainService chainService;
    @Resource
    private SafetyCheckPlanExtDao safetyCheckPlanExtDao;
    @Resource
    private SafetyCheckPlanDetailService safetyCheckPlanDetailService;

    /**
     * 列表
     *
     * @param chainId
     * @return
     */
    public List<PointListResponse> pointList(Integer chainId,Boolean status) {
        List<SafetyCheckPointEntity> safetyCheckPointEntities = safetyCheckPointDao.queryListByChainId(chainId,status);
        return SafetyCheckPointWrapper.pointListWrapper(safetyCheckPointEntities);
    }

    /**
     * 新增
     * @param request
     */

    public void pointAdd(PointAddRequest request) {
        ChainDTO chainDTO = chainService.getChainByChainId(request.getChainId());
        if (Objects.isNull(chainDTO)) {
            throw new BusinessException("该酒店不存在");
        }

        Integer dataCount = safetyCheckPointDao.getCountByChainId(request.getChainId());
        if (dataCount > CHECK_POINT_MAX_COUNT) {
            log.error("数据库条数{}", dataCount);
            throw new BusinessException("已超过最大限制200");
        }
        SafetyCheckPointEntity safetyCheckPointEntity = new SafetyCheckPointEntity();
        safetyCheckPointEntity.setChainId(chainDTO.getChainId());
        safetyCheckPointEntity.setCheckLocation(request.getCheckLocation());
        safetyCheckPointEntity.setCheckName(request.getCheckName());
        safetyCheckPointEntity.setSortRule(request.getSortRule());
        safetyCheckPointDao.insertSelective(safetyCheckPointEntity);


    }

    /**
     * 更新
     *
     * @param request
     */
    @Transactional(transactionManager = "omsTransactionManager", rollbackFor = Exception.class)
    public void pointUpdate(PointUpdateRequest request) {
        SafetyCheckPointEntity safetyCheckPointEntity = safetyCheckPointDao.selectByPrimaryKey(request.getId());
        if (Objects.isNull(safetyCheckPointEntity)) {
            throw new BusinessException("数据不存在");
        }

        if (request.getStatus() != null || request.getSortRule() != null) {
            SafetyCheckPointEntity updateEntity = new SafetyCheckPointEntity();
            updateEntity.setSortRule(request.getSortRule());
            updateEntity.setId(request.getId());
            updateEntity.setStatus(request.getStatus());
            safetyCheckPointDao.updateByPrimaryKeySelective(updateEntity);
        }

        if (Boolean.FALSE.equals(request.getStatus()) ) {
            List<SafetyCheckPlanExtEntity> toDeletePlanExtList = safetyCheckPlanExtDao.selectByChainIdAndPointId(safetyCheckPointEntity.getChainId(), safetyCheckPointEntity.getId());
            if (CollectionUtils.isNotEmpty(toDeletePlanExtList)) {
                List<Long> toDeletePlanExtIds = toDeletePlanExtList.stream().map(SafetyCheckPlanExtEntity::getId).collect(Collectors.toList());
                safetyCheckPlanExtDao.batchRemove(toDeletePlanExtIds);
                toDeletePlanExtList.forEach(planExt->{
                    List<SafetyCheckPlanDetailEntity> toDeletePlanDetailList = safetyCheckPlanDetailService.selectWaitFinishedDetailByChainIdAndPlanIdAndPointId(safetyCheckPointEntity.getChainId(), planExt.getPlanId(), safetyCheckPointEntity.getId());
                    safetyCheckPlanDetailService.batchRemove(toDeletePlanDetailList);
                });
            }
        }
    }
}
