package com.atour.hotel.module.safety.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author：子烁
 * @date：2021/9/23 下午4:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class CheckPointRequest {
    @ApiModelProperty(value = "计划ID")
    @NotNull(message = "计划ID不能为空")
    private Long planId;


    @ApiModelProperty(value = "操作时间")
    private String operateTime;

    @ApiModelProperty(value = "计划详情ID")
    @NotNull(message = "计划详情ID")
    private Long detailId;

    @ApiModelProperty(value = "计划详情ID")
    @NotEmpty(message = "ossKey不能为空")
    private List<String> imageOssKeys;
}
