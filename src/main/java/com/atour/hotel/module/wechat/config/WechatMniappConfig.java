package com.atour.hotel.module.wechat.config;

import cn.binarywang.wx.miniapp.config.WxMaInMemoryConfig;
import com.atour.notify.api.remote.WeChatRemote;
import me.chanjar.weixin.common.bean.WxAccessToken;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/19
 */
@Component
public class WechatMniappConfig extends WxMaInMemoryConfig {

    /**
     * 小程序二维码
     */
    @Value("${wechat.miniapp.appId}")
    private String wechatMiniAppId;

    @Resource
    private WeChatRemote weChatRemote;

    @Override
    public String getAccessToken() {

        return weChatRemote.queryAccessToken(wechatMiniAppId, false, null).getAccessToken();
    }

    @Override
    public void setAccessToken(String accessToken) {
        // do nothing
    }

    @Override
    public boolean isAccessTokenExpired() {

        return false;
    }

    @Override
    public synchronized void updateAccessToken(WxAccessToken accessToken) {
        // do nothing
    }

    @Override
    public synchronized void updateAccessToken(String accessToken, int expiresInSeconds) {
        // do nothing
    }

    @Override
    public void expireAccessToken() {
        // do nothing
    }

    @Override
    public String getAppid() {

        return wechatMiniAppId;
    }
}
