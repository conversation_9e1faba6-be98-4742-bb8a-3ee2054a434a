package com.atour.hotel.module.cost.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2021/3/16
 * 计提成本列表页dto
 */
@Data
@ApiModel(value = "AccrualCostListItemDTO", description = "计提成本列表页")
public class AccrualCostListItemDTO {

    /**
     * 日期 yyyy-MM-dd
     */
    @ApiModelProperty("日期 yyyy-MM-dd")
    private String date;

    /**
     * 具体项目:key: 分类code，value: 合同列表
     */
    @ApiModelProperty("具体项目")
    private List<Item> itemList;

    @Data
    public static class Item {
        /**
         * 分类code
         */
        @ApiModelProperty("分类code")
        private String categoryCode;

        /**
         * 分类名字
         */
        @ApiModelProperty("分类名字")
        private String categoryName;

        /**
         * 成本合同
         */
        @ApiModelProperty("成本合同")
        private List<Cost> costs;
    }

    @Data
    public static class Cost {
        /**
         * 成本id
         */
        @ApiModelProperty("成本id")
        private Integer costId;

        /**
         * 金额
         */
        @ApiModelProperty("金额")
        private BigDecimal value;

        /**
         * 合同附件信息
         */
        @ApiModelProperty("合同附件信息")
        private AccrualCostListItemDTO.Contract contract;

        /**
         * 可进行的操作
         */
        @ApiModelProperty("操作: 1 查看 2 录入 3 修改 4 冲减")
        private List<Integer> operator;

    }

    @Data
    public static class Contract {

        @ApiModelProperty("文件名")
        private String fileName;

        @ApiModelProperty("ossKEy")
        private String ossKey;
    }
}
