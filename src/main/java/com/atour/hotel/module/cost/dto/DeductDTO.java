package com.atour.hotel.module.cost.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数据返回：冲减数据实体
 *
 * <AUTHOR>
 * @date 2020/3/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeductDTO {
    @ApiModelProperty("本月操作冲减")
    private List<DeductData> createList;

    @ApiModelProperty("本月被冲减")
    private List<DeductData> eventList;

    @Data
    public static class DeductData {
        /**
         * 主键id
         */
        @ApiModelProperty(value = "主键id")
        private Integer id;

        /**
         * 冲减数值
         */
        @ApiModelProperty(value = "冲减数值")
        private BigDecimal deductValue;

        /**
         * 冲减日期
         */
        @ApiModelProperty(value = "冲减日期")
        private String eventDate;

        /**
         * 被冲减账期
         */
        @ApiModelProperty(value = "被冲减账期")
        private String eventMonth;

        /**
         * 冲减金额所属成本周期
         */
        @ApiModelProperty(value = "冲减金额所属成本周期")
        private String eventBelongMonth;

        /**
         * 冲减金额所属成本周期
         */
        @ApiModelProperty(value = "每有均摊成本")
        private BigDecimal preShared;

        @ApiModelProperty(value = "是否均摊")
        private Boolean shared;

        @ApiModelProperty(value = "冲减后的金额")
        private BigDecimal afterValue;

    }

}
