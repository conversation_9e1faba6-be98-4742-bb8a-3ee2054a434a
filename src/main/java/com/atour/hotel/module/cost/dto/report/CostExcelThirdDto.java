package com.atour.hotel.module.cost.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 成本报表底部报表 第三次 Dto
 *
 * <AUTHOR>
 * @date 2020/03/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostExcelThirdDto {

    /**
     * 一级层级项目code
     */
    @ApiModelProperty(value = "一级层级项目code")
    private String costItemCodeLevel1;

    /**
     * 二级层级项目code
     */
    @ApiModelProperty(value = "二级层级项目code")
    private String costItemCodeLevel2;

    /**
     * 一级层级项目名称
     */
    @ApiModelProperty(value = "一级层级项目名称")
    private String costItemNameLevel1;

    /**
     * 二级层级项目名称
     */
    @ApiModelProperty(value = "二级层级项目名称")
    private String costItemNameLevel2;

    /**
     * 是否冲减
     */
    @ApiModelProperty(value = "是否冲减")
    private boolean isDeduct;

    /**
     * 成本数值
     */
    @ApiModelProperty(value = "成本数值")
    private Double dataValue;

}
