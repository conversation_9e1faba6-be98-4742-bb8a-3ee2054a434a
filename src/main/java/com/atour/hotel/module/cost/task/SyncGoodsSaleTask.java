package com.atour.hotel.module.cost.task;

import com.alibaba.fastjson.JSON;
import com.atour.hotel.module.cost.enums.GoodsTypeEnum;
import com.atour.hotel.persistent.cost.dao.GoodsDetailDao;
import com.atour.hotel.persistent.cost.entity.GoodsDetailEntity;
import com.atour.hotel.persistent.cost.param.ErpOrderQueryParam;
import com.atour.hotel.persistent.cost.response.ErpOrderResponse;
import com.atour.monitor.AMonitor;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.atour.web.exception.BusinessException;
import com.atour.web.httpclient.AtourRestTemplate;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2021/3/30
 * 同步商品销售明细
 */
@JobHandler(value = "syncGoodsSaleTask")
@Component
@Slf4j
public class SyncGoodsSaleTask extends IJobHandler {

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${erp.sync.sleep:500}")
    private Long erpSyncSleep;

    @Resource
    private AtourRestTemplate erpRestTemplate;

    @Resource
    private GoodsDetailDao goodsDetailDao;


    private static final int PAGE_SIZE = 50;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        int pageNo = 1;
        Date now = new Date();
        Date yesterday = DateUtil.addDays(now, -1);
        long yesterdayStartTime = DateUtil.getStartOfDay(yesterday)
            .getTime();
        long yesterdayEndTime = DateUtil.getEndOfDay(yesterday)
            .getTime();
        while (true) {
            ErpOrderQueryParam erpOrderQueryParam = new ErpOrderQueryParam();
            erpOrderQueryParam.setStartTime(yesterdayStartTime);
            erpOrderQueryParam.setEndTime(now.getTime());
            erpOrderQueryParam.setPageNo(pageNo);
            erpOrderQueryParam.setPageSize(PAGE_SIZE);
            ErpOrderResponse erpOrderResponse = erpRestTemplate.getDelegate()
                .postForObject(erpDomain + "/inner/getSalesOrder", erpOrderQueryParam, ErpOrderResponse.class);

            if (Objects.isNull(erpOrderResponse)) {
                AMonitor.meter("cost.get_sales_order_error");
                throw new BusinessException("同步erp订单失败", 1000);
            }
            log.info("销售数据：{}", JSON.toJSONString(erpOrderResponse));
            ErpOrderResponse.ErpOrderResult result = erpOrderResponse.getResult();
            Safes.of(result.getData())
                .forEach(item -> {
                    if (item.getCreateTime() > yesterdayEndTime || item.getCreateTime() < yesterdayStartTime ) {
                        return;
                    }
                    GoodsDetailEntity goodsDetailEntity = new GoodsDetailEntity();
                    goodsDetailEntity.setChainId(item.getChainId());
                    List<GoodsDetailEntity.ContextExt> contextExtList = Safes.of(item.getGoodsData())
                        .stream()
                        .map(goods -> {
                            GoodsDetailEntity.ContextExt contextExt = new GoodsDetailEntity.ContextExt();
                            contextExt.setCostValue(Safes.of(goods.getMemberPrice()));
                            contextExt.setNum(goods.getNum());
                            contextExt.setSkuNo(goods.getSkuNo());
                            return contextExt;
                        })
                        .collect(Collectors.toList());
                    goodsDetailEntity.setContext(JSON.toJSONString(contextExtList));
                    goodsDetailEntity.setOrderActualAmount(item.getOrderActualAmount());
                    goodsDetailEntity.setOrderId(item.getOrderNumber());
                    goodsDetailEntity.setType(GoodsTypeEnum.ORDER.getCode());
                    goodsDetailEntity.setOrderCreateDate(DateUtil.printDate(new Date(item.getCreateTime())));
                    goodsDetailDao.insert(goodsDetailEntity);
                });
            if (Safes.of(result.getData())
                .size() < PAGE_SIZE) {
                break;
            }
            pageNo++;
            try {
                TimeUnit.MILLISECONDS.sleep(erpSyncSleep);
            } catch (InterruptedException ignore) {
            }
        }
        return ReturnT.SUCCESS;
    }
}
