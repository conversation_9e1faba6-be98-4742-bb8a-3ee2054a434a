package com.atour.hotel.module.cost.wrapper;

import com.atour.hotel.module.cost.bo.CostReportItemBo;

import java.math.BigDecimal;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2020/4/12
 * 成本报表数据包装
 */
public class CostReportItemBoWrapper {
    private CostReportItemBoWrapper(){}

    /**
     * 包装成本报表数据
     * @param itemCode 成本报表code
     * @param itemName 成本报表名字
     * @param value 数值
     * @param chainId 酒店id
     * @param chainName 酒店名称
     * @param evenMonth 月份
     * @param isDeduct 是否冲减
     * @return 成本报表数据
     */
    public static CostReportItemBo wrapCostReportItemBo(String itemCode, String itemName, BigDecimal value, Integer chainId, String chainName, String evenMonth, boolean isDeduct){
        CostReportItemBo costReportItemBo = new CostReportItemBo();
        costReportItemBo.setCode(itemCode);
        costReportItemBo.setName(itemName);
        costReportItemBo.setValue(value);
        costReportItemBo.setChainId(chainId);
        costReportItemBo.setChainName(chainName);
        costReportItemBo.setEventMonth(evenMonth);
        costReportItemBo.setDeduct(isDeduct);
        return costReportItemBo;
    }
}
