package com.atour.hotel.module.cost.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 饮食成本 dto
 *
 * <AUTHOR>
 * @date 2020-03-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FoodCostPageDto {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 成本所属时间
     */
    @ApiModelProperty(value = "时间 格式：2019年08月")
    private String costMonth;

    /**
     * 客餐实际成本
     */
    @ApiModelProperty(value = "客餐实际成本")
    private Double customFoodCost;

    /**
     * 客餐进项税
     */
    @ApiModelProperty(value = "客餐进项税")
    private String customFoodTax;

    /**
     * 客餐总金额
     */
    @ApiModelProperty(value = "客餐总金额")
    private String totalCustomFoodCost;

    /**
     * 员餐实际成本
     */
    @ApiModelProperty(value = "员餐实际成本")
    private String staffFoodCost;

    /**
     * 进项税
     */
    @ApiModelProperty(value = "进项税")
    private String staffFoodTax;

    /**
     * 员餐总金额
     */
    @ApiModelProperty(value = "员餐总金额")
    private String totalStaffFoodCost;

    /**
     * 一级分类
     */
    @ApiModelProperty(value = "一级分类")
    private Integer categoryLevel1;

    /**
     * 操作按钮 true-展示录入，false-不展示
     */
    @ApiModelProperty(value = "操作按钮 true-展示录入，false-不展示")
    private Integer showEditButton;

    /**
     * 操作按钮 true-展示查看，false-不展示查看
     */
    @ApiModelProperty(value = "操作按钮 true-展示查看，false-不展示查看")
    private Integer showViewButton;

}
