package com.atour.hotel.module.cost.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 成本在线操作日志 Dto
 *
 * <AUTHOR>
 * @date 2020/03/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostOperationLogDto {

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间 格式：2019-08-01 21:22:11")
    private String operateTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operateUser;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String content;

}
