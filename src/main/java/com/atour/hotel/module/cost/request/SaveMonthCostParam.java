package com.atour.hotel.module.cost.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2021/3/23
 * 保存参数
 */
@ApiModel("保存参数")
@Data
public class SaveMonthCostParam {

    @Positive(message = "酒店id必须大于0")
    @ApiModelProperty("酒店id")
    private Integer chainId;

    @NotBlank(message = "月份不能为空")
    @ApiModelProperty("月份")
    private String eventMonth;

    @ApiModelProperty("具体条目")
    private List<Item> itemList;

    @ApiModel("具体明细")
    @Data
    public static class Item {

        @ApiModelProperty("成本id，有时修改，无时新增")
        private Integer costId;

        @ApiModelProperty("金额")
        private BigDecimal value;

        @ApiModelProperty("上级分类code")
        private String parentCategoryCode;

        @ApiModelProperty("分类code")
        private String categoryCode;
    }
}
