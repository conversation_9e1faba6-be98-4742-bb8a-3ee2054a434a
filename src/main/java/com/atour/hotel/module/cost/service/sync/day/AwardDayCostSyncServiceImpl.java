package com.atour.hotel.module.cost.service.sync.day;

import com.atour.hotel.module.cost.enums.ChainTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2021/4/8
 * 计算工资日成本
 */
@Slf4j
@Service
public class AwardDayCostSyncServiceImpl extends AbstractHumanDayCostSyncServiceImpl {

    @Override
    public String getItemName() {
        return "奖金";
    }

    @Override
    public ChainTypeEnum applyChainType() {
        return ChainTypeEnum.COMMON;
    }

    @Override
    protected String getItemCode() {
        return "award";
    }
}
