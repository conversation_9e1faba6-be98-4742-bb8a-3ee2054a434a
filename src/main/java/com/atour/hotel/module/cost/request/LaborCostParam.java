/**
 * 
 */
package com.atour.hotel.module.cost.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@ApiModel(value = "laborCostParam", description = "人力成本请求参数")
@Data
@Valid
public class LaborCostParam {
    
    /**
     * 酒店ids
     */
    @NotNull(message = "酒店ids不能为空")
    @Size(min = 1)
    @ApiModelProperty(value = "酒店id")
    private List<Integer> chainIds;

    /**
     * 开始查询月份(包含当月)
     */
    @ApiModelProperty(value = "开始查询月份(包含当月)")
    private String beginDate;

    /**
     * 结束查询月份(包含当月)
     */
    @ApiModelProperty(value = "开始查询月份(包含当月)")
    private String endDate;
    
    @ApiModelProperty(value = "酒店类型，@0:特许，@1:直营")
    @Max(value = 1, message = "酒店类型不合法")
    @Min(value = 0, message = "酒店类型不合法")
    @NotNull(message = "酒店类型不能为空")
    private Integer chainType;

}
