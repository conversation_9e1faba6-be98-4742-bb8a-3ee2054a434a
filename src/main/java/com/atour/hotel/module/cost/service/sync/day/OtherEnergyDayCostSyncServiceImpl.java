package com.atour.hotel.module.cost.service.sync.day;

import com.atour.hotel.module.cost.enums.ChainTypeEnum;
import com.atour.hotel.module.energy.dto.CostDayReportQueryDetailDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2021/4/8
 * 其他能源
 */
@Service
public class OtherEnergyDayCostSyncServiceImpl extends AbstractEnergyDayCostSyncServiceImpl {
    @Override
    protected BigDecimal getFee(CostDayReportQueryDetailDTO costDayReportQueryDetailDTO) {
        return costDayReportQueryDetailDTO.getTotalEnergyCost()
            .subtract(costDayReportQueryDetailDTO.getSingleNightWaterFee().multiply(BigDecimal.valueOf(costDayReportQueryDetailDTO.getRoomCount())))
            .subtract(costDayReportQueryDetailDTO.getSingleNightElectricFee().multiply(BigDecimal.valueOf(costDayReportQueryDetailDTO.getRoomCount())))
            .subtract(costDayReportQueryDetailDTO.getSingleNightGasFee().multiply(BigDecimal.valueOf(costDayReportQueryDetailDTO.getRoomCount())));
    }

    @Override
    public String getItemName() {
        return "其他能源";
    }

    @Override
    public ChainTypeEnum applyChainType() {
        return ChainTypeEnum.COMMON;
    }
}
