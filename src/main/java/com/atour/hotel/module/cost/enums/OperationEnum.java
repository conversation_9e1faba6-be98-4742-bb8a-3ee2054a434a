package com.atour.hotel.module.cost.enums;

import lombok.Getter;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2020-03-26
 * 酒店类型枚举
 */

@Getter
public enum OperationEnum {

    /**
     * 查看
     */
    VIEW(1, "查看"),
    /**
     * 录入
     */
    ADD(2, "录入"),

    /**
     * 录入
     */
    UPDATE(3, "修改"),

    /**
     * 冲减
     */
    DEDUCT(4, "冲减"),
    /**
     * 删除
     */

    DELETE(5, "删除");

    /**
     * 类型
     */
    private int type;

    /**
     * 中文名称
     */
    private String name;

    OperationEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static OperationEnum getInstance(Integer type) {
        for (OperationEnum value : OperationEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }
}
