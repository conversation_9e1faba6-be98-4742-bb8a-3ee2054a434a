package com.atour.hotel.module.cost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2020/4/9
 * 成本报表数据类型枚举
 */
@AllArgsConstructor
@Getter
public enum CostFormTypeEnum {

    /**
     * 成本报表
     */
    COST(1, "成本报表"),

    /**
     * GOP报表
     */
    GOP(2, "GOP报表"),
    
    /**
     * 可售单间夜成本
     */
    SINGLEROOM_SALE(3, "可售单间夜成本"),
    
    /**
     * 已售单间夜成本
     */
    SINGLEROOM_SOLD(4, "已售单间夜成本"),

    /**
     * 人力成本
     */
    HUMAN(5, "人力成本"),

    /**
     * 每日成本
     */
    DAY_COST(6, "每日成本"),

    /**
     * 日间夜成本
     */
    DAY_NIGHT_COST(7, "日间夜成本");

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

}
