package com.atour.hotel.module.login.request;

import com.atour.hotel.framework.annotation.RoleChainAnnotation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 根据系统过滤其关联的酒店及区域
 *
 * <AUTHOR>
 * @date 2019年5月28日18:18:55
 */
@Data
public class SystemRelateHotelParam {
	/**
	 * 系统id
	 */
	@NotNull(message = "系统id不能为空")
	private Integer systemId;

	/**
	 * 酒店列表
	 */
	@RoleChainAnnotation
	private List<Integer> chainIds;

	/**
	 * 酒店类型
	 */
	private Integer chainType;

	/**
	 * true：所有酒店 false：开业酒店
	 */
	private Boolean isAll;

	/**
	 * 过滤类型;防止影响老的逻辑
	 * @see com.atour.hotel.module.login.enums.SystemRelateHotelFilterEnum
	 */
	private String filterKey;
}
