package com.atour.hotel.module.login.service;

import com.atour.asso.api.dto.AssoUserInfoDTO;
import com.atour.asso.api.dto.ServiceIdentifyResultDTO;
import com.atour.asso.api.remote.AssoUserRemote;
import com.atour.db.redis.RedisClient;
import com.atour.hotel.common.constants.RedisContants;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.util.AssoAutoLoginManager;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.common.util.CookieUtils;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.framework.enums.AppTypeEnum;
import com.atour.hotel.framework.exception.TokenExpireException;
import com.atour.hotel.module.login.response.LoginCookieDTO;
import com.atour.hotel.module.login.service.gateway.JmlAppUserAuthManager4Gateway;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.center.entity.SysUserEntity;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.utils.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/2
 */
@Service
@Slf4j
public class AssoAutoLoginService {

    @Resource
    private LoginService loginService;
    @Resource
    private RedisClient redisClient;
    @Resource
    private SysUserService sysUserService;
    @Value("${hotelManageLoginCookie.maxAgeSecond:7776000}")
    private Integer maxAgeSecond;
    @Resource
    private AssoUserRemote assoUserRemote;
    @Resource
    private JmlAppUserAuthManager4Gateway jmlAppUserAuthManager4Gateway;


    public String tryRebuildCookie(HttpServletRequest request, HttpServletResponse response, String cookieKey,
                                   String bundleId, String version, AppTypeEnum appType, String oldToken) {
//        log.info("--ASSO::生成cookieKey: {}", cookieKey);

        ServiceIdentifyResultDTO serviceIdentifyResultDTO = AssoAutoLoginManager.get();
        if (StringUtils.isBlank(cookieKey) || Objects.isNull(serviceIdentifyResultDTO)) {
            log.info("tryRebuildCookie returned cookieKey:{} serviceIdentifyResultDTO:{}", cookieKey, serviceIdentifyResultDTO);
            return null;
        }

        AssoUserInfoDTO userInfo = serviceIdentifyResultDTO.getUserInfo();
        //通过账号密码登录获取用户所有的权限
        String token = oldToken;
        if (StringUtils.isBlank(token)) {
            token = serviceIdentifyResultDTO.getToken();
        }
        UserPermissionDTO userPermission = redisClient.getObject(RedisContants.ASSO_AUTO_LOGIN_USER_KEY
                .concat(String.valueOf(userInfo.getUserId())));
        if (Objects.isNull(userPermission)) {
            SysUserEntity sysUser = sysUserService.getUserByEmployeeId(userInfo.getEmployeeId());
            if (sysUser == null) {
                //查询不到 sysUser 强制退出
                loginService.clearAssoToken(token);
                loginService.clearRedisReqToken(token);
                //cookie清除reqToken
                CookieUtils.deleteCookie(request, response, SystemContants.HOTELMANAGE_COOKIE_KEY, FileConfig.defaultDomain);
                CookieUtils.deleteCookie(request, response, SystemContants.COOKIE_PARAM_ASSO_TOKEN, FileConfig.defaultDomain);
                log.info(" clearUser Cookie sysUser is employeeId:{} null:{}", userInfo.getEmployeeId(), token);
                throw new TokenExpireException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(), ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
            }
            userPermission = loginService.getUserPermissionInfoByMobileFromRbac(sysUser.getMobile(), appType);
            redisClient.setObject(CommonParamsManager.getUserInfoKeyPrefix(), RedisContants.ASSO_AUTO_LOGIN_USER_KEY,
                    userPermission, RedisContants.ASSO_AUTO_LOGIN_USER_KEY_EXPIRE_IN_SECONDS);
        }
        if (Objects.isNull(userPermission)) {
            log.info("--ASSO::userPermission为空, userCode: {}", userInfo.getUserCode());
            return null;
        }

        if (Objects.equals(SystemContants.ENERGY_LOGIN_COOKIE_KEY, cookieKey)) {
            redisClient.setObject(RedisContants.HOTEL_ENERGY_USERINFO_KEY, String.valueOf(userPermission.getUserId()),
                    userPermission, SystemContants.HOTEL_USERINFO_TIME_OUT);
            LoginCookieDTO cookieData = loginService.saveLoginToken(userInfo.getUserCode(),
                    String.valueOf(userPermission.getUserId()), RedisContants.HOTEL_ENERGY_AUTHTOKEN_KEY,
                    SystemContants.ENERGY_REDIS_TIME_OUT);
            CookieUtils.saveCookieValue(response, SystemContants.ENERGY_LOGIN_COOKIE_KEY, cookieData,
                    FileConfig.defaultDomain, SystemContants.ENERGY_REDIS_TIME_OUT, Boolean.TRUE);
            return ObjectUtil.toJsonQuietly(cookieData);
        } else if (Objects.equals(SystemContants.HOTELMANAGE_COOKIE_KEY, cookieKey)) {
            redisClient.setObject(RedisContants.HOTEL_OWNER_APP_USER_INFO_REDIS_KEY, token, userPermission,
                    RedisContants.HOTEL_OWNER_APP_USER_INFO_REDIS_KEY_EXPIRE_IN_SECONDS);
            CookieUtils.saveCookieValue(response, SystemContants.HOTELMANAGE_COOKIE_KEY, token,
                    FileConfig.defaultDomain, maxAgeSecond, Boolean.TRUE);
            log.info("rebuild hotelOwner Cookie:{} token:{}", cookieKey, token);
            if (StringUtils.isBlank(bundleId) && StringUtils.isBlank(version)) {
                log.info("--ASSO::兼容WEB端的token值为json, uri: {}, userCode: {}", request.getRequestURI(), userInfo.getUserCode());
                LoginCookieDTO cookieData = loginService.saveLoginToken(userInfo.getUserCode(), String.valueOf(userPermission.getUserId()),
                        RedisContants.HOTEL_ENERGY_AUTHTOKEN_KEY, SystemContants.ENERGY_REDIS_TIME_OUT);
                return ObjectUtil.toJsonQuietly(cookieData);
            }
            return token;
        }
        log.warn("--ASSO::发现需要添加到处理里的CookieKey: {}", cookieKey);

        return null;
    }

    public void logout(HttpServletRequest request, HttpServletResponse response) {
        String assoToken = CookieUtils.getCookieValue(request, SystemContants.COOKIE_PARAM_ASSO_TOKEN);
        if (StringUtils.isNotBlank(assoToken)) {
            CookieUtils.deleteCookie(request, response, SystemContants.COOKIE_PARAM_ASSO_TOKEN, FileConfig.defaultDomain);
        }
        log.info("--ASSO::退出");
    }
}
