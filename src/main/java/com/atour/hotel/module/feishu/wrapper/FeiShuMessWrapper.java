package com.atour.hotel.module.feishu.wrapper;

import lombok.Data;

/**
 * 发送消息模板
 */
@Data
public class FeiShuMessWrapper {

    /**
     * 活动空间需求
     */
    public static final String MEETING_ROOM = "活动空间需求";
    /**
     * 会议室预定
     */
    public static final String MEETING_ROOM_BOOK = "活动空间预定";


    /**
     * 新工单提醒
     */
    public static final String WORK_ORDER_REMINDER_TITLE = "新工单提醒";

    /**
     * 新工单提醒内容
     */
    public static final String WORK_ORDER_REMINDER_CONTENT = "您有工单超过6小时未处理，请及时前往OPS处理";



    public static String builderApproveMsgTemplate(String chainName,String rateCode,String url){
        StringBuilder builder = new StringBuilder();
        builder.append("特殊房价订单审批通知");
        builder.append("\n");
        builder.append(" **申请详情** ");
        builder.append("\n");

        builder.append("门店名称：").append(chainName).append("\n");
        builder.append("申请房价代码：").append(rateCode).append("\n");

        builder.append("房价类型：").append(rateCode).append("\n");

        builder.append("入住时间：").append(rateCode).append("\n");
        builder.append("离店时间：").append(rateCode).append("\n");
        builder.append("间夜数：").append(rateCode).append("\n");
        builder.append("预订人姓名：").append(rateCode).append("\n");
        builder.append("申请原因：").append(rateCode).append("\n");
        builder.append("请注意及时审批：").append(rateCode).append("\n");
        builder.append("点击详情：").append("[审批](").append(url).append("/)");
        return builder.toString();
    }













}
