package com.atour.hotel.module.paymentReturned.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/12/24
 */
@ApiModel("未回款明细")
@Data
public class PaymentReturnedDetailBo {
    @ApiModelProperty("名字 如 AR超时未回款")
    private String name;

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("金额数")
    private BigDecimal amount;
}
