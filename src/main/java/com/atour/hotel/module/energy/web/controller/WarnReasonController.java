package com.atour.hotel.module.energy.web.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.common.page.Pager;
import com.atour.hotel.module.energy.web.dto.WarnReasonListDTO;
import com.atour.hotel.module.energy.web.dto.WarnReasonLogDTO;
import com.atour.hotel.module.energy.web.request.WarnReasonLogRequest;
import com.atour.hotel.module.energy.web.request.WarnReasonRequest;
import com.atour.hotel.module.energy.web.request.WarnReasonUpdateRequest;
import com.atour.hotel.module.energy.web.service.WarnReasonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 预警原因
 *
 * <AUTHOR>
 * @date 2019年5月23日17:33:29
 */
@Slf4j
@RestController
@RequestMapping(value = "api/web/energy/warnreason/", produces = {"application/json;charset=UTF-8"})
public class WarnReasonController {

    @Autowired
    private WarnReasonService warnReasonService;

    /**
     * 获取预警原因列表
     *
     * @param request
     */
    @GetMapping(value = "/getWarnReasonList")
    public ApiResult<List<WarnReasonListDTO>> getChainListByRuleId(@Valid WarnReasonRequest request) {

        return ApiResult.success(warnReasonService.getWarnReasonList(request));
    }

    /**
     * 新增预警原因
     */
    @PostMapping(value = "/addWarnReason")
    public ApiResult<Boolean> addWarnReason(@Valid @RequestBody WarnReasonRequest request) {
        warnReasonService.addWarnReason(request);
        return ApiResult.success(Boolean.TRUE);
    }

    /**
     * 更新预警原因
     */
    @PostMapping(value = "/updateWarnReason")
    public ApiResult<Boolean> updateWarnReason(@Valid @RequestBody WarnReasonUpdateRequest request) {
        warnReasonService.updateWarnReason(request);
        return ApiResult.success(Boolean.TRUE);
    }

    /**
     * 删除预警原因
     */
    @PostMapping(value = "/deleteWarnReason")
    public ApiResult<Boolean> deleteWarnReason(@Valid @RequestBody WarnReasonUpdateRequest request) {
        warnReasonService.deleteWarnReason(request);
        return ApiResult.success(Boolean.TRUE);
    }

    /**
     * 获取预警原因操作日志
     */
    @GetMapping(value = "/getWarnReasonLog")
    public ApiResult<Pager<WarnReasonLogDTO>> getWarnReasonLog(@Valid WarnReasonLogRequest request) {
        return ApiResult.success(warnReasonService.getWarnReasonLog(request));
    }
}
