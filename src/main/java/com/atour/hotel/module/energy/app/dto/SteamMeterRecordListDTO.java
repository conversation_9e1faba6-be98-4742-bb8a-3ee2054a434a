package com.atour.hotel.module.energy.app.dto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 蒸汽表抄表记录
 * <AUTHOR>
 * @date 2019年8月8日11:33:40
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SteamMeterRecordListDTO implements Serializable {

	/**
	 * 总表
	 */
	private List<SteamTotalMeterDTO> totalMeterList;

	/**
	 * 分表
	 */
	private List<SteamSubMeterDTO> subMeterList;

}
