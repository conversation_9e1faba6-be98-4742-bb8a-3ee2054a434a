package com.atour.hotel.module.energy.app.service.cost;


import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.atour.hotel.common.util.HttpUtil;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.energy.app.response.SumTransItemResponse;
import com.atour.hotel.module.energy.app.response.TransItemResponse;
import com.atour.hotel.module.energy.app.response.TransResponse;
import com.atour.utils.Safes;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 				"itemCode": "ota_commission_agreement",
 * 				"itemName": "OTA协议佣金",
 *
 * <AUTHOR>
 * @date 2020/10/23
 */
@Service
public class CalCommissionTotalService implements CostCommonCalInterfaceService {
    @Resource
    private ParamService paramService;

    private final Integer ITEM_ID = 2011;


    public BigDecimal calValue(Integer chainId, String evenMonth) {
        String startTime = evenMonth + "-01";
        LocalDate parse = LocalDate.parse(startTime);
        LocalDate currentAccLocalDate = paramService.getCurrentAccLocalDate(chainId);
        LocalDate first = parse.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = parse.with(TemporalAdjusters.lastDayOfMonth());

        //如果当前月的，结束日期等于营业日
        if(parse.getYear() == currentAccLocalDate.getYear()
                && parse.getMonth().getValue() == currentAccLocalDate.getMonth().getValue()){
            end = currentAccLocalDate;
        }

        List<SumTransItemResponse> sumTransItemResponses = queryReport(first.toString(), end.toString(), chainId);

        return Safes.of(sumTransItemResponses).stream()
                .map(SumTransItemResponse::getSumDebit)
                .findFirst().orElse(BigDecimal.ZERO);

    }

    @Override
    public String getName() {
        return "计算佣金统计";
    }

    /**
     * http请求调用report获取数据
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param chainId   门店id
     */
    private List<SumTransItemResponse> queryReport(String startTime, String endTime, Integer chainId) {
        String result =
                HttpUtil.requestByGetMethod(FileConfig.reportAccTransSummary + "?chainid=" + chainId +"&startDate=" + startTime + "&endDate=" + endTime
                         + "&itemId=" + ITEM_ID+"&isGroup=0");
        TransResponse transResponse = JSON.parseObject(result, TransResponse.class);
        if (Objects.isNull(transResponse) ||
                CollectionUtils.isEmpty(transResponse.getTransData())) {
            return Collections.emptyList();
        }

        List<SumTransItemResponse> list = transResponse.getTransData()
                .stream()
                .map(TransItemResponse::getData)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        return Safes.of(list)
                .stream()
                .filter(resp -> Objects.nonNull(resp.getItemID()))
                .filter(resp -> resp.getItemID().equals(ITEM_ID))
                .collect(Collectors.toList());


    }

}
