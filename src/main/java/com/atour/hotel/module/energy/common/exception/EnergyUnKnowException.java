package com.atour.hotel.module.energy.common.exception;

import java.text.MessageFormat;

/**
 * 自定义未知异常类
 *
 * <AUTHOR>
 * @date 2018/5/13
 */
public class EnergyUnKnowException extends RuntimeException {

    static final long serialVersionUID = 1L;

    public EnergyUnKnowException() {
    }

    public EnergyUnKnowException(String message) {
        super(message);
    }

    public EnergyUnKnowException(String msgTemplate, Object... args) {
        super(MessageFormat.format(msgTemplate, args));
    }

    public EnergyUnKnowException(String message, Throwable cause) {
        super(message, cause);
    }

    public EnergyUnKnowException(Throwable cause, String msgTemplate, Object... args) {
        super(MessageFormat.format(msgTemplate, args), cause);
    }

    public EnergyUnKnowException(Throwable cause) {
        super(cause);
    }

}