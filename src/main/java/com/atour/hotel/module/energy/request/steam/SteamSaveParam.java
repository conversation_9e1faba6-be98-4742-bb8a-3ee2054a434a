package com.atour.hotel.module.energy.request.steam;

import com.atour.hotel.module.energy.biz.json.steam.SteamJson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 业务层: 蒸汽配置保存请求参数
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@ApiModel(value = "SteamSaveParam", description = "蒸汽配置保存请求参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SteamSaveParam implements Serializable {

    private static final long serialVersionUID = -2189244995686018386L;

    /**
	 * 酒店ID
	 */
    @ApiModelProperty(value = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
	private Integer chainId;

    /**
     * 蒸汽配置
     */
    @ApiModelProperty(value = "蒸汽配置")
    @NotNull(message = "蒸汽配置不能为空")
	private SteamJson steamJson;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;
}
