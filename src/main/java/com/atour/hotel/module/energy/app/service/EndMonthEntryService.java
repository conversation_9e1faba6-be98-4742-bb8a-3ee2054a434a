package com.atour.hotel.module.energy.app.service;

import com.alibaba.fastjson.JSON;
import com.atour.dicts.enums.energy.HotelBaseConfigTypeEnum;
import com.atour.dicts.enums.energy.HotelPlaceConfigTypeEnum;
import com.atour.dicts.enums.energy.MeterTypeEnum;
import com.atour.dicts.enums.energy.TableTypeEnum;
import com.atour.hotel.common.enums.DeleteFlagEnum;
import com.atour.hotel.common.enums.EndMonthChannelEnum;
import com.atour.hotel.common.enums.EndMonthRecordTypeEnum;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.energy.app.request.EndMonthEntryRequest;
import com.atour.hotel.module.energy.app.request.EndMonthEntrySubListRequest;
import com.atour.hotel.module.energy.app.request.EndMonthEntrySubRequest;
import com.atour.hotel.module.energy.app.request.GasMeterReadingRequest;
import com.atour.hotel.module.energy.app.response.EndMonthEntryResponse;
import com.atour.hotel.module.energy.app.response.EndMonthEntrySubResponse;
import com.atour.hotel.module.energy.biz.json.electricity.ElectricityJson;
import com.atour.hotel.module.energy.biz.json.electricity.SubElectricityMeterJson;
import com.atour.hotel.module.energy.biz.json.gas.GasJson;
import com.atour.hotel.module.energy.biz.json.gas.SubGasMeterJson;
import com.atour.hotel.module.energy.biz.json.steam.SteamJson;
import com.atour.hotel.module.energy.biz.json.steam.SubSteamMeterJson;
import com.atour.hotel.module.energy.biz.json.water.SubWaterMeterJson;
import com.atour.hotel.module.energy.biz.json.water.WaterJson;
import com.atour.hotel.module.energy.common.BizPreconditions;
import com.atour.hotel.module.energy.common.CommonHelp;
import com.atour.hotel.module.energy.job.bo.HotelConfigSnapshotJsonBo;
import com.atour.hotel.module.energy.job.service.DailyMeterRecordService;
import com.atour.hotel.module.energy.service.HotelBaseConfigService;
import com.atour.hotel.module.energy.web.dto.EndMonthRecordDTO;
import com.atour.hotel.persistent.energy.dao.DailyMeterRecordDao;
import com.atour.hotel.persistent.energy.dao.EndMonthRecordMeterMapper;
import com.atour.hotel.persistent.energy.dao.HotelBaseConfigMapper;
import com.atour.hotel.persistent.energy.dao.ReportCalRecordMapper;
import com.atour.hotel.persistent.energy.entity.*;
import com.atour.monitor.AMonitor;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.google.common.collect.Lists;
import groovy.json.JsonException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.json.JsonParseException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/7/31
 * 月底录入
 */
@Log4j2
@Service
public class EndMonthEntryService {

    @Autowired
    private HotelBaseConfigService hotelBaseConfigService;
    @Resource
    private HotelBaseConfigMapper hotelBaseConfigMapper;
    @Resource
    private EndMonthRecordMeterMapper endMonthRecordMeterMapper;
    @Resource
    private ParamService paramService;
    @Resource
    private ReportCalRecordMapper reportCalRecordMapper;
    @Autowired
    private DailyMeterRecordService dailyMeterRecordService;
    @Resource
    private DailyMeterRecordDao dailyMeterRecordDao;
    @Resource
    private ElectricityMeterService electricityMeterService;
    @Resource
    private CommonService commonService;

    /**
     * 如果选择2019-9，营业日是2019-9-3，只能修改9月的数据，回显9月【修改状态】
     * 如果选择2019-8，营业日是2019-9-3，只能修改8月的数据，回显8月【修改状态】
     * 如果选择2019-9，营业日是2019-9-4，只能修改9月的数据，回显9月【修改状态】
     * 如果选择2019-8，营业日是2019-9-4，回显8月的数据，【不可修改】
     * <p>
     * 补录的数据优先展示补录 ，如果补录没有就展示初始【app】
     *
     * @param chainId
     * @param accMonth
     */
    public List<EndMonthEntryResponse> endMonthEntryInfo(Integer chainId, String accMonth) {

        List<EndMonthEntryResponse> respList = new ArrayList<>();

        // 获取营业日
        LocalDate currentAccLocalDate = paramService.getCurrentAccLocalDate(chainId);
        // 检查参数
        checkParam(accMonth, currentAccLocalDate);

        try {

            // 获取配置数据
            Pair<LocalDate, HotelConfigSnapshotJsonBo> reportData =
                getReportData(chainId, accMonth, currentAccLocalDate);

            if (Objects.isNull(reportData)) {
                log.error("reportData配置数据获取异常，chainid={},accMonth={},accDate={}", chainId, accMonth,
                    currentAccLocalDate);
                return respList;
            }

            respList = buildFormValueData(chainId, accMonth, reportData.getRight());

        } catch (Exception e) {

            AMonitor.meter("EndMonthEntryService.endMonthEntryInfo.error");
            log.error("endMonthEntryInfo chainid={},currentAccDate={},accMonth={}", chainId, currentAccLocalDate,
                accMonth, e);
        }

        return respList;
    }

    public int saveEndMonthEntry(EndMonthEntryRequest request) {

        Integer result = 0;

        this.checkEndMonthSaveRequest(request);

        try {
            YearMonth parse = YearMonth.parse(request.getAccMonth(), CommonUtils.YYYY_MM);

            EndMonthRecordMeterEntity entity = EndMonthRecordMeterEntity.builder()
                .chainId(request.getChainId())
                // TODO: 2019/9/30 这里不能直接是营业日，默认1号
                .recordDate(LocalDate.of(parse.getYear(), parse.getMonth(), 1))
                .recordMonth(request.getAccMonth())
                .endMonthRecord(JsonUtils.toJson(buildJson(request)))
                .channel(EndMonthChannelEnum.CHANNEL_APP.getValue())
                .meterReadId(request.getUserId())
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .build();

            result = endMonthRecordMeterMapper.insertSelective(entity);
            // 同步每日抄表记录
            dailyMeterRecordService.saveDailyMeterRecordForEndMonth(entity, false);

        } catch (JsonParseException e) {
            log.error("endMonthEntryInfo chainId = {},accdate = {}", request.getChainId(), request.getAccMonth(), e);
        } catch (Exception e) {
            log.error("endMonthEntryInfo chainId = {},accdate = {}", request.getChainId(), request.getAccMonth(), e);
        }
        return result;
    }

    /**
     * 新增的时候判断，更新还是新增，兼容补录信息
     */
    private List<EndMonthRecordDTO> buildJson(EndMonthEntryRequest request) {

        List<EndMonthRecordDTO> collect = request.getItems()
            .stream()
            .map(x -> {

                // 冗余这个类型，给外部场地报表使用
                Safes.of(x.getSubList())
                    .stream()
                    .forEach(y -> y.setMeterType(x.getMeterType()));

                return EndMonthRecordDTO.builder()
                    .subList(x.getSubList())
                    .meterType(x.getMeterType())
                    // 默认为0
                    .invoiceType(0)
                    .fireList(x.getFireList())
                    .recordType(EndMonthRecordTypeEnum.INITIAL.getValue())
                    .basePrice(x.getBasePrice())
                    .lossPrice(x.getLossPrice())
                    .build();
            })
            .collect(Collectors.toList());

        List<EndMonthRecordMeterEntity> endMonthRecordMeterEntityList = endMonthRecordMeterMapper.selectBySelective(
            EndMonthRecordMeterEntity.builder()
                .chainId(request.getChainId())
                .recordMonth(request.getAccMonth())
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .channel(EndMonthChannelEnum.CHANNEL_PC.getValue())
                .build());

        // 按时间取最新的一条
        EndMonthRecordMeterEntity entity = Safes.of(endMonthRecordMeterEntityList)
            .stream()
            .max(Comparator.comparing(EndMonthRecordMeterEntity::getCreateTime))
            .orElse(null);

        if (Objects.isNull(entity) || StringUtils.isBlank(entity.getEndMonthRecord())) {
            // 新增
            return collect;

        } else {
            // 更新
            List<EndMonthRecordDTO> oldJsonList =
                JsonUtils.parseList(entity.getEndMonthRecord(), EndMonthRecordDTO.class);

            Map<Integer, EndMonthRecordDTO> collectBytype = oldJsonList.stream()
                // 过滤出初始
                .filter(f -> Objects.equals(f.getRecordType(), EndMonthRecordTypeEnum.INITIAL.getValue()))
                .collect(Collectors.toMap(EndMonthRecordDTO::getMeterType, Function.identity()));

            // request的类型可能为null
            for (EndMonthRecordDTO o : collect) {
                if (collectBytype.containsKey(o.getMeterType())) {
                    // json包含了初始录入
                    collectBytype.get(o.getMeterType())
                        .setSubList(o.getSubList());
                    collectBytype.get(o.getMeterType())
                        .setFireList(o.getFireList());
                    collectBytype.get(o.getMeterType())
                        .setBasePrice(o.getBasePrice());
                    collectBytype.get(o.getMeterType())
                        .setLossPrice(o.getLossPrice());
                } else {
                    // 不包含新增类型
                    collectBytype.put(o.getMeterType(), o);
                }
            }

            return Safes.of(collectBytype)
                .values()
                .stream()
                .collect(Collectors.toList());
        }
    }

    /**
     * 取配置数据
     *
     * @param chainId
     * @param accMonth 前端穿的yyyy-mm
     * @param accDate  营业日
     * @return 备份OR实时的配置数据
     */
    private Pair<LocalDate, HotelConfigSnapshotJsonBo> getReportData(Integer chainId, String accMonth,
        LocalDate accDate) {

        String[] accYearMonth = accMonth.split("-");
        LocalDate startDate = LocalDate.of(Integer.parseInt(accYearMonth[0]), Integer.parseInt(accYearMonth[1]), 1);
        int daysOfMonth = CommonHelp.getDaysOfMonth(CommonHelp.localDate2Date(startDate));
        LocalDate endDate =
            LocalDate.of(Integer.parseInt(accYearMonth[0]), Integer.parseInt(accYearMonth[1]), daysOfMonth);

        // 如果2019-06 == 营业日 && > 3 取的是实时的
        if (Objects.equals(endDate.getMonthValue(), accDate.getMonthValue())) {

            // 实时修改的就是实时数据
            return Pair.of(accDate, HotelConfigSnapshotJsonBo.builder()
                .elecJson(getELecByCurrtMonthDate(chainId))
                .waterJson(getWaterByCurrtMonthDate(chainId))
                .gasJson(getGasByCurrtMonthDate(chainId))
                .steamJson(getSteamByCurrtMonthDate(chainId))
                .build());
        } else {

            // 备份取的是当月的最后一条
            ReportCalRecordEntity recordEntities = reportCalRecordMapper.selectByAccDate(ReportCalRecordEntity.builder()
                .chainId(chainId)
                .accDate(endDate)
                .build());

            if (Objects.nonNull(recordEntities) && StringUtils.isNotEmpty(recordEntities.getHotelBaseConfig())) {
                //备份数据自然修改的就是上个月的数据
                return Pair.of(endDate,
                    JSON.parseObject(recordEntities.getHotelBaseConfig(), HotelConfigSnapshotJsonBo.class));
            }
        }

        return null;
    }

    /**
     * 回显或则form
     *
     * @param chainId
     * @param accMonth
     * @param reportData
     * @return
     */
    private List<EndMonthEntryResponse> buildFormValueData(Integer chainId, String accMonth,
        HotelConfigSnapshotJsonBo reportData) {

        // 获取月底录入数据，包含所有类型
        List<EndMonthRecordMeterEntity> endMonthList = endMonthRecordMeterMapper.selectBySelective(
            EndMonthRecordMeterEntity.builder()
                .chainId(chainId)
                .recordMonth(accMonth)
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .build());

        // 如果1个月内多次填写，那么以最后一次作为最终读数
        EndMonthRecordMeterEntity entity = Safes.of(endMonthList)
            .stream()
            .max(Comparator.comparing(EndMonthRecordMeterEntity::getCreateTime))
            .orElse(null);

        List<EndMonthRecordDTO> endMonthEntryJsonBos = null;

        if (Objects.nonNull(entity)) {
            // 拿到数据，解析json
            endMonthEntryJsonBos = JsonUtils.parseList(entity.getEndMonthRecord(), EndMonthRecordDTO.class);
        }

        // 没有录入数据，也要展示录入的输入form
        List<EndMonthEntryResponse> dataList = Lists.newArrayListWithExpectedSize(4);
        List<EndMonthEntryResponse> valuesList = formatCollectByType(endMonthEntryJsonBos);

        EndMonthEntryResponse elec = buildFormNullElecData(reportData.getElecJson(), valuesList);
        EndMonthEntryResponse water = buildFormNullWaterData(reportData.getWaterJson(), valuesList);
        EndMonthEntryResponse gas = buildFormNullGasData(reportData.getGasJson(), valuesList);
        EndMonthEntryResponse steam = buildFormNullSteamData(reportData.getSteamJson(), valuesList);

        // 约定前端，如果都为空，就不做展示
        if (Objects.nonNull(elec) && (Objects.nonNull(elec.getBasePrice()) || Objects.nonNull(elec.getLossPrice())
            || CollectionUtils.isNotEmpty(elec.getSubList()))) {
            dataList.add(elec);
        }
        if (Objects.nonNull(water) && (Objects.nonNull(water.getBasePrice()) || Objects.nonNull(water.getLossPrice())
            || CollectionUtils.isNotEmpty(water.getSubList()) || CollectionUtils.isNotEmpty(water.getFireList()))) {
            dataList.add(water);
        }
        if (Objects.nonNull(gas) && (Objects.nonNull(gas.getBasePrice()) || Objects.nonNull(gas.getLossPrice())
            || CollectionUtils.isNotEmpty(gas.getSubList()))) {
            dataList.add(gas);
        }
        if (Objects.nonNull(steam) && (Objects.nonNull(steam.getBasePrice()) || Objects.nonNull(steam.getLossPrice())
            || CollectionUtils.isNotEmpty(steam.getSubList()))) {
            dataList.add(steam);
        }

        return dataList;
    }

    /**
     * 兼容补录逻辑，优先展示补录
     *
     * @param endMonthEntryJsonBos
     * @return
     */
    private List<EndMonthEntryResponse> formatCollectByType(List<EndMonthRecordDTO> endMonthEntryJsonBos) {

        // 类型分组
        Map<Integer, List<EndMonthRecordDTO>> collect = Safes.of(endMonthEntryJsonBos)
            .stream()
            .collect(Collectors.groupingBy(EndMonthRecordDTO::getMeterType));

        return collect.values()
            .stream()
            .map(x -> {

                // 补录，初始分组(map 2中类型，只有一条)
                Map<Integer, EndMonthRecordDTO> collect1 = x.stream()
                    .collect(Collectors.toMap(EndMonthRecordDTO::getRecordType, Function.identity()));

                EndMonthRecordDTO bo = new EndMonthRecordDTO();

                if (Safes.of(collect1)
                    .containsKey(EndMonthRecordTypeEnum.LATER.getValue())) {
                    bo = collect1.get(EndMonthRecordTypeEnum.LATER.getValue());
                } else if (Safes.of(collect1)
                    .containsKey(EndMonthRecordTypeEnum.INITIAL.getValue())) {
                    bo = collect1.get(EndMonthRecordTypeEnum.INITIAL.getValue());
                }

                // 处理json分表
                List<EndMonthEntrySubResponse> subCollect = Safes.of(bo.getSubList())
                    .stream()
                    .map(x1 -> EndMonthEntrySubResponse.builder()
                        .dosage(x1.getDosage())
                        .subNo(x1.getSubNo())
                        .name(x1.getName())
                        .placeId(x1.getPlaceId())
                        .build())
                    .collect(Collectors.toList());

                // 处理json消防水表
                List<EndMonthEntrySubResponse> fireCollect = Safes.of(bo.getFireList())
                    .stream()
                    .map(x2 -> EndMonthEntrySubResponse.builder()
                        .dosage(x2.getDosage())
                        .subNo(x2.getSubNo())
                        .name(x2.getName())
                        .placeId(x2.getPlaceId())
                        .build())
                    .collect(Collectors.toList());

                return EndMonthEntryResponse.builder()
                    .meterType(bo.getMeterType())
                    .subList(subCollect)
                    .fireList(fireCollect)
                    .basePrice(bo.getBasePrice())
                    .lossPrice(bo.getLossPrice())
                    .build();
            })
            .collect(Collectors.toList());
    }

    private ElectricityJson getELecByCurrtMonthDate(Integer chainId) {

        HotelBaseConfigEntity elecEntity = hotelBaseConfigService.query(HotelBaseConfigEntity.builder()
            .type(HotelBaseConfigTypeEnum.ELECTRIC_CONFIG.getValue())
            .chainId(chainId)
            .build());

        return Objects.nonNull(elecEntity) ?
            JsonUtils.parseObject(elecEntity.getHotelBaseConfig(), ElectricityJson.class) : null;
    }

    private WaterJson getWaterByCurrtMonthDate(Integer chainId) {

        HotelBaseConfigEntity waterEntity = hotelBaseConfigService.query(HotelBaseConfigEntity.builder()
            .type(HotelBaseConfigTypeEnum.WATER_CONFIG.getValue())
            .chainId(chainId)
            .build());

        return Objects.nonNull(waterEntity) ? JsonUtils.parseObject(waterEntity.getHotelBaseConfig(), WaterJson.class) :
            null;
    }

    private GasJson getGasByCurrtMonthDate(Integer chainId) {

        HotelBaseConfigEntity gasEntity = hotelBaseConfigService.query(HotelBaseConfigEntity.builder()
            .type(HotelBaseConfigTypeEnum.GAS_CONFIG.getValue())
            .chainId(chainId)
            .build());

        return Objects.nonNull(gasEntity) ? JsonUtils.parseObject(gasEntity.getHotelBaseConfig(), GasJson.class) : null;
    }

    private SteamJson getSteamByCurrtMonthDate(Integer chainId) {

        HotelBaseConfigEntity steamEntity = hotelBaseConfigService.query(HotelBaseConfigEntity.builder()
            .type(HotelBaseConfigTypeEnum.STEAM_CONFIG.getValue())
            .chainId(chainId)
            .build());

        return Objects.nonNull(steamEntity) ? JsonUtils.parseObject(steamEntity.getHotelBaseConfig(), SteamJson.class) :
            null;
    }

    private EndMonthEntryResponse buildFormNullElecData(ElectricityJson elecJson,
        List<EndMonthEntryResponse> endMonthEntryResponses) {

        if (Objects.isNull(elecJson)) {
            return null;
        }

        EndMonthEntryResponse endMonthEntryResponse = Safes.of(endMonthEntryResponses)
            .stream()
            .filter(f -> Objects.equals(f.getMeterType(), MeterTypeEnum.METER_ELEC.getValue()))
            .findFirst()
            .orElse(new EndMonthEntryResponse());

        // 过滤出电分表
        Map<Integer, EndMonthEntrySubResponse> valuesMap = Safes.of(endMonthEntryResponse.getSubList())
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubResponse::getSubNo, Function.identity()));

        return EndMonthEntryResponse.builder()
            .meterType(MeterTypeEnum.METER_ELEC.getValue())
            .subList(Safes.of(elecJson.getSubElectricityMeterList())
                .stream()
                // 月底录入只显示外部场地
                .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
                .map(x -> EndMonthEntrySubResponse.builder()
                    .name(x.getElectricityName())
                    .placeId(x.getPlaceId())
                    .subNo(x.getElectricityMeterNo())
                    // 赋值
                    .dosage(valuesMap.containsKey(x.getElectricityMeterNo()) ? valuesMap.get(x.getElectricityMeterNo())
                        .getDosage() : 0.0)
                    .build())
                .collect(Collectors.toList()))
            // 前端约定好，null不展示，0.0展示不回显
            .basePrice(Objects.nonNull(elecJson.getMonthAdditionalFee()) && elecJson.getMonthAdditionalFee()
                .getMonthBaseFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getBasePrice()) ? 0.0 : endMonthEntryResponse.getBasePrice()) :
                null)
            .lossPrice(Objects.nonNull(elecJson.getMonthAdditionalFee()) && elecJson.getMonthAdditionalFee()
                .getMonthWearFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getLossPrice()) ? 0.0 : endMonthEntryResponse.getLossPrice()) :
                null)
            .build();
    }

    private EndMonthEntryResponse buildFormNullWaterData(WaterJson waterJson,
        List<EndMonthEntryResponse> endMonthEntryResponses) {

        if (Objects.isNull(waterJson)) {
            return null;
        }

        EndMonthEntryResponse endMonthEntryResponse = Safes.of(endMonthEntryResponses)
            .stream()
            .filter(f -> Objects.equals(f.getMeterType(), MeterTypeEnum.METER_WATER.getValue()))
            .findFirst()
            .orElse(new EndMonthEntryResponse());

        // 过滤出水分表
        Map<Integer, EndMonthEntrySubResponse> valuesMap = Safes.of(endMonthEntryResponse.getSubList())
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubResponse::getSubNo, Function.identity()));

        // 过滤出消防表
        Map<Integer, EndMonthEntrySubResponse> frieValuesMap = Safes.of(endMonthEntryResponse.getFireList())
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubResponse::getSubNo, Function.identity()));

        return EndMonthEntryResponse.builder()
            .meterType(MeterTypeEnum.METER_WATER.getValue())
            .subList(Safes.of(waterJson.getSubWaterMeterJsons())
                .stream()
                // 月底录入只显示外部场地
                .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
                .map(x -> EndMonthEntrySubResponse.builder()
                    .name(x.getWaterName())
                    .subNo(x.getWaterMeterNo())
                    .placeId(x.getPlaceId())
                    // 赋值
                    .dosage(valuesMap.containsKey(x.getWaterMeterNo()) ? valuesMap.get(x.getWaterMeterNo())
                        .getDosage() : 0.0)
                    .build())
                .collect(Collectors.toList()))
            .basePrice(Objects.nonNull(waterJson.getMonthAdditionalFee()) && waterJson.getMonthAdditionalFee()
                .getMonthBaseFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getBasePrice()) ? 0.0 : endMonthEntryResponse.getBasePrice()) :
                null)
            .lossPrice(Objects.nonNull(waterJson.getMonthAdditionalFee()) && waterJson.getMonthAdditionalFee()
                .getMonthWearFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getLossPrice()) ? 0.0 : endMonthEntryResponse.getLossPrice()) :
                null)
            // 消防水表
            .fireList(Safes.of(waterJson.getFireWaterMeters())
                .stream()
                .map(x -> EndMonthEntrySubResponse.builder()
                    .name(x.getWaterName())
                    .subNo(x.getWaterMeterNo())
                    // 赋值
                    .dosage(frieValuesMap.containsKey(x.getWaterMeterNo()) ? frieValuesMap.get(x.getWaterMeterNo())
                        .getDosage() : 0.0)
                    .build())
                .collect(Collectors.toList()))
            .build();
    }

    private EndMonthEntryResponse buildFormNullGasData(GasJson gasJson,
        List<EndMonthEntryResponse> endMonthEntryResponses) {

        if (Objects.isNull(gasJson)) {
            return null;
        }

        EndMonthEntryResponse endMonthEntryResponse = Safes.of(endMonthEntryResponses)
            .stream()
            .filter(f -> Objects.equals(f.getMeterType(), MeterTypeEnum.METER_GAS.getValue()))
            .findFirst()
            .orElse(new EndMonthEntryResponse());

        // 过滤出燃气分表
        Map<Integer, EndMonthEntrySubResponse> valuesMap = Safes.of(endMonthEntryResponse.getSubList())
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubResponse::getSubNo, Function.identity()));

        return EndMonthEntryResponse.builder()
            .meterType(MeterTypeEnum.METER_GAS.getValue())
            .subList(Safes.of(gasJson.getSubGasMeterJsons())
                .stream()
                // 月底录入只显示外部场地
                .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
                .map(x -> EndMonthEntrySubResponse.builder()
                    .name(x.getGasName())
                    .subNo(x.getGasMeterNo())
                    .placeId(x.getPlaceId())
                    // 赋值
                    .dosage(valuesMap.containsKey(x.getGasMeterNo()) ? valuesMap.get(x.getGasMeterNo())
                        .getDosage() : 0.0)
                    .build())
                .collect(Collectors.toList()))
            // 前端约定好，null不展示，0.0展示不回显
            .basePrice(Objects.nonNull(gasJson.getMonthAdditionalFee()) && gasJson.getMonthAdditionalFee()
                .getMonthBaseFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getBasePrice()) ? 0.0 : endMonthEntryResponse.getBasePrice()) :
                null)
            .lossPrice(Objects.nonNull(gasJson.getMonthAdditionalFee()) && gasJson.getMonthAdditionalFee()
                .getMonthWearFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getLossPrice()) ? 0.0 : endMonthEntryResponse.getLossPrice()) :
                null)
            .build();
    }

    private EndMonthEntryResponse buildFormNullSteamData(SteamJson steamJson,
        List<EndMonthEntryResponse> endMonthEntryResponses) {

        if (Objects.isNull(steamJson)) {
            return null;
        }

        EndMonthEntryResponse endMonthEntryResponse = Safes.of(endMonthEntryResponses)
            .stream()
            .filter(f -> Objects.equals(f.getMeterType(), MeterTypeEnum.METER_STEAM.getValue()))
            .findFirst()
            .orElse(new EndMonthEntryResponse());

        // 过滤出蒸汽分表
        Map<Integer, EndMonthEntrySubResponse> valuesMap = Safes.of(endMonthEntryResponse.getSubList())
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubResponse::getSubNo, Function.identity()));

        return EndMonthEntryResponse.builder()
            .meterType(MeterTypeEnum.METER_STEAM.getValue())
            .subList(Safes.of(steamJson.getSubSteamMeterJsons())
                .stream()
                // 月底录入只显示外部场地
                .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
                .map(x -> EndMonthEntrySubResponse.builder()
                    .name(x.getSteamName())
                    .subNo(x.getSteamMeterNo())
                    .placeId(x.getPlaceId())
                    // 赋值
                    .dosage(valuesMap.containsKey(x.getSteamMeterNo()) ? valuesMap.get(x.getSteamMeterNo())
                        .getDosage() : 0.0)
                    .build())
                .collect(Collectors.toList()))
            // 前端约定好，null不展示，0.0展示不回显
            .basePrice(Objects.nonNull(steamJson.getMonthAdditionalFee()) && steamJson.getMonthAdditionalFee()
                .getMonthBaseFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getBasePrice()) ? 0.0 : endMonthEntryResponse.getBasePrice()) :
                null)
            .lossPrice(Objects.nonNull(steamJson.getMonthAdditionalFee()) && steamJson.getMonthAdditionalFee()
                .getMonthWearFee() ?
                // 如果月底录入没数据也要展示给前端所以是0.0，录入了就展示录入的值
                (Objects.isNull(endMonthEntryResponse.getLossPrice()) ? 0.0 : endMonthEntryResponse.getLossPrice()) :
                null)
            .build();
    }

    private void checkParam(String accMonth, LocalDate localDate) {
        BizPreconditions.checkArgument(StringUtils.isNotEmpty(accMonth), "日期不能为空");
        String[] accYearMonth = accMonth.split("-");
        BizPreconditions.checkArgument(accYearMonth.length == 2, "日期格式不正确");
        BizPreconditions.checkArgument(NumberUtils.isNumber(accYearMonth[0]), "日期格式不正确(yyyy-MM)");
        int month = Integer.parseInt(accYearMonth[1]);
        BizPreconditions.checkArgument(NumberUtils.isNumber(accYearMonth[1]) && (0 < month && month <= 31),
            "日期格式不正确(yyyy-MM)");

        localDate = localDate.plusMonths(1);
        LocalDate yearMonth = LocalDate.of(Integer.parseInt(accYearMonth[0]), month, 1);

        BizPreconditions.checkArgument(yearMonth.isBefore(localDate), "只能筛选本月及本月之前的月份");
    }

    /**
     * 是否展示tab
     * true=展示，false不展示
     *
     * @param chainId
     * @return
     */
    public Boolean queryEndMonthTab(Integer chainId, LocalDate accDate) {

        LocalDate currentAccLocalDate = paramService.getCurrentAccLocalDate(chainId);

        ElectricityJson elecJson = null;
        WaterJson waterJson = null;
        GasJson gasJson = null;
        SteamJson steamJson = null;

        if (accDate.isEqual(currentAccLocalDate)) {
            // 取实时
            List<HotelBaseConfigEntity> hotelBaseConfigEntityList = hotelBaseConfigMapper.selectByChainId(chainId);

            HotelBaseConfigEntity elecObj = Safes.of(hotelBaseConfigEntityList)
                .stream()
                .filter(f -> Objects.equals(f.getType(), HotelBaseConfigTypeEnum.ELECTRIC_CONFIG.getValue()))
                .findFirst()
                .orElse(null);
            HotelBaseConfigEntity waterObj = Safes.of(hotelBaseConfigEntityList)
                .stream()
                .filter(f -> Objects.equals(f.getType(), HotelBaseConfigTypeEnum.WATER_CONFIG.getValue()))
                .findFirst()
                .orElse(null);
            HotelBaseConfigEntity gasObj = Safes.of(hotelBaseConfigEntityList)
                .stream()
                .filter(f -> Objects.equals(f.getType(), HotelBaseConfigTypeEnum.GAS_CONFIG.getValue()))
                .findFirst()
                .orElse(null);
            HotelBaseConfigEntity steamObj = Safes.of(hotelBaseConfigEntityList)
                .stream()
                .filter(f -> Objects.equals(f.getType(), HotelBaseConfigTypeEnum.STEAM_CONFIG.getValue()))
                .findFirst()
                .orElse(null);

            elecJson =
                JsonUtils.parseObject(Objects.nonNull(elecObj) ? elecObj.getHotelBaseConfig() : StringUtils.EMPTY,
                    ElectricityJson.class);

            waterJson =
                JsonUtils.parseObject(Objects.nonNull(waterObj) ? waterObj.getHotelBaseConfig() : StringUtils.EMPTY,
                    WaterJson.class);

            gasJson = JsonUtils.parseObject(Objects.nonNull(gasObj) ? gasObj.getHotelBaseConfig() : StringUtils.EMPTY,
                GasJson.class);

            steamJson =
                JsonUtils.parseObject(Objects.nonNull(steamObj) ? steamObj.getHotelBaseConfig() : StringUtils.EMPTY,
                    SteamJson.class);

        } else {

            // 从备份里，查询其他配置
            ReportCalRecordEntity reportCalRecordEntity = reportCalRecordMapper.selectByAccDate(
                ReportCalRecordEntity.builder()
                    .chainId(chainId)
                    .accDate(accDate)
                    .build());

            if (Objects.isNull(reportCalRecordEntity)) {
                log.error("备份数据获取为空，不展示 chainId = {},accDate = {}", chainId, accDate);
                return false;
            }

            HotelConfigSnapshotJsonBo hotelConfigSnapshotJsonBo;

            try {

                hotelConfigSnapshotJsonBo =
                    JsonUtils.parseObject(reportCalRecordEntity.getHotelBaseConfig(), HotelConfigSnapshotJsonBo.class);

                if (Objects.isNull(hotelConfigSnapshotJsonBo)) {
                    log.error("备份数据获取为空，不展示 chainId = {},accDate = {}", chainId, accDate);
                    return false;
                }

                elecJson = hotelConfigSnapshotJsonBo.getElecJson();
                waterJson = hotelConfigSnapshotJsonBo.getWaterJson();
                gasJson = hotelConfigSnapshotJsonBo.getGasJson();
                steamJson = hotelConfigSnapshotJsonBo.getSteamJson();

            } catch (JsonException e) {
                log.error("queryOutSourcingTab json error chainId = {}", chainId);
            }

        }

        // 只要有一个配置了，就是true就会展示，本酒店有外部分表配置或月度基础费用或本月损耗费用，才呈现此tab
        return isHasElec(elecJson) || isHasWater(waterJson) || isHasGas(gasJson) || isHasSteam(steamJson);
    }

    private Boolean isHasElec(ElectricityJson electricityJson) {

        if (Objects.isNull(electricityJson)) {
            return false;
        }
        // 月底录入只关心外部场地
        List<SubElectricityMeterJson> collect = Safes.of(electricityJson.getSubElectricityMeterList())
            .stream()
            .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
            .collect(Collectors.toList());

        return (Objects.nonNull(electricityJson) && Objects.nonNull(electricityJson.getMonthAdditionalFee()) && (
            electricityJson.getMonthAdditionalFee()
                .getMonthBaseFee() || electricityJson.getMonthAdditionalFee()
                .getMonthWearFee())) || (Objects.nonNull(electricityJson) && CollectionUtils.isNotEmpty(collect));
    }

    private Boolean isHasWater(WaterJson waterJson) {

        if (Objects.isNull(waterJson)) {
            return false;
        }
        // 月底录入只关心外部场地
        List<SubWaterMeterJson> collect = Safes.of(waterJson.getSubWaterMeterJsons())
            .stream()
            .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
            .collect(Collectors.toList());

        return (Objects.nonNull(waterJson) && Objects.nonNull(waterJson.getMonthAdditionalFee()) && (
            waterJson.getMonthAdditionalFee()
                .getMonthBaseFee() || waterJson.getMonthAdditionalFee()
                .getMonthWearFee())) || (Objects.nonNull(waterJson) && CollectionUtils.isNotEmpty(collect)) || (
            Objects.nonNull(waterJson) && CollectionUtils.isNotEmpty(waterJson.getFireWaterMeters()));
    }

    private Boolean isHasGas(GasJson gasJson) {

        if (Objects.isNull(gasJson)) {
            return false;
        }
        // 月底录入只关心外部场地
        List<SubGasMeterJson> collect = Safes.of(gasJson.getSubGasMeterJsons())
            .stream()
            .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
            .collect(Collectors.toList());

        return (Objects.nonNull(gasJson) && Objects.nonNull(gasJson.getMonthAdditionalFee()) && (
            gasJson.getMonthAdditionalFee()
                .getMonthBaseFee() || gasJson.getMonthAdditionalFee()
                .getMonthWearFee())) || (Objects.nonNull(gasJson) && CollectionUtils.isNotEmpty(collect));
    }

    private Boolean isHasSteam(SteamJson steamJson) {

        if (Objects.isNull(steamJson)) {
            return false;
        }
        // 月底录入只关心外部场地
        List<SubSteamMeterJson> collect = Safes.of(steamJson.getSubSteamMeterJsons())
            .stream()
            .filter(f -> Objects.equals(HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue(), f.getPlaceType()))
            .collect(Collectors.toList());

        return (Objects.nonNull(steamJson) && Objects.nonNull(steamJson.getMonthAdditionalFee()) && (
            steamJson.getMonthAdditionalFee()
                .getMonthBaseFee() || steamJson.getMonthAdditionalFee()
                .getMonthWearFee())) || (Objects.nonNull(steamJson) && CollectionUtils.isNotEmpty(collect));
    }

    public void updateEndMonthEntry(EndMonthEntryRequest request) {
        // 获取当天最新的月底录入数据
        YearMonth parse = YearMonth.parse(request.getAccMonth(), CommonUtils.YYYY_MM);
        LocalDate requestDate = LocalDate.of(parse.getYear(), parse.getMonth(), 1);

        this.checkUpdateRequest(request, requestDate);
        // 获取最新的月度录入数据
        List<EndMonthRecordMeterEntity> endMonthRecordMeterEntityList = endMonthRecordMeterMapper.selectBySelective(
            EndMonthRecordMeterEntity.builder()
                .chainId(request.getChainId())
                .recordMonth(request.getAccMonth())
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .build());
        EndMonthRecordMeterEntity entity = Safes.of(endMonthRecordMeterEntityList)
            .stream()
            .max(Comparator.comparing(EndMonthRecordMeterEntity::getCreateTime))
            .orElse(null);

        this.update(request, parse, entity);
        // 报表
        commonService.reloadOutPlaceReport(request.getChainId(), requestDate);
    }

    @Transactional(transactionManager = "energyTransactionManager", rollbackFor = Exception.class)
    public void update(EndMonthEntryRequest request, YearMonth parse, EndMonthRecordMeterEntity entity) {
        if (Objects.isNull(entity)) {
            // 新增
            EndMonthRecordMeterEntity saveEntity = EndMonthRecordMeterEntity.builder()
                .chainId(request.getChainId())
                .recordDate(LocalDate.of(parse.getYear(), parse.getMonth(), 1))
                .recordMonth(request.getAccMonth())
                .endMonthRecord(JsonUtils.toJson(buildJson(request)))
                .channel(EndMonthChannelEnum.CHANNEL_APP.getValue())
                .meterReadId(request.getUserId())
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .build();
            endMonthRecordMeterMapper.insertSelective(saveEntity);
        } else {

            List<EndMonthRecordDTO> endMonthRecordDTOS =
                JsonUtils.parseList(entity.getEndMonthRecord(), EndMonthRecordDTO.class);
            if (CollectionUtils.isEmpty(endMonthRecordDTOS)) {
                throw new BusinessException("数据解析异常，修改数据失败！");
            }
            List<EndMonthEntrySubRequest> requestItems = request.getItems();

            Map<Integer, EndMonthEntrySubRequest> requestMap = requestItems.stream()
                .collect(Collectors.toMap(EndMonthEntrySubRequest::getMeterType, Function.identity(), (k1, k2) -> k1));

            Map<Integer, EndMonthRecordDTO> endMonthRecordDTOMap = endMonthRecordDTOS.stream()
                .collect(Collectors.toMap(EndMonthRecordDTO::getMeterType, Function.identity(), (k1, k2) -> k1));

            endMonthRecordDTOMap.values()
                .forEach(f -> {
                    if (requestMap.containsKey(f.getMeterType())) {
                        EndMonthEntrySubRequest endMonthEntrySubRequest = requestMap.get(f.getMeterType());
                        Safes.of(endMonthEntrySubRequest.getSubList())
                            .forEach(f1 -> f1.setMeterType(f.getMeterType()));
                        f.setBasePrice(endMonthEntrySubRequest.getBasePrice());
                        f.setLossPrice(endMonthEntrySubRequest.getLossPrice());
                        f.setSubList(endMonthEntrySubRequest.getSubList());
                        Safes.of(endMonthEntrySubRequest.getFireList())
                            .forEach(f1 -> f1.setMeterType(f.getMeterType()));
                        f.setFireList(endMonthEntrySubRequest.getFireList());
                        f.setMeterType(f.getMeterType());
                    }
                });

            entity.setEndMonthRecord(JsonUtils.toJson(endMonthRecordDTOMap.values()
                .stream()
                .collect(Collectors.toList())));
            endMonthRecordMeterMapper.updateByPrimaryKeySelective(entity);
        }

        // 没必要修改，直接新增一条
        dailyMeterRecordService.saveDailyMeterRecordForEndMonth(entity, true);
    }

    private void checkUpdateRequest(EndMonthEntryRequest request, LocalDate requestAccDate) {
        if (Objects.isNull(request)) {
            throw new BusinessException("尚未填写能耗数据，无法保存");
        }

        List<Integer> meterNoList = this.getRequestAllSubNo(request);

        // 限制一下相邻的一个月，不取全量
        Pair<LocalDate, LocalDate> lastAndNeatDate = commonService.getLastAndNextDate(requestAccDate);
        List<MeterReadDayReportEntity> meterList = dailyMeterRecordDao.selectBySelective(
            MeterReadDayReportEntity.builder()
                .chainId(request.getChainId())
                .meterNoList(meterNoList)
                .type(MeterTypeEnum.METER_ENDMONTH.getValue())
                .startMeterReadDate(lastAndNeatDate.getLeft())
                .endMeterReadDate(lastAndNeatDate.getRight())
                .build());

        // 校验分表
        List<EndMonthEntrySubRequest> requestItems = request.getItems();
        long timestamp = requestAccDate.atStartOfDay(ZoneOffset.ofHours(8))
            .toInstant()
            .toEpochMilli();

        requestItems.forEach(f -> {
            switch (MeterTypeEnum.getInstance(f.getMeterType())) {
                case METER_ELEC:
                    f.getSubList()
                        .stream()
                        .forEach(f1 -> commonService.checkElecEveryDayNewRecord(
                            this.getListByMeter(meterList, MeterTypeEnum.METER_ELEC), request.getChainId(), timestamp,
                            f1.getDosage(), null, f1.getSubNo(), TableTypeEnum.SUB, null, null, true));
                    break;
                case METER_WATER:
                    f.getSubList()
                        .stream()
                        .forEach(f1 -> commonService.checkEveryDayNewRecord(MeterTypeEnum.METER_WATER,
                            this.getListByMeter(meterList, MeterTypeEnum.METER_WATER), request.getChainId(), timestamp,
                            f1.getDosage(), f1.getSubNo(), TableTypeEnum.SUB, true));
                    f.getFireList()
                        .stream()
                        .forEach(f1 -> commonService.checkEveryDayNewRecord(MeterTypeEnum.METER_WATER,
                            this.getListByMeter(meterList, MeterTypeEnum.METER_WATER), request.getChainId(), timestamp,
                            f1.getDosage(), f1.getSubNo(), TableTypeEnum.SUB, true));
                    break;
                case METER_STEAM:
                    f.getSubList()
                        .stream()
                        .forEach(f1 -> commonService.checkEveryDayNewRecord(MeterTypeEnum.METER_STEAM,
                            this.getListByMeter(meterList, MeterTypeEnum.METER_STEAM), request.getChainId(), timestamp,
                            f1.getDosage(), f1.getSubNo(), TableTypeEnum.SUB, true));
                    break;
                case METER_GAS:
                    f.getSubList()
                        .stream()
                        .forEach(f1 -> commonService.checkEveryDayNewRecord(MeterTypeEnum.METER_STEAM,
                            this.getListByMeter(meterList, MeterTypeEnum.METER_GAS), request.getChainId(), timestamp,
                            f1.getDosage(), f1.getSubNo(), TableTypeEnum.SUB, true));
                    break;
                default:
            }
        });
    }

    /**
     * 获取request中所有的分表no
     *
     * @param request
     * @return
     */
    private List<Integer> getRequestAllSubNo(EndMonthEntryRequest request) {
        List<Integer> meterNoList = Lists.newArrayList();
        List<EndMonthEntrySubRequest> requestItems = request.getItems();

        meterNoList.addAll(requestItems.stream()
            .flatMap(f -> Safes.of(f.getSubList())
                .stream()
                .map(EndMonthEntrySubListRequest::getSubNo)
                .collect(Collectors.toList())
                .stream())
            .collect(Collectors.toList()));

        meterNoList.addAll(requestItems.stream()
            .flatMap(f -> Safes.of(f.getFireList())
                .stream()
                .map(EndMonthEntrySubListRequest::getSubNo)
                .collect(Collectors.toList())
                .stream())
            .collect(Collectors.toList()));

        return meterNoList;
    }

    private List<MeterReadDayReportEntity> getListByMeter(List<MeterReadDayReportEntity> meterReadDayReportEntityList,
        MeterTypeEnum meterTypeEnum) {
        Map<Integer, List<MeterReadDayReportEntity>> collect = Safes.of(meterReadDayReportEntityList)
            .stream()
            .collect(Collectors.groupingBy(MeterReadDayReportEntity::getTableType));
        if (collect.containsKey(meterTypeEnum.getValue())) {
            return collect.get(meterTypeEnum.getValue());
        }
        return null;
    }

    private void checkEndMonthSaveRequest(EndMonthEntryRequest request) {
        if (Objects.isNull(request)) {
            throw new BusinessException("参数为空，保存异常");
        }
        YearMonth yearMonth = YearMonth.parse(request.getAccMonth());
        // 上月
        YearMonth lastMonth = yearMonth.minusMonths(1);
        LocalDate of = LocalDate.of(lastMonth.getYear(), lastMonth.getMonth(), 1);
        // 获取月底录入数据，包含所有类型
        List<EndMonthRecordMeterEntity> endMonthList = endMonthRecordMeterMapper.selectBySelective(
            EndMonthRecordMeterEntity.builder()
                .chainId(request.getChainId())
                .recordMonth(of.getYear() + "-" + CommonHelp.getMonthStr(of))
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .build());

        // 如果1个月内多次填写，那么以最后一次作为最终读数
        EndMonthRecordMeterEntity entity = Safes.of(endMonthList)
            .stream()
            .max(Comparator.comparing(EndMonthRecordMeterEntity::getCreateTime))
            .orElse(null);

        List<EndMonthEntrySubRequest> requestItems = request.getItems();
        if (Objects.nonNull(entity)) {
            // 拿到数据，解析json
            List<EndMonthRecordDTO> endMonthEntryJsonBos =
                JsonUtils.parseList(entity.getEndMonthRecord(), EndMonthRecordDTO.class);
            requestItems.forEach(x -> this.doCheckEndMontRequest(x.getMeterType(), x.getSubList(), x.getFireList(),
                endMonthEntryJsonBos));
        } else {
            requestItems.forEach(
                x -> this.doCheckEndMonthInitRequest(request, of, x.getMeterType(), x.getSubList(), x.getFireList()));
        }
    }

    private void doCheckEndMontRequest(Integer meterType, List<EndMonthEntrySubListRequest> subList,
        List<EndMonthEntrySubListRequest> fireList, List<EndMonthRecordDTO> endMonthEntryJsonBos) {

        Map<Integer, List<EndMonthEntrySubListRequest>> subMap = endMonthEntryJsonBos.stream()
            .collect(Collectors.toMap(EndMonthRecordDTO::getMeterType, EndMonthRecordDTO::getSubList, (k1, k2) -> k2));

        List<EndMonthEntrySubListRequest> list = Safes.of(subMap.get(meterType));
        Map<Integer, Double> subNoMaps = Safes.of(list)
            .stream()
            .collect(Collectors.toMap(EndMonthEntrySubListRequest::getSubNo, EndMonthEntrySubListRequest::getDosage,
                (k1, k2) -> k2));

        if (Safes.of(subList)
            .stream()
            .anyMatch(request -> request.getDosage() < Safes.of(subNoMaps.get(request.getSubNo())))) {
            throw new BusinessException("保存失败，当前用量小于上一次输入");
        }

        if (Objects.equals(MeterTypeEnum.METER_WATER.getValue(), meterType)) {
            Map<Integer, List<EndMonthEntrySubListRequest>> fireMap = endMonthEntryJsonBos.stream()
                .filter(f -> CollectionUtils.isNotEmpty(f.getFireList()))
                .collect(
                    Collectors.toMap(EndMonthRecordDTO::getMeterType, EndMonthRecordDTO::getFireList, (k1, k2) -> k2));
            List<EndMonthEntrySubListRequest> fireMeterList = Safes.of(fireMap.get(meterType));
            Map<Integer, Double> fireNoMaps = Safes.of(fireMeterList)
                .stream()
                .collect(Collectors.toMap(EndMonthEntrySubListRequest::getSubNo, EndMonthEntrySubListRequest::getDosage,
                    (k1, k2) -> k2));
            if (Safes.of(fireList)
                .stream()
                .anyMatch(request -> request.getDosage() < Safes.of(fireNoMaps.get(request.getSubNo())))) {
                throw new BusinessException("保存失败，当前用量小于上一次输入");
            }
        }
    }

    private void doCheckEndMonthInitRequest(EndMonthEntryRequest request, LocalDate of, Integer meterType,
        List<EndMonthEntrySubListRequest> subList, List<EndMonthEntrySubListRequest> fireList) {
        Date startDate = CommonHelp.localDate2Date(of);
        Date endDate = DateUtil.getLastDayTimeOfMonth(startDate);
        //查询快照表
        List<ReportCalRecordEntity> recordList =
            reportCalRecordMapper.selectBetweenAccDate(request.getChainId(), startDate, endDate);
        ReportCalRecordEntity entity = Safes.of(recordList)
            .stream()
            .max(Comparator.comparing(ReportCalRecordEntity::getAccDate))
            .orElse(null);
        if (Objects.isNull(entity)) {
            return;
        }
        HotelConfigSnapshotJsonBo reportCalRecord =
            JsonUtils.parseObject(entity.getHotelBaseConfig(), HotelConfigSnapshotJsonBo.class);
        if (Safes.of(subList)
            .stream()
            .anyMatch(req -> {
                Double initRead = NumberUtils.DOUBLE_ZERO;
                if (Objects.equals(MeterTypeEnum.METER_ELEC.getValue(), meterType)) {
                    initRead =
                        commonService.getInitReadByElec(TableTypeEnum.SUB, req.getSubNo(), null, reportCalRecord, null);
                } else if (Objects.equals(MeterTypeEnum.METER_WATER.getValue(), meterType)) {
                    initRead = commonService.getInitReadByWater(TableTypeEnum.SUB, req.getSubNo(), reportCalRecord);
                } else if (Objects.equals(MeterTypeEnum.METER_STEAM.getValue(), meterType)) {
                    initRead = commonService.getInitReadBySteam(TableTypeEnum.SUB, req.getSubNo(), reportCalRecord);
                } else if (Objects.equals(MeterTypeEnum.METER_GAS.getValue(), meterType)) {
                    initRead = commonService.getInitReadByGas(TableTypeEnum.SUB, req.getSubNo(), reportCalRecord);
                }
                return req.getDosage() < initRead;
            })) {
            throw new BusinessException("保存失败，当前用量小于上一次输入");
        }
        if (Safes.of(fireList)
            .stream()
            .anyMatch(req -> req.getDosage() < Safes.of(
                commonService.getInitReadByWater(TableTypeEnum.SUB, req.getSubNo(), reportCalRecord)))) {
            throw new BusinessException("保存失败，当前用量小于上一次输入");
        }
    }

}
