package com.atour.hotel.module.energy.web.bo;

import com.atour.hotel.framework.annotation.FieldMetaCN;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2019/9/6
 * 酒店配置存的场地冗余字段给日志用
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SavePlaceJsonBo {
    /**
     * 场地id
     */
    @FieldMetaCN(desc = "场地id")
    private Integer placeId;
    /**
     * 场地名称
     */
    @FieldMetaCN(desc = "场地名称")
    private String name;
    /**
     * 面积
     */
    @FieldMetaCN(desc = "场地面积")
    private Double area;
    /**
     * 固定电费
     */
    @FieldMetaCN(desc = "固定电费")
    private BigDecimal elecPrice;
    /**
     * 固定水费
     */
    @FieldMetaCN(desc = "固定水费")
    private BigDecimal waterPrice;
    /**
     * 固定供暖费
     */
    @FieldMetaCN(desc = "固定供暖费")
    private BigDecimal heatingPrice;
    /**
     * 开始时间
     */
    @FieldMetaCN(desc = "电费开始时间")
    private LocalDate elecStartDate;
    /**
     * 开始时间
     */
    @FieldMetaCN(desc = "水费开始时间")
    private LocalDate waterStartDate;
}
