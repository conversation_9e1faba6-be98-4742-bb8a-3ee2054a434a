package com.atour.hotel.module.energy.job.service;

import com.atour.dicts.enums.energy.ElectricityFeeTypeEnum;
import com.atour.dicts.enums.energy.MeterTypeEnum;
import com.atour.dicts.enums.energy.TableTypeEnum;
import com.atour.hotel.common.enums.DeleteFlagEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.energy.app.bo.OutSourcingJsonBo;
import com.atour.hotel.module.energy.app.dto.ElectricityMeterRecordListDTO;
import com.atour.hotel.module.energy.app.dto.ElectricityTotalMeterDTO;
import com.atour.hotel.module.energy.app.dto.GasMeterRecordListDTO;
import com.atour.hotel.module.energy.app.dto.SteamMeterRecordListDTO;
import com.atour.hotel.module.energy.app.dto.WaterMeterRecordListDTO;
import com.atour.hotel.module.energy.job.bo.EndMonthMeterReadNumJson;
import com.atour.hotel.module.energy.web.bo.dailymeterrecord.ElecMeterReadNumJson;
import com.atour.hotel.module.energy.web.bo.dailymeterrecord.OutSourcingMeterReadNumJson;
import com.atour.hotel.module.energy.web.dto.EndMonthRecordDTO;
import com.atour.hotel.module.user.service.SysUserService;
import com.atour.hotel.persistent.energy.dao.DailyMeterRecordDao;
import com.atour.hotel.persistent.energy.dao.HotelPlaceConfigDao;
import com.atour.hotel.persistent.energy.entity.ElectricityMeterEntity;
import com.atour.hotel.persistent.energy.entity.EndMonthRecordMeterEntity;
import com.atour.hotel.persistent.energy.entity.GasMeterEntity;
import com.atour.hotel.persistent.energy.entity.HotelPlaceConfigEntity;
import com.atour.hotel.persistent.energy.entity.MeterReadDayReportEntity;
import com.atour.hotel.persistent.energy.entity.OutBuyMeterEntity;
import com.atour.hotel.persistent.energy.entity.SteamMeterEntity;
import com.atour.hotel.persistent.energy.entity.WaterMeterEntity;
import com.atour.monitor.AMonitor;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.gson.JsonParseException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/16
 * 每日抄表记录，实时
 */
@Log4j2
@Service("JobDailyMeterRecordService")
public class DailyMeterRecordService {

    @Resource
    private ParamService paramService;
    @Resource
    private DailyMeterRecordDao dailyMeterRecordDao;
    @Resource
    private HotelPlaceConfigDao hotelPlaceConfigDao;
    @Resource
    private SysUserService sysUserService;

    public void saveDailyMeterRecordForElec(ElectricityMeterEntity entity, Boolean isupdate) {
        if (Objects.isNull(entity)) {
            log.error("保存实体为空");
            return;
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);
            Integer areaCode = paramService.getRegionByChain(entity.getChainId());

            ElectricityMeterRecordListDTO dto =
                    JsonUtils.parseObject(entity.getElectricity(), ElectricityMeterRecordListDTO.class);

            if (Objects.nonNull(dto)) {
                List<MeterReadDayReportEntity> saveList = Lists.newArrayList();
                saveList.addAll(Safes.of(dto.getTotalMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 电表
                                .type(MeterTypeEnum.METER_ELEC.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(getElecMeterNumberByType(x.getPriceType(), x).getReadNum())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(getElecMeterNumberByType(x.getPriceType(), x)))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.TOTAL.getValue())
                                // 区域
                                .areaCode(areaCode)
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                AMonitor.timer("api_app_energy_electricity_saveDailyMeterRecord_List", stopwatch.elapsed(TimeUnit.MILLISECONDS));

                saveList.addAll(Safes.of(dto.getSubMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 场地
                                .placeId(x.getPlaceId())
                                // 内部、外部
                                .placeType(getPlaceType(x.getPlaceId()))
                                // 电表
                                .type(MeterTypeEnum.METER_ELEC.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.SUB.getValue())
                                // 区域
                                .areaCode(areaCode)
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                AMonitor.timer("api_app_energy_electricity_saveDailyMeterRecord_ListEnd", stopwatch.elapsed(TimeUnit.MILLISECONDS));
                doBatchInsert(saveList);
            }
        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForElec json error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForElec.error");
            log.error("saveDailyMeterRecordForElec  error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } finally {
            AMonitor.timer("api_app_energy_electricity_saveDailyMeterRecord_Finish", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    private void doBatchInsert(List<MeterReadDayReportEntity> saveList) {
        if (CollectionUtils.isNotEmpty(saveList)) {
            // 批量插入
            dailyMeterRecordDao.batchInsert(saveList);
        }
    }

    public void saveDailyMeterRecordForWater(WaterMeterEntity entity, Boolean isupdate) {

        if (Objects.isNull(entity)) {
            log.error("保存实体为空");
            return;
        }

        try {
            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);

            WaterMeterRecordListDTO dto = JsonUtils.parseObject(entity.getWater(), WaterMeterRecordListDTO.class);

            if (Objects.nonNull(dto)) {

                List<MeterReadDayReportEntity> saveList = Lists.newArrayList();

                saveList.addAll(Safes.of(dto.getTotalMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 电表
                                .type(MeterTypeEnum.METER_WATER.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.TOTAL.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                saveList.addAll(Safes.of(dto.getSubMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 场地
                                .placeId(x.getPlaceId())
                                // 内部、外部
                                .placeType(getPlaceType(x.getPlaceId()))
                                // 电表
                                .type(MeterTypeEnum.METER_WATER.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.SUB.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

               doBatchInsert(saveList);
            }

        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForWater json error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForWater.error");
            log.error("saveDailyMeterRecordForWater error chainId = {},accDate = {} : ", entity.getChainId(), e);
        }

    }

    public void saveDailyMeterRecordForGas(GasMeterEntity entity, Boolean isupdate) {

        if (Objects.isNull(entity)) {
            log.error("保存实体为空");
            return;
        }

        try {
            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);

            GasMeterRecordListDTO dto = JsonUtils.parseObject(entity.getGas(), GasMeterRecordListDTO.class);

            if (Objects.nonNull(dto)) {

                List<MeterReadDayReportEntity> saveList = Lists.newArrayList();

                saveList.addAll(Safes.of(dto.getTotalMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 电表
                                .type(MeterTypeEnum.METER_GAS.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.TOTAL.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                saveList.addAll(Safes.of(dto.getSubMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 场地
                                .placeId(x.getPlaceId())
                                // 内部、外部
                                .placeType(getPlaceType(x.getPlaceId()))
                                // 电表
                                .type(MeterTypeEnum.METER_GAS.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.SUB.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                // 批量插入
                doBatchInsert(saveList);
            }

        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForGas json error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForGas.error");
            log.error("saveDailyMeterRecordForGas error chainId = {},accDate = {} : ", entity.getChainId(), e);
        }
    }

    public void saveDailyMeterRecordForSteam(SteamMeterEntity entity, Boolean isupdate) {

        if (Objects.isNull(entity)) {
            log.error("保存实体为空");
            return;
        }

        try {
            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);

            SteamMeterRecordListDTO dto = JsonUtils.parseObject(entity.getSteam(), SteamMeterRecordListDTO.class);

            if (Objects.nonNull(dto)) {

                List<MeterReadDayReportEntity> saveList = Lists.newArrayList();

                saveList.addAll(Safes.of(dto.getTotalMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 电表
                                .type(MeterTypeEnum.METER_STEAM.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.TOTAL.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                saveList.addAll(Safes.of(dto.getSubMeterList())
                        .stream()
                        .map(x -> MeterReadDayReportEntity.builder()
                                .chainId(entity.getChainId())
                                .meterReadDate(entity.getRecordDate())
                                .meterReadTime(LocalDateTime.now())
                                // 场地
                                .placeId(x.getPlaceId())
                                // 内部、外部
                                .placeType(getPlaceType(x.getPlaceId()))
                                // 电表
                                .type(MeterTypeEnum.METER_STEAM.getValue())
                                // 表名称
                                .tableName(x.getMeterName())
                                // 读数
                                .meterReadNum(x.getInitRead())
                                // 读数json
                                .subMeterReadNumJson(JsonUtils.toJson(x))
                                // 抄表人
                                .meterReadName(userName)
                                .meterReadId(currUserId)
                                // 总、分
                                .tableType(TableTypeEnum.SUB.getValue())
                                // 区域
                                .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                // 编号
                                .meterNo(x.getMeterNo())
                                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                .isUpdate(isupdate == true ? 1 : 0)
                                .build())
                        .collect(Collectors.toList()));

                // 批量插入
                doBatchInsert(saveList);
            }

        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForSteam json error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForSteam.error");
            log.error("saveDailyMeterRecordForSteam error chainId = {},accDate = {} : ", entity.getChainId(), e);
        }
    }

    public void saveDailyMeterRecordForOutBuy(OutBuyMeterEntity entity) {

        if (Objects.isNull(entity)) {
            log.error("保存实体为空");
            return;
        }

        try {
            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);

            List<OutSourcingJsonBo> outSourcingJsonBoList =
                    JsonUtils.parseList(entity.getOutBuy(), OutSourcingJsonBo.class);

            List<MeterReadDayReportEntity> saveList = Safes.of(outSourcingJsonBoList)
                    .stream()
                    .map(x -> MeterReadDayReportEntity.builder()
                            .chainId(entity.getChainId())
                            .areaCode(paramService.getRegionByChain(entity.getChainId()))
                            .meterReadDate(entity.getRecordDate())
                            .meterReadTime(LocalDateTime.now())
                            // 外购类型
                            .type(x.getType())
                            .tableName(MeterTypeEnum.getInstance(x.getType())
                                    .getName())
                            .meterReadNum(x.getCount())
                            // json
                            .subMeterReadNumJson(
                                    JsonUtils.toJson(this.getOutSourcingSubMeterReadNumJson(x.getCount(), x.getPrice())))
                            .meterReadName(userName)
                            .meterReadId(currUserId)
                            .isUpdate(0)
                            .deleteFlag(DeleteFlagEnum.VALID.getValue())
                            .build())
                    .collect(Collectors.toList());

            // 批量插入
            doBatchInsert(saveList);

        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForOutBuy json error chainId = {},accDate = {} : ", entity.getChainId(), e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForOutBuy.error");
            log.error("saveDailyMeterRecordForOutBuy error chainId = {},accDate = {} : ", entity.getChainId(), e);
        }

    }

    public void saveDailyMeterRecordForEndMonth(EndMonthRecordMeterEntity entity, Boolean isupdate) {
        try {

            if (Objects.isNull(entity)) {
                log.error("保存实体为空");
                return;
            }

            Integer currUserId = CommonParamsManager.getCurrUserId();
            String userName = sysUserService.getSysUserNameByUserId(currUserId);

            // 拿到数据，解析json
            List<EndMonthRecordDTO> endMonthRecordDTOS =
                    JsonUtils.parseList(entity.getEndMonthRecord(), EndMonthRecordDTO.class);

            if (CollectionUtils.isEmpty(endMonthRecordDTOS)) {
                return;
            }

            List<MeterReadDayReportEntity> saveList = Lists.newArrayList();

            // 分表
            saveList.addAll(endMonthRecordDTOS.stream()
                    .flatMap(x -> Safes.of(x.getSubList())
                            .stream()
                            .map(x1 -> MeterReadDayReportEntity.builder()
                                    .chainId(entity.getChainId())
                                    .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                    .meterReadDate(entity.getRecordDate())
                                    .meterReadTime(LocalDateTime.now())
                                    // 月底录入
                                    .type(MeterTypeEnum.METER_ENDMONTH.getValue())
                                    .tableName(x1.getName())
                                    .meterNo(x1.getSubNo())
                                    // 这里存的是区分，电、水、燃气、蒸汽
                                    .tableType(x.getMeterType())
                                    .placeId(x1.getPlaceId())
                                    // 内部、外部
                                    .placeType(getPlaceType(x1.getPlaceId()))
                                    .meterReadNum(x1.getDosage())
                                    // json,把基础和损耗存进json
                                    .subMeterReadNumJson(JsonUtils.toJson(EndMonthMeterReadNumJson.builder()
                                            .basePrice(x.getBasePrice())
                                            .lossPrice(x.getLossPrice())
                                            .build()))
                                    .meterReadName(userName)
                                    .meterReadId(currUserId)
                                    .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                    .isUpdate(isupdate == true ? 1 : 0)
                                    .build())
                            .collect(Collectors.toList())
                            .stream())
                    .collect(Collectors.toList()));

            // 消防
            saveList.addAll(endMonthRecordDTOS.stream()
                    .flatMap(x -> Safes.of(x.getFireList())
                            .stream()
                            .map(x1 -> MeterReadDayReportEntity.builder()
                                    .chainId(entity.getChainId())
                                    .areaCode(paramService.getRegionByChain(entity.getChainId()))
                                    .meterReadDate(entity.getRecordDate())
                                    .meterReadTime(LocalDateTime.now())
                                    // 月底录入
                                    .type(MeterTypeEnum.METER_ENDMONTH.getValue())
                                    .tableName(x1.getName())
                                    .meterNo(x1.getSubNo())
                                    // 这里存的是区分，电、水、燃气、蒸汽
                                    .tableType(x.getMeterType())
                                    .placeId(x1.getPlaceId())
                                    // 内部、外部
                                    .placeType(getPlaceType(x1.getPlaceId()))
                                    .meterReadNum(x1.getDosage())
                                    // json,把基础和损耗存进json
                                    .subMeterReadNumJson(JsonUtils.toJson(EndMonthMeterReadNumJson.builder()
                                            .basePrice(x.getBasePrice())
                                            .lossPrice(x.getLossPrice())
                                            .build()))
                                    .meterReadName(userName)
                                    .meterReadId(currUserId)
                                    .deleteFlag(DeleteFlagEnum.VALID.getValue())
                                    .isUpdate(isupdate == true ? 1 : 0)
                                    .build())
                            .collect(Collectors.toList())
                            .stream())
                    .collect(Collectors.toList()));

            // 批量插入
            doBatchInsert(saveList);

        } catch (JsonParseException e) {
            log.error("saveDailyMeterRecordForEndMonth json error chainId = {},accDate = {} : ", entity.getChainId(),
                    e);
        } catch (Exception e) {
            AMonitor.meter("DailyMeterRecordService.saveDailyMeterRecordForEndMonth.error");
            log.error("saveDailyMeterRecordForEndMonth error chainId = {},accDate = {} : ", entity.getChainId(), e);
        }
    }

    public ElecMeterReadNumJson getElecMeterNumberByType(Integer type, ElectricityTotalMeterDTO dto) {

        ElecMeterReadNumJson json = new ElecMeterReadNumJson();

        if (Objects.equals(type, ElectricityFeeTypeEnum.SINGLE_PRICE.getValue())) {
            // 单一
            json = ElecMeterReadNumJson.builder()
                    .readNum(dto.getInitRead())
                    .build();
        } else if (Objects.equals(type, ElectricityFeeTypeEnum.PEAK_VALLEY_FLAT_PRICE.getValue())) {
            // 峰谷平尖
            json = ElecMeterReadNumJson.builder()
                    .readNum(
                            Safes.of(dto.getFlatInitRead()) + Safes.of(dto.getPeakInitRead()) + Safes.of(dto.getTipInitRead())
                                    + Safes.of(dto.getValleyInitRead()))
                    .jian(dto.getTipInitRead())
                    .feng(dto.getPeakInitRead())
                    .ping(dto.getFlatInitRead())
                    .gu(dto.getValleyInitRead())
                    .build();
        }
        return json;
    }

    private OutSourcingMeterReadNumJson getOutSourcingSubMeterReadNumJson(Double count, BigDecimal price) {

        return OutSourcingMeterReadNumJson.builder()
                .count(count)
                .price(price)
                .build();
    }

    private Integer getPlaceType(Integer palceId) {
        HotelPlaceConfigEntity hotelPlaceConfigEntity = hotelPlaceConfigDao.selectByPrimaryKey(palceId);
        if (Objects.nonNull(hotelPlaceConfigEntity)) {
            return hotelPlaceConfigEntity.getPlaceType();
        }
        return 0;
    }

}
