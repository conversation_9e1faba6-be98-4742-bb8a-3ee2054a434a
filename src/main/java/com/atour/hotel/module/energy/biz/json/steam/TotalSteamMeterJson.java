package com.atour.hotel.module.energy.biz.json.steam;

import com.atour.hotel.framework.annotation.FieldMetaCN;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 业务层: 总蒸汽表json
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@ApiModel(value = "TotalSteamMeterJson", description = "总蒸汽表json")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TotalSteamMeterJson implements Serializable {

    private static final long serialVersionUID = 4931966927004732176L;

    /**
     * 蒸汽表序号
     */
    @FieldMetaCN(desc = "蒸汽表序号")
    @ApiModelProperty(value = "蒸汽表序号")
    private Integer steamMeterNo;

    /**
     * 蒸汽表名称
     */
    @FieldMetaCN(desc = "蒸汽表名称")
    @ApiModelProperty(value = "蒸汽表名称")
    private String steamName;

    /**
     * 初始读数
     */
    @FieldMetaCN(desc = "初始读数")
    @ApiModelProperty(value = "初始读数")
    private Double initRead;

    /**
     * 倍率
     */
    @FieldMetaCN(desc = "倍率")
    @ApiModelProperty(value = "倍率")
    private Double rate;
}
