package com.atour.hotel.module.energy.biz.json.water;

import com.atour.hotel.framework.annotation.FieldMetaCN;
import com.atour.hotel.framework.annotation.LogDiff;
import com.atour.hotel.module.energy.biz.json.electricity.MonthAdditionalFeeJson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 业务层: 水配置json数据
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@ApiModel(value = "WaterJson", description = "水配置json数据")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class WaterJson implements Serializable {

    private static final long serialVersionUID = -6125675943868965302L;

    /**
     * 总水价信息
     */
    @FieldMetaCN(desc = "总水价信息")
    @LogDiff(diffBy = "startDate")
    @ApiModelProperty(value = "总水价信息")
    private List<TotalWaterPriceJson> totalWaterPriceJsons;

    /**
     * 总水表信息
     */
    @FieldMetaCN(desc = "总水表信息")
    @LogDiff(diffBy = "waterMeterNo")
    @ApiModelProperty(value = "总水表信息")
    private List<TotalWaterMeterJson> totalWaterMeterJsons;

    /**
     * 分水表信息
     */
    @FieldMetaCN(desc = "分水表信息")
    @LogDiff(diffBy = "waterMeterNo")
    @ApiModelProperty(value = "分水表信息")
    private List<SubWaterMeterJson> subWaterMeterJsons;

    /**
     * 消防水表
     */
    @FieldMetaCN(desc = "消防水表")
    @LogDiff(diffBy = "waterMeterNo")
    @ApiModelProperty(value = "消防水表")
    private List<FireWaterMeter> fireWaterMeters;

    /**
     * 每月额外费用信息
     */
    @FieldMetaCN(desc = "每月额外费用信息", hasFile = true)
    @ApiModelProperty(value = "每月额外费用信息")
    private MonthAdditionalFeeJson monthAdditionalFee;
}
