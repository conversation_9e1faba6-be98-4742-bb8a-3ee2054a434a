package com.atour.hotel.module.energy.common.exception;

import java.text.MessageFormat;

/**
 * 业务逻辑异常类
 *
 * <AUTHOR>
 * @date 2018/5/13
 */
public class EnergyBusinessException extends RuntimeException {

    private int code;

    public EnergyBusinessException(String message, int code) {
        super(message);
        this.code = code;
    }

    public EnergyBusinessException(String message, Throwable cause, int code) {
        super(message, cause);
        this.code = code;
    }

    public EnergyBusinessException(String msgTemplate, Object... args) {
        super(MessageFormat.format(msgTemplate, args));
    }

    public EnergyBusinessException(Throwable cause, String msgTemplate, Object... args) {
        super(MessageFormat.format(msgTemplate, args), cause);
    }

    public EnergyBusinessException(String message) {
        super(message);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
