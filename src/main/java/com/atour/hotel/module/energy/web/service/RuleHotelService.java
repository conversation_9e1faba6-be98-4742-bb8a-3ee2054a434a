package com.atour.hotel.module.energy.web.service;

import com.atour.api.bean.ApiResult;
import com.atour.chain.api.chain.dto.AtourChainDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.hotel.common.enums.DeleteFlagEnum;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.energy.web.dto.HotelInfoDTO;
import com.atour.hotel.module.energy.web.request.RuleRelateHotelRequest;
import com.atour.hotel.module.energy.web.request.RuleRelateHotelUpdateRequest;
import com.atour.hotel.persistent.energy.dao.RuleHotelConfigMapper;
import com.atour.hotel.persistent.energy.entity.RuleHotelConfigEntity;
import com.atour.hotel.persistent.energy.param.RuleHotelQuery;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 预警规则关联酒店 service
 *
 * <AUTHOR>
 * @date 2019年7月26日17:16:13
 **/
@Slf4j
@Service
public class RuleHotelService {

    @Resource
    private RuleHotelConfigMapper ruleHotelConfigMapper;

    @Resource
    private ChainRemote chainRemote;

    /**
     * 获取酒店列表
     *
     * @return
     */
    public List<HotelInfoDTO> getAllChainList() {
        List<HotelInfoDTO> listResult = Lists.newArrayList();
        ApiResult<List<AtourChainDTO>> resultRemote = chainRemote.getAllChain();

        log.debug("--getAllChainList 调用chain-center 获取酒店列表 返回结果:{}", JsonUtils.toJson(resultRemote));
        if (Objects.isNull(resultRemote)) {
            log.error("--getAllChainList 调用chain-center 获取酒店列表 返回结果为空");
            throw new BusinessException(ResponseCodeEnum.SYSTEM_ERROR.getMessage(),
                ResponseCodeEnum.SYSTEM_ERROR.getCode());
        }
        if (!Objects.equals(resultRemote.getCode(), ResponseCodeEnum.SUCCESS.getCode())) {
            log.error("--getAllChainList 调用chain-center 获取酒店列表 返回结果异常");
            throw new BusinessException(ResponseCodeEnum.SYSTEM_ERROR.getMessage(),
                ResponseCodeEnum.SYSTEM_ERROR.getCode());
        }
        List<AtourChainDTO> chainDTOS = resultRemote.getResult();
        if (Objects.isNull(resultRemote.getResult())) {
            return listResult;
        }
        chainDTOS.forEach(chain -> {
            listResult.add(HotelInfoDTO.builder()
                .chainId(chain.getChainId())
                .chainName(chain.getName())
                .build());
        });

        return listResult;
    }

    /**
     * 获取已关联的酒店id列表
     *
     * @param request
     * @return
     */
    public List<Integer> getRelateHotelList(RuleRelateHotelRequest request) {
        List<Integer> listResult = Lists.newArrayList();
        List<RuleHotelConfigEntity> dataList = ruleHotelConfigMapper.getRuleHotelList(RuleHotelQuery.builder()
            .ruleConfigId(request.getRuleId())
            .deleteFlag(DeleteFlagEnum.VALID.getValue())
            .build());
        if (CollectionUtils.isEmpty(dataList)) {
            return listResult;
        }
        dataList.forEach(d -> listResult.add(d.getChainId()));
        return listResult;
    }

    /**
     * 更新预警关联酒店
     *
     * @param request
     * @return
     */
    @Transactional(transactionManager = "energyTransactionManager", rollbackFor = Exception.class)
    public void updateRuleRelateHotel(RuleRelateHotelUpdateRequest request) {

        //规则id
        Integer ruleId = request.getRuleId();
        //传入要关联的酒店id
        List<Integer> chainIds = request.getChainIdList();
        //已经关联的酒店
        List<RuleHotelConfigEntity> oldHotelIds = ruleHotelConfigMapper.getRuleHotelList(RuleHotelQuery.builder()
            .ruleConfigId(ruleId)
            .deleteFlag(DeleteFlagEnum.VALID.getValue())
            .build());
        //假删除状态 关联的酒店
        List<RuleHotelConfigEntity> deletedHotelIds = ruleHotelConfigMapper.getRuleHotelList(RuleHotelQuery.builder()
            .ruleConfigId(ruleId)
            .deleteFlag(DeleteFlagEnum.INVALID.getValue())
            .build());

        Set<Integer> newHotelIdSet = Sets.newHashSet(chainIds);
        Set<Integer> oldHotelIdSet = Sets.newHashSet(Safes.of(oldHotelIds)
            .stream()
            .map(RuleHotelConfigEntity::getChainId)
            .collect(Collectors.toList()));
        Set<Integer> deletedHotelIdSet = Sets.newHashSet(Safes.of(deletedHotelIds)
            .stream()
            .map(RuleHotelConfigEntity::getChainId)
            .collect(Collectors.toList()));

        //需要假删除的酒店id
        Sets.SetView<Integer> deletingHotelIdSet = Sets.difference(oldHotelIdSet, newHotelIdSet);
        //需要新增的酒店id
        Sets.SetView<Integer> addingHotelIdSet = Sets.difference(newHotelIdSet, oldHotelIdSet);
        //需要由假删除至成有效状态的酒店id
        Sets.SetView<Integer> activeHotelIdSet = Sets.intersection(newHotelIdSet, deletedHotelIdSet);

        addingHotelIdSet = Sets.difference(addingHotelIdSet, activeHotelIdSet);

        //假删除的酒店id
        List<Integer> deletingHotelIds = Lists.newArrayList(deletingHotelIdSet);
        if (CollectionUtils.isNotEmpty(deletingHotelIds)) {
            ruleHotelConfigMapper.batchUpdateByRuleId(RuleHotelQuery.builder()
                .ruleConfigId(ruleId)
                .deleteFlag(DeleteFlagEnum.INVALID.getValue())
                .chainIdList(deletingHotelIds)
                .build());
        }
        //需要新增的酒店id
        List<Integer> addingHotelIds = Lists.newArrayList(addingHotelIdSet);
        if (CollectionUtils.isNotEmpty(addingHotelIds)) {
            List<RuleHotelConfigEntity> mappingList = Lists.newArrayListWithExpectedSize(addingHotelIds.size());
            addingHotelIds.forEach(e -> mappingList.add(RuleHotelConfigEntity.builder()
                .ruleConfigId(ruleId)
                .chainId(e)
                .build()));
            ruleHotelConfigMapper.batchInsert(mappingList);
        }
        //由假删除至成有效状态的酒店id
        List<Integer> activeHotelIds = Lists.newArrayList(activeHotelIdSet);
        if (CollectionUtils.isNotEmpty(activeHotelIds)) {
            ruleHotelConfigMapper.batchUpdateByRuleId(RuleHotelQuery.builder()
                .ruleConfigId(ruleId)
                .deleteFlag(DeleteFlagEnum.VALID.getValue())
                .chainIdList(activeHotelIds)
                .build());
        }

    }
}
