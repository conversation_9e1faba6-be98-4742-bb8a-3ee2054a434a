package com.atour.hotel.module.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务层: 酒店操作日志查询响应结果
 *
 * <AUTHOR>
 * @date 2019-08-08
 */
@ApiModel(value = "酒店操作日志查询响应结果")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OptLogQueryDTO implements Serializable {

    private static final long serialVersionUID = -2138453542407022875L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作时间")
    private Date optDate;

    /**
     * 操作员名称
     */
    @ApiModelProperty(value = "操作员名称")
    private String optName;

    /**
     * 日志类型
     */
    @ApiModelProperty(value = "日志类型")
    private Integer logModelType;

    /**
     * 日志类型名称
     */
    @ApiModelProperty(value = "日志类型名称")
    private String logModelName;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String diffContent;
}
