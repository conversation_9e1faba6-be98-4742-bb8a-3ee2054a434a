package com.atour.hotel.module.energy.app.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * 蒸汽表抄表记录-总蒸汽表
 * <AUTHOR>
 * @date 2019年8月8日11:33:40
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SteamTotalMeterDTO implements Serializable {

	/**
	 * 蒸汽表序号
	 */
	@ApiModelProperty(value = "蒸汽表序号")
	private Integer meterNo;

	/**
	 * 蒸汽表名称
	 */
	@ApiModelProperty(value = "蒸汽表名称")
	private String meterName;

	/**
	 * 读数
	 */
	@ApiModelProperty(value = "读数")
	private Double initRead;

}
