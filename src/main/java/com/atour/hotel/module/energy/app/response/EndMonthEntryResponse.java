package com.atour.hotel.module.energy.app.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EndMonthEntryResponse {
    /**
     * 类型 EndMonthEntrySubTypeEnum
     */
    private Integer meterType;
    /**
     * 外部分表
     */
    private List<EndMonthEntrySubResponse> subList;
    /**
     * 消防表
     */
    private List<EndMonthEntrySubResponse> fireList;
    /**
     * 基础
     */
    private Double basePrice;
    /**
     * 损耗
     */
    private Double lossPrice;
}
