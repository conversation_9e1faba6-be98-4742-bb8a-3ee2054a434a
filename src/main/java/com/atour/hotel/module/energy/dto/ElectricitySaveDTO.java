package com.atour.hotel.module.energy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 业务层: 电力配置保存响应结果
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ElectricitySaveDTO implements Serializable {

    private static final long serialVersionUID = -9181046893757665915L;

    /**
     * 酒店ID
     */
    private Integer chainId;
}
