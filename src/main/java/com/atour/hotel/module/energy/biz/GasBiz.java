package com.atour.hotel.module.energy.biz;

import com.alibaba.fastjson.JSON;
import com.atour.dicts.enums.energy.HotelBaseConfigTypeEnum;
import com.atour.dicts.enums.energy.HotelPlaceConfigTypeEnum;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.energy.biz.json.electricity.SubElectricityMeterJson;
import com.atour.hotel.module.energy.biz.json.gas.GasJson;
import com.atour.hotel.module.energy.biz.json.gas.SubGasMeterJson;
import com.atour.hotel.module.energy.biz.json.gas.TotalGasMeterJson;
import com.atour.hotel.module.energy.biz.json.gas.TotalGasPriceJson;
import com.atour.hotel.module.energy.biz.json.water.TotalWaterPriceJson;
import com.atour.hotel.module.energy.common.BizPreconditions;
import com.atour.hotel.module.energy.common.CommonHelp;
import com.atour.hotel.module.energy.common.template.BaseBizTemplate;
import com.atour.hotel.module.energy.dto.GasPriceRestructureDTO;
import com.atour.hotel.module.energy.dto.GasQueryDTO;
import com.atour.hotel.module.energy.dto.GasSaveDTO;
import com.atour.hotel.module.energy.request.gas.GasQueryParam;
import com.atour.hotel.module.energy.request.gas.GasSaveParam;
import com.atour.hotel.module.energy.service.HotelBaseConfigService;
import com.atour.hotel.persistent.energy.entity.HotelBaseConfigEntity;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 业务聚合层: 能耗配置-燃气配置
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@Slf4j
@Component
public class GasBiz {

    /**
     * 数据层: 酒店设施配置表
     */
    @Autowired
    private HotelBaseConfigService hotelBaseConfigService;

    /**
     * 业务层: 系统参数
     */
    @Autowired
    private ParamService paramService;

    /**
     * 燃气价格区间重组-燃气配置
     *
     * @param gasSaveParam 燃气配置入参
     * @return 重组后的燃气价格结果
     */
    public GasPriceRestructureDTO priceRestructure(GasSaveParam gasSaveParam) {
        return new BaseBizTemplate<GasPriceRestructureDTO>("GasBiz.priceRestructure") {
            @Override
            protected void checkParams() {
                checkGasParams(gasSaveParam);
            }

            @Override
            protected GasPriceRestructureDTO process() {

                // 版本校验
                HotelBaseConfigEntity configEntity =
                    hotelBaseConfigService.queryByVersion(gasSaveParam.getChainId(), HotelBaseConfigTypeEnum.GAS_CONFIG,
                        gasSaveParam.getVersion());

                List<TotalGasPriceJson> priceJsons = Safes.of(gasSaveParam.getGasJson()
                    .getTotalGasPriceJsons());

                // 如果没有填写水价信息,则不进行重组
                if (Objects.isNull(configEntity) || StringUtils.isEmpty(configEntity.getHotelBaseConfig())
                    || CollectionUtils.isEmpty(priceJsons)) {
                    return GasPriceRestructureDTO.builder()
                        .chainId(gasSaveParam.getChainId())
                        .restructure(Boolean.FALSE)
                        .priceJsons(Lists.newArrayList())
                        .build();
                }

                String hotelBaseConfig = configEntity.getHotelBaseConfig();
                GasJson gasJson = JSON.parseObject(hotelBaseConfig, GasJson.class);
                List<TotalGasPriceJson> totalGasPriceJsons = gasJson.getTotalGasPriceJsons();

                if (CollectionUtils.isEmpty(totalGasPriceJsons)) {
                    return GasPriceRestructureDTO.builder()
                        .chainId(gasSaveParam.getChainId())
                        .restructure(Boolean.FALSE)
                        .priceJsons(priceJsons)
                        .build();
                }

                totalGasPriceJsons.sort(Comparator.comparing(TotalGasPriceJson::getStartDate));

                // 酒店营业日
                LocalDate currentAccLocalDate = paramService.getCurrentAccLocalDate(gasSaveParam.getChainId());
                BizPreconditions.checkNotNull(currentAccLocalDate, "未获取到酒店营业日");

                boolean accDatePrice = totalGasPriceJsons.stream()
                    .anyMatch(priceJson ->
                        (currentAccLocalDate.isAfter(priceJson.getStartDate()) && currentAccLocalDate.isBefore(
                            priceJson.getEndDate())) || currentAccLocalDate.isEqual(priceJson.getStartDate())
                            || currentAccLocalDate.isEqual(priceJson.getStartDate()));

                if (accDatePrice) {
                    BizPreconditions.checkArgument(priceJsons.stream()
                        .anyMatch(priceJson ->
                            (currentAccLocalDate.isAfter(priceJson.getStartDate()) && currentAccLocalDate.isBefore(
                                priceJson.getEndDate())) || currentAccLocalDate.isEqual(priceJson.getStartDate())
                                || currentAccLocalDate.isEqual(priceJson.getStartDate())), "本营业日总燃气价已配置,无法重复配置");
                }

                String oldJson = JSON.toJSONString(totalGasPriceJsons);
                String newJson = JSON.toJSONString(priceJsons);
                boolean restructure = StringUtils.equals(oldJson, newJson);

                if (restructure) {
                    return GasPriceRestructureDTO.builder()
                        .chainId(gasSaveParam.getChainId())
                        .restructure(Boolean.FALSE)
                        .priceJsons(priceJsons)
                        .build();
                }

                List<TotalGasPriceJson> totalPriceList = priceRange(priceJsons);
                totalPriceList.sort(Comparator.comparing(TotalGasPriceJson::getStartDate));
                return GasPriceRestructureDTO.builder()
                    .chainId(gasSaveParam.getChainId())
                    .restructure(Boolean.TRUE)
                    .priceJsons(totalPriceList)
                    .build();
            }
        }.execute();
    }

    /**
     * 保存-燃气配置
     *
     * @param gasSaveParam 燃气配置入参
     * @return 保存响应结果
     */
    @Transactional(transactionManager = "energyTransactionManager", rollbackFor = Exception.class)
    public GasSaveDTO save(GasSaveParam gasSaveParam) {
        return new BaseBizTemplate<GasSaveDTO>("GasBiz.save") {
            @Override
            protected void checkParams() {
                checkGasParams(gasSaveParam);
            }

            @Override
            protected GasSaveDTO process() {

                // 版本校验
                HotelBaseConfigEntity configEntity =
                    hotelBaseConfigService.queryByVersion(gasSaveParam.getChainId(), HotelBaseConfigTypeEnum.GAS_CONFIG,
                        gasSaveParam.getVersion());

                // 生成表编号
                List<SubGasMeterJson> subGasMeterJsons = Safes.of(gasSaveParam.getGasJson()
                    .getSubGasMeterJsons());
                List<TotalGasMeterJson> totalGasMeterJsons = Safes.of(gasSaveParam.getGasJson()
                    .getTotalGasMeterJsons());

                final int[] subNo = {Integer.parseInt(String.valueOf(System.currentTimeMillis() / 1000))};
                final int[] totalNo = {Integer.parseInt(String.valueOf(System.currentTimeMillis() / 1000))};

                // 设置分表编号
                subGasMeterJsons.stream()
                    .filter(Objects::nonNull)
                    .filter(subGasMeterJson -> Objects.isNull(subGasMeterJson.getGasMeterNo()))
                    .forEach(subGasMeterJson -> subGasMeterJson.setGasMeterNo(++subNo[0]));

                // 设置总表编号
                totalGasMeterJsons.stream()
                    .filter(Objects::nonNull)
                    .filter(totalGasMeterJson -> Objects.isNull(totalGasMeterJson.getGasMeterNo()))
                    .forEach(totalGasMeterJson -> totalGasMeterJson.setGasMeterNo(++totalNo[0]));
                gasSaveParam.getGasJson()
                    .setTotalGasMeterJsons(totalGasMeterJsons);
                gasSaveParam.getGasJson()
                    .setSubGasMeterJsons(subGasMeterJsons);

                List<TotalGasPriceJson> priceJsons = Safes.of(gasSaveParam.getGasJson()
                    .getTotalGasPriceJsons());

                // 如果没有填写燃气价信息,则不进行重组
                if (CollectionUtils.isEmpty(priceJsons)) {
                    String config = JSON.toJSONString(gasSaveParam.getGasJson());
                    hotelBaseConfigService.insertOrUpdate(gasSaveParam.getChainId(), HotelBaseConfigTypeEnum.GAS_CONFIG,
                        config, configEntity);
                    return GasSaveDTO.builder()
                        .chainId(gasSaveParam.getChainId())
                        .build();
                }

                // 区间重组
                List<TotalGasPriceJson> totalPriceList = priceRange(priceJsons);
                gasSaveParam.getGasJson()
                    .setTotalGasPriceJsons(totalPriceList);

                // 数据入库
                String config = JSON.toJSONString(gasSaveParam.getGasJson());
                hotelBaseConfigService.insertOrUpdate(gasSaveParam.getChainId(), HotelBaseConfigTypeEnum.GAS_CONFIG,
                    config, configEntity);
                return GasSaveDTO.builder()
                    .chainId(gasSaveParam.getChainId())
                    .build();
            }
        }.execute();
    }

    /**
     * 查询-燃气配置
     *
     * @param gasQueryParam 燃气配置查询参数
     * @return 查询结果
     */
    public GasQueryDTO query(GasQueryParam gasQueryParam) {
        return new BaseBizTemplate<GasQueryDTO>("GasBiz.query") {
            @Override
            protected void checkParams() {
                BizPreconditions.checkNotNull(gasQueryParam.getChainId(), "酒店ID不能为空");
            }

            @Override
            protected GasQueryDTO process() {

                HotelBaseConfigEntity build = HotelBaseConfigEntity.builder()
                    .chainId(gasQueryParam.getChainId())
                    .deleteFlag(0)
                    .type(HotelBaseConfigTypeEnum.GAS_CONFIG.getValue())
                    .build();
                HotelBaseConfigEntity configEntity = hotelBaseConfigService.query(build);

                if (Objects.isNull(configEntity) || StringUtils.isEmpty(configEntity.getHotelBaseConfig())) {
                    return GasQueryDTO.builder()
                        .chainId(gasQueryParam.getChainId())
                        .build();
                }

                GasJson gasJson = JSON.parseObject(configEntity.getHotelBaseConfig(), GasJson.class);
                Safes.of(gasJson.getTotalGasPriceJsons())
                    .sort(Comparator.comparing(TotalGasPriceJson::getStartDate));

                return GasQueryDTO.builder()
                    .chainId(gasQueryParam.getChainId())
                    .gasJson(gasJson)
                    .version(configEntity.getVersion())
                    .build();
            }
        }.execute();
    }

    /**
     * 燃气配置参数校验
     *
     * @param gasSaveParam 燃气配置入参
     */
    private void checkGasParams(GasSaveParam gasSaveParam) {
        BizPreconditions.checkNotNull(gasSaveParam, "燃气配置请求参数不能为空");
        BizPreconditions.checkNotNull(gasSaveParam.getChainId(), "酒店ID不能为空");
        BizPreconditions.checkNotNull(gasSaveParam.getGasJson(), "燃气配置数据不能为空");

        // 校验总燃气价
        List<TotalGasPriceJson> priceJsons = Safes.of(gasSaveParam.getGasJson()
            .getTotalGasPriceJsons());
        if (CollectionUtils.isNotEmpty(priceJsons)) {
            BizPreconditions.checkArgument(priceJsons.stream()
                .allMatch(priceJson -> Objects.nonNull(priceJson.getStartDate())), "新增总燃气价数据开始日期不能为空");
            BizPreconditions.checkArgument(priceJsons.stream()
                .allMatch(priceJson -> Objects.nonNull(priceJson.getEndDate())), "新增总燃气价数据结束日期不能为空");
            BizPreconditions.checkArgument(priceJsons.stream()
                .filter(priceJson -> Objects.nonNull(priceJson.getEndDate()))
                .allMatch(priceJson -> priceJson.getEndDate()
                    .isAfter(priceJson.getStartDate()) || priceJson.getEndDate()
                    .isEqual(priceJson.getStartDate())), "新增总燃气价数据结束日期必须大于等于开始日期");
            BizPreconditions.checkArgument(priceJsons.stream()
                .allMatch(priceJson -> Objects.nonNull(priceJson.getUnitPrice())), "新增总燃气价数据燃气单价不能为空");

            final int[] sortNo = {priceJsons.stream()
                .filter(Objects::nonNull)
                .filter(price -> Objects.nonNull(price.getSort()))
                .mapToInt(TotalGasPriceJson::getSort)
                .max().orElse(0)};

            // 设置序号
            priceJsons.stream()
                .filter(Objects::nonNull)
                .filter(price -> Objects.isNull(price.getSort()))
                .forEach(priceJson -> priceJson.setSort(++sortNo[0]));
        }

        // 校验总燃气表
        List<TotalGasMeterJson> totalMeterJsons = Safes.of(gasSaveParam.getGasJson()
            .getTotalGasMeterJsons());
        if (CollectionUtils.isNotEmpty(totalMeterJsons)) {
            BizPreconditions.checkArgument(totalMeterJsons.stream()
                .allMatch(meterJson -> StringUtils.isNotEmpty(meterJson.getGasName())), "新增总燃气表数据燃气表名称不能为空");
            BizPreconditions.checkArgument(totalMeterJsons.stream()
                .allMatch(meterJson -> Objects.nonNull(meterJson.getRate())), "新增总燃气表数据倍率不能为空");
            BizPreconditions.checkArgument(totalMeterJsons.stream()
                .allMatch(meterJson -> Objects.nonNull(meterJson.getInitRead())), "新增总燃气表数据初始读数不能为空");

            int size = (int)totalMeterJsons.stream()
                .map(TotalGasMeterJson::getGasName)
                .distinct()
                .count();
            BizPreconditions.checkArgument(size == totalMeterJsons.size(), "新增总燃气表数据燃气表名称重复");
        }

        // 校验分燃气表
        List<SubGasMeterJson> subMeterJsons = Safes.of(gasSaveParam.getGasJson()
            .getSubGasMeterJsons());
        if (CollectionUtils.isNotEmpty(totalMeterJsons)) {
            BizPreconditions.checkArgument(subMeterJsons.stream()
                .allMatch(subMeterJson -> StringUtils.isNotEmpty(subMeterJson.getGasName())), "新增分燃气表数据燃气表名称不能为空");
            BizPreconditions.checkArgument(subMeterJsons.stream()
                .allMatch(subMeterJson -> Objects.nonNull(subMeterJson.getRate())), "新增分燃气表数据倍率不能为空");
            BizPreconditions.checkArgument(subMeterJsons.stream()
                .allMatch(subMeterJson -> Objects.nonNull(subMeterJson.getInitRead())), "新增分燃气表数据初始读数不能为空");

            int size = (int)subMeterJsons.stream()
                .map(SubGasMeterJson::getGasName)
                .distinct()
                .count();
            BizPreconditions.checkArgument(size == subMeterJsons.size(), "新增分燃气表数据燃气表名称重复");

            BizPreconditions.checkArgument(subMeterJsons.stream()
                    .allMatch(subMeterJson -> Objects.nonNull(subMeterJson.getPlaceId()) && Objects.nonNull(
                        subMeterJson.getPlaceType()) && StringUtils.isNotEmpty(subMeterJson.getPlaceName())),
                "新增分燃气表数据场地名称不能为空");

            BizPreconditions.checkArgument(subMeterJsons.stream()
                .filter(subMeterJson -> Objects.equals(subMeterJson.getPlaceType(),
                    HotelPlaceConfigTypeEnum.OUTER_PLACE.getValue()))
                .allMatch(subMeterJson -> Objects.nonNull(subMeterJson.getStartDate()) && Objects.nonNull(
                    subMeterJson.getEndDate()) && subMeterJson.getStartDate()
                    .isBefore(subMeterJson.getEndDate())), "新增分燃气表数据开始结束时间不能为空");
        }
    }

    /**
     * 价格重组
     *
     * @param priceJsons 页面的水配置
     * @return 区间重组后的水配置
     */
    private List<TotalGasPriceJson> priceRange(List<TotalGasPriceJson> priceJsons) {
        // 区间重组
        List<TotalGasPriceJson> totalPriceList = Lists.newArrayList();
        priceJsons.forEach(priceJson -> addTotalGasPriceRange(priceJson, totalPriceList));
        return totalPriceList;
    }

    private void addTotalGasPriceRange(TotalGasPriceJson gasPriceJson, List<TotalGasPriceJson> ranges) {

        final CopyOnWriteArrayList<TotalGasPriceJson> cowRangeList = new CopyOnWriteArrayList<>(ranges);

        for (TotalGasPriceJson priceJson : cowRangeList) {
            if (gasPriceJson.getEndDate()
                .isBefore(priceJson.getStartDate()) || gasPriceJson.getStartDate()
                .isAfter(priceJson.getEndDate())) {
                continue;
            }

            // 如果价格相等,则合并区间
            if (gasPriceJson.getUnitPrice()
                .compareTo(priceJson.getUnitPrice()) == 0) {
                LocalDate startDate = CommonHelp.minDate(gasPriceJson.getStartDate(), priceJson.getStartDate());
                LocalDate endDate = CommonHelp.maxDate(gasPriceJson.getEndDate(), priceJson.getEndDate());

                gasPriceJson.setStartDate(startDate);
                gasPriceJson.setEndDate(endDate);
                cowRangeList.remove(priceJson);
                continue;
            }

            if (gasPriceJson.getStartDate()
                .isEqual(priceJson.getStartDate())) {
                if (gasPriceJson.getStartDate()
                    .isEqual(priceJson.getEndDate()) || gasPriceJson.getEndDate()
                    .isAfter(priceJson.getEndDate()) || gasPriceJson.getEndDate()
                    .isEqual(priceJson.getEndDate())) {
                    cowRangeList.remove(priceJson);
                    continue;
                }
                if (gasPriceJson.getEndDate()
                    .isBefore(priceJson.getEndDate())) {
                    priceJson.setStartDate(CommonHelp.getDateByDays(gasPriceJson.getEndDate(), 1));
                    continue;
                }
            }

            if (gasPriceJson.getEndDate()
                .isEqual(priceJson.getStartDate())) {
                if (gasPriceJson.getEndDate()
                    .isEqual(priceJson.getEndDate())) {
                    cowRangeList.remove(priceJson);
                    continue;
                }
                priceJson.setStartDate(CommonHelp.getDateByDays(priceJson.getStartDate(), 1));
                continue;
            }

            if (gasPriceJson.getStartDate()
                .isEqual(priceJson.getEndDate())) {
                priceJson.setEndDate(CommonHelp.getDateByDays(priceJson.getEndDate(), -1));
                continue;
            }

            if (gasPriceJson.getStartDate()
                .isBefore(priceJson.getStartDate())) {
                if (gasPriceJson.getEndDate()
                    .isEqual(priceJson.getEndDate()) || gasPriceJson.getEndDate()
                    .isAfter(priceJson.getEndDate())) {
                    cowRangeList.remove(priceJson);
                    continue;
                }
                if (gasPriceJson.getEndDate()
                    .isBefore(priceJson.getEndDate())) {
                    priceJson.setStartDate(CommonHelp.getDateByDays(gasPriceJson.getEndDate(), 1));
                    continue;
                }
            }

            if (gasPriceJson.getStartDate()
                .isAfter(priceJson.getStartDate())) {
                if (gasPriceJson.getEndDate()
                    .isEqual(priceJson.getEndDate()) || gasPriceJson.getEndDate()
                    .isAfter(priceJson.getEndDate())) {
                    priceJson.setEndDate(CommonHelp.getDateByDays(gasPriceJson.getStartDate(), -1));
                    continue;
                }
                if (gasPriceJson.getEndDate()
                    .isBefore(priceJson.getEndDate())) {
                    TotalGasPriceJson build = TotalGasPriceJson.builder()
                        .endDate(priceJson.getEndDate())
                        .startDate(CommonHelp.getDateByDays(gasPriceJson.getEndDate(), 1))
                        .unitPrice(priceJson.getUnitPrice())
                        .build();
                    priceJson.setEndDate(CommonHelp.getDateByDays(gasPriceJson.getStartDate(), -1));
                    cowRangeList.add(build);
                }
            }
        }

        cowRangeList.add(gasPriceJson);
        ranges.clear();
        ranges.addAll(cowRangeList);

        final int[] sort = {1};
        ranges.stream()
            .sorted(Comparator.comparing(TotalGasPriceJson::getStartDate))
            .forEach(priceJson -> priceJson.setSort(sort[0]++));
    }
}
