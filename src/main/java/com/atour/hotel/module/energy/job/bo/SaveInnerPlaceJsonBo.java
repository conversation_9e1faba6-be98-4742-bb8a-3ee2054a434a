package com.atour.hotel.module.energy.job.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/8/20
 * 保存场地
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveInnerPlaceJsonBo implements Serializable {

    private static final long serialVersionUID = 2548214574347082185L;

    /**
     * 类型
     */
    private Integer type;
    /**
     * 用量
     */
    private Double dosage;
}
