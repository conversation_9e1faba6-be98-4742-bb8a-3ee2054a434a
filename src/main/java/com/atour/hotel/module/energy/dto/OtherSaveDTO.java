package com.atour.hotel.module.energy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 业务层: 蒸汽配置保存响应结果
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OtherSaveDTO implements Serializable {

    private static final long serialVersionUID = -4836690924503113532L;

    /**
     * 酒店ID
     */
    private Integer chainId;

}
