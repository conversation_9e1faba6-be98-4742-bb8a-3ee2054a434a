package com.atour.hotel.module.energy.web.bo.permonthouterplace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/8/20
 * 每月外部场地json
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InternalJsonBo implements Serializable {

    private static final long serialVersionUID = 1832677963858805219L;

    /**
     * 电
     */
    private ElecJsonBo elec;
    /**
     * 水
     */
    private WaterJsonBo water;
    /**
     * 燃气
     */
    private GasJsonBo gas;
    /**
     * 蒸汽
     */
    private SteamJsonBo steam;

    /**
     * 供暖
     */
    private HeatJsonBo heat;

}
