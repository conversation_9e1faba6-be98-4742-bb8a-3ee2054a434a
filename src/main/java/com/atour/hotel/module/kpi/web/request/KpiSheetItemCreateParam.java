package com.atour.hotel.module.kpi.web.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * KPI考核指标参数-新建
 *
 * <AUTHOR>
 * @date 2020/8/29
 */
@Data
public class KpiSheetItemCreateParam implements Serializable {

    /**
     * 考核ID。kpi_sheet.id
     */
    @NotNull(message = "考核ID不能为空")
    @Min(1)
    private Integer sheetId;

    /**
     * 基础指标ID。kpi.items.itemId
     */
    @NotNull(message = "基础指标ID不能为空")
    @Min(1)
    private Integer itemId;

    /**
     * 周期类型。1-年；2-半年；3-季；4-月；5-周
     *
     * @see com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum
     */
    @NotNull(message = "周期类型不能为空")
    @Min(1)
    private Integer periodType;

    /**
     * 周期值-年
     */
    @NotNull(message = "周期值-年不能为空")
    @Min(1900)
    @Max(9900)
    private Integer periodYear;

    /**
     * 周期值。如：2021；1-上半年，2-下半年；1-1季度；1-1月；1-第1周
     */
    @NotNull(message = "周期值不能为空")
    @Min(1)
    private Integer periodVal;

    /**
     * 是否整改项目
     */
    @NotNull(message = "是否整改项目不能为空")
    private Boolean rectify;

}
