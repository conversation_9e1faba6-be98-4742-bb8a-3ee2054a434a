package com.atour.hotel.module.kpi.app.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KpiSelectDTO {

    @ApiModelProperty(value = "1-品牌 2-酒店状态 3-酒店类型 ")
    private Integer type;

    @ApiModelProperty(value = "数据集合")
    private List<KpiKeyValueCommonDTO> list;

}
