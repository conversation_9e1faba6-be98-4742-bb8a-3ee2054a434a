package com.atour.hotel.module.kpi.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KpiMonthFinishRequest {

    /**
     * see KpiPeriodTypeEnum
     * 周期类型
     */
    @NotNull(message = "时间粒度类型不能为空")
    @ApiModelProperty(value = "周期类型 1-年；2-半年；3-季；4-月；5-周")
    private Integer periodType;

    @ApiModelProperty(value = "周期值")
    private Integer periodVal;

    @ApiModelProperty(value = "周期年")
    private Integer year;

    @ApiModelProperty(value = "条件类型 1-门店 2-区域")
    private Integer condType;

    @NotNull(message = "门店Id/区域id不能为空")
    @ApiModelProperty(value = "门店Id/区域id")
    private Long condVal;

    private Integer userId;

}
