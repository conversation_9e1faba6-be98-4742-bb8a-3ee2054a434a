package com.atour.hotel.module.kpi.web.request.online;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * kpi现长考核-新增奖金方案
 * <AUTHOR>
 * @date 2021/5/19
 */
@Data
public class KpiOnlineSheetCreateParam {

    /**
     * 奖金方案名称
     */
    @NotNull(message = "奖金方案名称不能为空")
    private String name;
    /**
     * 酒店列表
     */
    @NotEmpty(message = "奖金方案考核对象不能为空")
    private List<Integer[]> chainIds;
    /**
     * 考核周期 周期类型 1-年；2-半年；3-季；4-月；5-周
     */
    @NotNull(message = "奖金方案考核周期不能为空")
    private Integer periodType;

}