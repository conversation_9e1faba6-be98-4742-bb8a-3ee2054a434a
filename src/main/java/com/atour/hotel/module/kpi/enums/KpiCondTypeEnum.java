package com.atour.hotel.module.kpi.enums;

import com.alibaba.fastjson.JSONObject;
import com.atour.hotel.common.util.SpringContextsUtil;
import com.atour.hotel.module.common.dto.CommChainDTO;
import com.atour.hotel.module.common.service.CommonChainService;
import com.atour.hotel.module.kpi.service.factory.impl.KpiCondChainImpl;
import com.atour.hotel.module.kpi.web.response.cond.KpiSheetItemCondJsonDTO;
import com.atour.hotel.persistent.kpi.entity.KpiSheetItemCondEntity;
import com.atour.utils.Safes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设置条件类型
 * 注：KpiSheetTypeEnum的子集，方便映射到支持的条件类型上。
 *
 * <AUTHOR>
 * @date 2020/8/28
 */
public enum KpiCondTypeEnum {

    /**
     * 1-门店
     */
    CHAIN(KpiSheetTypeEnum.CHAIN),
    /**
     * 2-区域
     */
    AREA(KpiSheetTypeEnum.AREA),
    ;

    private final int value;
    private final String desc;

    KpiCondTypeEnum(KpiSheetTypeEnum typeEnum) {
        this.value = typeEnum.getValue();
        this.desc = typeEnum.getDesc();
    }

    public static KpiCondTypeEnum getInstance(Integer value) {
        for (KpiCondTypeEnum it : values()) {
            if (Objects.equals(it.getValue(), value)) {
                return it;
            }
        }
        return null;
    }

    /**
     * 获取有效配置
     * @param kpiSheetItemCondEntity
     * @return
     */
    public static Set<Integer> getResultId(KpiSheetItemCondEntity kpiSheetItemCondEntity) {
        if (Objects.isNull(kpiSheetItemCondEntity) || StringUtils.isBlank(kpiSheetItemCondEntity.getCondJson())) {
            return Collections.emptySet();
        }
        KpiSheetItemCondJsonDTO kpiSheetItemCondJsonDTO = JSONObject.parseObject(kpiSheetItemCondEntity.getCondJson(), KpiSheetItemCondJsonDTO.class);
        if (kpiSheetItemCondEntity.getCondType() == KpiCondTypeEnum.CHAIN.getValue()){
            KpiCondChainImpl kpiCondChainService = SpringContextsUtil.getBean(KpiCondChainImpl.class);
            List<Integer> chainIdList = kpiCondChainService.handleKpiCondMethod(kpiSheetItemCondEntity, kpiSheetItemCondJsonDTO);
            return new HashSet<>(Safes.of(chainIdList));
        } else if (kpiSheetItemCondEntity.getCondType() == KpiCondTypeEnum.AREA.getValue()) {
            return new HashSet<>(Safes.of(kpiSheetItemCondJsonDTO.getAreas()));
        }
        return Collections.emptySet();
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
