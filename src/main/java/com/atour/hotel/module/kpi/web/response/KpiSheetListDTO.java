package com.atour.hotel.module.kpi.web.response;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * KPI考核列表响应
 *
 * <AUTHOR>
 * @date 2020/8/29
 */
@Data
public class KpiSheetListDTO implements Serializable {

    /**
     * 考核ID
     */
    private Integer id;

    /**
     * 考核名称
     */
    private String name;

    /**
     * 考核对象类型。1-区域；2-门店
     *
     * @see com.atour.hotel.module.kpi.enums.KpiSheetTypeEnum
     */
    private Integer sheetType;

    /**
     * 考核对象类型名称
     */
    private String sheetTypeDesc;

    /**
     * 考核描述信息（生成）
     */
    private String sheetDesc;

    /**
     * 创建时间
     * 注：前端不方便转换日期的显示，后端处理了。
     */
    private String createTime;
}
