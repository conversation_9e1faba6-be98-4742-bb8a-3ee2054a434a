package com.atour.hotel.module.kpi.app.request.rectify;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 整改计划修改
 *
 * <AUTHOR>
 * @date 2020/9/10
 */
@Data
public class KpiRectifyPlanEditParam implements Serializable {

    /**
     * 整改计划ID
     */
    @NotNull(message = "整改计划ID不能为空")
    @Min(1)
    private Integer id;

    /**
     * 未完成原因（Json）
     */
    @NotNull(message = "未完成原因不能为空")
    private String reason;

    /**
     * 整改方案正文（Json）
     */
    @NotNull(message = "整改方案正文不能为空")
    private String plan;

    /**
     * 附件清单
     * 注：[{"ossKey": "xxxx", "filename": "yyyyyy"}]
     */
    @NotEmpty(message = "附件清单不能为空")
    private String attFileJson;
}
