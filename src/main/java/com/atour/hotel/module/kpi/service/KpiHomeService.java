package com.atour.hotel.module.kpi.service;

import com.atour.hotel.common.util.WeekUtil;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.module.common.dto.CommChainDTO;
import com.atour.hotel.module.common.service.CommonChainService;
import com.atour.hotel.module.kpi.app.response.KpiHomePageDTO;
import com.atour.hotel.module.kpi.app.response.home.KpiHomeElementDTO;
import com.atour.hotel.module.kpi.app.response.home.KpiHomePairDTO;
import com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum;
import com.atour.hotel.module.kpi.enums.KpiRightEnum;
import com.atour.hotel.module.kpi.enums.KpiSheetTypeEnum;
import com.atour.hotel.module.login.service.LoginService;
import com.atour.hotel.module.rbac.service.RbacUserService;
import com.atour.hotel.persistent.kpi.dao.KpiSheetConfigMapper;
import com.atour.hotel.persistent.kpi.dao.KpiSheetReportMapper;
import com.atour.hotel.persistent.kpi.entity.KpiSheetConfigEntity;
import com.atour.hotel.persistent.kpi.entity.KpiSheetItemCondJSONExt;
import com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * KPI管理 - APP首页服务
 *
 * <AUTHOR>
 * @date 2020/9/22
 */
@Service
@Slf4j
public class KpiHomeService {

    @Resource
    private KpiSheetReportMapper kpiSheetReportMapper;
    @Resource
    private CommonChainService commonChainService;
    @Resource
    private KpiSheetConfigMapper kpiSheetConfigMapper;
    @Resource
    private KpiRectifyPlanService kpiRectifyPlanService;
    @Resource
    private LoginService loginService;
    @Resource
    private KpiFinishService kpiFinishService;
    @Resource
    private RbacUserService rbacUserService;

    //业主端kpi功能开关
    @Value("${owner.kpi.open:false}")
    private boolean ownerKpiOpen;

    /**
     * 门店KPI
     */
    public KpiHomePageDTO homeChain(Long chainId) {
        // 校验门店关联系统
        CommChainDTO chainDTO = filterBySystemRelated(chainId.intValue());
        if (Objects.isNull(chainDTO)) {
            log.debug("门店尚未关联到KPI系统上 chainId: {}", chainId);
            return null;
        }
        // 校验当前用户权限
        String hrId = kpiFinishService.getHrIdByUserIdAndSession(null);
        if (StringUtils.isBlank(hrId)) {
            log.warn("获取当前登录用户失败");
            return null;
        }
        Set<String> buttonSet = rbacUserService.queryGrantedElementByEmployeeId(hrId);
        if (CollectionUtils.isEmpty(buttonSet) || !buttonSet.contains(KpiRightEnum.APP_CHAIN_ENT.getCode())) {
            log.debug("用户无此权限 chainId: {}, employeeId: {}", chainId, hrId);
            return null;
        }

        DateTime operateDate = WeekUtil.getChainOperateDateTime(LocalDate.now());
        KpiSheetTypeEnum sheetTypeEnum = KpiSheetTypeEnum.CHAIN;
        return getKpiHomePageDTO(chainId, operateDate, sheetTypeEnum, true);
    }

    /**
     * 区域KPI
     */
    public KpiHomePageDTO homeArea(Long chainId) {
        // 校验门店关联系统
        CommChainDTO chainDTO = filterBySystemRelated(chainId.intValue());
        if (Objects.isNull(chainDTO)) {
            log.debug("门店尚未关联到KPI系统上 chainId: {}", chainId);
            return null;
        }
        // 校验当前用户权限
        String hrId = kpiFinishService.getHrIdByUserIdAndSession(null);
        if (StringUtils.isBlank(hrId)) {
            log.warn("获取当前登录用户失败");
            return null;
        }
        Set<String> buttonSet = rbacUserService.queryGrantedElementByEmployeeId(hrId);
        if (CollectionUtils.isEmpty(buttonSet) || !buttonSet.contains(KpiRightEnum.APP_AREA_ENT.getCode())) {
            log.debug("用户无此权限 chainId: {}, employeeId: {}", chainId, hrId);
            return null;
        }

        DateTime operateDate = WeekUtil.getChainOperateDateTime(LocalDate.now());
        return getKpiHomePageDTO(chainDTO.getAreaId().longValue(), operateDate, KpiSheetTypeEnum.AREA, true);
    }

    /**
     * 业主端门店KPI
     */
    public KpiHomePageDTO ownerChain(Long chainId) {
        if (!ownerKpiOpen) {
            return null;
        }
        // 校验门店关联系统
        CommChainDTO chainDTO = filterBySystemRelated(chainId.intValue());
        if (Objects.isNull(chainDTO)) {
            log.warn("门店尚未关联到KPI系统上 chainId: {}", chainId);
            return null;
        }

        DateTime operateDate = WeekUtil.getChainOperateDateTime(LocalDate.now());
        KpiSheetTypeEnum sheetTypeEnum = KpiSheetTypeEnum.CHAIN;
        return getKpiHomePageDTO(chainId, operateDate, sheetTypeEnum, false);
    }

    /**
     * 过滤出系统关联门店
     */
    private CommChainDTO filterBySystemRelated(Integer chainId) {
        // 获取OMS门店关联系统
        List<CommChainDTO> chainDTOList = commonChainService.getActiveChainListFromCache();
        Optional<CommChainDTO> matchedChainOptional = Safes.of(chainDTOList).stream()
                .filter(it -> Objects.equals(chainId, it.getChainId()))
                .findFirst();
        if (!matchedChainOptional.isPresent()) {
            return null;
        }
        // 校验App门店关联系统
        List<Integer> relateHotels = loginService.getSystemRelateHotelList(FileConfig.kpiMgrAppSystemId);
        if (CollectionUtils.isEmpty(relateHotels)) {
            return null;
        }
        if (!relateHotels.contains(chainId)) {
            return null;
        }
        return matchedChainOptional.get();
    }

    /**
     * 获取KPI考核首页信息
     */
    private KpiHomePageDTO getKpiHomePageDTO(Long entityId, DateTime operateDate,
                                             KpiSheetTypeEnum sheetTypeEnum, boolean showRectify) {
        // 周统计
        KpiHomeElementDTO weekCurrElem = calcKpiHomeElementDTO(entityId, operateDate, sheetTypeEnum, KpiPeriodTypeEnum.WEEK, showRectify);
        DateTime weekPreOperateDate = operateDate.minusWeeks(1);
        KpiHomeElementDTO weekPreElem = calcKpiHomeElementDTO(entityId, weekPreOperateDate, sheetTypeEnum, KpiPeriodTypeEnum.WEEK, showRectify);
        KpiHomePairDTO week = new KpiHomePairDTO();
        week.setCurr(weekCurrElem);
        week.setPre(weekPreElem);
        // 月统计
        KpiHomeElementDTO monthCurrElem = calcKpiHomeElementDTO(entityId, operateDate, sheetTypeEnum, KpiPeriodTypeEnum.MONTH, showRectify);
        DateTime monthPreOperateDate = operateDate.minusMonths(1);
        KpiHomeElementDTO monthPreElem = calcKpiHomeElementDTO(entityId, monthPreOperateDate, sheetTypeEnum, KpiPeriodTypeEnum.MONTH, showRectify);
        KpiHomePairDTO month = new KpiHomePairDTO();
        month.setCurr(monthCurrElem);
        month.setPre(monthPreElem);

        // 包装结果
        KpiHomePageDTO homePageDTO = new KpiHomePageDTO();
        homePageDTO.setWeek(week);
        homePageDTO.setMonth(month);

        return homePageDTO;
    }


    /**
     * 计算首页的不达标和需整改记录数
     *
     * @param showRectify true:几木里展示需要整改的；false:业务端展示完成整改计划的
     */
    private KpiHomeElementDTO calcKpiHomeElementDTO(
            Long entityId, DateTime operateDate, KpiSheetTypeEnum sheetTypeEnum,
            KpiPeriodTypeEnum periodTypeEnum, boolean showRectify) {
        // 计算当前第几经营周
        int year = operateDate.getYear();
        int month = operateDate.getMonthOfYear();
        int week = operateDate.getWeekOfWeekyear();
        // 查询统计表
        KpiSheetReportEntity reportQuery = KpiSheetReportEntity.builder()
                .entityType(sheetTypeEnum.getValue())
                .entityId(entityId)
                .periodYear(year)
                .periodType(periodTypeEnum.getValue())
                .periodVal(week)
                .build();
        if (Objects.equals(KpiPeriodTypeEnum.WEEK, periodTypeEnum)) {
            reportQuery.setPeriodVal(week);
        } else {
            reportQuery.setPeriodVal(month);
        }
        List<KpiSheetReportEntity> reportList = kpiSheetReportMapper.selectBySelective(reportQuery);
        // 统计达标和整改数量
        int notReachCnt = 0;
        int needRectifyCnt = 0;
        List<Long> configIdList = Lists.newArrayList();
        for (KpiSheetReportEntity it : reportList) {
            List<KpiSheetItemCondJSONExt> condJsonExtList = ObjectUtil.fromJson(it.getInfoJson(),
                    new TypeReference<List<KpiSheetItemCondJSONExt>>() {
                    });
            if (CollectionUtils.isEmpty(condJsonExtList)) {
                continue;
            }
            for (KpiSheetItemCondJSONExt condJsonExt : condJsonExtList) {
                if (Objects.nonNull(condJsonExt) && Objects.equals(condJsonExt.getIsReach(), 0)) {
                    notReachCnt = notReachCnt + 1;
                    Long configId = condJsonExt.getConfigId();
                    if (Objects.nonNull(configId)) {
                        configIdList.add(configId);
                    }
                }
            }
        }

        // 拼接文案
        List<String> titleList = Lists.newArrayListWithExpectedSize(2);
        if (notReachCnt > 0) {
            titleList.add(String.format("不达标%s项", notReachCnt));
        }
        // 计算历史月的整改数量
        DateTime currOperateDate = WeekUtil.getChainOperateDateTime(LocalDate.now());
        if (Objects.equals(periodTypeEnum, KpiPeriodTypeEnum.MONTH) && currOperateDate.isAfter(operateDate)) {
            if (CollectionUtils.isNotEmpty(configIdList)) {
                List<KpiSheetConfigEntity> configEntityList = kpiSheetConfigMapper.selectByIds(configIdList);
                List<KpiSheetConfigEntity> rectifyConfigList = configEntityList.stream()
                        .filter(it -> Objects.equals(Boolean.TRUE, it.getRectify())).collect(Collectors.toList());
                // 几木里APP，展示需要整改的数量（应整改-已整改）
                if (showRectify) {
                    needRectifyCnt = rectifyConfigList.size();
                    if (needRectifyCnt > 0) {
                        int finishedCount = kpiRectifyPlanService.queryFinishedCount(rectifyConfigList);
                        int count = needRectifyCnt > finishedCount ? (needRectifyCnt - finishedCount) : 0;
                        titleList.add(String.format("需整改%s项", count));
                    }
                }
            }
        }
        String title = String.join("，", titleList);

        // 包装结果
        KpiHomeElementDTO elem = new KpiHomeElementDTO();
        elem.setPeriodType(reportQuery.getPeriodType());
        elem.setPeriodVal(reportQuery.getPeriodVal());
        elem.setYear(reportQuery.getPeriodYear());
        elem.setEntityType(reportQuery.getEntityType());
        elem.setEntityId(reportQuery.getEntityId());
        elem.setTitle(title);
        elem.setNotReachCnt(notReachCnt);
        elem.setNeedRectifyCnt(needRectifyCnt);
        return elem;
    }

}
