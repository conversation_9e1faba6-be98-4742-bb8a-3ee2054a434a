package com.atour.hotel.module.kpi.web.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @author：子烁
 * @date：2021/8/27 上午11:44
 */
@Data
@Valid
public class ActualDataDownLoadRequest {
    @NotNull(message = "sheetId不能为空")
    @ApiModelProperty(value = "方案ID")
    private Integer sheetId;
    @ApiModelProperty(value = "周期值")
    private Integer periodVal;
    @ApiModelProperty(value = "周期值")
    private Integer periodYear;
}
