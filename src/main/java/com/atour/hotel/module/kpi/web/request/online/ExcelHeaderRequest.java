package com.atour.hotel.module.kpi.web.request.online;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author：子烁
 * @date：2021/8/25 上午10:43
 */
@Data
@Valid
public class ExcelHeaderRequest {

    @NotEmpty(message = "指标ID不能为空")
    private List<Integer> itemIds;
    @NotNull(message = "sheetId不能为空")
    @ApiModelProperty(value = "方案ID")
    private Integer sheetId;
    @ApiModelProperty(value = "周期值")
    private Integer periodVal;
    @ApiModelProperty(value = "年")
    private Integer periodYear;
}
