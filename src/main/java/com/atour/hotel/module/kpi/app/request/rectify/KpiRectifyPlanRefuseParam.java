package com.atour.hotel.module.kpi.app.request.rectify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 整改计划驳回
 *
 * <AUTHOR>
 * @date 2020/9/10
 */
@Data
public class KpiRectifyPlanRefuseParam implements Serializable {

    /**
     * 整改计划ID
     */
    @NotNull(message = "整改计划ID不能为空")
    @Min(1)
    @ApiModelProperty(value ="整改计划ID")
    private Integer id;

    /**
     * 审批意见
     */
    @NotEmpty(message = "审批意见不能为空")
    @ApiModelProperty(value ="审批意见")
    private String remark;

    /**
     * 当前整改状态
     */
    @NotNull(message = "当前整改状态不能为空")
    @ApiModelProperty(value ="当前整改状态")
    private Integer rectifyState;

}
