package com.atour.hotel.module.kpi.web.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * KPI考核参数-通用的主键ID
 *
 * <AUTHOR>
 * @date 2020/8/29
 */
@Data
public class KpiSheetItemCondSingleClearParam implements Serializable {

    /**
     * 考核指标ID
     */
    @NotNull(message = "考核指标ID不能为空")
    @Min(1)
    private Integer sheetItemId;

    /**
     * 选择门店ID清单
     */
    @NotEmpty(message = "选择门店ID清单不能为空")
    private List<Integer> entityIdList;

    /**
     * 设置条件类型。1-门店；2-区域
     *
     * @see com.atour.hotel.module.kpi.enums.KpiCondTypeEnum
     */
    @NotNull(message = "设置条件类型不能为空")
    private Integer condType;
}
