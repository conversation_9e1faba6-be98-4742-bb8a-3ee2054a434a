package com.atour.hotel.module.kpi.service.factory.interfaces;

import com.atour.hotel.module.kpi.enums.online.KpiOnlineCalTypeEnum;
import com.atour.hotel.module.kpi.web.request.online.KpiOnlineEditScoreParam;

/**
 * <AUTHOR>
 * @date 2021/5/20
 */
public interface IKpiOnlineScoreHandle {

    /**
     * 处理业务逻辑,生成要保存的数据
     */
    void doHandle(KpiOnlineEditScoreParam param);

    /**
     * 处理支持类型
     * @return
     */
    KpiOnlineCalTypeEnum supportType();
}
