package com.atour.hotel.module.kpi.service.factory.impl;

import com.alibaba.fastjson.JSONObject;
import com.atour.hotel.module.common.dto.CommChainDTO;
import com.atour.hotel.module.kpi.constants.KpiConstant;
import com.atour.hotel.module.kpi.enums.KpiCondMethodEnum;
import com.atour.hotel.module.kpi.enums.KpiCondTypeEnum;
import com.atour.hotel.module.kpi.enums.KpiConfEntityTypeEnum;
import com.atour.hotel.module.kpi.enums.KpiHalfYearEnum;
import com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum;
import com.atour.hotel.module.kpi.enums.KpiQuarterEnum;
import com.atour.hotel.module.kpi.service.factory.abstracts.AbstractKpiCondLogic;
import com.atour.hotel.module.kpi.service.factory.interfaces.IKpiCondHandle;
import com.atour.hotel.module.kpi.service.factory.param.KpiCondLogicParam;
import com.atour.hotel.module.kpi.web.response.cond.KpiSheetItemCondJsonDTO;
import com.atour.hotel.persistent.kpi.entity.KpiSheetConfigEntity;
import com.atour.hotel.persistent.kpi.entity.KpiSheetItemCondEntity;
import com.atour.hotel.persistent.kpi.entity.KpiSheetItemEntity;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.Month;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 单店维度设置条件
 *
 * <AUTHOR>
 * @date 2020/9/8 5:46 下午
 */
@Slf4j
@Service
public class KpiCondChainImpl extends AbstractKpiCondLogic implements IKpiCondHandle {


	private static List<Integer> getChainIds(List<CommChainDTO> chainDTOS, KpiSheetItemCondJsonDTO kpiSheetItemCondJsonDTO) {
		//门店属性
		List<Integer> chainTypes = kpiSheetItemCondJsonDTO.getChainTypes();
		Preconditions.checkState(CollectionUtils.isNotEmpty(chainTypes), "chainTypes can`t be empty!!!");

		//区域
		List<Integer> areas = kpiSheetItemCondJsonDTO.getAreas();
		Preconditions.checkState(CollectionUtils.isNotEmpty(areas), "areas can`t be empty!!!");

		//品牌
		List<Integer> brands = kpiSheetItemCondJsonDTO.getBrands();
		Preconditions.checkState(CollectionUtils.isNotEmpty(brands), "brands can`t be empty!!!");

		Set<Integer> allChainIds = chainDTOS.stream()
				.map(CommChainDTO::getChainId)
				.collect(Collectors.toSet());
		//酒店结果

		//先处理门店属性
		final Set<Integer> guideChains =
				chainTypes.get(0) == -1 ?
						allChainIds :
						chainDTOS.stream()
								.filter(chainDTO -> chainTypes.contains(chainDTO.getIsGuide()))
								.map(CommChainDTO::getChainId)
								.collect(Collectors.toSet());
		//处理区域
		Set<Integer> areaChains = areas.get(0) == -1 ?
				allChainIds : chainDTOS.stream()
				.filter(chainDTO -> areas.contains(chainDTO.getAreaId()))
				.map(CommChainDTO::getChainId)
				.collect(Collectors.toSet());
		List<Integer> guideAreaChains = guideChains
				.stream()
				.filter(areaChains::contains)
				.collect(Collectors.toList());

		//处理品牌
		Set<Integer> brandChainIds = brands.get(0) == -1 ?
				allChainIds : chainDTOS.stream()
				.filter(chainDTO -> brands.contains(chainDTO.getBrand()))
				.map(CommChainDTO::getChainId)
				.collect(Collectors.toSet());

		return guideAreaChains
				.stream()
				.filter(brandChainIds::contains)
				.distinct()
				.collect(Collectors.toList());

	}

	@Override
	public void doHandle(KpiSheetItemCondEntity kpiSheetItemCondEntity, KpiSheetItemEntity kpiSheetItemEntity) {
		if (Objects.isNull(kpiSheetItemEntity)) {
			log.error("cond id:{} sheetItemId:{}获取不到对应的sheetItem.直接略过!!!", kpiSheetItemCondEntity.getId(), kpiSheetItemCondEntity.getSheetItemId());
			return;
		}
		//处理单店设置的
		String condJson = kpiSheetItemCondEntity.getCondJson();
		KpiSheetItemCondJsonDTO kpiSheetItemCondJsonDTO = JSONObject.parseObject(condJson, KpiSheetItemCondJsonDTO.class);
		List<Integer> chains = handleKpiCondMethod(kpiSheetItemCondEntity, kpiSheetItemCondJsonDTO);
		if (CollectionUtils.isEmpty(chains)) {
			log.error("KpiCondChainImpl chains为空,不执行任何操作,condId:{}", kpiSheetItemCondEntity.getId());
			return;
		}


		Set<KpiSheetConfigEntity> kpiSheetConfigEntities = Sets.newHashSet();


		KpiCondLogicParam kpiCondLogicParam = KpiCondLogicParam.builder()
				.kpiConfEntityTypeEnum(KpiConfEntityTypeEnum.CHAIN)
				.kpiSheetItemCondEntity(kpiSheetItemCondEntity)
				.kpiSheetItemEntity(kpiSheetItemEntity)
				.build();
		for (Integer chainId : chains) {

			kpiCondLogicParam.setEntityId(Long.valueOf(chainId));

			//根据周期值获取对应的
			KpiPeriodTypeEnum kpiPeriodTypeEnum = KpiPeriodTypeEnum.getInstance(kpiSheetItemEntity.getPeriodType());
			Integer periodVal = kpiSheetItemEntity.getPeriodVal();
			switch (Objects.requireNonNull(kpiPeriodTypeEnum)) {
				case YEAR:
					kpiSheetConfigEntities.addAll(handleYear(kpiCondLogicParam));
					break;
				case HALF_YEAR:
					if (KpiHalfYearEnum.UP_HALF.getValue() == periodVal) {
						kpiSheetConfigEntities.addAll(handleUpHalfYear(kpiCondLogicParam));
					} else {
						kpiSheetConfigEntities.addAll(handleDownHalfYear(kpiCondLogicParam));
					}
					break;
				case QUARTER:
					KpiQuarterEnum quarterEnum = KpiQuarterEnum.getInstance(periodVal);
					kpiSheetConfigEntities.addAll(handleQuarter(kpiCondLogicParam, quarterEnum));
					break;
				case MONTH:
					Month month = Month.of(periodVal);
					kpiCondLogicParam.setMonths(new Month[]{month});
					kpiSheetConfigEntities.addAll(handleMonth(kpiCondLogicParam));
					break;
				case WEEK:
					kpiSheetConfigEntities.addAll(handleWeek(kpiCondLogicParam));
					break;
			}
			if (CollectionUtils.isEmpty(kpiSheetConfigEntities)) {
				log.error("kpiSheetConfigEntities为空,不跑task");
				return;
			}

			List<KpiSheetConfigEntity> wait4TodBKpiSheetConfigEntities = Lists.newArrayList(kpiSheetConfigEntities);


			for (List<KpiSheetConfigEntity> list : Lists.partition(wait4TodBKpiSheetConfigEntities, KpiConstant.DB_SIZE)) {
				//批量插入更新--
				kpiSheetConfigMapper.insertOrUpdateMany(list);
			}
		}
	}

	//处理批量还是单店
	public List<Integer> handleKpiCondMethod(KpiSheetItemCondEntity kpiSheetItemCondEntity, KpiSheetItemCondJsonDTO kpiSheetItemCondJsonDTO) {
		if (kpiSheetItemCondEntity.getCondMethod() == KpiCondMethodEnum.SINGLE.getValue()) {
			return kpiSheetItemCondJsonDTO.getChains();
		}
		List<CommChainDTO> chainDTOS = commonChainService.getActiveChainListFromCache();

		return getChainIds(chainDTOS, kpiSheetItemCondJsonDTO);
	}

	@Override
	public KpiCondTypeEnum supportedType() {
		return KpiCondTypeEnum.CHAIN;
	}


}

