package com.atour.hotel.module.kpi.wrapper;

import com.atour.dicts.db.common.DeletedEnum;
import com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum;
import com.atour.hotel.module.kpi.enums.KpiRightEnum;
import com.atour.hotel.module.kpi.web.request.KpiSheetItemCreateParam;
import com.atour.hotel.module.kpi.web.response.KpiSheetItemDropdownListDTO;
import com.atour.hotel.module.kpi.web.response.KpiSheetItemListDTO;
import com.atour.hotel.module.kpi.web.response.base.KpiBaseItemDTO;
import com.atour.hotel.persistent.kpi.entity.KpiSheetEntity;
import com.atour.hotel.persistent.kpi.entity.KpiSheetItemEntity;
import com.atour.hotel.persistent.kpi.param.KpiSheetItemConflictParam;
import org.apache.commons.lang3.BooleanUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 考核指标包装工具
 *
 * <AUTHOR>
 * @date 2020/9/8
 */
public class KpiSheetItemWrapper {

    /**
     * 生成名称
     */
    public static String buildSheetItemDesc(KpiSheetItemEntity it, Map<Integer, KpiBaseItemDTO> baseItemDict) {
        if (Objects.isNull(it)) {
            return null;
        }

        StringBuilder builder = new StringBuilder();
        Integer itemId = it.getItemId();
        KpiBaseItemDTO baseItemDTO = baseItemDict.get(itemId);
        if (Objects.nonNull(baseItemDTO)) {
            builder.append(baseItemDTO.getItemName());
        }
        String periodDesc = buildPeriodDesc(it.getPeriodType(), it.getPeriodYear(), it.getPeriodVal());
        builder.append("(").append(periodDesc).append(")");

        return builder.toString();
    }

    /**
     * 生成考核周期的中文描述
     */
    private static String buildPeriodDesc(Integer periodType, Integer periodYear, Integer periodVal) {
        StringBuilder builder = new StringBuilder();
        builder.append(periodYear);
        builder.append("年");
        KpiPeriodTypeEnum periodTypeEnum = KpiPeriodTypeEnum.getInstance(periodType);
        if (Objects.equals(KpiPeriodTypeEnum.HALF_YEAR, periodTypeEnum)) {
            builder.append((periodVal > 1) ? "下" : "上");
        } else {
            builder.append(periodVal);
        }
        if (Objects.nonNull(periodTypeEnum)) {
            builder.append(periodTypeEnum.getDesc());
        }
        return builder.toString();
    }

    /**
     * 转换到DTO
     */
    public static KpiSheetItemListDTO buildDTO(
            KpiSheetItemEntity entity, Map<Integer, KpiBaseItemDTO> itemDict, Set<String> rightCodeSet) {
        if (Objects.isNull(entity)) {
            return null;
        }

        KpiSheetItemListDTO dto = new KpiSheetItemListDTO();
        dto.setId(entity.getId());
        dto.setSheetId(entity.getSheetId());
        Integer itemId = entity.getItemId();
        dto.setItemId(itemId);
        KpiBaseItemDTO kpiBaseItemDTO = itemDict.get(itemId);
        if (Objects.nonNull(kpiBaseItemDTO)) {
            dto.setItemName(kpiBaseItemDTO.getItemName());
        }
        dto.setPeriodType(entity.getPeriodType());
        KpiPeriodTypeEnum periodTypeEnum = KpiPeriodTypeEnum.getInstance(entity.getPeriodType());
        if (Objects.nonNull(periodTypeEnum)) {
            dto.setPeriodTypeDesc(periodTypeEnum.getDesc());
        }
        dto.setPeriodYear(entity.getPeriodYear());
        dto.setPeriodVal(entity.getPeriodVal());
        String periodDesc = buildPeriodDesc(entity.getPeriodType(), entity.getPeriodYear(), entity.getPeriodVal());
        dto.setPeriodDesc(periodDesc);
        dto.setRectify(entity.getRectify());
        dto.setRectifyDesc(BooleanUtils.isTrue(entity.getRectify()) ? "是" : "否");
        LocalDateTime now = LocalDateTime.now();
        dto.setCreateTime(now);
        dto.setUpdateTime(now);
        // 同时有考核指标删除和对应二级指标权限时，才可删除
        boolean deleteAble = false;
        if (rightCodeSet.contains(KpiRightEnum.ITEM_DELETE.getCode())) {
            if (Objects.nonNull(kpiBaseItemDTO) && rightCodeSet.contains(kpiBaseItemDTO.getRightCode())) {
                deleteAble = true;
            }
        }
        dto.setDeleteAble(deleteAble);
        return dto;
    }

    /**
     * 转换为实体
     */
    public static KpiSheetItemEntity buildEntity(KpiSheetItemCreateParam param) {
        KpiSheetItemEntity entity = new KpiSheetItemEntity();
        entity.setSheetId(param.getSheetId());
        entity.setItemId(param.getItemId());
        entity.setPeriodType(param.getPeriodType());
        entity.setPeriodYear(param.getPeriodYear());
        entity.setPeriodVal(param.getPeriodVal());
        entity.setRectify(param.getRectify());
        entity.setDeleted(DeletedEnum.UNDELETED.getCode());
        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        return entity;
    }

    /**
     * 转换到DTO
     */
    public static KpiSheetItemDropdownListDTO buildDropdownDTO(
            KpiSheetItemEntity it, Map<Integer, KpiBaseItemDTO> itemDict) {
        if (Objects.isNull(it)) {
            return null;
        }
        KpiSheetItemDropdownListDTO dto = new KpiSheetItemDropdownListDTO();
        dto.setSheetItemId(it.getId());
        dto.setSheetId(it.getSheetId());
        Integer itemId = it.getItemId();
        dto.setItemId(itemId);
        KpiBaseItemDTO kpiBaseItemDTO = itemDict.get(itemId);
        if (Objects.nonNull(kpiBaseItemDTO)) {
            dto.setItemName(kpiBaseItemDTO.getItemName());
        }
        String periodDesc = buildPeriodDesc(it.getPeriodType(), it.getPeriodYear(), it.getPeriodVal());
        dto.setPeriodDesc(periodDesc);
        return dto;
    }

    /**
     * 转换到查询条件参数
     */
    public static KpiSheetItemConflictParam buildCond(KpiSheetItemCreateParam param, KpiSheetEntity kpiSheetEntity) {
        KpiSheetItemConflictParam conflictParam = new KpiSheetItemConflictParam();
        conflictParam.setItemId(param.getItemId());
        conflictParam.setSheetType(kpiSheetEntity.getSheetType());
        conflictParam.setPeriodType(param.getPeriodType());
        conflictParam.setPeriodYear(param.getPeriodYear());
        conflictParam.setPeriodVal(param.getPeriodVal());
        return conflictParam;
    }
}
