package com.atour.hotel.module.kpi.enums;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 达标线逻辑
 * 注：只有正向
 *
 * <AUTHOR>
 * @date 2020/8/28
 */
public enum KpiReachLogicEnum {

    /**
     * 1-等于
     */
    EQ(1, "等于", "="),
    /**
     * 2-大于
     */
    GT(2, "大于", ">"),
    /**
     * 3-大于等于
     */
    GE(3, "大于等于", ">="),
    ;

    private final int value;
    private final String desc;
    private final String exp;

    KpiReachLogicEnum(int value, String desc, String exp) {
        this.value = value;
        this.desc = desc;
        this.exp = exp;
    }

    public static KpiReachLogicEnum getInstance(Integer value) {
        for (KpiReachLogicEnum it : values()) {
            if (Objects.equals(it.getValue(), value)) {
                return it;
            }
        }
        return null;
    }


    /**
     * 计算是否达标 0-不达标 1-达标
     * @param type
     * @param realVal
     * @return
     */
    public static Integer calIsReach(Integer type, BigDecimal realVal,BigDecimal reachVal) {
        if(Objects.isNull(type) || Objects.isNull(getInstance(type))||
         Objects.isNull(realVal)|| Objects.isNull(reachVal)){
            return null;
        }
        KpiReachLogicEnum instance = getInstance(type);
        int result = realVal.compareTo(reachVal);
        if(instance == KpiReachLogicEnum.EQ){
            return  result == 0? 1:0;
        } else if(instance == KpiReachLogicEnum.GT){
            return result > 0? 1:0;
        }else if(instance == KpiReachLogicEnum.GE){
            return result >= 0? 1:0;
        }
        return null;
    }

    public static KpiReachLogicEnum getInstanceByExp(String logicExp) {
        for (KpiReachLogicEnum it : values()) {
            if (Objects.equals(it.getExp(), logicExp)) {
                return it;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getExp() {
        return exp;
    }
}
