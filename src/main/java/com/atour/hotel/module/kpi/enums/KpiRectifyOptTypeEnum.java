package com.atour.hotel.module.kpi.enums;

import java.util.Objects;

/**
 * 整改方案操作类型
 *
 * <AUTHOR>
 * @date 2020/10/11
 */
public enum KpiRectifyOptTypeEnum {

    /**
     * 操作类型
     */
    CREATE(1, "新增"),
    DELETE(2, "删除"),
    UPDATE(3, "修改"),
    ;

    private final int value;
    private final String desc;


    KpiRectifyOptTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static KpiRectifyOptTypeEnum getInstance(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (KpiRectifyOptTypeEnum it : values()) {
            if (Objects.equals(it.getValue(), value)) {
                return it;
            }
        }
        return null;
    }


    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}