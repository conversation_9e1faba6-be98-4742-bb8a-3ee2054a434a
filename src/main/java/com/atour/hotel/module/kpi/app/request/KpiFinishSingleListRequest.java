package com.atour.hotel.module.kpi.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

//单指标门店列表页
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KpiFinishSingleListRequest implements Serializable {

	/**
	 * @see com.atour.hotel.module.kpi.enums.KpiPeriodTypeEnum
	 * 周期类型
	 */
	@Min(value = 4)
	@Max(value = 5)
	@NotNull(message = "时间粒度类型不能为空")
	@ApiModelProperty(value = "周期类型 1-年；2-半年；3-季；4-月；5-周")
	private Integer periodType;

	@ApiModelProperty(value = "周期值")
	private Integer periodVal;

	@ApiModelProperty(value = "周期年")
	private Integer year;

	@ApiModelProperty(value = "条件类型 1-门店 2-区域")
	private Integer condType;

	@NotNull(message = "门店Id/区域id不能为空")
	@ApiModelProperty(value = "门店Id/区域id")
	private Long condVal;

	/**
	 * 二级指标ID
	 */
	@NotNull(message = "二级指标ID不能为空")
	private Integer itemId;

	/**
	 * 区域Id1
	 */
	@NotNull(message = "区域Id不能为空")
	private Integer deptId;


}
