package com.atour.hotel.module.kpi.service;

import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.common.DeletedEnum;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.util.CommonParamsDTO;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.kpi.condition.KpiOptLogPageCondition;
import com.atour.hotel.module.kpi.condition.KpiRectifyListPageCondition;
import com.atour.hotel.module.kpi.web.request.KpiCommIdentityParam;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyCreateParam;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyEditParam;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyListParam;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyLogParam;
import com.atour.hotel.module.kpi.web.response.base.KpiBaseItemDTO;
import com.atour.hotel.module.kpi.web.response.rectify.KpiRectifyInfoDTO;
import com.atour.hotel.module.kpi.web.response.rectify.KpiRectifyListDTO;
import com.atour.hotel.module.kpi.web.response.rectify.KpiRectifyLogDTO;
import com.atour.hotel.module.kpi.wrapper.KpiRectifyLogWrapper;
import com.atour.hotel.module.kpi.wrapper.KpiRectifyWrapper;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.persistent.kpi.dao.KpiOptLogDao;
import com.atour.hotel.persistent.kpi.dao.KpiRectifyMapper;
import com.atour.hotel.persistent.kpi.entity.KpiOptLogEntity;
import com.atour.hotel.persistent.kpi.entity.KpiRectifyEntity;
import com.atour.rbac.api.response.UserDTO;
import com.atour.utils.Safes;
import com.atour.web.exception.BusinessException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 整改方案服务
 *
 * <AUTHOR>
 * @date 2020/9/28
 */
@Service
@Slf4j
public class KpiRectifyService {

    @Resource
    private KpiBaseItemService baseItemService;
    @Resource
    private KpiRectifyMapper kpiRectifyMapper;
    @Resource
    private KpiOptLogDao kpiOptLogDao;
    @Resource
    private CommentHelper commentHelper;

    /**
     * 列表
     */
    public Pair<List<KpiRectifyListDTO>, PageInfo> list(KpiRectifyListParam param) {
        // 获取分页信息
        PageInfo pageInfo = new PageInfo(Safes.of(param.getPageNo(), SystemContants.DEFAULT_PAGE[0]),
                Safes.of(param.getPageSize(), SystemContants.DEFAULT_PAGE[1]));
        KpiRectifyListPageCondition queryCondition = KpiRectifyWrapper.buildQueryCondition(param, pageInfo);
        int totalCount = kpiRectifyMapper.pageCount(queryCondition);
        pageInfo.compute(totalCount);
        if (totalCount <= 0) {
            return Pair.of(Lists.newArrayList(), pageInfo);
        }

        // 获取列表
        List<KpiRectifyEntity> entityList = kpiRectifyMapper.pageList(queryCondition);
        if (CollectionUtils.isEmpty(entityList)) {
            return Pair.of(Lists.newArrayList(), pageInfo);
        }

        // 包装结果
        Map<Integer, KpiBaseItemDTO> flatItemDict = baseItemService.getFlatItemDict();
        List<KpiRectifyListDTO> rectifyDTOList = entityList.stream()
                .map(it -> KpiRectifyWrapper.buildListDTO(it, flatItemDict)).collect(Collectors.toList());
        return Pair.of(rectifyDTOList, pageInfo);
    }

    /**
     * 新建
     */
    public Boolean create(KpiRectifyCreateParam param) {
        // 校验是否指标已存在（忽略是否有效）
        KpiRectifyEntity queryParam = new KpiRectifyEntity();
        queryParam.setItemId(param.getItemId());
        queryParam.setDeleted(DeletedEnum.UNDELETED.getCode());
        List<KpiRectifyEntity> existEntityList = kpiRectifyMapper.selectBySelective(queryParam);
        if (CollectionUtils.isNotEmpty(existEntityList)) {
            String errorMessage = "二级指标" + param.getItemId() + "对应整改方案已存在 id:"
                    + existEntityList.stream().map(KpiRectifyEntity::getId).collect(Collectors.toList());
            log.error(errorMessage);
            throw new BusinessException(errorMessage, ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        Map<Integer, KpiBaseItemDTO> flatItemDict = baseItemService.getFlatItemDict();
        KpiRectifyEntity entity = KpiRectifyWrapper.buildCreateEntity(param, flatItemDict);
        int count = kpiRectifyMapper.insertSelective(entity);

        // 记录日志
        String employeeId = tryGetEmployeeIdFromTl();
        List<KpiOptLogEntity> logList = ImmutableList.of(KpiRectifyLogWrapper.buildCreateLog(entity, employeeId, flatItemDict));
        kpiOptLogDao.batchInsert(logList);

        return count > 0;
    }

    /**
     * 尝试从TL获取当前用户employeeId
     */
    private String tryGetEmployeeIdFromTl() {
        CommonParamsDTO commonParamsDTO = CommonParamsManager.get();
        String employeeId = null;
        if (Objects.nonNull(commonParamsDTO) && Objects.nonNull(commonParamsDTO.getUserPermissionDTO())) {
            employeeId = commonParamsDTO.getUserPermissionDTO().getHrId();
        }
        return employeeId;
    }

    /**
     * 修改
     */
    public Boolean edit(KpiRectifyEditParam param) {
        KpiRectifyEntity rectifyEntity = kpiRectifyMapper.selectByPrimaryKey(param.getId());
        if (Objects.isNull(rectifyEntity)) {
            throw new BusinessException("指定整改方案不存在 " + param.getId(), ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        KpiRectifyEntity forUpdate = new KpiRectifyEntity();
        forUpdate.setId(param.getId());
        forUpdate.setStdJson(param.getStdJson());
        forUpdate.setCaseJson(param.getCaseJson());
        int count = kpiRectifyMapper.updateByPrimaryKeySelective(forUpdate);

        // 记录日志
        String employeeId = tryGetEmployeeIdFromTl();
        Map<Integer, KpiBaseItemDTO> flatItemDict = baseItemService.getFlatItemDict();
        KpiOptLogEntity logEntity = KpiRectifyLogWrapper.buildEditLog(forUpdate, rectifyEntity, employeeId, flatItemDict);
        kpiOptLogDao.batchInsert(ImmutableList.of(logEntity));

        return count > 0;
    }

    /**
     * 删除
     */
    public Boolean remove(KpiCommIdentityParam param) {
        KpiRectifyEntity rectifyEntity = kpiRectifyMapper.selectByPrimaryKey(param.getId());
        if (Objects.isNull(rectifyEntity)) {
            throw new BusinessException("指定整改方案不存在 " + param.getId(), ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        int count = kpiRectifyMapper.remove(param.getId());

        // 记录日志
        String employeeId = tryGetEmployeeIdFromTl();
        Map<Integer, KpiBaseItemDTO> flatItemDict = baseItemService.getFlatItemDict();
        KpiOptLogEntity logEntity = KpiRectifyLogWrapper.buildRemoveLog(rectifyEntity, employeeId, flatItemDict);
        kpiOptLogDao.batchInsert(ImmutableList.of(logEntity));

        return count > 0;
    }

    /**
     * 详情
     */
    public KpiRectifyInfoDTO info(KpiCommIdentityParam param) {
        KpiRectifyEntity rectifyEntity = kpiRectifyMapper.selectByPrimaryKey(param.getId());
        if (Objects.isNull(rectifyEntity)) {
            throw new BusinessException("指定整改方案不存在 " + param.getId(), ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        Map<Integer, KpiBaseItemDTO> flatItemDict = baseItemService.getFlatItemDict();
        return KpiRectifyWrapper.buildInfoDTO(rectifyEntity, flatItemDict);
    }

    /**
     * 日志
     */
    public Pair<List<KpiRectifyLogDTO>, PageInfo> logList(KpiRectifyLogParam param) {
        // 获取分页信息
        PageInfo pageInfo = new PageInfo(Safes.of(param.getPageNo(), SystemContants.DEFAULT_PAGE[0]),
                Safes.of(param.getPageSize(), SystemContants.DEFAULT_PAGE[1]));
        KpiOptLogPageCondition queryCondition = KpiRectifyLogWrapper.buildQueryCondition(param, pageInfo);
        int totalCount = kpiOptLogDao.pageCount(queryCondition);
        pageInfo.compute(totalCount);
        if (totalCount <= 0) {
            return Pair.of(Lists.newArrayList(), pageInfo);
        }

        // 获取列表
        List<KpiOptLogEntity> entityList = kpiOptLogDao.pageList(queryCondition);
        if (CollectionUtils.isEmpty(entityList)) {
            return Pair.of(Lists.newArrayList(), pageInfo);
        }
        // 获取操作人名称
        Set<String> employeeIdSet = entityList.stream().map(KpiOptLogEntity::getOperator)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<UserDTO> userDTOList = commentHelper.getRbacUserDetail(employeeIdSet);
        Map<String, String> employIdToFlowerNameDict = Safes.of(userDTOList).stream()
                .collect(Collectors.toMap(UserDTO::getEmployeeId, UserDTO::getFlowerName, (left, right) -> left));

        // 包装结果
        List<KpiRectifyLogDTO> rectifyDTOList = entityList.stream()
                .map(it -> KpiRectifyLogWrapper.buildListDTO(it, employIdToFlowerNameDict)).collect(Collectors.toList());
        return Pair.of(rectifyDTOList, pageInfo);
    }
}
