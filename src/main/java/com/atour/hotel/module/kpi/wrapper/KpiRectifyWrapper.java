package com.atour.hotel.module.kpi.wrapper;

import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.common.DeletedEnum;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.module.kpi.condition.KpiRectifyListPageCondition;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyCreateParam;
import com.atour.hotel.module.kpi.web.request.rectify.KpiRectifyListParam;
import com.atour.hotel.module.kpi.web.response.base.KpiBaseItemDTO;
import com.atour.hotel.module.kpi.web.response.rectify.KpiRectifyInfoDTO;
import com.atour.hotel.module.kpi.web.response.rectify.KpiRectifyListDTO;
import com.atour.hotel.persistent.kpi.entity.KpiRectifyEntity;
import com.atour.utils.Safes;
import com.atour.web.exception.BusinessException;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 整改方案包装工具
 *
 * <AUTHOR>
 * @date 2020/9/28
 */
public class KpiRectifyWrapper {

    /**
     * 组装查询条件
     */
    public static KpiRectifyListPageCondition buildQueryCondition(KpiRectifyListParam param, PageInfo pageInfo) {
        KpiRectifyListPageCondition condition = new KpiRectifyListPageCondition();
        List<Integer> itemIdList = Lists.newArrayList(Splitter.on(",").omitEmptyStrings()
                .split(Safes.of(param.getItems()))).stream()
                .filter(NumberUtils::isCreatable).map(NumberUtils::createInteger)
                .collect(Collectors.toList());
        if (!itemIdList.contains(-1)) {
            condition.setItemIdList(itemIdList);
        }
        condition.setOffset((pageInfo.getPageNo() - 1) * pageInfo.getPageSize());
        condition.setLimit(pageInfo.getPageSize());
        return condition;
    }

    /**
     * 转换为DTO
     */
    public static KpiRectifyListDTO buildListDTO(KpiRectifyEntity it, Map<Integer, KpiBaseItemDTO> flatItemDict) {
        KpiRectifyListDTO dto = new KpiRectifyListDTO();
        dto.setId(it.getId());
        Integer itemParentId = it.getItemParentId();
        dto.setItemParentId(itemParentId);
        KpiBaseItemDTO parentItemDTO = flatItemDict.get(itemParentId);
        if (Objects.nonNull(parentItemDTO)) {
            dto.setItemParentName(parentItemDTO.getItemName());
        }
        Integer itemId = it.getItemId();
        dto.setItemId(itemId);
        KpiBaseItemDTO itemDTO = flatItemDict.get(itemId);
        if (Objects.nonNull(itemDTO)) {
            dto.setItemName(itemDTO.getItemName());
        }
        dto.setStdJson(it.getStdJson());
        dto.setCaseJson(it.getCaseJson());
        return dto;
    }

    /**
     * 转换为新建Entity
     */
    public static KpiRectifyEntity buildCreateEntity(
            KpiRectifyCreateParam param, Map<Integer, KpiBaseItemDTO> flatItemDict) {
        Integer itemId = param.getItemId();
        KpiBaseItemDTO itemDTO = flatItemDict.get(itemId);
        if (Objects.isNull(itemDTO)) {
            throw new BusinessException("二级指标无效 " + param.getItemId(), ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        // 找出一级基础指标
        KpiBaseItemDTO parentItemDTO = null;
        for (Map.Entry<Integer, KpiBaseItemDTO> entry : flatItemDict.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue().getSubItemList())) {
                continue;
            }
            boolean matched = entry.getValue().getSubItemList().stream()
                    .anyMatch(it -> Objects.equals(it.getItemId(), itemId));
            if (matched) {
                parentItemDTO = entry.getValue();
                break;
            }
        }
        if (Objects.isNull(parentItemDTO)) {
            throw new BusinessException("二级指标未找到对应一级指标 " + param.getItemId(),
                    ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        KpiRectifyEntity entity = new KpiRectifyEntity();
        entity.setItemParentId(parentItemDTO.getItemId());
        entity.setItemId(itemId);
        entity.setStdJson(param.getStdJson());
        entity.setCaseJson(param.getCaseJson());
        entity.setDeleted(DeletedEnum.UNDELETED.getCode());
        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        return entity;
    }

    public static KpiRectifyInfoDTO buildInfoDTO(
            KpiRectifyEntity entity, Map<Integer, KpiBaseItemDTO> flatItemDict) {
        if (Objects.isNull(entity)) {
            return null;
        }

        KpiBaseItemDTO itemDTO = flatItemDict.get(entity.getItemId());
        if (Objects.isNull(itemDTO)) {
            throw new BusinessException("二级指标无效 " + entity.getItemId(), ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        KpiBaseItemDTO parentItemDTO = flatItemDict.get(entity.getItemParentId());
        if (Objects.isNull(parentItemDTO)) {
            throw new BusinessException("一级指标无效 " + entity.getItemParentId(),
                    ResponseCodeEnum.PARAM_ERROR.getCode());
        }

        KpiRectifyInfoDTO infoDTO = new KpiRectifyInfoDTO();
        infoDTO.setId(entity.getId());
        infoDTO.setItemParentId(entity.getItemParentId());
        infoDTO.setItemParentName(parentItemDTO.getItemName());
        infoDTO.setItemId(entity.getItemId());
        infoDTO.setItemName(itemDTO.getItemName());
        infoDTO.setStdJson(entity.getStdJson());
        infoDTO.setCaseJson(entity.getCaseJson());
        infoDTO.setCreateTime(entity.getCreateTime());
        return infoDTO;
    }
}
