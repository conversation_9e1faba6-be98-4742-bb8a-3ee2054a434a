package com.atour.hotel.module.kpi.web.request.rectify;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 方案配置列表参数
 *
 * <AUTHOR>
 * @date 2020/9/9
 */
@Data
public class KpiRectifyListParam implements Serializable {

    /**
     * 二级指标清单
     */
    @NotNull(message = "基础指标ID清单不能为空")
    private String items;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页记录数
     */
    private Integer pageSize;
}
