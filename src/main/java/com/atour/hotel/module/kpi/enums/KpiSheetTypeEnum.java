package com.atour.hotel.module.kpi.enums;

import java.util.Objects;

/**
 * 考核对象类型
 *
 * <AUTHOR>
 * @date 2020/8/28
 */
public enum KpiSheetTypeEnum {

    /**
     * 1-门店
     */
    CHAIN(1, "门店"),
    /**
     * 2-区域
     */
    AREA(2, "区域"),
    ;

    private final int value;
    private final String desc;

    KpiSheetTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static KpiSheetTypeEnum getInstance(Integer value) {
        for (KpiSheetTypeEnum it : values()) {
            if (Objects.equals(it.getValue(), value)) {
                return it;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
