package com.atour.hotel.module.hotel.param.questionnaire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 问卷问题填写的选项信息
 *
 * <AUTHOR>
 * @date 2020/08/20
 */
@Data
@Valid
@ApiModel(value = "问卷问题填写的选项信息")
public class QuestionAnswerOptionParam {

    /**
     * 选项编号
     */
    @NotNull(message = "选项编号不能为空")
    @Positive(message = "选项编号不合法")
    @ApiModelProperty(value = "选项编号", required = true)
    private Integer answerNumber;

    /**
     * 填空内容
     */
    @ApiModelProperty(value = "填空内容")
    private Integer answerContent;

}
