package com.atour.hotel.module.hotel.response.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 按物料|房间维度查询任务库存
 * @date 2024年02月23日16:32
 * @since JDK1.8
 */
@Data
@AllArgsConstructor
@Builder
public class TaskStockResponse implements Serializable {
    private static final long serialVersionUID = 1876897140508138280L;

    @Schema(description = "物料名称/房间号")
    private String queryName;

    @Schema(description = "在用")
    private Integer quantitySummary = 0;

    @Schema(description ="库存")
    private Long stock;

    @Schema(description ="产品id")
    private Long productId;

    @Schema(description ="物料名称-房间维度查询页使用")
    private List<TaskStockDetail> supplyList;

    @Schema(description ="房间号-物料维度查询页使用")
    private List<String> rooms;

    @Schema(description ="酒店Id")
    private Integer chainId;
}
