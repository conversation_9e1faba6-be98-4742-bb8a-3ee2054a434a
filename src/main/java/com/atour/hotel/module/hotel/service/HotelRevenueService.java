package com.atour.hotel.module.hotel.service;

import HotelRevenueService.ValueUnitEnum;
import com.atour.api.bean.ApiResult;
import com.atour.chain.api.chain.dto.AtourChainDTO;
import com.atour.chain.api.chain.dto.ChainDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.crs.api.dto.ChainBudgetDTO;
import com.atour.dicts.db.atour_pms.ChainBudgetProgressTypeEnum;
import com.atour.dicts.db.atour_pms.CoreRevenueStatsTypeEnum;
import com.atour.dicts.db.atour_pms.RevenueTypeEnum;
import com.atour.galaxy.api.bean.statistics.ChainStatisticsDTO;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.util.WeekUtil;
import com.atour.hotel.framework.configuration.bean.CostReportItemConfigBean;
import com.atour.hotel.framework.configuration.bean.DayCostReportConfigBean;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.bigdata.BigDataApiService;
import com.atour.hotel.module.common.dto.CommChainDTO;
import com.atour.hotel.module.common.service.CommonChainService;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.cost.enums.ChainTypeEnum;
import com.atour.hotel.module.cost.enums.CostFormTypeEnum;
import com.atour.hotel.module.cost.enums.GopItemEnum;
import com.atour.hotel.module.cost.service.CostFormService;
import com.atour.hotel.module.cost.service.CostReportItemService;
import com.atour.hotel.module.hotel.config.BigDecimalRoundingModeConfig;
import com.atour.hotel.module.hotel.dto.BudgetProgressDetailDTO;
import com.atour.hotel.module.hotel.dto.BudgetProgressItemDTO;
import com.atour.hotel.module.hotel.dto.BudgetProgressItemDTO.CompareData;
import com.atour.hotel.module.hotel.dto.BudgetProgressItemDTO.DailyBudgetValue;
import com.atour.hotel.module.hotel.dto.CoreStatsDTO;
import com.atour.hotel.module.hotel.dto.CoreStatsDetailDTO;
import com.atour.hotel.module.hotel.dto.CostDataDTO;
import com.atour.hotel.module.hotel.dto.CostDataItemDTO;
import com.atour.hotel.module.hotel.dto.CostDataItemPartDTO;
import com.atour.hotel.module.hotel.dto.GOPDataDTO;
import com.atour.hotel.module.hotel.dto.GOPDetailDTO;
import com.atour.hotel.module.hotel.dto.RevenueItemDTO;
import com.atour.hotel.module.hotel.dto.RevenueItemDTO.DailyValue;
import com.atour.hotel.module.hotel.dto.RevenueStatsDTO;
import com.atour.hotel.module.hotel.dto.RevenueStatsDTO.DateTypeEnum;
import com.atour.hotel.module.hotel.dto.RevenueStatsDetailDTO;
import com.atour.hotel.module.hotel.dto.WeekendDefinitionQueryAreaDTO;
import com.atour.hotel.module.hotel.dto.WeekendDefinitionQueryDTO;
import com.atour.hotel.module.hotel.dto.WeekendDefinitionQueryDTO.Weekend;
import com.atour.hotel.module.hotel.wrapper.ChainStatistisDTOWrapper;
import com.atour.hotel.module.hotel.wrapper.GOPDetailWrapper;
import com.atour.hotel.module.kpi.utils.KpiTimeUtil;
import com.atour.hotel.module.kpi.utils.entity.KpiTimeEntity;
import com.atour.hotel.persistent.cost.dao.CostFormDAO;
import com.atour.hotel.persistent.cost.entity.CostFormEntity;
import com.atour.hotel.persistent.cost.entity.CostFromBigData;
import com.atour.hotel.persistent.cost.entity.CostInf;
import com.atour.hotel.persistent.franchise.dao.WeekendDefinitionDAO;
import com.atour.hotel.persistent.franchise.entity.WeekendDefinitionEntity;
import com.atour.monitor.AMonitor;
import com.atour.utils.DateUtil;
import com.atour.utils.Safes;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.math3.util.Pair;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 营业数据
 *
 * <AUTHOR>
 * @date 2019/09/26
 */
@Service
@Slf4j
public class HotelRevenueService {

    private static List<Pair<String, String>> quarterlist = Lists.newArrayList();

    static {
        quarterlist.add(Pair.create("01-01", "3-31"));
        quarterlist.add(Pair.create("04-01", "6-30"));
        quarterlist.add(Pair.create("07-01", "9-30"));
        quarterlist.add(Pair.create("10-01", "12-31"));
    }


    @Resource
    private ChainRemote chainRemote;

    @Resource
    private RmsService rmsService;

    @Resource
    private ParamService paramService;

    @Resource
    private WeekendDefinitionDAO weekendDefinitionDAO;

    @Resource
    private CommonChainService commonChainService;

    @Resource
    private CostFormDAO costFormDAO;

    @Resource
    private CostFormService costFormService;

    @Resource
    CostReportItemService costReportItemService;

    private static final String REVENUE_DESCRIPTION =
            "1、总营收中包括了：客房营收与非客房营收；非客房营收主要分为商品营收、会员卡营收、餐饮营收三大类以及会议费用、违约金等，其中会议费用、违约金等费用均展示为其他营收\n" + "2、客房营收为剥除ota佣金、早餐费用费用的房费\n" + "3、餐饮营收为早餐费及其他用餐类费用\n";

    private static final String BUDGET_PROGRESS_DESCRIPTION =
            "1、该图表为总营收的预算、实际营收、预算完成度\n" + "2、预算为预计达成的指标，预算完成度为实际完成的数量/预算\n" + "3、图表内虚线代表100%的完成度，折线若在虚线之上则代表超额完成预算，若在预算之下则代表未完成预算";

    /**
     * 日成本明细项目旁小问号
     */
    private static final String DAY_COST_CHILD_ITEM_PROMPT = "因部分项目属性原因，每日经营成本的成本项目与月度经营成本项目有差异";

    @ApolloJsonValue("${room.gop.cost.item:[]}")
    private List<String> roomGopCostItem;

    private final int monthSlot = 6;

    @ApolloJsonValue("${cost.exclude.item:[]}")
    private List<String> costExclude;

    private static final String TOTAL_COST = "total_cost";

    private static final String TOTAL_COST_NAME = "总成本";

    private static final String GOPRATE = "GOP率";

    private static final String ROOM_GOP_RATE = "客房GOP率";

    private static final String GOODS_GOP_RATE = "小商品GOP率";

    private static final String GOODS_CODE = "goods";

    private static final int maxQueryMonth = 5;

    private static final int middleMonthDay = 13;

    private static final int middleMonthDayBI = 15;

    @ApolloJsonValue("${day.cost.report.config:{}}")
    private volatile Map<Integer, DayCostReportConfigBean> dayCostReportConfigBeanMap;

    @Resource
    private BigDataApiService bigDataApiService;


    public ApiResult<RevenueStatsDTO> queryRevenueStats(Integer chainId, Date beginDate, Date endDate, String dateType) {

        Date actualBeginDate = beginDate;

        if (DateUtil.compareDateOnly(beginDate, endDate) == 0) {
            actualBeginDate = DateUtil.subDays(beginDate, 6);
        }
        //判断是否是年

        List<ChainStatisticsDTO> chainStatistics = rmsService.queryChainStatistics(chainId, actualBeginDate, endDate);
        List<ChainStatisticsDTO> chainStatisticsForQuery = Safes.of(chainStatistics)
                .stream()
                .filter(c -> DateUtil.dateRange(c.getDate(), beginDate, endDate))
                .collect(Collectors.toList());

        CoreStatsDTO coreStatsDTO = getCoreStats(beginDate, endDate, chainStatisticsForQuery);
        List<RevenueItemDTO> revenueItems = getRevenueItems(beginDate, endDate, chainStatistics, dateType);
        BudgetProgressItemDTO budgetProgressItem = getTotalRevenueBudgetProgressItem(chainId, actualBeginDate, endDate);

        RevenueStatsDTO.RevenueStatsDTOBuilder revenueStatsBuilder = RevenueStatsDTO.builder();
        revenueStatsBuilder.chainId(chainId)
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .revenueDescription(REVENUE_DESCRIPTION)
                .budgetProgressDescription(BUDGET_PROGRESS_DESCRIPTION)
                .coreStatsDTO(coreStatsDTO)
                .revenueItems(Safes.of(revenueItems)
                        .stream()
                        .map(RevenueItemDTO::scaleFormat)
                        .collect(Collectors.toList()))
                .budgetProgress(budgetProgressItem);

        return ApiResult.success(revenueStatsBuilder.build());
    }

    CoreStatsDTO getHomePageCoreStats(Integer chainId, Date lastAccDate) {

        List<ChainStatisticsDTO> chainStatistics = rmsService.queryChainStatistics(chainId, lastAccDate, lastAccDate);
        return getCoreStats(lastAccDate, lastAccDate, chainStatistics);
    }

    private CoreStatsDTO getCoreStats(Date beginDate, Date endDate, List<ChainStatisticsDTO> chainStatistics) {

        ChainStatisticsDTO sumDTO = Optional.ofNullable(chainStatistics)
                .orElse(Collections.emptyList())
                .stream()
                .reduce(ChainStatistisDTOWrapper.empty(), ChainStatistisDTOWrapper::add);

        BigDecimal comprehensiveRevPar = Objects.equals(sumDTO.getRoomInventory(), NumberUtils.INTEGER_ZERO) ? BigDecimal.ZERO :
                sumDTO.getRoomIncome()
                        .add(sumDTO.getOtherIncome())
                        .divide(BigDecimal.valueOf(sumDTO.getRoomInventory()), BigDecimalRoundingModeConfig.SCALE,
                                BigDecimalRoundingModeConfig.ROUNDING_MODE);

        return CoreStatsDTO.builder()
                .avgRoomPrice(sumDTO.getNight()
                        .compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumDTO.getRoomIncome()
                        .divide(sumDTO.getNight(), BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE))
                .rentRatio(sumDTO.getRoomInventory() == null || sumDTO.getRoomInventory() == 0 ? BigDecimal.ZERO : sumDTO.getNight()
                        .multiply(BigDecimal.valueOf(100))
                        .divide(BigDecimal.valueOf(sumDTO.getRoomInventory()), BigDecimalRoundingModeConfig.SCALE,
                                BigDecimalRoundingModeConfig.ROUNDING_MODE))
                .revPar(comprehensiveRevPar)
                .totalRevenue(ChainStatistisDTOWrapper.totalRevenue(sumDTO)
                        .setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE))
                .build();
    }

    private final Map<CoreRevenueStatsTypeEnum, Function<ChainStatisticsDTO, BigDecimal>> coreRevenueStatsTypeEnumFunctionMap =
            ImmutableMap.of(CoreRevenueStatsTypeEnum.AVERAGE_ROOM_PRICE,
                    dto -> dto.getAverageRoomPrice() == null ? BigDecimal.ZERO : dto.getAverageRoomPrice(), CoreRevenueStatsTypeEnum.RENT_RATIO,
                    chainStatisticsDTO -> {
                        if (chainStatisticsDTO.getRoomInventory() == null || chainStatisticsDTO.getRoomInventory() == 0) {
                            return BigDecimal.ZERO;
                        } else {
                            return chainStatisticsDTO.getNight()
                                    .multiply(BigDecimal.valueOf(100))
                                    .divide(BigDecimal.valueOf(chainStatisticsDTO.getRoomInventory()), BigDecimalRoundingModeConfig.SCALE,
                                            BigDecimalRoundingModeConfig.ROUNDING_MODE);
                        }
                    }, CoreRevenueStatsTypeEnum.REV_PAR, dto -> dto.getComprehensiveRevPar() == null ? BigDecimal.ZERO : dto.getComprehensiveRevPar(),
                    CoreRevenueStatsTypeEnum.ROOM_REV_PAR, dto -> Safes.of(dto.getRevPar())
                            .setScale(2, BigDecimal.ROUND_HALF_UP));

    public ApiResult<CoreStatsDetailDTO> queryCoreStatsDetail(Integer chainId, Date beginDate, Date endDate, Integer type, String dateType) {

        Date actualBeginDate = beginDate;
        if (DateUtil.compareDateOnly(beginDate, endDate) == 0) {
            actualBeginDate = DateUtil.subDays(beginDate, 6);
        }
        int count = DateUtil.daysBetween(actualBeginDate, endDate) + 1;
        List<ChainStatisticsDTO> chainStatisticsForQuery = rmsService.queryChainStatistics(chainId, actualBeginDate, endDate);

        CoreStatsDetailDTO.CoreStatsDetailDTOBuilder builder = CoreStatsDetailDTO.builder();
        builder.chainId(chainId)
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate));

        List<RevenueItemDTO> revenueItems = coreRevenueStatsTypeEnumFunctionMap.entrySet()
                .stream()
                .map(entry -> {

                    CoreRevenueStatsTypeEnum coreRevenueStatsTypeEnum = entry.getKey();
                    Function<ChainStatisticsDTO, BigDecimal> valueGetter = entry.getValue();

                    List<RevenueItemDTO.DailyValue> dailyValues = Optional.ofNullable(chainStatisticsForQuery)
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(dto -> new RevenueItemDTO.DailyValue(DateUtil.printDate(dto.getDate()), valueGetter.apply(dto)
                                    .setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE)))
                            .collect(Collectors.toList());

                    BigDecimal sum = dailyValues.stream()
                            .filter(d -> DateUtil.dateRange(DateUtil.parseDate(d.getDate()), beginDate, endDate))
                            .map(RevenueItemDTO.DailyValue::getValue)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    ValueUnitEnum valueUnitEnum = getValueUnitType(coreRevenueStatsTypeEnum);

                    if (StringUtils.isNotEmpty(dateType) && (dateType.equals(DateTypeEnum.q.name()) || dateType.equals(DateTypeEnum.y.name()))) {
                        //按照月份聚合数据
                        Map<String, List<DailyValue>> monthGroupBy = dailyValues.stream().collect(Collectors.groupingBy(item -> item.getDate().substring(0, 7) + "-01"));
                        List<DailyValue> monthValues = monthGroupBy.entrySet().stream().map(item -> {
                            return new DailyValue(item.getKey(), item.getValue().stream().map(DailyValue::getValue).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                        }).sorted(Comparator.comparing(DailyValue::getDate).reversed()).collect(Collectors.toList());
                        dailyValues = monthValues;
                    }

                    return RevenueItemDTO.builder()
                            .id(coreRevenueStatsTypeEnum.getId())
                            .unitType(valueUnitEnum.valueUnit.getUnitType())
                            .name(coreRevenueStatsTypeEnum.getName())
                            .alias(coreRevenueStatsTypeEnum.getAlias())
                            .averageValue(new RevenueItemDTO.DailyValue(DateUtil.printDate(beginDate), count == 0 ? BigDecimal.ZERO :
                                    sum.divide(BigDecimal.valueOf(count), BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE)))
                            .dailyValues(dailyValues)
                            .build();
                })
                .collect(Collectors.toList());

        CoreStatsDetailDTO coreStatsDetailDTO = CoreStatsDetailDTO.builder()
                .chainId(chainId)
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .revenueItems(revenueItems)
                .build();
        return ApiResult.success(coreStatsDetailDTO);
    }

    private ValueUnitEnum getValueUnitType(CoreRevenueStatsTypeEnum coreRevenueStatsTypeEnum) {

        ValueUnitEnum valueUnitEnum = ValueUnitEnum.ONE;
        if (coreRevenueStatsTypeEnum.getUnitType() == 2) {
            valueUnitEnum = ValueUnitEnum.RATIO;
        }
        return valueUnitEnum;
    }

    private Function<ChainStatisticsDTO, BigDecimal> totalGetter = dto -> {
        BigDecimal total = BigDecimal.ZERO;
        if (dto.getRoomIncome() != null) {
            total = total.add(dto.getRoomIncome());
        }
        if (dto.getOtherIncome() != null) {
            total = total.add(dto.getOtherIncome());
        }
        return total;
    };

    private Function<ChainStatisticsDTO, BigDecimal> otherIncomeGetter = dto -> {
        BigDecimal otherIncome = dto.getOtherIncome();
        if (otherIncome == null || BigDecimal.ZERO.compareTo(otherIncome) == 0) {
            return BigDecimal.ZERO;
        }
        if (dto.getRestaurantIncome() != null) {
            otherIncome = otherIncome.subtract(dto.getRestaurantIncome());
        }
        if (dto.getGoodsIncome() != null) {
            otherIncome = otherIncome.subtract(dto.getGoodsIncome());
        }
        if (dto.getMemberCardIncome() != null) {
            otherIncome = otherIncome.subtract(dto.getMemberCardIncome());
        }
        if (otherIncome.compareTo(BigDecimal.ZERO) < 0) {
            AMonitor.meter("OTHER_INCOME_NEGATIVE");
            log.info("otherIncome is negative, data:{}", dto);
            //            otherIncome = BigDecimal.ZERO;
        }
        return otherIncome;
    };

    private Map<RevenueTypeEnum, Function<ChainStatisticsDTO, BigDecimal>> revenueTypeEnumFunctionMap =
            ImmutableMap.<RevenueTypeEnum, Function<ChainStatisticsDTO, BigDecimal>>builder().put(RevenueTypeEnum.TOTAL, totalGetter)
                    .put(RevenueTypeEnum.ROOM_RENT, ChainStatisticsDTO::getRoomIncome)
                    .put(RevenueTypeEnum.MEALS, ChainStatisticsDTO::getRestaurantIncome)
                    .put(RevenueTypeEnum.COMMODITY, ChainStatisticsDTO::getGoodsIncome)
                    .put(RevenueTypeEnum.MEMBER_CARD, ChainStatisticsDTO::getMemberCardIncome)
                    .put(RevenueTypeEnum.OTHERS, otherIncomeGetter)
                    .build();

    private Map<ChainBudgetProgressTypeEnum, Function<ChainBudgetDTO, BudgetProgressItemDTO.CompareData>> budgetProgressTypeEnumFunctionMap =
            ImmutableMap.<ChainBudgetProgressTypeEnum, Function<ChainBudgetDTO, BudgetProgressItemDTO.CompareData>>builder().put(
                            ChainBudgetProgressTypeEnum.TOTAL_REVENUE, chainBudgetDTO -> transformBigDecimal(chainBudgetDTO.getTotalRevenue()))
                    .put(ChainBudgetProgressTypeEnum.REV_PAR, chainBudgetDTO -> transformBigDecimal(chainBudgetDTO.getRevPar()))
                    .put(ChainBudgetProgressTypeEnum.ROOM_REV_PAR, chainBudgetDTO -> transformBigDecimal(chainBudgetDTO.getRoomRevPar()))
                    .put(ChainBudgetProgressTypeEnum.RENT_RATIO, chainBudgetDTO -> transformRentRatio(chainBudgetDTO.getRentRatio()))
                    .put(ChainBudgetProgressTypeEnum.AVERAGE_ROOM_PRICE, chainBudgetDTO -> transformBigDecimal(chainBudgetDTO.getAverageRoomPrice()))
                    .put(ChainBudgetProgressTypeEnum.ROOM_REVENUE, chainBudgetDTO -> transformBigDecimal(chainBudgetDTO.getRoomRevenue()))
                    .build();

    private BudgetProgressItemDTO.CompareData transformBigDecimal(ChainBudgetDTO.Budget<BigDecimal> budget) {

        BudgetProgressItemDTO.CompareData<BigDecimal> compareData;
        if (budget == null) {
            compareData = BudgetProgressItemDTO.CompareData.from(BigDecimal.ZERO, BigDecimal.ZERO);
        } else {
            compareData = BudgetProgressItemDTO.CompareData.from(budget.getBudget(), budget.getActual());
        }
        BudgetProgressItemDTO.CompareData.bigDecimalRatioSetter.accept(compareData);
        return compareData;
    }

    private BigDecimal scaleFormat(BigDecimal data) {

        if (data != null) {
            data = data.setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE);
        }
        return data;
    }

    private BudgetProgressItemDTO.CompareData transformRentRatio(ChainBudgetDTO.Budget<Double> budget) {

        BudgetProgressItemDTO.CompareData<BigDecimal> compareData;
        if (budget == null) {
            compareData = BudgetProgressItemDTO.CompareData.from(BigDecimal.ZERO, BigDecimal.ZERO);
        } else {
            compareData = BudgetProgressItemDTO.CompareData.from(BigDecimal.valueOf(budget.getBudget())
                    .multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(budget.getActual())
                    .multiply(BigDecimal.valueOf(100)));
        }
        BudgetProgressItemDTO.CompareData.bigDecimalRatioSetter.accept(compareData);
        return compareData;
    }

    public ApiResult<RevenueStatsDetailDTO> queryRevenueStatsDetail(Integer chainId, Date beginDate, Date endDate, String dateType) {

        RevenueStatsDetailDTO.RevenueStatsDetailDTOBuilder revenueStatsDetailBuilder = RevenueStatsDetailDTO.builder()
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .chainId(chainId);

        Date actualBeginDate = beginDate;
        if (DateUtil.compareDateOnly(beginDate, endDate) == 0) {
            actualBeginDate = DateUtil.subDays(beginDate, 6);
        }

        List<ChainStatisticsDTO> chainStatistics = rmsService.queryChainStatistics(chainId, actualBeginDate, endDate);

        List<RevenueItemDTO> revenueItems = getRevenueItems(beginDate, endDate, chainStatistics, dateType);

        revenueStatsDetailBuilder.revenueItems(Safes.of(revenueItems)
                .stream()
                .map(RevenueItemDTO::scaleFormat3)
                .collect(Collectors.toList()));

        return ApiResult.success(revenueStatsDetailBuilder.build());
    }

    /**
     * 分类营收
     *
     * @param beginDate
     * @param endDate
     * @param chainStatistics
     * @param dateType
     * @return
     */
    private List<RevenueItemDTO> getRevenueItems(Date beginDate, Date endDate, List<ChainStatisticsDTO> chainStatistics, String dateType) {

        ValueUnitEnum totalRevenueValueUnitEnum = null;

        List<RevenueItemDTO> revenueItems = Lists.newArrayList();
        for (Map.Entry<RevenueTypeEnum, Function<ChainStatisticsDTO, BigDecimal>> entry : revenueTypeEnumFunctionMap.entrySet()) {

            RevenueTypeEnum revenueTypeEnum = entry.getKey();
            Function<ChainStatisticsDTO, BigDecimal> valueGetter = entry.getValue();

            List<RevenueItemDTO.DailyValue> dailyValues = null;

            dailyValues = Optional.of(chainStatistics)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(dto -> new RevenueItemDTO.DailyValue(DateUtil.printDate(dto.getDate()), valueGetter.apply(dto)))
                    .collect(Collectors.toList());

            BigDecimal sum = dailyValues.stream()
                    .filter(d -> DateUtil.dateRange(DateUtil.parseDate(d.getDate()), beginDate, endDate))
                    .map(RevenueItemDTO.DailyValue::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (StringUtils.isNotEmpty(dateType) && (dateType.equals(DateTypeEnum.q.name()) || dateType.equals(DateTypeEnum.y.name()))) {
                //按照月份聚合数据
                Map<String, List<DailyValue>> monthGroupBy = dailyValues.stream().collect(Collectors.groupingBy(item -> item.getDate().substring(0, 7) + "-01"));
                List<DailyValue> monthValues = monthGroupBy.entrySet().stream().map(item -> {
                    return new DailyValue(item.getKey(), item.getValue().stream().map(DailyValue::getValue).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                }).sorted(Comparator.comparing(DailyValue::getDate).reversed()).collect(Collectors.toList());
                dailyValues = monthValues;
            }

            // 统一使用totalRevenue的单位
            totalRevenueValueUnitEnum = processValueUnit(revenueTypeEnum, dailyValues, totalRevenueValueUnitEnum);

            RevenueItemDTO item = RevenueItemDTO.builder()
                    .id(revenueTypeEnum.getCode())
                    .name(revenueTypeEnum.getName())
                    .alias(revenueTypeEnum.getAlias())
                    .unitType(totalRevenueValueUnitEnum.valueUnit.getUnitType())
                    .averageValue(new RevenueItemDTO.DailyValue(DateUtil.printDate(beginDate),
                            sum.setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE)))
                    .dailyValues(dailyValues)
                    .build();

            revenueItems.add(item);
        }

        return revenueItems;
    }

    private List<RevenueItemDTO> getRevenueItemsOriginal(Date beginDate, Date endDate, List<ChainStatisticsDTO> chainStatistics) {


        List<RevenueItemDTO> revenueItems = Lists.newArrayList();
        for (Map.Entry<RevenueTypeEnum, Function<ChainStatisticsDTO, BigDecimal>> entry : revenueTypeEnumFunctionMap.entrySet()) {

            RevenueTypeEnum revenueTypeEnum = entry.getKey();
            Function<ChainStatisticsDTO, BigDecimal> valueGetter = entry.getValue();

            List<RevenueItemDTO.DailyValue> dailyValues = Optional.of(chainStatistics)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(dto -> new RevenueItemDTO.DailyValue(DateUtil.printDate(dto.getDate()), valueGetter.apply(dto)))
                    .collect(Collectors.toList());

            BigDecimal sum = dailyValues.stream()
                    .filter(d -> DateUtil.dateRange(DateUtil.parseDate(d.getDate()), beginDate, endDate))
                    .map(RevenueItemDTO.DailyValue::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 统一使用totalRevenue的单位

            RevenueItemDTO item = RevenueItemDTO.builder()
                    .id(revenueTypeEnum.getCode())
                    .name(revenueTypeEnum.getName())
                    .alias(revenueTypeEnum.getAlias())
                    .averageValue(new RevenueItemDTO.DailyValue(DateUtil.printDate(beginDate),
                            sum.setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE)))
                    .dailyValues(dailyValues)
                    .build();

            revenueItems.add(item);
        }

        return revenueItems;
    }

    private ValueUnitEnum processValueUnit(RevenueTypeEnum revenueTypeEnum, List<RevenueItemDTO.DailyValue> dailyValues,
                                           ValueUnitEnum valueUnitEnum) {

        if (revenueTypeEnum.getUnitType() == 1) {

            ValueUnitEnum valueUnit = valueUnitEnum;
            if (valueUnit == null) {
                BigDecimal minValue = dailyValues.stream()
                        .map(RevenueItemDTO.DailyValue::getValue)
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);

                valueUnit = Stream.of(ValueUnitEnum.values())
                        .filter(e -> e.valueUnit.getValueRange()
                                .contains(minValue))
                        .findFirst()
                        .orElseGet(() -> {
                            log.warn("get value unit get null, revenueType:{} minValue:{}", revenueTypeEnum, minValue);
                            return ValueUnitEnum.ONE;
                        });
            }
            final ValueUnitEnum finalValueUnit = valueUnit;

            dailyValues.forEach(dailyValue -> dailyValue.setValue(finalValueUnit.valueUnit.valueMapper.apply(dailyValue.getValue())));

            return valueUnit;

        } else {
            return ValueUnitEnum.RATIO;
        }

    }

    public ApiResult<WeekendDefinitionQueryAreaDTO> queryWeekendDefinitionByArea(Integer deptId, LocalDate localDate) {

        if (Objects.isNull(deptId)) {
            throw new BusinessException("请选择区域");
        }

        localDate = Objects.isNull(localDate) ? LocalDate.now() : localDate;
        KpiTimeEntity kpiTimeEntity = KpiTimeUtil.getKpiTimeEntity(localDate);

        List<CommChainDTO> allChainListFromCache = commonChainService.getActiveChainListFromCache();
        Integer chainId = Safes.of(allChainListFromCache)
                .stream()
                .filter(dto -> Objects.nonNull(dto.getChainId()))
                .map(CommChainDTO::getChainId)
                .findFirst()
                .orElse(null);

        if (Objects.isNull(chainId)) {
            return ApiResult.success(WeekendDefinitionQueryAreaDTO.builder()
                    .build());
        }
        ApiResult<WeekendDefinitionQueryDTO> weekResult = queryWeekendDefinition(chainId);
        if (Objects.isNull(weekResult) || Objects.isNull(weekResult.getResult()) || CollectionUtils.isEmpty(weekResult.getResult()
                .getYears())) {
            return ApiResult.success(WeekendDefinitionQueryAreaDTO.builder()
                    .build());
        }

        WeekendDefinitionQueryDTO result = weekResult.getResult();
        List<WeekendDefinitionQueryAreaDTO.Year> list = Lists.newArrayList();
        for (WeekendDefinitionQueryDTO.Year year : result.getYears()) {
            WeekendDefinitionQueryAreaDTO.Year areaYear = new WeekendDefinitionQueryAreaDTO.Year();
            List<WeekendDefinitionQueryAreaDTO.Weekend> weekList = Lists.newArrayList();
            areaYear.setWeekends(weekList);
            areaYear.setYear(year.getYear());
            year.getWeekends()
                    .stream()
                    .forEach(week -> {
                        Boolean enable = true;
                        //年份大于当前年 || 年份相等 周大于 当前周
                        if (Objects.isNull(year.getYear()) || (year.getYear() > kpiTimeEntity.getTrueYear()) || (year.getYear()
                                .equals(kpiTimeEntity.getTrueYear()) && week.getWeekNo() > kpiTimeEntity.getWeekIndex())) {
                            enable = false;
                        }
                        weekList.add(WeekendDefinitionQueryAreaDTO.Weekend.builder()
                                .beginDate(week.getBeginDate())
                                .endDate(week.getEndDate())
                                .enable(enable)
                                .weekNo(week.getWeekNo())
                                .build());
                    });
            list.add(areaYear);
        }
        return ApiResult.success(WeekendDefinitionQueryAreaDTO.builder()
                .years(list)
                .deptId(deptId)
                .build());
    }

    public ApiResult<WeekendDefinitionQueryDTO> queryWeekendDefinition(Integer chainId) {

        if (Objects.isNull(chainId)) {
            throw new BusinessException("请选择酒店");
        }

        ApiResult<AtourChainDTO> apiResult = chainRemote.getChainById(chainId);
        if (apiResult == null || apiResult.getCode() != ApiResult.DEFAULT_SUCCEED_CODE || apiResult.getResult() == null) {
            throw new BusinessException("未找到酒店");
        }

        AtourChainDTO atourChainDTO = apiResult.getResult();
        Date currentAccDate = paramService.getCurrentAccDate(chainId);
        Date lastAccDate = DateUtils.addYears(currentAccDate, 1);

        Date chainOpeningDate = atourChainDTO.getOpeningDate();
        if (chainOpeningDate == null) {
            chainOpeningDate = DateUtil.subMonths(lastAccDate, 12);
        }

        WeekendDefinitionQueryDTO.WeekendDefinitionQueryDTOBuilder builder = WeekendDefinitionQueryDTO.builder()
                .chainId(chainId);

        List<WeekendDefinitionEntity> weekendDefinitionEntities = weekendDefinitionDAO.selectByDate(chainOpeningDate, lastAccDate);

        Table<Integer, Integer, DateRange> dateRangeTable = HashBasedTable.create();
        weekendDefinitionEntities.forEach(w -> {
            DateRange dateRange = dateRangeTable.get(w.getYear(), w.getWeekNo5());
            if (dateRange == null) {
                dateRange = new DateRange(w.getDate(), w.getDate());
                dateRangeTable.put(w.getYear(), w.getWeekNo5(), dateRange);
            } else {
                if (DateUtil.dateEarlier(w.getDate(), dateRange.begin)) {
                    dateRange.begin = w.getDate();
                }
                if (DateUtil.dateEarlier(dateRange.end, w.getDate())) {
                    dateRange.end = w.getDate();
                }
            }
        });

        List<WeekendDefinitionQueryDTO.Year> years = Lists.newArrayList();

        dateRangeTable.rowMap()
                .forEach((year, weekNoMap) -> {
                    List<WeekendDefinitionQueryDTO.Weekend> weekends = Lists.newArrayList();
                    List<WeekendDefinitionQueryDTO.Weekend> quarters = Lists.newArrayList();
                    WeekendDefinitionQueryDTO.Year yearDTO = WeekendDefinitionQueryDTO.Year.builder()
                            .year(year)
                            .weekends(weekends)
                            .quarters(quarters)
                            .build();
                    List<DateRange> yearDateRange = Lists.newArrayList();
                    for (Pair<String, String> quarter : quarterlist) {
                        DateRange dateRange = new DateRange(DateUtil.parseDate(year + "-" + quarter.getFirst()),
                                DateUtil.parseDate(year + "-" + quarter.getSecond()));
                        yearDateRange.add(dateRange);
                    }

                    weekNoMap.forEach((weekNo, dateRange) -> {
                        boolean enable = DateUtil.subDays(currentAccDate, 1)
                                .after(dateRange.begin);
                        Weekend weekend = Weekend.builder()
                                .weekNo(weekNo)
                                .beginDate(DateUtil.printDate(dateRange.begin))
                                .endDate(DateUtil.printDate(dateRange.end))
                                .enable(enable)
                                .build();

                        weekends.add(weekend);
                    });

                    Date maxDateRange = weekends.stream().filter(Weekend::getEnable).map(item->DateUtil.parseDate(item.getBeginDate())).max(Comparator.comparing(Function.identity())).orElse(null);
                    Date minDateRange = weekends.stream().filter(Weekend::getEnable).map(item->DateUtil.parseDate(item.getEndDate())).min(Comparator.comparing(Function.identity())).orElse(null);

                    quarters.addAll(parseWeekend(maxDateRange, minDateRange, yearDateRange));

                    Date firstDate = DateUtil.parseDate(year + "-01-01");
                    Date endDate = DateUtil.parseDate(year + "-12-31");

                    yearDTO.setYearSelect(parseWeekend(maxDateRange, minDateRange, Lists.newArrayList(new DateRange(firstDate, endDate))).get(0));

                    years.add(yearDTO);

                });

        return ApiResult.success(builder.years(years)
                .build());
    }


    private static List<Weekend> parseWeekend(Date maxDateRange, Date minDateRange, List<DateRange> dateRangeOrigins) {
        List<Weekend> list = Lists.newArrayList();
        for (int i = 0; i < dateRangeOrigins.size(); i++) {
            DateRange dateRangeOrigin = dateRangeOrigins.get(i);
            Date begin = dateRangeOrigin.begin;
            Date end = dateRangeOrigin.end;
            boolean enabled = true;
            if (DateUtil.dateEarlier(dateRangeOrigin.getEnd(), minDateRange) || DateUtil.dateEarlier(maxDateRange, dateRangeOrigin.getBegin())) {
                enabled = false;
            }
            //最小时间落在dateRangeOrigin内
            if (DateUtil.dateEarlier(dateRangeOrigin.begin, minDateRange) && DateUtil.dateEarlier(minDateRange, dateRangeOrigin.end)) {
                begin = minDateRange;
            }

            //最大时间落在dateRangeOrigin内
            if (DateUtil.dateEarlier(dateRangeOrigin.begin, maxDateRange) && DateUtil.dateEarlier(maxDateRange, dateRangeOrigin.end)) {
                end = maxDateRange;
            }
            list.add(Weekend.builder()
                    .weekNo(i + 1)
                    .enable(enabled)
                    .beginDate(DateUtil.printDate(begin))
                    .endDate(DateUtil.printDate(end))
                    .build());
        }
        return list;
    }

    /**
     * 获取成本项配置中的所有子项
     *
     * @param costReportItemConfigBeanList
     * @return
     */
    public List<String> getCostsItemConfig(List<CostReportItemConfigBean> costReportItemConfigBeanList) {

        List<CostReportItemConfigBean> childCodesListBean = Lists.newArrayListWithCapacity(costReportItemConfigBeanList.size());
        costReportItemConfigBeanList.forEach(e -> {
            childCodesListBean.addAll(e.getChildItem());
        });
        List<String> itemCodesList = childCodesListBean.stream()
                .map(CostReportItemConfigBean::getItemCode)
                .collect(Collectors.toList());
        return itemCodesList;
    }

    /**
     * 获取成本项配置中的所有子项
     *
     * @param costReportItemConfigBeanList
     * @return
     */
    public Map<String, String> getCostsItemConfigMap(List<CostReportItemConfigBean> costReportItemConfigBeanList) {

        List<CostReportItemConfigBean> childCodesListBean = Lists.newArrayListWithCapacity(costReportItemConfigBeanList.size());
        costReportItemConfigBeanList.forEach(e -> {
            childCodesListBean.addAll(e.getChildItem());
        });
        Map<String, String> itemCodesList = childCodesListBean.stream()
                .collect(Collectors.toMap(CostReportItemConfigBean::getItemCode, CostReportItemConfigBean::getItemName));
        return itemCodesList;
    }

    /**
     * 判斷指定酒店对应类型过往统计数据是否存在
     *
     * @param chainId
     * @param dataType
     * @return
     */
    public boolean costOrGopDatasExist(Integer chainId, Integer dataType) {
        //1成本 2 gop率
        List<Integer> chainIdList = Lists.newArrayListWithCapacity(1);
        chainIdList.add(chainId);
        List<CostReportItemConfigBean> costReportItemConfigBeanList = costReportItemService.getCostReportItemConfigBean();
        List<String> itemCodesList = getCostsItemConfig(costReportItemConfigBeanList);
        String earliestMonth = "";
        if (dataType.equals(CostFormTypeEnum.COST.getCode())) {
            earliestMonth = costFormDAO.costEarliestMonth(chainId, itemCodesList, dataType);
        } else {
            earliestMonth = costFormDAO.costEarliestMonth(chainId, GopItemEnum.getAllDesc(), dataType);
        }
        if (StringUtil.isNotBlank(earliestMonth)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 查询指定酒店日成本统计相关数据
     *
     * @param chainId   酒店Id
     * @param queryDate 查询日,可以为空
     * @param night     是否勾选单间夜成本 0 ：未勾选 1：勾选
     * @return
     */
    public CostDataDTO queryDayCostData(Integer chainId, Date queryDate, Integer night) {

        Integer queryType = CostFormTypeEnum.DAY_COST.getCode();
        // 查询间夜
        if (night == 1) {
            queryType = CostFormTypeEnum.DAY_NIGHT_COST.getCode();
        }

        String lastDate = costFormDAO.queryEarliestDate(chainId, queryType);
        String earliestDate = costFormDAO.queryLastDate(chainId, queryType);

        if (Objects.isNull(queryDate)) {
            queryDate = DateUtil.parseDatetime(lastDate, DateUtil.DATE_FORMAT);
        }
        String queryDateStr = DateUtil.formatDate(queryDate, DateUtil.DATE_FORMAT);
        String queryMonth = DateUtil.formatDate(queryDate, DateUtil.FORMAT_MONTH);
        String beginMonth = DateUtil.formatDate(DateUtil.addMonths(queryDate, -4), DateUtil.FORMAT_MONTH);
        String endMonth = DateUtil.formatDate(DateUtil.addMonths(queryDate, 4), DateUtil.FORMAT_MONTH);


        ChainDTO chainDTO = chainRemote.getPmsChainById(chainId).getResult();
        if (Objects.isNull(chainDTO)) {
            throw new BusinessException(ResponseCodeEnum.CHAIN_NOT_EXIST.getMessage(), ResponseCodeEnum.CHAIN_NOT_EXIST.getCode());
        }

        Integer isGuide = chainDTO.getIsGuide();
        DayCostReportConfigBean dayCostReportConfigBean = dayCostReportConfigBeanMap.get(isGuide);
        if (Objects.isNull(dayCostReportConfigBean)) {
            return null;
        }

        List<Integer> chainIdList = new ArrayList<>();
        chainIdList.add(chainDTO.getChainId());

        List<DayCostReportConfigBean.CostItem> costItems = dayCostReportConfigBean.getCostItems();
        if (CollectionUtils.isEmpty(costItems)) {
            return null;
        }

        List<CostDataItemDTO> costDataItemDTOS = new ArrayList<>();
        for (DayCostReportConfigBean.CostItem costItem : costItems) {
            //父级科目code
            String costCode = costItem.getCostCode();
            //父级科目名称
            String costName = costItem.getCostName();
            List<DayCostReportConfigBean.ChildCostItem> childCostItemList = costItem.getChildCostItemList();
            List<String> childCostCodeList = childCostItemList.stream().map(DayCostReportConfigBean.ChildCostItem::getCostCode).collect(Collectors.toList());

            List<CostFormEntity> dayCostFormEntities = costFormDAO.queryCostDayList(chainIdList,
                    childCostCodeList, queryDateStr, queryType);

            Map<String, CostFormEntity> childCostItemMap = dayCostFormEntities.stream()
                    .collect(Collectors.toMap(CostFormEntity::getItemCode, Function.identity(), (key1, key2) -> key2));

            // 父级科目
            CostDataItemDTO costDataItemDTO = new CostDataItemDTO();
            costDataItemDTO.setRenvenuType(costCode);
            costDataItemDTO.setRenvenuTypeName(costName);
            costDataItemDTO.setNight(night);

            BigDecimal sum = BigDecimal.ZERO;
            List<CostDataItemPartDTO> costDataItemPartDTOS = new ArrayList<>();
            for (DayCostReportConfigBean.ChildCostItem childCostItem : childCostItemList) {
                // 子级科目
                String childCostCode = childCostItem.getCostCode();
                String childCostName = childCostItem.getCostName();

                CostFormEntity costFormEntity = childCostItemMap.get(childCostCode);
                BigDecimal childCost = Objects.isNull(costFormEntity) ? BigDecimal.ZERO : Safes.of(costFormEntity.getValue());

                CostDataItemPartDTO costDataItemPartDTO = new CostDataItemPartDTO();
                costDataItemPartDTO.setPartItem(childCostName);
                costDataItemPartDTO.setPartItemCode(childCostCode);
                costDataItemPartDTO.setCost(childCost);
                costDataItemPartDTO.setPrompt(DAY_COST_CHILD_ITEM_PROMPT);
                costDataItemPartDTOS.add(costDataItemPartDTO);

                sum = sum.add(Objects.isNull(costFormEntity) ? BigDecimal.ZERO : costFormEntity.getValue());
            }
            costDataItemDTO.setRevenueDataItemPart(costDataItemPartDTOS);
            // 父级科目成本
            costDataItemDTO.setRevenueCost(sum);

            List<CostFormEntity> monthCostFormEntities;
            // 查询间夜
            if (night == 1) {
                monthCostFormEntities = costFormDAO.queryList(chainIdList, childCostCodeList, beginMonth, endMonth, CostFormTypeEnum.SINGLEROOM_SALE.getCode());
            } else {
                monthCostFormEntities = costFormDAO.queryList(chainIdList, childCostCodeList, beginMonth, endMonth, CostFormTypeEnum.COST.getCode());
            }

            List<CostDataItemDTO.MonthlyValue> monthlyValues = bulidCostMonthData(monthCostFormEntities, queryMonth);
            costDataItemDTO.setCostTrendList(monthlyValues);

            costDataItemDTOS.add(costDataItemDTO);
        }


        // 计算总成本
        List<CostDataItemPartDTO> totalCostRevenueDataItemPart = new ArrayList<>();
        BigDecimal totalCost = BigDecimal.ZERO;

        Map<String, BigDecimal> totalMonthCostMap = Maps.newHashMap();
        for (CostDataItemDTO dataItemDTO : costDataItemDTOS) {
            BigDecimal revenueCost = dataItemDTO.getRevenueCost();
            String renvenuTypeName = dataItemDTO.getRenvenuTypeName();
            String renvenuType = dataItemDTO.getRenvenuType();
            BigDecimal renvenueCost = dataItemDTO.getRevenueCost();

            List<CostDataItemDTO.MonthlyValue> costTrendList = dataItemDTO.getCostTrendList();
            if (CollectionUtils.isNotEmpty(costTrendList)) {
                Map<String, BigDecimal> monthCostMap = costTrendList.stream().collect(Collectors.toMap(CostDataItemDTO.MonthlyValue::getMonth, CostDataItemDTO.MonthlyValue::getValue));
                monthCostMap.forEach(
                        (k, v) -> totalMonthCostMap.put(k, Safes.of(totalMonthCostMap.get(k)).add(v))
                );
            }

            totalCost = totalCost.add(renvenueCost);

            CostDataItemPartDTO costDataItemPartDTO = new CostDataItemPartDTO();
            costDataItemPartDTO.setPartItemCode(renvenuType);
            costDataItemPartDTO.setPartItem(renvenuTypeName);
            costDataItemPartDTO.setCost(revenueCost);
            costDataItemPartDTO.setPrompt(DAY_COST_CHILD_ITEM_PROMPT);
            totalCostRevenueDataItemPart.add(costDataItemPartDTO);
        }

        // 总成本折线图数据
        List<CostDataItemDTO.MonthlyValue> totalMonthlyValues = new ArrayList<>();
        if (MapUtils.isNotEmpty(totalMonthCostMap)) {
            totalMonthCostMap.forEach(
                    (k, v) -> {
                        CostDataItemDTO.MonthlyValue monthlyValue = new CostDataItemDTO.MonthlyValue();
                        monthlyValue.setMonth(k);
                        monthlyValue.setValue(v);
                        totalMonthlyValues.add(monthlyValue);
                    }
            );
        }

        CostDataItemDTO totalCostDataItem = new CostDataItemDTO();
        totalCostDataItem.setRenvenuType(TOTAL_COST);
        totalCostDataItem.setRenvenuTypeName(TOTAL_COST_NAME);
        totalCostDataItem.setNight(night);
        totalCostDataItem.setRevenueCost(totalCost);
        totalCostDataItem.setRevenueDataItemPart(totalCostRevenueDataItemPart);
        totalCostDataItem.setCostTrendList(totalMonthlyValues);
        costDataItemDTOS.add(0, totalCostDataItem);

        CostDataDTO costDataDTO = new CostDataDTO();
        costDataDTO.setQueryDate(DateUtil.formatDate(queryDate, DateUtil.DATE_FORMAT));
        costDataDTO.setEarliestDate(earliestDate);
        costDataDTO.setLastDate(lastDate);
        costDataDTO.setItem(costDataItemDTOS);
        return costDataDTO;
    }

    /***
     * 展示所选月份及之前五个月的成本折线图
     * 若门店仅有一个月的成本数据，则不展示成本趋势入口及成
     * 若所选月份之前的月份不足五个月，则有几个月展示几个月
     * 若所选月份为有数据的第一个月，则展示所选月份及向后最多5个月成本数据的折线图
     *
     * @param monthCostFormEntities 当前月及前四个月和后四个月的数据
     * */
    public List<CostDataItemDTO.MonthlyValue> bulidCostMonthData(List<CostFormEntity> monthCostFormEntities, String queryMonth) {

        Map<String, BigDecimal> monthCostMap = Maps.newHashMap();
        for (CostFormEntity monthCostFormEntity : monthCostFormEntities) {
            String eventMonth = monthCostFormEntity.getEventMonth();
            BigDecimal value = monthCostFormEntity.getValue();
            BigDecimal sum = Safes.of(monthCostMap.get(eventMonth)).add(value);
            monthCostMap.put(eventMonth, sum);
        }

        if (MapUtils.isEmpty(monthCostMap)) {
            AMonitor.meter("bulidDayCostMonthData.monthCostMap.empty");
            return null;
        }
        // 仅有一个月
        if (monthCostMap.size() == 1) {
            AMonitor.meter("bulidDayCostMonthData.monthCostMap.size.one");
            return null;
        }

        List<CostDataItemDTO.MonthlyValue> monthlyValues = new ArrayList<>();

        // 查询月前面的月份没有数据,有几个月数据展示几个月数据
        if (!monthCostMap.containsKey(addMonth(queryMonth, -1))) {
            monthCostMap.forEach(
                    (k, v) -> {
                        CostDataItemDTO.MonthlyValue monthlyValue = new CostDataItemDTO.MonthlyValue();
                        monthlyValue.setMonth(k);
                        monthlyValue.setValue(v);
                        monthlyValues.add(monthlyValue);
                    }
            );
        } else {
            // 查询月前面的月份有数据,前面有几个月的数据展示几个月的数据
            for (int i = 4; i >= 0; i--) {
                String s = addMonth(queryMonth, -i);
                if (monthCostMap.containsKey(s)) {
                    CostDataItemDTO.MonthlyValue monthlyValue = new CostDataItemDTO.MonthlyValue();
                    monthlyValue.setMonth(s);
                    monthlyValue.setValue(monthCostMap.get(s));
                    monthlyValues.add(monthlyValue);
                }
            }
        }
        return monthlyValues;
    }

    public String addMonth(String month, int addMonth) {
        Date date = DateUtil.parseDatetime(month, DateUtil.FORMAT_MONTH);
        Date addDate = DateUtil.addMonths(date, addMonth);
        return DateUtil.formatDate(addDate, DateUtil.FORMAT_MONTH);
    }


    /**
     * 查询指定酒店月成本统计相关数据
     *
     * @param chainId
     * @param queryMonth
     * @param night      是否勾选单间夜成本 0 ：未勾选 1：勾选
     * @return
     */
    public CostDataDTO queryCostData(Integer chainId, Date queryMonth, Integer night) {

        Date currentDate = DateUtil.getCurrentDate();
        String lastQueryMonth = "";
        int currentDay = DateUtil.getDay(currentDate);
        int realQueryIndex = 0;
        if (currentDay < middleMonthDay) {
            realQueryIndex = 2;
        } else {
            realQueryIndex = 1;
        }
        if (queryMonth == null) {
            queryMonth = DateUtil.addMonths(currentDate, -realQueryIndex);
            lastQueryMonth = DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH);
        } else {
            lastQueryMonth = DateUtil.formatDate(DateUtil.addMonths(currentDate, -realQueryIndex), DateUtil.FORMAT_MONTH);
        }
        List<Integer> chainIdList = Lists.newArrayListWithCapacity(1);
        chainIdList.add(chainId);
        List<CostReportItemConfigBean> costReportItemConfigBeanList = costReportItemService.getCostReportItemConfigBean();
        ChainDTO chainDTO = chainRemote.getPmsChainById(chainId).getResult();
        if (Objects.isNull(chainDTO)) {
            AMonitor.meter("chainNotExist_error");
            throw new com.atour.web.exception.BusinessException(ResponseCodeEnum.CHAIN_NOT_EXIST.getMessage(), ResponseCodeEnum.CHAIN_NOT_EXIST.getCode());
        }
        //特许店统计项chainCostType 为0 和2 的 0为特许 1为直营 2通用
        if (Objects.equals(chainDTO.getIsGuide().intValue(), ChainTypeEnum.FRANCHISE.getType())) {
            costReportItemConfigBeanList = costReportItemConfigBeanList.stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType()
                            .intValue() != ChainTypeEnum.STORE.getType())
                    .collect(Collectors.toList());
        } else {
            costReportItemConfigBeanList = costReportItemConfigBeanList.stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType()
                            .intValue() != ChainTypeEnum.FRANCHISE.getType())
                    .collect(Collectors.toList());
        }
        Map<String, CostReportItemConfigBean> parentCodeMapping = costReportItemConfigBeanList.stream()
                .collect(Collectors.toMap(item -> item.getItemCode(), Function.identity()));
        CostDataDTO revenueDataDTO = new CostDataDTO();
        revenueDataDTO.setQueryMonth(DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH));
        List<CostDataItemDTO> costDataItemDTOS = Lists.newArrayList();
        CostDataItemDTO totalCostDataItem = new CostDataItemDTO();
        List<CostDataItemPartDTO> totalDTOS = Lists.newArrayList();
        Map<String, CostDataItemDTO.MonthlyValue> totalMonthValue = Maps.newHashMap();
        totalCostDataItem.setRenvenuType(TOTAL_COST);
        totalCostDataItem.setRenvenuTypeName(TOTAL_COST_NAME);
        totalCostDataItem.setNight(night);
        List<CostFormEntity> costFormEntitiesNoneType = Lists.newArrayList();
        List<String> itemCodesList = getCostsItemConfig(costReportItemConfigBeanList);
        String earliestMonth = costFormDAO.costEarliestMonth(chainId, itemCodesList, CostFormTypeEnum.COST.getCode());
        revenueDataDTO.setEarliestMonth(earliestMonth);
        revenueDataDTO.setLastMonth(lastQueryMonth);
        Date oldQueryMonth = queryMonth;
        //判断查询月份数据是否是有数据的第一个月
        if (DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH)
                .equals(earliestMonth)) {
            //判断有数据的是否是第一个月
            queryMonth = DateUtil.addMonths(queryMonth, maxQueryMonth);
        }
        if (night == 0) {
            //未勾选单间夜成本
            costFormEntitiesNoneType = costFormDAO.queryList(chainIdList, itemCodesList,
                    DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
                    DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.COST.getCode());
        } else {
            costFormEntitiesNoneType = costFormService.queryCostFormList(chainIdList, null,
                    DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
                    DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.SINGLEROOM_SALE.getCode());
        }
        costFormEntitiesNoneType = costFormEntitiesNoneType.stream()
                .sorted(Comparator.comparing(CostFormEntity::getId))
                .collect(Collectors.toList());
        Map<String, BigDecimal> itemCostMapping = costFormEntitiesNoneType.stream()
                .collect(Collectors.toMap(item -> (item.getItemCode() + "_" + item.getEventMonth()), CostFormEntity::getValue, (k1, k2) -> k2));
        final BigDecimal[] costZong = {new BigDecimal(0)};
        Date finalQueryMonth = queryMonth;
        parentCodeMapping.forEach((k, v) -> {
            CostDataItemDTO costDataItemDTO = new CostDataItemDTO();
            costDataItemDTO.setRenvenuType(k);
            costDataItemDTO.setRenvenuTypeName(v.getItemName());
            List<CostDataItemPartDTO> itemPartDTOS = Lists.newArrayList();
            //区分子项是直营还是特许
            List<CostReportItemConfigBean> childList = filterTypeItem(v, chainDTO.getIsGuide());
            Map<String, CostDataItemDTO.MonthlyValue> itemMonthValue = Maps.newHashMap();
            final BigDecimal[] parentCost = {new BigDecimal(0)};
            CostDataItemPartDTO totalItem = new CostDataItemPartDTO();
            childList.forEach(child -> {
                CostDataItemPartDTO partDTO = new CostDataItemPartDTO();
                String itemName = child.getItemName();
                String itemCode = child.getItemCode();
                partDTO.setPartItem(itemName);
                partDTO.setPartItemCode(itemCode);
                //构造子项成本趋势数据
                for (int i = 0; i < monthSlot; i++) {
                    String month = DateUtil.formatDate(DateUtil.addMonths(finalQueryMonth, 0 - i), DateUtil.FORMAT_MONTH);
                    if (itemCostMapping.get(itemCode + "_" + month) != null) {//判断 当前月数据是否存在
                        CostDataItemDTO.MonthlyValue monthlyValue = new CostDataItemDTO.MonthlyValue();
                        monthlyValue.setParentKey(k);
                        monthlyValue.setMonth(month);
                        monthlyValue.setValue(Safes.of(itemMonthValue.get(month) != null ? itemMonthValue.get(month)
                                        .getValue() : BigDecimal.ZERO)
                                .add(Safes.of(itemCostMapping.get(itemCode + "_" + month))));
                        itemMonthValue.put(month, monthlyValue);
                    }
                }
                partDTO.setCost(Safes.of(itemCostMapping.get(itemCode + "_" + DateUtil.formatDate(oldQueryMonth, DateUtil.FORMAT_MONTH))));
                itemPartDTOS.add(partDTO);
                parentCost[0] = parentCost[0].add(partDTO.getCost());
            });
            costDataItemDTO.setRevenueDataItemPart(itemPartDTOS);
            costDataItemDTO.setNight(night);
            costDataItemDTO.setRevenueCost(parentCost[0]);
            List<CostDataItemDTO.MonthlyValue> costTrendList = Lists.newArrayList(itemMonthValue.values());
            Collections.sort(costTrendList);
            costDataItemDTO.setCostTrendList(costTrendList);
            costDataItemDTOS.add(costDataItemDTO);
            costTrendList.forEach(e -> {
                CostDataItemDTO.MonthlyValue newMonthValue = new CostDataItemDTO.MonthlyValue();
                newMonthValue.setMonth(e.getMonth());
                newMonthValue.setValue(Safes.of(totalMonthValue.get(e.getMonth()) != null ? totalMonthValue.get(e.getMonth())
                                .getValue() : BigDecimal.ZERO)
                        .add(Safes.of(e.getValue())));
                totalMonthValue.put(e.getMonth(), newMonthValue);
            });
            totalItem.setPartItemCode(k);
            totalItem.setPartItem(v.getItemName());
            totalItem.setCost(Safes.of(parentCost[0]));
            totalDTOS.add(totalItem);
            costZong[0] = costZong[0].add(Safes.of(parentCost[0]));
        });
        totalCostDataItem.setRevenueCost(costZong[0]);
        totalCostDataItem.setRevenueDataItemPart(totalDTOS);
        List<CostDataItemDTO.MonthlyValue> totalTrendList = Lists.newArrayList(totalMonthValue.values());
        Collections.sort(totalTrendList);
        totalCostDataItem.setCostTrendList(totalTrendList);
        //构造总成本
        costDataItemDTOS.add(0, totalCostDataItem);
        revenueDataDTO.setItem(costDataItemDTOS);
        return revenueDataDTO;
    }

    /**
     * 查询指定酒店月成本统计相关数据
     *
     * @param chainId
     * @param queryMonth
     * @param night      是否勾选单间夜成本 0 ：未勾选 1：勾选
     * @return
     */
    public CostDataDTO queryCostDataFromBI(Integer chainId, Date queryMonth, Integer night) {
        Date currentDate = DateUtil.getCurrentDate();
        String lastQueryMonth = "";
        int currentDay = DateUtil.getDay(currentDate);
        int realQueryIndex = 0;
        if (currentDay < middleMonthDayBI) {
            realQueryIndex = 2;
        } else {
            realQueryIndex = 1;
        }
        if (queryMonth == null) {
            queryMonth = DateUtil.addMonths(currentDate, -realQueryIndex);
            lastQueryMonth = DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH);
        } else {
            lastQueryMonth = DateUtil.formatDate(DateUtil.addMonths(currentDate, -realQueryIndex), DateUtil.FORMAT_MONTH);
        }
        List<Integer> chainIdList = Lists.newArrayListWithCapacity(1);
        chainIdList.add(chainId);
        List<CostReportItemConfigBean> costReportItemConfigBeanList = costReportItemService.getCostReportItemConfigBeanV2();
        ChainDTO chainDTO = chainRemote.getPmsChainById(chainId).getResult();
        if (Objects.isNull(chainDTO)) {
            AMonitor.meter("chainNotExist_error");
            throw new com.atour.web.exception.BusinessException(ResponseCodeEnum.CHAIN_NOT_EXIST.getMessage(), ResponseCodeEnum.CHAIN_NOT_EXIST.getCode());
        }
        //特许店统计项chainCostType 为0 和2 的 0为特许 1为直营 2通用
        if (Objects.equals(chainDTO.getIsGuide().intValue(), ChainTypeEnum.FRANCHISE.getType())) {
            costReportItemConfigBeanList = costReportItemConfigBeanList.stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType()
                            .intValue() != ChainTypeEnum.STORE.getType())
                    .collect(Collectors.toList());
        } else {
            costReportItemConfigBeanList = costReportItemConfigBeanList.stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType()
                            .intValue() != ChainTypeEnum.FRANCHISE.getType())
                    .collect(Collectors.toList());
        }
        Map<String, CostReportItemConfigBean> parentCodeMapping = costReportItemConfigBeanList.stream()
                .collect(Collectors.toMap(CostReportItemConfigBean::getItemCode, Function.identity()));

        CostDataDTO revenueDataDTO = new CostDataDTO();
        revenueDataDTO.setQueryMonth(DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH));
        List<CostDataItemDTO> costDataItemDTOS = Lists.newArrayList();
        CostDataItemDTO totalCostDataItem = new CostDataItemDTO();
        List<CostDataItemPartDTO> totalDTOS = Lists.newArrayList();
        Map<String, CostDataItemDTO.MonthlyValue> totalMonthValue = Maps.newHashMap();
        totalCostDataItem.setRenvenuType(TOTAL_COST);
        totalCostDataItem.setRenvenuTypeName(TOTAL_COST_NAME);
        totalCostDataItem.setNight(night);
        totalCostDataItem.setPriority(1);

        List<CostInf> costFormEntitiesNoneType;

        Map<String, String> costsItemConfigMap = getCostsItemConfigMap(costReportItemConfigBeanList);
        List<String> itemCodesList = Lists.newArrayList(costsItemConfigMap.keySet());

        String earliestMonth = "2022-09";
        revenueDataDTO.setEarliestMonth(earliestMonth);
        revenueDataDTO.setLastMonth(lastQueryMonth);
        Date oldQueryMonth = queryMonth;
        //判断查询月份数据是否是有数据的第一个月
        if (DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH)
                .equals(earliestMonth)) {
            //判断有数据的是否是第一个月
            queryMonth = DateUtil.addMonths(queryMonth, maxQueryMonth);
        }
        Set<String> noValueSet = new HashSet<>();
        if (night == 0) {
            //未勾选单间夜成本
            //查询GOP bigdata
            List<Map<String, String>> monthGopInfoList = bigDataApiService.queryFinGopOperateInfo(chainId, DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH), DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH));
            costFormEntitiesNoneType = Lists.newArrayList();

            for (Map<String, String> gopInfoMap : monthGopInfoList) {
                for (String key : itemCodesList) {
                    CostFromBigData costFormEntity = new CostFromBigData();
                    costFormEntity.setItemCode(key);
                    costFormEntity.setItemName(costsItemConfigMap.get(key));
                    costFormEntity.setEventMonth(gopInfoMap.get("operate_month"));
                    costFormEntity.setValue(BigDecimal.valueOf(Double.parseDouble(Safes.of(gopInfoMap.get(key), "0"))));
                    if (gopInfoMap.get(key) == null) {
                        noValueSet.add(key + " " + costFormEntity.getItemName());
                    }
                    costFormEntitiesNoneType.add(costFormEntity);
                }
            }
//            for (String va : noValueSet) {
//                System.out.println(va);
//            }
//            costFormEntitiesNoneType = costFormDAO.queryList(chainIdList, itemCodesList,
//                    DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
//                    DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.COST.getCode());
        } else {
            //查询GOP bigdata
            List<Map<String, String>> monthGopInfoList = bigDataApiService.queryFinGopOperateSingle(chainId, DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH), DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH));
            costFormEntitiesNoneType = Lists.newArrayList();
            for (Map<String, String> gopInfoMap : monthGopInfoList) {
                for (String key : itemCodesList) {
                    CostFromBigData costFormEntity = new CostFromBigData();
                    costFormEntity.setItemCode(key);
                    costFormEntity.setItemName(costsItemConfigMap.get(key));
                    costFormEntity.setEventMonth(gopInfoMap.get("operate_month"));
                    costFormEntity.setValue(BigDecimal.valueOf(Double.parseDouble(Safes.of(gopInfoMap.get(key), "0"))));
                    if (gopInfoMap.get(key) == null) {
                        noValueSet.add(key + " " + costFormEntity.getItemName());
                    }
                    costFormEntitiesNoneType.add(costFormEntity);
                }
            }

//            for (String va : noValueSet) {
//                System.out.println(va);
//            }
//            costFormEntitiesNoneType = costFormService.queryCostFormList(chainIdList, null,
//                    DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
//                    DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.SINGLEROOM_SALE.getCode());
        }

//        costFormEntitiesNoneType = costFormEntitiesNoneType.stream()
//                .sorted(Comparator.comparing(CostFormEntity::getId))
//                .collect(Collectors.toList());

        Map<String, BigDecimal> itemCostMapping = costFormEntitiesNoneType.stream().collect(Collectors.toMap(item -> (item.getItemCode() + "_" + item.getEventMonth()), CostInf::getValue, (k1, k2) -> k2));

        final BigDecimal[] costZong = {new BigDecimal(0)};
        Date finalQueryMonth = queryMonth;
        parentCodeMapping.forEach((k, v) -> {
            CostDataItemDTO costDataItemDTO = new CostDataItemDTO();
            costDataItemDTO.setRenvenuType(k);
            costDataItemDTO.setRenvenuTypeName(v.getItemName());
            costDataItemDTO.setPriority(v.getPriority());
            List<CostDataItemPartDTO> itemPartDTOS = Lists.newArrayList();
            //区分子项是直营还是特许
            List<CostReportItemConfigBean> childList = filterTypeItem(v, chainDTO.getIsGuide());
            Map<String, CostDataItemDTO.MonthlyValue> itemMonthValue = Maps.newHashMap();
            final BigDecimal[] parentCost = {new BigDecimal(0)};
            CostDataItemPartDTO totalItem = new CostDataItemPartDTO();
            childList.forEach(child -> {
                CostDataItemPartDTO partDTO = new CostDataItemPartDTO();
                String itemName = child.getItemName();
                String itemCode = child.getItemCode();
                partDTO.setPartItem(itemName);
                partDTO.setPartItemCode(itemCode);
                //构造子项成本趋势数据
                for (int i = 0; i < monthSlot; i++) {
                    String month = DateUtil.formatDate(DateUtil.addMonths(finalQueryMonth, -i), DateUtil.FORMAT_MONTH);
                    if (itemCostMapping.get(itemCode + "_" + month) != null) {//判断 当前月数据是否存在
                        CostDataItemDTO.MonthlyValue monthlyValue = new CostDataItemDTO.MonthlyValue();
                        monthlyValue.setParentKey(k);
                        monthlyValue.setMonth(month);
                        monthlyValue.setValue(Safes.of(itemMonthValue.get(month) != null ? itemMonthValue.get(month)
                                        .getValue() : BigDecimal.ZERO)
                                .add(Safes.of(itemCostMapping.get(itemCode + "_" + month))));
                        itemMonthValue.put(month, monthlyValue);
                    }
                }
                partDTO.setCost(Safes.of(itemCostMapping.get(itemCode + "_" + DateUtil.formatDate(oldQueryMonth, DateUtil.FORMAT_MONTH))));
                itemPartDTOS.add(partDTO);
                parentCost[0] = parentCost[0].add(partDTO.getCost());
            });
            costDataItemDTO.setRevenueDataItemPart(itemPartDTOS);
            costDataItemDTO.setNight(night);
            costDataItemDTO.setRevenueCost(parentCost[0]);
            List<CostDataItemDTO.MonthlyValue> costTrendList = Lists.newArrayList(itemMonthValue.values());
            Collections.sort(costTrendList);
            costDataItemDTO.setCostTrendList(costTrendList);
            costDataItemDTOS.add(costDataItemDTO);
            costTrendList.forEach(e -> {
                CostDataItemDTO.MonthlyValue newMonthValue = new CostDataItemDTO.MonthlyValue();
                newMonthValue.setMonth(e.getMonth());
                newMonthValue.setValue(Safes.of(totalMonthValue.get(e.getMonth()) != null ? totalMonthValue.get(e.getMonth())
                                .getValue() : BigDecimal.ZERO)
                        .add(Safes.of(e.getValue())));
                totalMonthValue.put(e.getMonth(), newMonthValue);
            });
            totalItem.setPartItemCode(k);
            totalItem.setPartItem(v.getItemName());
            totalItem.setCost(Safes.of(parentCost[0]));
            totalItem.setPriority(v.getPriority());
            totalDTOS.add(totalItem);
            costZong[0] = costZong[0].add(Safes.of(parentCost[0]));
        });
        totalCostDataItem.setRevenueCost(costZong[0]);
        Collections.sort(totalDTOS);
        totalCostDataItem.setRevenueDataItemPart(totalDTOS);
        List<CostDataItemDTO.MonthlyValue> totalTrendList = Lists.newArrayList(totalMonthValue.values());
        Collections.sort(totalTrendList);
        totalCostDataItem.setCostTrendList(totalTrendList);
        Collections.sort(costDataItemDTOS);
        //构造总成本
        costDataItemDTOS.add(0, totalCostDataItem);
        revenueDataDTO.setItem(costDataItemDTOS);
        return revenueDataDTO;
    }

    /**
     * 根据酒店类型获取父节点下对应的成本子项
     *
     * @param parent
     * @param chainType
     * @return
     */
    private List<CostReportItemConfigBean> filterTypeItem(CostReportItemConfigBean parent, int chainType) {
        List<CostReportItemConfigBean> childList;
        if (Objects.equals(chainType, ChainTypeEnum.FRANCHISE.getType())) {
            childList = parent.getChildItem()
                    .stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType().intValue() != ChainTypeEnum.STORE.getType())
                    .collect(Collectors.toList());
        } else {
            childList = parent.getChildItem()
                    .stream()
                    .filter(costReportItemConfigBean -> costReportItemConfigBean.getChainType().intValue() != ChainTypeEnum.FRANCHISE.getType())
                    .collect(Collectors.toList());
        }
        return childList;
    }

    /**
     * 查询指定酒店GOP统计相关数据
     *
     * @param chainId
     * @param queryMonth
     * @return
     */
    public GOPDataDTO queryGOPDatas(Integer chainId, Date queryMonth) {

        Date currentDate = DateUtil.getCurrentDate();
        String lastQueryMonth = "";
        int currentDay = DateUtil.getDay(currentDate);
        int realQueryIndex = 0;
        if (currentDay < middleMonthDay) {
            realQueryIndex = 2;
        } else {
            realQueryIndex = 1;
        }
        if (queryMonth == null) {
            queryMonth = DateUtil.addMonths(currentDate, 0 - realQueryIndex);
            lastQueryMonth = DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH);
        } else {
            lastQueryMonth = DateUtil.formatDate(DateUtil.addMonths(currentDate, 0 - realQueryIndex), DateUtil.FORMAT_MONTH);
        }
        List<Integer> chainIds = Lists.newArrayListWithCapacity(1);
        chainIds.add(chainId);
        List<CostReportItemConfigBean> costReportItemConfigBeanList = costReportItemService.getCostReportItemConfigBean();
        List<String> itemCodesList = getCostsItemConfig(costReportItemConfigBeanList);
        String earliestMonth = costFormDAO.costEarliestMonth(chainId, itemCodesList, CostFormTypeEnum.COST.getCode());
        GOPDataDTO gopDataDTO = new GOPDataDTO();
        gopDataDTO.setQueryMonth(DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH));
        gopDataDTO.setEarliestMonth(earliestMonth);
        gopDataDTO.setLastMonth(lastQueryMonth);
        Date oldQueryMonth = queryMonth;
        if (DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH)
                .equals(earliestMonth)) {
            queryMonth = DateUtil.addMonths(queryMonth, maxQueryMonth);
        }
        List<CostFormEntity> rateEntities = costFormService.queryCostFormList(chainIds, GopItemEnum.getAllDesc(),
                DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
                DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.GOP.getCode());
        rateEntities = rateEntities.stream()
                .filter(costFormEntity -> (costFormEntity.getItemCode()
                        .equals(GopItemEnum.TOTAL_GOP.getDesc()) || costFormEntity.getItemCode()
                        .equals(GopItemEnum.ROOM_GOP.getDesc()) || costFormEntity.getItemCode()
                        .equals(GopItemEnum.GOODS_INTEREST_RATE.getDesc())))
                .collect(Collectors.toList());
        List<CostFormEntity> costEntities =
                costFormDAO.queryList(chainIds, itemCodesList, DateUtil.formatDate(DateUtil.addMonths(queryMonth, -maxQueryMonth), DateUtil.FORMAT_MONTH),
                        DateUtil.formatDate(queryMonth, DateUtil.FORMAT_MONTH), CostFormTypeEnum.COST.getCode());
        List<CostFormEntity> roomCostEntities = costEntities.stream()
                .filter(costFormEntity -> roomGopCostItem.contains(costFormEntity.getItemCode()))
                .collect(Collectors.toList());
        List<CostFormEntity> goodsCodeEntities = costEntities.stream()
                .filter(costFormEntity -> GOODS_CODE.equals(costFormEntity.getItemCode()))
                .collect(Collectors.toList());
        Map<String, BigDecimal> itemCostMapping = costEntities.stream()
                .collect(Collectors.toMap(item -> (item.getItemCode() + "_" + item.getEventMonth()), CostFormEntity::getValue, (k1, k2) -> k2));
        //收入
        Map<String, BigDecimal> monthCostMap = costEntities.stream()
                .collect(Collectors.toMap(item -> (item.getEventMonth()), CostFormEntity::getValue, (k1, k2) -> k2));
        List<ChainStatisticsDTO> chainStatistics = rmsService.queryChainStatistics(chainId, DateUtil.getFirstDayOfMonth(DateUtil.addMonths(queryMonth, -maxQueryMonth)), DateUtil.getLastDayTimeOfMonth(queryMonth));
        List<RevenueItemDTO> revenueItems = getRevenueItemsOriginal(DateUtil.getFirstDayOfMonth(DateUtil.addMonths(queryMonth, -maxQueryMonth)),
                DateUtil.getLastDayTimeOfMonth(queryMonth), chainStatistics);
        revenueItems = revenueItems.stream()
                .filter(revenueItemDTO -> (revenueItemDTO.getId() == RevenueTypeEnum.TOTAL.getCode()
                        || revenueItemDTO.getId() == RevenueTypeEnum.ROOM_RENT.getCode() || revenueItemDTO.getId() == RevenueTypeEnum.COMMODITY.getCode()))
                .collect(Collectors.toList());
        List<GOPDetailDTO> gopDetailDTOList = Lists.newArrayListWithCapacity(3);
        //构造gop率，客房gop率，小商品gop率
        GOPDetailDTO totalGopDetailDto = new GOPDetailDTO();
        GOPDetailDTO roomGopDetailDto = new GOPDetailDTO();
        GOPDetailDTO goodsDetailDto = new GOPDetailDTO();
        //构造月度数据 成本项
        List<GOPDetailDTO.MonthlyValue> totalMonthlyValues = Lists.newArrayListWithCapacity(monthSlot);
        List<GOPDetailDTO.MonthlyValue> roomMonthlyValues = Lists.newArrayListWithCapacity(monthSlot);
        List<GOPDetailDTO.MonthlyValue> goodsMonthlyValues = Lists.newArrayListWithCapacity(monthSlot);
        for (int i = 0; i < monthSlot; i++) {
            String month = DateUtil.formatDate(DateUtil.addMonths(queryMonth, 0 - i), DateUtil.FORMAT_MONTH);
            if (monthCostMap.containsKey(month)) {
                GOPDetailDTO.MonthlyValue totalMonthlyValue = new GOPDetailDTO.MonthlyValue();
                GOPDetailDTO.MonthlyValue roomMonthlyValue = new GOPDetailDTO.MonthlyValue();
                GOPDetailDTO.MonthlyValue goodsMonthlyValue = new GOPDetailDTO.MonthlyValue();
                totalMonthlyValue.setMonth(month);
                roomMonthlyValue.setMonth(month);
                goodsMonthlyValue.setMonth(month);
                itemCostMapping.forEach((k, v) -> {
                    if (k.contains(month)) {
                        totalMonthlyValue.setCostValue(Safes.of(v)
                                .add(Safes.of(totalMonthlyValue.getCostValue())));
                    }
                });
                Iterator<CostFormEntity> roomiterator = roomCostEntities.iterator();
                while (roomiterator.hasNext()) {
                    CostFormEntity entity = roomiterator.next();
                    if (entity.getEventMonth()
                            .equals(month)) {
                        roomMonthlyValue.setCostValue(Safes.of(entity.getValue())
                                .add(Safes.of(roomMonthlyValue.getCostValue())));
                        roomiterator.remove();
                    }
                }
                Iterator<CostFormEntity> goodsiterator = goodsCodeEntities.iterator();
                while (goodsiterator.hasNext()) {
                    CostFormEntity entity = goodsiterator.next();
                    if (entity.getEventMonth()
                            .equals(month)) {
                        goodsMonthlyValue.setCostValue(Safes.of(entity.getValue())
                                .add(Safes.of(goodsMonthlyValue.getCostValue())));
                        goodsiterator.remove();
                    }
                }
                Iterator<CostFormEntity> rateiterator = rateEntities.iterator();
                while (rateiterator.hasNext()) {
                    CostFormEntity entity = rateiterator.next();
                    if (entity.getEventMonth()
                            .equals(month)) {
                        if (entity.getItemName()
                                .equals(GopItemEnum.TOTAL_GOP.getDesc())) {
                            totalMonthlyValue.setGopRateValue(Safes.of(entity.getValue()
                                    .multiply(new BigDecimal(100))));
                        } else if (entity.getItemName()
                                .equals(GopItemEnum.ROOM_GOP.getDesc())) {
                            roomMonthlyValue.setGopRateValue(Safes.of(entity.getValue()
                                    .multiply(new BigDecimal(100))));
                        } else {
                            goodsMonthlyValue.setGopRateValue(Safes.of(entity.getValue()
                                    .multiply(new BigDecimal(100))));
                        }
                    }
                }
                // 遍历收入
                revenueItems.forEach(e -> {
                    if (e.getId() == RevenueTypeEnum.TOTAL.getCode()) {
                        //全部收入
                        e.getDailyValues()
                                .forEach(eData -> {
                                    if (eData.getDate()
                                            .contains(month)) {
                                        totalMonthlyValue.setRevenueValue(Safes.of(eData.getValue())
                                                .add(Safes.of(totalMonthlyValue.getRevenueValue())));
                                    }
                                });
                    } else if (e.getId() == RevenueTypeEnum.ROOM_RENT.getCode()) {
                        //客房收入
                        e.getDailyValues()
                                .forEach(eData -> {
                                    if (eData.getDate()
                                            .contains(month)) {
                                        roomMonthlyValue.setRevenueValue(Safes.of(eData.getValue())
                                                .add(Safes.of(roomMonthlyValue.getRevenueValue())));
                                    }
                                });
                    } else {
                        //商品收入
                        e.getDailyValues()
                                .forEach(eData -> {
                                    if (eData.getDate()
                                            .contains(month)) {
                                        goodsMonthlyValue.setRevenueValue(Safes.of(eData.getValue())
                                                .add(Safes.of(goodsMonthlyValue.getRevenueValue())));
                                    }
                                });
                    }
                });
                if (!DateUtil.formatDate(oldQueryMonth, DateUtil.FORMAT_MONTH)
                        .equals(earliestMonth) && i == 0) {
                    //为3个对象增加 成本 收入  率数据
                    GOPDetailWrapper.wrapperDetail(totalGopDetailDto, totalMonthlyValue);
                    GOPDetailWrapper.wrapperDetail(roomGopDetailDto, roomMonthlyValue);
                    GOPDetailWrapper.wrapperDetail(goodsDetailDto, goodsMonthlyValue);
                }
                if (DateUtil.formatDate(oldQueryMonth, DateUtil.FORMAT_MONTH)
                        .equals(earliestMonth) && i == maxQueryMonth) {
                    GOPDetailWrapper.wrapperDetail(totalGopDetailDto, totalMonthlyValue);
                    GOPDetailWrapper.wrapperDetail(roomGopDetailDto, roomMonthlyValue);
                    GOPDetailWrapper.wrapperDetail(goodsDetailDto, goodsMonthlyValue);
                }
                totalMonthlyValues.add(totalMonthlyValue);
                roomMonthlyValues.add(roomMonthlyValue);
                goodsMonthlyValues.add(goodsMonthlyValue);
            }
        }
        Collections.sort(totalMonthlyValues);
        Collections.sort(roomMonthlyValues);
        Collections.sort(goodsMonthlyValues);
        totalGopDetailDto.setMonthlyValueList(totalMonthlyValues);
        roomGopDetailDto.setMonthlyValueList(roomMonthlyValues);
        goodsDetailDto.setMonthlyValueList(goodsMonthlyValues);
        totalGopDetailDto.setGopDataTypeName(GOPRATE);
        roomGopDetailDto.setGopDataTypeName(ROOM_GOP_RATE);
        goodsDetailDto.setGopDataTypeName(GOODS_GOP_RATE);
        gopDetailDTOList.add(totalGopDetailDto);
        gopDetailDTOList.add(roomGopDetailDto);
        gopDetailDTOList.add(goodsDetailDto);
        gopDataDTO.setGopDetailDtoList(gopDetailDTOList);
        return gopDataDTO;
    }

    @AllArgsConstructor
    @Getter
    private static class DateRange {

        private Date begin;

        private Date end;
    }

    private enum ValueUnitEnum {

        /**
         * 元
         */
        ONE(new ValueUnit("元", Range.closedOpen(BigDecimal.valueOf(Integer.MIN_VALUE), BigDecimal.valueOf(1000)), v -> v)),

        /**
         * 千元
         */
        THOUSAND(new ValueUnit("千元", Range.closedOpen(BigDecimal.valueOf(1000), BigDecimal.valueOf(10000)), v -> {
            if (v == null) {
                return null;
            } else {
                return v.divide(BigDecimal.valueOf(1000));
            }
        })),

        /**
         * 万元
         */
        TEN_THOUSAND(new ValueUnit("万元", Range.closedOpen(BigDecimal.valueOf(10000), BigDecimal.valueOf(Integer.MAX_VALUE)), v -> {
            if (v == null) {
                return null;
            } else {
                return v.divide(BigDecimal.valueOf(10000));
            }
        })),

        /**
         * 比例
         */
        RATIO(new ValueUnit("%", null, v -> v)),

        ;

        private ValueUnit valueUnit;

        ValueUnitEnum(ValueUnit valueUnit) {

            this.valueUnit = valueUnit;
        }
    }

    @Data
    @AllArgsConstructor
    private static class ValueUnit {

        /**
         * 元/千元/万元
         */
        private String unitType;

        /**
         * 取值范围
         */
        private Range<BigDecimal> valueRange;

        /**
         * 除数
         */
        private Function<BigDecimal, BigDecimal> valueMapper;

    }

    public ApiResult<BudgetProgressDetailDTO> queryBudgetProgressDetail(Integer chainId, Date beginDate, Date endDate, String dateType) {

        BudgetProgressDetailDTO.BudgetProgressDetailDTOBuilder builder = BudgetProgressDetailDTO.builder()
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .chainId(chainId);

        List<BudgetProgressItemDTO> budgetProgressItemDTOS = getBudgetProgressItems(chainId, beginDate, endDate, dateType);

        return ApiResult.success(builder.budgetProgressItems(budgetProgressItemDTOS)
                .build());
    }

    private List<BudgetProgressItemDTO> getBudgetProgressItems(Integer chainId, Date beginDate, Date endDate, String dateType) {

        Date actualBeginDate = beginDate;
        if (DateUtil.compareDateOnly(beginDate, endDate) == 0) {
            actualBeginDate = DateUtil.subDays(beginDate, 6);
        }

        List<ChainBudgetDTO> chainBudgetDTOS = rmsService.queryChainBudgetProgress(chainId, actualBeginDate, endDate);

        List<BudgetProgressItemDTO> budgets = budgetProgressTypeEnumFunctionMap.entrySet()
                .stream()
                .map(entry -> buildBudgetProgressItem(chainBudgetDTOS, entry.getKey(), entry.getValue(), dateType))
                .collect(Collectors.toList());
        return budgets;
    }

    private BudgetProgressItemDTO buildBudgetProgressItem(List<ChainBudgetDTO> chainBudgetDTOS,
                                                          ChainBudgetProgressTypeEnum chainBudgetProgressTypeEnum, Function<ChainBudgetDTO, CompareData> valueGetter, String dateType) {

        List<BudgetProgressItemDTO.DailyBudgetValue> dailyBudgetValues = Safes.of(chainBudgetDTOS)
                .stream()
                .map(dto -> new BudgetProgressItemDTO.DailyBudgetValue(dto.getDate(), valueGetter.apply(dto)))
                .collect(Collectors.toList());


        if (StringUtils.isNotEmpty(dateType) && (dateType.equals(DateTypeEnum.q.name()) || dateType.equals(DateTypeEnum.y.name()))) {
            //按照月份聚合数据
            Map<String, List<DailyBudgetValue>> monthGroupBy = dailyBudgetValues.stream().collect(Collectors.groupingBy(item -> item.getDate().substring(0, 7) + "-01"));
            List<DailyBudgetValue> monthValues = monthGroupBy.entrySet().stream().map(item -> {
                return new DailyBudgetValue(item.getKey(), item.getValue().stream().map(DailyBudgetValue::getValue).reduce(CompareData::sum).orElse(BudgetProgressItemDTO.CompareData.from(BigDecimal.ZERO, BigDecimal.ZERO)));
            }).sorted(Comparator.comparing(DailyBudgetValue::getDate).reversed()).collect(Collectors.toList());
            dailyBudgetValues = monthValues;
        }

        ValueUnitEnum valueUnitEnum = processValueUnit(chainBudgetProgressTypeEnum, dailyBudgetValues);

        return BudgetProgressItemDTO.builder()
                .id(chainBudgetProgressTypeEnum.getCode())
                .name(chainBudgetProgressTypeEnum.getName())
                .unitType(valueUnitEnum.valueUnit.getUnitType())
                .dailyBudgetValues(dailyBudgetValues)
                .build();
    }

    @SuppressWarnings("unchecked")
    private HotelRevenueService.ValueUnitEnum processValueUnit(ChainBudgetProgressTypeEnum budgetProgressTypeEnum,
                                                               List<BudgetProgressItemDTO.DailyBudgetValue> dailyBudgetValues) {

        if (budgetProgressTypeEnum.getUnitType() == 1) {
            BigDecimal minValue = dailyBudgetValues.stream()
                    .map(BudgetProgressItemDTO.DailyBudgetValue::getValue)
                    .filter(Objects::nonNull)
                    .map(v -> {
                        BigDecimal budget = BigDecimal.ZERO;
                        if (v.getBudget() != null) {
                            budget = (BigDecimal) v.getBudget();
                        }

                        BigDecimal actual = BigDecimal.ZERO;
                        if (v.getActual() != null) {
                            actual = (BigDecimal) v.getActual();
                        }

                        return budget.compareTo(actual) <= 0 ? budget : actual;
                    })
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            ValueUnitEnum valueUnitEnum = Stream.of(ValueUnitEnum.values())
                    .filter(e -> e.valueUnit.getValueRange()
                            .contains(minValue))
                    .findFirst()
                    .orElseGet(() -> {
                        log.warn("get value unit get null, budgetType:{} minValue:{}", budgetProgressTypeEnum, minValue);
                        return ValueUnitEnum.ONE;
                    });

            dailyBudgetValues.stream()
                    .filter(e -> e.getValue() != null)
                    .forEach(dailyBudgetValue -> {
                        BudgetProgressItemDTO.CompareData compareData = dailyBudgetValue.getValue();
                        compareData.setBudget(scaleFormat(valueUnitEnum.valueUnit.valueMapper.apply((BigDecimal) compareData.getBudget())));
                        compareData.setActual(scaleFormat(valueUnitEnum.valueUnit.valueMapper.apply((BigDecimal) compareData.getActual())));
                    });

            return valueUnitEnum;
        } else {
            dailyBudgetValues.stream()
                    .filter(e -> e.getValue() != null)
                    .forEach(dailyBudgetValue -> {
                        BudgetProgressItemDTO.CompareData compareData = dailyBudgetValue.getValue();
                        compareData.setBudget(scaleFormat(((BigDecimal) compareData.getBudget())));
                        compareData.setActual(scaleFormat(((BigDecimal) compareData.getActual())));
                    });
            return ValueUnitEnum.RATIO;
        }
    }

    private BudgetProgressItemDTO getTotalRevenueBudgetProgressItem(Integer chainId, Date beginDate, Date endDate) {

        List<ChainBudgetDTO> chainBudgetDTOS = rmsService.queryChainBudgetProgress(chainId, beginDate, endDate);
        ChainBudgetProgressTypeEnum chainBudgetProgressTypeEnum = ChainBudgetProgressTypeEnum.TOTAL_REVENUE;
        Function<ChainBudgetDTO, BudgetProgressItemDTO.CompareData> valueGetter = budgetProgressTypeEnumFunctionMap.get(chainBudgetProgressTypeEnum);
        return buildBudgetProgressItem(chainBudgetDTOS, chainBudgetProgressTypeEnum, valueGetter, dateType);
    }

    /**
     * 生成周期数据
     *
     * @param str 指定日期后
     */
    @Transactional(value = "omsTransactionManager", rollbackFor = Exception.class)
    public void generateData(String str) {
        Date dateNow = null;
        if (StringUtils.isNotBlank(str)) {
            dateNow = DateUtil.parseDatetime(str, DateUtil.DATE_FORMAT);
        } else {
            dateNow = new Date();
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateNow);
        int yearNow = calendar.get(Calendar.YEAR);
        int monthNow = calendar.get(Calendar.MONTH);

        List<String> dayByMonth = WeekUtil.getMonthFullDay(yearNow, monthNow + 1);

        if (CollectionUtils.isEmpty(dayByMonth)) {
            log.error("generateData error: next month day is empty");
            return;
        }

        weekendDefinitionDAO.batchDel(dayByMonth);

        int maxWeek = NumberUtils.INTEGER_ONE;
        int maxWeekNo5 = NumberUtils.INTEGER_ONE;
        WeekendDefinitionEntity weekendDefinitionEntity;
        List<WeekendDefinitionEntity> list = new ArrayList<>();
        for (String day : dayByMonth) {
            weekendDefinitionEntity = new WeekendDefinitionEntity();
            String trim = day.replaceAll("-", "").trim();

            Date date = DateUtil.parseDatetime(day, DateUtil.DATE_FORMAT);

            //获取周信息(已周日为周开始)
            Calendar weekNo = WeekUtil.getWeekNo(date, Calendar.SUNDAY, 4);
            int month = weekNo.get(Calendar.MONTH);
            int year = weekNo.get(Calendar.YEAR);
            Integer weekOfYear = WeekUtil.getWeekOfYear(year, weekNo, maxWeek, 4, Calendar.SUNDAY);
            if (weekNo.get(Calendar.WEEK_OF_YEAR) >= maxWeek && weekOfYear > maxWeek) {
                maxWeek = weekOfYear;
            }

            weekendDefinitionEntity.setDate(date);
            weekendDefinitionEntity.setDateInt(Integer.valueOf(trim));
            weekendDefinitionEntity.setQuarter(WeekUtil.getQuarter(month));
            weekendDefinitionEntity.setYear(year);
            weekendDefinitionEntity.setMonth(month + 1);
            weekendDefinitionEntity.setDay(weekNo.get(Calendar.DAY_OF_MONTH));
            weekendDefinitionEntity.setWeekday(WeekUtil.getWeekDay(weekNo.get(Calendar.DAY_OF_WEEK)));
            weekendDefinitionEntity.setWeekNo(weekOfYear);

            //获取周信息（以周五为周开始）
            Calendar weekNo5 = WeekUtil.getWeekNo(date, Calendar.FRIDAY, 2);
            Integer weekNo5OfYear = WeekUtil.getWeekOfYear(year, weekNo5, maxWeekNo5, 2, Calendar.FRIDAY);
            if (weekNo5.get(Calendar.WEEK_OF_YEAR) >= maxWeekNo5 && weekNo5OfYear > maxWeekNo5) {
                maxWeekNo5 = weekNo5OfYear;
            }
            weekendDefinitionEntity.setWeekNo5(weekNo5OfYear);

            //获取经营周信息（以周五为周开始）
            Calendar weekNo5Fw = WeekUtil.getWeekNo(date, Calendar.FRIDAY, 1);
            weekendDefinitionEntity.setYearFw(weekNo5Fw.get(Calendar.YEAR));
            weekendDefinitionEntity.setWeekNo5Fw(weekNo5Fw.get(Calendar.WEEK_OF_YEAR));

            //获取会员部定制周信息（以周六为周开始）
            Calendar weekNoMeb = WeekUtil.getWeekNo(date, Calendar.SATURDAY, 2);
            weekendDefinitionEntity.setYearMeb(weekNoMeb.get(Calendar.YEAR));
            weekendDefinitionEntity.setWeekMeb(weekNoMeb.get(Calendar.WEEK_OF_YEAR));

            list.add(weekendDefinitionEntity);
        }

        //批量新增
        weekendDefinitionDAO.batchInsert(list);
    }


}
