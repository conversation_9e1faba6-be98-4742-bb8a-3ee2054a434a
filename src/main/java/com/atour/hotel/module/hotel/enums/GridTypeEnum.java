package com.atour.hotel.module.hotel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/5/29
 */
@Getter
@AllArgsConstructor
public enum GridTypeEnum {

    /**
     * 当日营收情况
     */
    DAILY_INCOME(1, "当日营收情况", 3, 1),

    ROOM_RENT(2, "出租数据", 2, 1),

    MARKETING(3, "经营数据", 3, 1),

    RUNNING_COSTS(4, "运营成本", 2, 1),

    ESTIMATE_GOP(5, "预估日GOP", 3, 0);

    /**
     * 类型 code
     */
    private int code;

    /**
     * 描述
     */
    private String description;

    /**
     * 每行的列数
     */
    private int columnCount;

    /**
     * 是否展示详情
     */
    private int showDetail;

}
