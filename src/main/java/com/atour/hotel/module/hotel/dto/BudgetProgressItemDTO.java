package com.atour.hotel.module.hotel.dto;

import com.atour.hotel.module.hotel.config.BigDecimalRoundingModeConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2019/10/24
 */
@ApiModel("预算进度")
@Data
@Builder
public class BudgetProgressItemDTO {

    @ApiModelProperty("类型id")
    private Integer id;

    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("单位, 元/千元/万元/%")
    private String unitType;

    @ApiModelProperty("每天的数据")
    private List<DailyBudgetValue> dailyBudgetValues;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyBudgetValue {

        @ApiModelProperty("日期")
        private String date;

        @ApiModelProperty("预算数据")
        private CompareData value;
    }

    @Data
    @Accessors(chain = true)
    public static class CompareData<T> {

        @ApiModelProperty("预算值")
        private T budget;

        @ApiModelProperty("实际完成")
        private T actual;

        @ApiModelProperty("实际完成率")
        private BigDecimal targetCompleteRatio;

        @ApiModelProperty("实际完成率")
        private BigDecimal actualCompleteRatio;

        public static <T> CompareData<T> from(T budget, T actual) {
            CompareData<T> compareData = new CompareData<>();
            compareData.setBudget(budget);
            compareData.setActual(actual);
            compareData.setTargetCompleteRatio(BigDecimal.valueOf(100));
            return compareData;
        }

        public static Consumer<CompareData<BigDecimal>> bigDecimalRatioSetter = data -> {
            if (data.getBudget() == null || data.getActual() == null || BigDecimal.ZERO.compareTo(data.getBudget()) == 0) {
                data.setActualCompleteRatio(BigDecimal.ZERO);
            } else {
                data.setActualCompleteRatio(data.getActual()
                    .divide(data.getBudget(), BigDecimalRoundingModeConfig.SCALE_3,
                        BigDecimalRoundingModeConfig.ROUNDING_MODE)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(BigDecimalRoundingModeConfig.SCALE_0, RoundingMode.DOWN));
            }
        };

        /**
         * 聚合两个CompareData对象
         */
        public static CompareData<BigDecimal> sum(CompareData<BigDecimal> data1, CompareData<BigDecimal> data2) {
            if (data1 == null && data2 == null) {
                return CompareData.from(BigDecimal.ZERO, BigDecimal.ZERO);
            }
            if (data1 == null) {
                return data2;
            }
            if (data2 == null) {
                return data1;
            }

            BigDecimal budget1 = data1.getBudget() != null ? data1.getBudget() : BigDecimal.ZERO;
            BigDecimal actual1 = data1.getActual() != null ? data1.getActual() : BigDecimal.ZERO;
            BigDecimal budget2 = data2.getBudget() != null ? data2.getBudget() : BigDecimal.ZERO;
            BigDecimal actual2 = data2.getActual() != null ? data2.getActual() : BigDecimal.ZERO;

            CompareData<BigDecimal> result = CompareData.from(budget1.add(budget2), actual1.add(actual2));
            bigDecimalRatioSetter.accept(result);
            return result;
        }

        //对两个CompareData对象求和
        public static CompareData<BigDecimal> sum(CompareData<BigDecimal> data1, CompareData<BigDecimal> data2) {
            CompareData<BigDecimal> sum = new CompareData<>();
            sum.setBudget(data1.getBudget().add(data2.getBudget()));
            sum.setActual(data1.getActual().add(data2.getActual()));
            bigDecimalRatioSetter.accept(sum);
            return sum;
        }
    }
}
