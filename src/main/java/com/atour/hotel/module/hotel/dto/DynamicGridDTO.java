package com.atour.hotel.module.hotel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 动态网格
 *
 * <AUTHOR>
 * @date 2020/5/29
 */
@Data
@ApiModel
public class DynamicGridDTO {

    @ApiModelProperty(value = "标题, 如 当日营收数据")
    private String title;

    @ApiModelProperty(value = "类型, 1: 当日经营数据, 2: 出租数据, 3: 经营数据, 4:运营成本, 5:预估日GOP")
    private Integer type;

    @ApiModelProperty(value = "每行的列数")
    private Integer columnCount;

    @ApiModelProperty(value = "是否展示详细数据")
    private Integer showDetail;

    @ApiModelProperty(value = "数据")
    private List<DynamicGridItemDTO> items;

    @Data
    public static class DynamicGridItemDTO {

        @ApiModelProperty(value = "单项的标题, 如 总营收(元)")
        private String title;

        @ApiModelProperty(value = "单项的值, 如 648121.24")
        private String value;

        @ApiModelProperty(value = "比较值, 如 较昨日+6530, 是个html，会控制部分文本的颜色")
        private String compare;

        @ApiModelProperty(value = "排版方式, 1: 独占一行, 2: 多列展示")
        private Integer typesetting;

    }

}
