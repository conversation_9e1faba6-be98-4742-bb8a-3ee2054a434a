package com.atour.hotel.module.hotel.service;

import com.atour.hotel.persistent.franchise.dao.HotelOpeningHintDAO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/10/16
 */
@Service
public class OpeningHintService {

    @Resource
    private HotelOpeningHintDAO hotelOpeningHintDAO;

    /**
     * 查询开提示
     *
     * @return
     */
    public String query() {
        return hotelOpeningHintDAO.selectHotelOpeningHint()
            .getContent();
    }
}
