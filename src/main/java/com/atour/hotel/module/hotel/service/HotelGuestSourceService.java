package com.atour.hotel.module.hotel.service;

import com.atour.api.bean.ApiResult;
import com.atour.galaxy.api.bean.statistics.ChainStatisticsDTO;
import com.atour.hotel.framework.enums.ResponseCodeEnum;
import com.atour.hotel.module.bigdata.BigDataApiService;
import com.atour.hotel.module.hotel.dto.DailyGuestSourceDTO;
import com.atour.hotel.module.hotel.dto.DailyGuestSourceDTOV2;
import com.atour.hotel.module.hotel.dto.GuestSourceCountRatioDTO;
import com.atour.hotel.module.hotel.dto.GuestSourceCountRatioDTOV2;
import com.atour.hotel.module.hotel.dto.RevenueStatsDTO.DateTypeEnum;
import com.atour.hotel.module.hotel.dto.TypedGuestSourceDTO;
import com.atour.hotel.module.hotel.dto.TypedGuestSourceDTOV2;
import com.atour.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/09/25
 */
@Service
@Slf4j
public class HotelGuestSourceService {

    @Resource
    private RmsService rmsService;

    @Resource
    private BigDataApiService bigDataApiService;

    public ApiResult<GuestSourceCountRatioDTO> queryGuestSourceCountRatio(Integer chainId, Date beginDate,
                                                                          Date endDate) {
        List<ChainStatisticsDTO> chainStatistics = rmsService.queryChainStatistics(chainId, beginDate, endDate);
        List<DailyGuestSourceDTO> dailyGuestSource = chainStatistics.stream()
                .map(DailyGuestSourceDTO::from)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        TypedGuestSourceDTO avg = dailyGuestSource.stream()
                .map(DailyGuestSourceDTO::getTypedGuestSourceDTO)
                .reduce(TypedGuestSourceDTO.empty(), TypedGuestSourceDTO::add);
        avg.computeRatio();

        GuestSourceCountRatioDTO.GuestSourceCountRatioDTOBuilder builder = GuestSourceCountRatioDTO.builder()
                .chainId(chainId)
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .dailyGuestSourceDTOS(dailyGuestSource)
                .guestSourceDTO(avg);
        return ApiResult.success(builder.build());
    }

    /**
     * 客源结构 BI数据来源
     *
     * @param chainId
     * @param beginDate
     * @param endDate
     * @param dateType
     * @return
     */
    public ApiResult<GuestSourceCountRatioDTOV2> queryGuestSourceCountRatioV2(Integer chainId, Date beginDate, Date endDate, String dateType) {
        if (chainId == null) {
            return ApiResult.result(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getMsg(), null);
        }
        List<Map<String, String>> gopPassengersourceList = bigDataApiService.queryFinGopPassengersourceList(chainId, beginDate, endDate);

        List<DailyGuestSourceDTOV2> dailyGuestSource = gopPassengersourceList.stream().map(DailyGuestSourceDTOV2::from).filter(Objects::nonNull).collect(Collectors.toList());

        TypedGuestSourceDTOV2 avg = dailyGuestSource.stream()
                .map(DailyGuestSourceDTOV2::getTypedGuestSourceDTO)
                .reduce(TypedGuestSourceDTOV2.empty(), TypedGuestSourceDTOV2::add);
        avg.computeRatio();

        if (StringUtils.isNotEmpty(dateType) && (dateType.equals(DateTypeEnum.q.name()) || dateType.equals(DateTypeEnum.y.name()))) {
            //按照月份聚合数据
            Map<String, List<DailyGuestSourceDTOV2>> monthGroupBy = dailyGuestSource.stream().collect(Collectors.groupingBy(item -> item.getDate().substring(0, 7)));
            List<DailyGuestSourceDTOV2> monthValues = monthGroupBy.entrySet().stream().map(item -> {
                TypedGuestSourceDTOV2 typedGuestSourceDTOV2Sum = TypedGuestSourceDTOV2.empty();
                for (DailyGuestSourceDTOV2 dailyGuestSourceDTOV2 : item.getValue()) {
                    typedGuestSourceDTOV2Sum.add(dailyGuestSourceDTOV2.getTypedGuestSourceDTO());
                }
                typedGuestSourceDTOV2Sum.computeRatio();
                DailyGuestSourceDTOV2 dailyGuestSourceDTOV2 = new DailyGuestSourceDTOV2();
                dailyGuestSourceDTOV2.setDate(item.getKey());
                dailyGuestSourceDTOV2.setTypedGuestSourceDTO(typedGuestSourceDTOV2Sum);
                return dailyGuestSourceDTOV2;
            }).sorted(Comparator.comparing(DailyGuestSourceDTOV2::getDate).reversed()).collect(Collectors.toList());
            dailyGuestSource = monthValues;
        }

        GuestSourceCountRatioDTOV2.GuestSourceCountRatioDTOV2Builder builder = GuestSourceCountRatioDTOV2.builder()
                .chainId(chainId)
                .beginDate(DateUtil.printDate(beginDate))
                .endDate(DateUtil.printDate(endDate))
                .dailyGuestSourceDTOS(dailyGuestSource)
                .guestSourceDTO(avg);
        return ApiResult.success(builder.build());
    }
}
