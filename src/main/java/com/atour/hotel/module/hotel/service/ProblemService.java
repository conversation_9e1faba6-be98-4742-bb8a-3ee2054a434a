package com.atour.hotel.module.hotel.service;

import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.atour_pms.NoticeOfCategoryEnum;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.module.hotel.response.HelpPageResponse;
import com.atour.hotel.module.hotel.response.HelpResponse;
import com.atour.hotel.module.hotel.wrapper.ProblemWrapper;
import com.atour.hotel.persistent.franchise.dao.ProblemDao;
import com.atour.hotel.persistent.franchise.entity.ProblemEntity;
import com.atour.utils.Safes;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 问题方案 service
 *
 * <AUTHOR>
 * @date 2019年10月24日
 */
@Service
@Slf4j
public class ProblemService {

    @Value("#{'${problem.ids}'.split(',')}")
    private List<Integer> ids;

    @Resource
    private ProblemDao problemDao;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    public HelpResponse getDetailById(Integer id) {
        ProblemEntity problemEntity = problemDao.getDetailById(id);
        return ProblemWrapper.toHelpResponse(problemEntity);
    }

    /**
     * 问题集合
     *
     * @param category
     * @param page
     * @param pageSize
     * @return
     */
    public HelpPageResponse getProblemList(Integer category, Integer page, Integer pageSize) {
        PageInfo pageInfo = new PageInfo(SystemContants.DEFAULT_PAGE[0], SystemContants.DEFAULT_PAGE[1]);
        if (null != page && null != pageSize) {
            pageInfo.setPageNo(page);
            page = (page - 1) * pageSize;
            pageInfo.setPageSize(pageSize);
        }
        Integer totalCount = problemDao.countProblem(category, page, pageSize);
        if (totalCount <= 0) {
            pageInfo.setTotalCount(totalCount);
            return HelpPageResponse.builder()
                .pageInfo(pageInfo)
                .build();
        }
        List<ProblemEntity> problemEntities = problemDao.getList(category, page, pageSize);
        Optional<ProblemEntity> first = problemEntities.stream()
            .filter(t -> Strings.isBlank(t.getContent()))
            .findFirst();

        pageInfo.setTotalCount(totalCount);
        return HelpPageResponse.builder()
            .pageInfo(pageInfo)
            .type(first.isPresent() ? ProblemTypeEnum.CATEGORY.getCode() : ProblemTypeEnum.PROBLEM.getCode())
            .helpResponseList(Safes.of(problemEntities)
                .stream()
                .filter(Objects::nonNull)
                .map(this::buildHelpResponse)
                .collect(Collectors.toList()))
            .build();
    }

    /**
     * 首页固定展示问题集合
     *
     * @return
     */
    public List<HelpResponse> indexProblemList() {
        List<ProblemEntity> noticeEntityList = problemDao.indexProblemList(ids);
        return Safes.of(noticeEntityList)
            .stream()
            .filter(Objects::nonNull)
            .map(this::buildHelpResponse)
            .collect(Collectors.toList());
    }

    private HelpResponse buildHelpResponse(ProblemEntity problemEntity) {
        HelpResponse helpResponse = ProblemWrapper.toHelpResponse(problemEntity);
        if (Strings.isBlank(helpResponse.getCategoryName())) {
            helpResponse.setCategoryName(getCategoryEnumName(problemEntity.getCategory()));
        }
        return helpResponse;

    }

    /**
     * 获取类目名字
     *
     * @return
     */
    public String getCategoryEnumName(int category) {
        String valueByCode = NoticeOfCategoryEnum.getValueByCode(category);
        if (Strings.isNotBlank(valueByCode)) {
            return valueByCode;
        }
        ProblemEntity detailById = problemDao.getDetailById(category);
        if (detailById == null) {
            return Strings.EMPTY;
        }
        return getCategoryEnumName(detailById.getCategory());

    }

}
