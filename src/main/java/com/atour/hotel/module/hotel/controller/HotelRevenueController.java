package com.atour.hotel.module.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.hotel.dto.BudgetProgressDetailDTO;
import com.atour.hotel.module.hotel.dto.CoreStatsDetailDTO;
import com.atour.hotel.module.hotel.dto.CostDataDTO;
import com.atour.hotel.module.hotel.dto.DynamicGridDTO;
import com.atour.hotel.module.hotel.dto.GOPDataDTO;
import com.atour.hotel.module.hotel.dto.RevenueStatsDTO;
import com.atour.hotel.module.hotel.dto.RevenueStatsDetailDTO;
import com.atour.hotel.module.hotel.dto.WeekendDefinitionQueryAreaDTO;
import com.atour.hotel.module.hotel.dto.WeekendDefinitionQueryDTO;
import com.atour.hotel.module.hotel.service.DailyReportService;
import com.atour.hotel.module.hotel.service.HotelRevenueService;
import com.atour.web.validation.annotation.NotNull;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 经营数据相关接口
 *
 * <AUTHOR>
 * @date 2019/09/24
 */
@Api(tags = "经营数据相关接口", value = "经营数据相关接口", description = "经营数据相关接口")
@RestController
@RequestMapping(value = "/app/revenue", produces = {"application/json;charset=UTF-8"})
public class HotelRevenueController {

    /**
     * 日成本
     */
    private static final Integer COST_DATA_QUERY_TYPE_DAY = 1;

    @Resource
    private HotelRevenueService hotelRevenueService;

    @Resource
    private DailyReportService dailyReportService;

    @ApiOperation(value = "经营数据", httpMethod = "GET")
    @RequestMapping("/queryRevenueStats")
    public ApiResult<RevenueStatsDTO> queryRevenueStats(
            @ApiParam("酒店id") @RequestParam("chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @ApiParam("开始日期") @RequestParam("beginDate") @NotNull(message = "beginDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
            @ApiParam("结束日期") @RequestParam("endDate") @NotNull(message = "endDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @ApiParam("时段类型，季度=q，年=y") @RequestParam(value = "dateType",required = false) String dateType) {

        return hotelRevenueService.queryRevenueStats(chainId, beginDate, endDate, dateType);
    }

    @ApiOperation(value = "核心指标", httpMethod = "GET")
    @RequestMapping("/queryCoreStatsDetail")
    public ApiResult<CoreStatsDetailDTO> queryCoreStatsDetail(
            @ApiParam("酒店id") @RequestParam("chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @ApiParam("开始日期") @RequestParam("beginDate") @NotNull(message = "beginDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
            @ApiParam("结束日期") @RequestParam("endDate") @NotNull(message = "endDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @ApiParam("指标类型") @RequestParam("type") @NotNull(message = "指标类型不能为空") @PositiveOrZero(message = "类型不能为负数") Integer type,
            @ApiParam("时段类型，季度=q，年=y") @RequestParam(value = "dateType",required = false) String dateType) {

        return hotelRevenueService.queryCoreStatsDetail(chainId, beginDate, endDate, type, dateType);
    }

    @ApiOperation(value = "营收详情", httpMethod = "GET")
    @RequestMapping("/queryRevenueStatsDetail")
    public ApiResult<RevenueStatsDetailDTO> queryRevenueStatsDetail(
            @ApiParam("酒店id") @RequestParam("chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @ApiParam("开始日期") @RequestParam("beginDate") @NotNull(message = "beginDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
            @ApiParam("结束日期") @RequestParam("endDate") @NotNull(message = "endDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @ApiParam("指标类型") @RequestParam("type") @NotNull(message = "营收类型不能为空") @PositiveOrZero(message = "类型不能为负数") Integer type,
            @ApiParam("时段类型，季度=q，年=y") @RequestParam(value = "dateType", required = false) String dateType) {

        return hotelRevenueService.queryRevenueStatsDetail(chainId, beginDate, endDate, dateType);
    }

    @ApiOperation(value = "预算完成度详情", httpMethod = "GET")
    @RequestMapping("/queryBudgetProgressDetail")
    public ApiResult<BudgetProgressDetailDTO> queryBudgetProgressDetail(
            @ApiParam("酒店id") @RequestParam("chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @ApiParam("开始日期") @RequestParam("beginDate") @NotNull(message = "beginDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
            @ApiParam("结束日期") @RequestParam("endDate") @NotNull(message = "endDate不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        return hotelRevenueService.queryBudgetProgressDetail(chainId, beginDate, endDate);
    }

    @ApiOperation(value = "营业周定义查询", httpMethod = "GET")
    @RequestMapping("/queryWeekendDefinition")
    public ApiResult<WeekendDefinitionQueryDTO> queryWeekendDefinition(
            @ApiParam("酒店id") @NotNull(message = "酒店id不能为空") @Positive(message = "酒店id必须大于0") @RequestParam("chainId") Integer chainId) {

        return hotelRevenueService.queryWeekendDefinition(chainId);
    }

    @ApiOperation(value = "根据区域id营业周定义查询", httpMethod = "GET")
    @RequestMapping("/queryAreaWeekendDefinition")
    public ApiResult<WeekendDefinitionQueryAreaDTO> queryAreaWeekendDefinition(
            @ApiParam("酒店id") @NotNull(message = "区域id不能为空") @Positive(message = "区域id必须大于0") @RequestParam("deptId") Integer deptId) {

        return hotelRevenueService.queryWeekendDefinitionByArea(deptId, LocalDate.now());
    }

    @ApiOperation(value = "经营日报", httpMethod = "GET", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "chainId", value = "酒店 id", required = true),
            @ApiImplicitParam(name = "date", value = "日期, 格式: 2020-05-22", required = true)})
    @RequestMapping("/dailyReport")
    public ApiResult<List<DynamicGridDTO>> dailyReport(@RequestParam("chainId") int chainId,
                                                       @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam("date") Date date) {

        return ApiResult.success(dailyReportService.dailyReport(chainId, date));
    }

    @ApiOperation(value = "查询经营成本数据(日成本和月成本)", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "chainId", value = "酒店id", required = true),
            @ApiImplicitParam(name = "queryType", value = "查询类型, 1日成本 2月成本", required = false),
            @ApiImplicitParam(name = "queryMonth", value = "查询月份, 格式: 2020-05", required = false),
            @ApiImplicitParam(name = "queryDate", value = "查询日期, 格式: 2020-05-01", required = false),
            @ApiImplicitParam(name = "night", value = "是否勾选单间夜成本", required = true)})
    @RequestMapping("/queryRevenueData")
    public ApiResult<CostDataDTO> queryRevenueData(
            @RequestParam(value = "chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @RequestParam(value = "queryType", required = false) Integer queryType,
            @RequestParam(value = "queryMonth", required = false) @DateTimeFormat(pattern = "yyyy-MM") Date queryMonth,
            @RequestParam(value = "queryDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date queryDate,
            @RequestParam(value = "night") @NotNull(message = "是否勾选单间夜成本") Integer night) {

        if (Objects.equals(COST_DATA_QUERY_TYPE_DAY, queryType)) {
            //查询日成本
            return ApiResult.success(hotelRevenueService.queryDayCostData(chainId, queryDate, night));
        }

        //查询月成本,老版本app不传queryType参数
//        return ApiResult.success(hotelRevenueService.queryCostData(chainId, queryMonth, night));
        return queryRevenueDataV2(chainId, queryType, queryMonth, queryDate, night);
    }

    @ApiOperation(value = "查询经营成本数据(日成本和月成本)", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "chainId", value = "酒店id", required = true),
            @ApiImplicitParam(name = "queryType", value = "查询类型, 1日成本 2月成本", required = false),
            @ApiImplicitParam(name = "queryMonth", value = "查询月份, 格式: 2020-05", required = false),
            @ApiImplicitParam(name = "queryDate", value = "查询日期, 格式: 2020-05-01", required = false),
            @ApiImplicitParam(name = "night", value = "是否勾选单间夜成本", required = true)})
    @RequestMapping("/queryRevenueDataV2")
    public ApiResult<CostDataDTO> queryRevenueDataV2(
            @RequestParam(value = "chainId") @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
            @RequestParam(value = "queryType", required = false) Integer queryType,
            @RequestParam(value = "queryMonth", required = false) @DateTimeFormat(pattern = "yyyy-MM") Date queryMonth,
            @RequestParam(value = "queryDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date queryDate,
            @RequestParam(value = "night") @NotNull(message = "是否勾选单间夜成本") Integer night) {
        if (Objects.equals(COST_DATA_QUERY_TYPE_DAY, queryType)) {
            //查询日成本
            return ApiResult.success(hotelRevenueService.queryDayCostData(chainId, queryDate, night));
        }

        //查询月成本,老版本app不传queryType参数
        return ApiResult.success(hotelRevenueService.queryCostDataFromBI(chainId, queryMonth, night));
    }

    @ApiOperation(value = "查询GOP率", httpMethod = "GET")
    @RequestMapping("/queryGOPData")
    public ApiResult<GOPDataDTO> queryGOPData(@ApiParam(value = "酒店id", required = true) @RequestParam(value = "chainId", required = true)
                                              @NotNull(message = "chainId不能为空") @Positive(message = "chainId必须大于0") Integer chainId,
                                              @ApiParam(value = "查询月份") @RequestParam(value = "queryMonth") @DateTimeFormat(pattern = "yyyy-MM") Date queryMonth) {

        return ApiResult.success(hotelRevenueService.queryGOPDatas(chainId, queryMonth));
    }

}
