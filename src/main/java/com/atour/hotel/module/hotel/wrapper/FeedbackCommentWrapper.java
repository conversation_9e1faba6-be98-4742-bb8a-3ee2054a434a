package com.atour.hotel.module.hotel.wrapper;

import com.atour.dicts.db.common.DeletedEnum;
import com.atour.hotel.module.hotel.param.FeedbackCommentParam;
import com.atour.hotel.persistent.franchise.entity.FeedbackCommentEntity;
import com.atour.utils.DateUtil;

/**
 * 公告或问题方案 wrapper
 *
 * <AUTHOR>
 * @date 2019/9/25
 */
public class FeedbackCommentWrapper {


    /**
     * 公告或问题方案组装返回
     *
     * @param feedbackParam
     * @return
     */
    public static FeedbackCommentEntity wrapperFeedBackCommentEntity(FeedbackCommentParam feedbackParam) {
        if (null == feedbackParam) {
            return null;
        }
        FeedbackCommentEntity feedbackCommentEntity = new FeedbackCommentEntity();
        feedbackCommentEntity.setHandleSpeed(feedbackParam.getHandleSpeed());
        feedbackCommentEntity.setAttitude(feedbackParam.getAttitude());
        feedbackCommentEntity.setResultFeel(feedbackParam.getResultFeel());
        feedbackCommentEntity.setContent(feedbackParam.getContent());
        feedbackCommentEntity.setCreateTime(DateUtil.getCurrentDateTime());
        feedbackCommentEntity.setUpdateTime(DateUtil.getCurrentDateTime());
        feedbackCommentEntity.setFeedbackId(feedbackParam.getFeedbackId());
        feedbackCommentEntity.setUserId(feedbackParam.getUserId());
        feedbackCommentEntity.setDeleted(DeletedEnum.UNDELETED.getCode());
        return feedbackCommentEntity;
    }

}
