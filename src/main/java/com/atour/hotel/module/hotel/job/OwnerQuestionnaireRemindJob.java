package com.atour.hotel.module.hotel.job;

import com.atour.api.bean.ApiResult;
import com.atour.api.questionnaire.param.CheckUserAnswerParam;
import com.atour.api.questionnaire.remote.QuestionnaireModuleRemote;
import com.atour.chain.api.chain.dto.AtourChainDTO;
import com.atour.chain.api.chain.remote.ChainRemote;
import com.atour.dicts.enums.rbac.UserOfTypeEnum;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.enums.ProjectBaseInfoOfNatureEnum;
import com.atour.hotel.common.enums.ProjectNodeTypeEnum;
import com.atour.hotel.module.hotel.bo.PreSeasonDateBO;
import com.atour.hotel.module.hotel.dto.questionnaire.QuestionnaireTypeBindDTO;
import com.atour.hotel.module.hotel.enums.SeasonDataEnum;
import com.atour.hotel.module.hotel.service.ProjectService;
import com.atour.notify.api.enums.JPushNotifyUrlTypeEnum;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 业主端问卷推送
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Slf4j
@Component
@JobHandler(value = "ownerQuestionnaireRemindJob")
public class OwnerQuestionnaireRemindJob extends AbstractOwnerPushJob {

    private static final String OWNER_REMIND_INFO = "烦请填写业主满意度调研，反馈您的真实感受";

    @ApolloJsonValue("${chain.questionnaire:{}}")
    private volatile QuestionnaireTypeBindDTO questionnaireTypeBindDTO;

    @Value("${message.host}")
    private volatile String messageHost;

    @Resource
    private ChainRemote chainRemote;
    @Resource
    private ProjectService projectService;
    @Resource
    private QuestionnaireModuleRemote questionnaireModuleRemote;

    /**
     * 对触发满意度调研且未填写满意度调研的业主进行push提示
     * <p>
     * 1、在业主触发满意度调研的第1、6、11、16、21、26天的10：00进行push
     * <p>
     * 2、点击push进入调研页面
     */
    @Override
    protected void doPush() {
        log.info("业主端问卷推送 start");
        PreSeasonDateBO currentSeasonDates = SeasonDataEnum.getCurrentSeasonDates();
        if (Objects.isNull(currentSeasonDates)) {
            log.error("当前季度日期异常");
            return;
        }
        // 上季度日期
        Date preSeasonBegin =
            DateUtil.subMonths(currentSeasonDates.getPreSeasonFistDay(), SystemContants.PRE_SEASON_MONTH_SUB);
        Date preSeasonEnd =
            DateUtil.subMonths(currentSeasonDates.getPreSeasonLastDay(), SystemContants.PRE_SEASON_MONTH_SUB);

        // 爬坡期// 成熟期// 开业审批通过
        ApiResult<List<AtourChainDTO>> atourChainDTOS = chainRemote.getAllOpenedChain();
        List<AtourChainDTO> result = atourChainDTOS.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<Integer, List<Integer>> questionnaireChainId = projectService.getNeedQuestionChainListMap(result);

        // 上季度标记营建沟通会完成
        List<Integer> projectFlowFinishChains =
            projectService.getStartProjectFlowByRealDate(ProjectNodeTypeEnum.COMMUNICATE_MEETING.getType(),
                ProjectBaseInfoOfNatureEnum.LICENCE.getValue(), preSeasonBegin, preSeasonEnd);
        for (List<Integer> value : questionnaireChainId.values()) {
            projectFlowFinishChains.removeAll(value);
        }
        questionnaireChainId.put(questionnaireTypeBindDTO.getOpenQuestionnaire(), projectFlowFinishChains);
        // 上季度标记项目开始
        List<Integer> chainIds =
            projectService.getStartProjectFlowByRealDate(ProjectNodeTypeEnum.PROJECT_START.getType(),
                ProjectBaseInfoOfNatureEnum.LICENCE.getValue(), preSeasonBegin, preSeasonEnd);
        for (List<Integer> value : questionnaireChainId.values()) {
            chainIds.removeAll(value);
            chainIds.removeAll(projectFlowFinishChains);
        }
        questionnaireChainId.put(questionnaireTypeBindDTO.getStartQuestionnaire(), chainIds);

        log.info("问卷推送questionnaireChainId={}", ObjectUtil.toJsonQuietly(questionnaireChainId));
        // 且问卷未完成
        for (Map.Entry<Integer, List<Integer>> map : questionnaireChainId.entrySet()) {
            if (CollectionUtils.isEmpty(map.getValue())) {
                continue;
            }
            // 问卷id
            Integer key = map.getKey();
            // 酒店ids
            List<Integer> list = map.getValue();
            Map<Integer, Integer> matchChainOwner = super.matchChainOwner(list);
            // 本季度已经填写过
            CheckUserAnswerParam checkUserAnswerParam = new CheckUserAnswerParam();
            checkUserAnswerParam.setUserIdList(Lists.newArrayList(String.valueOf(matchChainOwner.keySet())));
            checkUserAnswerParam.setUserType(UserOfTypeEnum.OWNER.getCode());
            checkUserAnswerParam.setBeginDate(DateUtil.formatDate(currentSeasonDates.getPreSeasonFistDay()));
            checkUserAnswerParam.setEndDate(DateUtil.formatDate(currentSeasonDates.getPreSeasonLastDay()));
            List<String> checkFinishedByUser = Safes.of(
                questionnaireModuleRemote.checkFinishedByUser(checkUserAnswerParam)
                    .getResult());
            log.info("问卷推送 matchChainOwner={}", ObjectUtil.toJsonQuietly(matchChainOwner));
            final String url = String.format(messageHost + "/questionnaire/list?id=%d", key);
            for (Integer userId : matchChainOwner.keySet()) {
                if (checkFinishedByUser.contains(String.valueOf(userId))) {
                    continue;
                }
                super.ownerPushRemind(userId, url, OWNER_REMIND_INFO,
                    String.valueOf(JPushNotifyUrlTypeEnum.H5.getCode()));
            }
        }
        log.info("业主端问卷推送 end");
    }
}
