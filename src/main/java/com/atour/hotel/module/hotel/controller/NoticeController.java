package com.atour.hotel.module.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.api.bean.PageInfo;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.util.CommonParamsDTO;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.module.hotel.param.NoticePageParam;
import com.atour.hotel.module.hotel.response.NoticePageResponse;
import com.atour.hotel.module.hotel.response.NoticeResponse;
import com.atour.hotel.module.hotel.service.NoticeService;
import com.atour.hotel.module.login.response.SystemInfoLimit;
import com.atour.utils.Safes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;

/**
 * 公告 controller
 *
 * <AUTHOR>
 * @date 2019年9月24日
 */
@RestController
@RequestMapping(value = "/app/notice", produces = {"application/json;charset=UTF-8"})
@Api(tags = "公告", value = "公告", description = "公告")
public class NoticeController {

    @Resource
    private NoticeService noticeService;

    /**
     * 公告列表 分页为空查全部
     *
     * @param noticePageParam
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "公告列表", httpMethod = "GET")
    public ApiResult<NoticePageResponse> list(@Valid NoticePageParam noticePageParam) {

        final CommonParamsDTO commonParamsDTO = CommonParamsManager.get();
        if (SystemInfoLimit.compareVersion(Safes.of(commonParamsDTO.getVersion(), "1.0.0"), "1.3.5") >= 0) {
            PageInfo pageInfo = new PageInfo(Safes.of(noticePageParam.getPage(), SystemContants.DEFAULT_PAGE[0]),
                Safes.of(noticePageParam.getPageSize(), SystemContants.DEFAULT_PAGE[1]));
            pageInfo.compute(0);
            final NoticePageResponse noticePageResponse = new NoticePageResponse(pageInfo, Collections.emptyList());
            return ApiResult.success(noticePageResponse);
        }
        return ApiResult.success(noticeService.list(noticePageParam));
    }

    /**
     * 公告详情
     *
     * @param noticeId
     * @return
     */
    @RequestMapping(value = "/detail", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "公告详情", httpMethod = "GET")
    public ApiResult<NoticeResponse> detail(
        @Valid @NotNull(message = "id不能为空") @ApiParam(value = "公告id") Integer noticeId) {
        return ApiResult.success(noticeService.getDetailById(noticeId));
    }

    /**
     * 公告已读状态设置修改
     *
     * @param noticeId
     * @return
     */
    @PostMapping(value = "/updateReadState")
    @ApiOperation(value = "公告已读状态设置修改", httpMethod = "POST")
    public ApiResult<Boolean> updateReadState(
        @Valid @NotNull(message = "id不能为空") @ApiParam(value = "公告id") Integer noticeId) {
        final CommonParamsDTO commonParamsDTO = CommonParamsManager.get();
        if (SystemInfoLimit.compareVersion(Safes.of(commonParamsDTO.getVersion(), "1.0.0"), "1.3.5") >= 0) {
            return ApiResult.success(Boolean.TRUE);
        }
        return ApiResult.success(noticeService.updateReadState(noticeId));
    }

}
