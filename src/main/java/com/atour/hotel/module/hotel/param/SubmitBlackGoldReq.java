package com.atour.hotel.module.hotel.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 提交黑金反馈请求参数
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class SubmitBlackGoldReq {

    @ApiModelProperty(value = "主键id", required = true)
    private Long id;

    @ApiModelProperty(value = "反馈类型 1-续住反馈 2-退房反馈", required = true)
    private Integer feedbackType;

    @ApiModelProperty(value = "续住单id")
    private Long renewalFolioId;

    @ApiModelProperty(value = "初见倾心")
    private List<String> firstSightOssKeyList;

    @ApiModelProperty(value = "APLUS布房完成")
    private List<String> aplusOssKeyList;

    @ApiModelProperty(value = "房间正对客床")
    private List<String> roomBedOssKeyList;

    @ApiModelProperty(value = "卫生间图片")
    private List<String> toiletOssKeyList;

    @ApiModelProperty(value = "接待流程")
    private String receptionProcessContext;

    @ApiModelProperty(value = "喜欢和禁忌")
    private String likeAndTaboosContext;

    @ApiModelProperty(value = "其他")
    private String otherContext;

}
