package com.atour.hotel.module.hotel.service;

import com.atour.api.bean.PageInfo;
import com.atour.dicts.db.atour_pms.*;
import com.atour.hotel.common.constants.SystemContants;
import com.atour.hotel.common.enums.ResponseCodeEnum;
import com.atour.hotel.common.util.CommonParamsManager;
import com.atour.hotel.common.util.ImgUtils;
import com.atour.hotel.framework.configuration.FileConfig;
import com.atour.hotel.module.email.service.EmailService;
import com.atour.hotel.module.energy.job.service.WarnMailService;
import com.atour.hotel.module.hotel.condition.FeedbackCondition;
import com.atour.hotel.module.hotel.param.FeedbackParam;
import com.atour.hotel.module.hotel.param.ImageInfoParam;
import com.atour.hotel.module.hotel.response.FeedbackPageResponse;
import com.atour.hotel.module.hotel.response.FeedbackTypeResponse;
import com.atour.hotel.module.hotel.response.ReplyResponse;
import com.atour.hotel.module.hotel.wrapper.FeedbackWrapper;
import com.atour.hotel.persistent.franchise.dao.FeedbackDao;
import com.atour.hotel.persistent.franchise.entity.FeedbackEntity;
import com.atour.hotel.persistent.franchise.entity.FeedbackReplyEntity;
import com.atour.rbac.api.response.HotelManagerDTO;
import com.atour.rbac.api.response.HotelManagerInfoDTO;
import com.atour.rbac.api.response.UserPermissionDTO;
import com.atour.web.exception.BusinessException;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学堂推送学习任务service
 *
 * <AUTHOR>
 * @date 2019年9月24日
 */
@Service
@Slf4j
public class FeedbackService {

    @ApolloJsonValue("${upgrade.mail.receivers:[<EMAIL>]}")
    private volatile List<String> mailReceivers;

    /**
     * 图片最多上传数量
     */
    private static final int IMAGE_MAX_SIZE_UPLOAD = 9;

    @Resource
    private FeedbackDao feedbackDao;
    @Resource
    private FeedbackReplyService feedbackReplyService;
    @Resource
    private FeedbackCommentService feedbackCommentService;
    @Resource
    private EmailService emailService;
    @Resource
    private WarnMailService warnMailService;

    /**
     * 新增
     *
     * @param feedbackParam
     * @return
     */
    public Boolean add(FeedbackParam feedbackParam) {
        UserPermissionDTO userPermissionDTO = CommonParamsManager.getLocalUserHotel();
        if (null == userPermissionDTO) {
            throw new BusinessException(ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getMessage(),
                ResponseCodeEnum.AUTH_NOT_LOGIN_INFO.getCode());
        }
        List<ImageInfoParam> imageInfoList = feedbackParam.getImageInfoList();
        List<String> imageListOrigin = Lists.newArrayListWithExpectedSize(IMAGE_MAX_SIZE_UPLOAD);
        List<String> imageListThumb = Lists.newArrayListWithExpectedSize(IMAGE_MAX_SIZE_UPLOAD);
        if (CollectionUtils.isNotEmpty(imageInfoList)) {
            imageInfoList.forEach(imageInfoParam -> {
                imageListOrigin.add(FileConfig.imageQiniuServer + imageInfoParam.getUrl());
                int[] thumbWidthAndHeight =
                    ImgUtils.getThumbWidhtAndHeight(imageInfoParam.getWidth(), imageInfoParam.getHeight());
                imageListThumb.add(
                    FileConfig.imageQiniuServer + imageInfoParam.getUrl() + "?imageView2/1/w/" + thumbWidthAndHeight[0]
                        + "/h/" + thumbWidthAndHeight[1]);
            });
        }

        FeedbackEntity feedbackEntity =
            FeedbackWrapper.wrapperFeedbackEntity(feedbackParam, userPermissionDTO, imageListOrigin, imageListThumb);
        boolean flag = feedbackDao.addFeedback(feedbackEntity) > 0;
        sendMail(feedbackEntity, flag, SendEmailOfTypeEnum.FEEDBACK, null);
        return flag;
    }

    /**
     * 区分类型发送邮件
     *
     * @param feedbackEntity
     * @param flag
     */
    public void sendMail(FeedbackEntity feedbackEntity, boolean flag, SendEmailOfTypeEnum feedback,
        FeedbackReplyEntity param) {
        if (flag) {
            //发送邮件
            Integer chainId = feedbackEntity.getChainId();
            List<String> mailTo = getMailToByFeedbackType(chainId, feedbackEntity.getType());
            emailService.sendUpgradeEmail(CommonParamsManager.getLocalUserHotel(), feedbackEntity.getId(), mailTo,
                feedback, param);
        }
    }

    /**
     * 获取邮件收件人
     *
     * @param chainId
     * @param type
     * @return
     */
    public List<String> getMailToByFeedbackType(Integer chainId, Integer type) {
        //发送邮件
        List<String> mailTo = Lists.newArrayList();
        mailTo.add("<EMAIL>");
        switch (Objects.requireNonNull(HotelFeedbackTypeEnum.of(type))) {
            case CONSTRUCT:
                // 发送邮件营建：<EMAIL>(长河)，酒店对应区域的区域省长、省政委邮箱
                mailTo.add("<EMAIL>");
                //获取酒店的管理人员邮箱
                setMailTo(mailTo, chainId);
                break;
            case PROCUREMENT:
                //采购：<EMAIL>
                mailTo.add("<EMAIL>");
                break;
            case HOTEL_OPERATE:
                //酒店运营：对应城区的省长、省政委邮箱
                //获取酒店的管理人员邮箱
                setMailTo(mailTo, chainId);
                break;
            case XIANZHANG_ZHENGWEI:
                //现长/政委：对应城区的省长、省政委邮箱
                //获取酒店的管理人员邮箱
                setMailTo(mailTo, chainId);
                break;
            case FINANCE:
                //财务：<EMAIL>
                mailTo.add("<EMAIL>");
                break;
            case HR:
                //人事：对于城区的HRBP邮箱，对应城区的省政委邮箱
                List<HotelManagerDTO> managerList = warnMailService.getManagerByChainId(Sets.newHashSet(chainId));
                if (CollectionUtils.isNotEmpty(managerList)) {
                    Map<Integer, HotelManagerDTO> managerMap = managerList.stream()
                        .collect(Collectors.toMap(HotelManagerDTO::getChainId, Function.identity(), (k1, k2) -> k1));
                    HotelManagerDTO hotelManagerDTO = managerMap.get(chainId);
                    // 省政委
                    mailTo.addAll(hotelManagerDTO.getShengzhengweiResults()
                        .stream()
                        .map(HotelManagerInfoDTO::getEmail)
                        .collect(Collectors.toList()));
                    // HRBP
                    mailTo.addAll(hotelManagerDTO.getHrbpResults()
                        .stream()
                        .map(HotelManagerInfoDTO::getEmail)
                        .collect(Collectors.toList()));
                }
                break;
            default:
                log.info("发送邮件：chainId={}，feedbackType={}，邮件收件人=={}", chainId, type, mailTo);
        }
        return mailTo;
    }

    private void setMailTo(List<String> mailTo, Integer chainId) {
        List<HotelManagerDTO> managerList = warnMailService.getManagerByChainId(Sets.newHashSet(chainId));
        if (CollectionUtils.isNotEmpty(managerList)) {
            Map<Integer, HotelManagerDTO> managerMap = managerList.stream()
                .collect(Collectors.toMap(HotelManagerDTO::getChainId, Function.identity(), (k1, k2) -> k1));
            HotelManagerDTO hotelManagerDTO = managerMap.get(chainId);
            // 省长
            mailTo.addAll(hotelManagerDTO.getShengzhangList()
                .stream()
                .map(HotelManagerInfoDTO::getEmail)
                .collect(Collectors.toList()));
            // 省政委
            mailTo.addAll(hotelManagerDTO.getShengzhengweiResults()
                .stream()
                .map(HotelManagerInfoDTO::getEmail)
                .collect(Collectors.toList()));
        }
    }

    /**
     * 反馈列表
     *
     * @param page
     * @param userId
     * @param pageSize
     * @return
     */
    public FeedbackPageResponse getFeedbackList(Integer userId, Integer page, Integer pageSize) {
        // 获取反馈列表
        PageInfo pageInfo = new PageInfo(SystemContants.DEFAULT_PAGE[0], SystemContants.DEFAULT_PAGE[1]);
        if (null != page && null != pageSize) {
            pageInfo.setPageNo(page);
            page = (page - 1) * pageSize;
            pageInfo.setPageSize(pageSize);
        }
        FeedbackCondition feedbackCondition =
            FeedbackWrapper.buildFeedbackCondition(userId, null, null, null, null, page, pageSize);
        Integer totalCount = feedbackDao.countFeedback(feedbackCondition);
        if (totalCount <= 0) {
            pageInfo.setTotalCount(totalCount);
            return FeedbackPageResponse.builder()
                .pageInfo(pageInfo)
                .build();
        }
        List<FeedbackEntity> feedbackEntityList = feedbackDao.getFeedbackList(feedbackCondition);
        // 分装id
        List<Integer> feedbackIds = feedbackEntityList.stream()
            .map(FeedbackEntity::getId)
            .collect(Collectors.toList());
        // 查询对应的回复
        Map<Integer, List<ReplyResponse>> replyResponseListMap =
            feedbackReplyService.getFeedReplyByFeedbackIds(feedbackIds);
        // 查询对应的评价<feedbackId,grade>
        Map<Integer, Double> replyMap = feedbackCommentService.getFeedCommentByFeedbackIds(feedbackIds);
        pageInfo.setTotalCount(totalCount);
        return FeedbackPageResponse.builder()
            .pageInfo(pageInfo)
            .feedbackResponseList(feedbackEntityList.stream()
                .map(entity -> FeedbackWrapper.toFeedbackResponse(entity, replyResponseListMap, replyMap))
                .collect(Collectors.toList()))
            .build();
    }

    /**
     * 升级反馈
     *
     * @param feedbackId
     * @return
     */
    public Boolean upgradeFeedback(Integer feedbackId) {
        // 升级更新
        FeedbackEntity feedbackEntity = new FeedbackEntity();
        feedbackEntity.setId(feedbackId);
        feedbackEntity.setUpgraded(FeedbackOfIsUpgradeEnum.UPGRADE.getCode());
        Integer flag = feedbackDao.updateByPrimaryKeySelective(feedbackEntity);
        // 发送邮件
        emailService.sendUpgradeEmail(CommonParamsManager.getLocalUserHotel(), feedbackId, mailReceivers,
            SendEmailOfTypeEnum.FEEDBACK_UPGRADE, null);
        return flag > 0;
    }

    /**
     * 更新反馈
     *
     * @param feedbackEntity
     * @return
     */
    public Boolean updateFeedbackByKey(FeedbackEntity feedbackEntity) {
        // 更新
        return feedbackDao.updateByPrimaryKeySelective(feedbackEntity) > 0;
    }

    /**
     * 查詢
     *
     * @param userId
     * @param readState
     * @return
     */
    public Integer countNotReadReplyByUserId(Integer userId, Integer readState) {
        // 查询用户对应的反馈
        Integer countFeedback =
            feedbackDao.countNotReadFeedbackByUserId(userId, readState, HotelFeedbackStateEnum.TO_EVALUATE.code());
        // 被回复人id查询未读的回复
        Integer countFeedbackReply = feedbackReplyService.countFeedbackReplyByTargetUserId(userId, readState);
        return countFeedback + countFeedbackReply;
    }

    /**
     * 更新已读状态
     *
     * @param userId
     * @return
     */
    public Boolean upgradeFeedbackReadState(Integer userId) {
        Integer updateFeedbackRead = feedbackDao.upgradeFeedbackReadState(userId, FeedbackOfIsReadEnum.READ.getCode());
        Integer updateReply =
            feedbackReplyService.upgradeFeedbackReplyReadState(userId, FeedbackOfIsReadEnum.READ.getCode());
        return updateFeedbackRead + updateReply > 0;
    }

    /**
     * 获取反馈类型集合
     *
     * @return
     */
    public List<FeedbackTypeResponse> getFeedbackType() {
        List<FeedbackTypeResponse> feedbackResponseList =
            Lists.newArrayListWithCapacity(HotelFeedbackTypeEnum.values().length);
        for (HotelFeedbackTypeEnum feedbackOfTypeEnum : HotelFeedbackTypeEnum.values()) {
            if (Objects.equals(HotelFeedbackTypeEnum.UNKNOWN.code(), feedbackOfTypeEnum.code())) {
                continue;
            }
            feedbackResponseList.add(new FeedbackTypeResponse(feedbackOfTypeEnum.code(), feedbackOfTypeEnum.desc()));
        }
        return feedbackResponseList;
    }

    /**
     * 查询未处理状态的反馈
     *
     * @param state
     * @param laterTime 24小时前的时间
     * @return
     */
    public Integer countFeedbackByState(Integer state, Date laterTime) {
        if (Objects.isNull(state)) {
            throw new BusinessException(ResponseCodeEnum.PARAM_ERROR.getMessage(),
                ResponseCodeEnum.PARAM_ERROR.getCode());
        }
        return feedbackDao.countFeedbackByState(state, laterTime);
    }


}