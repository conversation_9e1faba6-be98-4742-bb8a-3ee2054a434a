package com.atour.hotel.module.hotel.enums;

import com.yaduo.infras.core.base.exception.AtourBizValidException;
import io.swagger.models.auth.In;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum GopQuotaEnum {
    revpar("revpar",1,"综合RevPAR(元)"),
    single_business_fee_manage("business_fee_manage",2,"单间夜总成本(元)"),
    gop_rate("gop_rate",3,"总体GOP率"),
    avg_room_price("avg_room_price",4,"平均房价(元)"),
    letting_rate("letting_rate",5,"出租率"),
    person_room_ratio("person_room_ratio",6,"人房比"),
    ;


    GopQuotaEnum(String code, Integer sort, String desc) {
        this.code = code;
        this.sort = sort;
        this.desc = desc;
    }

    private final String code;
    private final String desc;
    private final Integer sort;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getSort() {
        return sort;
    }

    public static GopQuotaEnum codeOf(String value) {
        for (GopQuotaEnum item : values()) {
            if (Objects.equals(item.getCode(), value)) {
                return item;
            }
        }
        throw new AtourBizValidException("value: " + value + " of GopQuotaEnum not found");
    }
}
