package com.atour.hotel.module.hotel.param.task;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 任务物料统计请求参数
 * @date 2024年02月23日16:07
 * @since JDK1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TaskSupplyParam", description = "任务物料统计请求参数")
public class TaskSupplyParam {

    /**
     * 酒店ID
     */
    @Schema(description = "酒店ID")
    @NotNull(message = "酒店ID不能为空")
    private Integer chainId;

    @Schema(description = "任务ID")
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
}
