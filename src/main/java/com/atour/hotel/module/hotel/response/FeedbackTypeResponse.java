package com.atour.hotel.module.hotel.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 反馈类型
 *
 * <AUTHOR>
 * @date 2019/9/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackTypeResponse {

    /**
     * 类型code
     */
    @ApiModelProperty(name = "code", value = "类型code", dataType = "Integer")
    private Integer code;

    /**
     * 类型名称
     */
    @ApiModelProperty(name = "typeName", value = "类型名称", dataType = "String")
    private String typeName;

}
