package com.atour.hotel.module.hotel.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2019/09/24
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CoreStatsDTO {

    @ApiModelProperty("平均房价")
    private BigDecimal avgRoomPrice;

    @ApiModelProperty("出租率")
    private BigDecimal rentRatio;

    @ApiModelProperty("综合revPar")
    private BigDecimal revPar;

    @ApiModelProperty("总营收")
    private BigDecimal totalRevenue;

}
