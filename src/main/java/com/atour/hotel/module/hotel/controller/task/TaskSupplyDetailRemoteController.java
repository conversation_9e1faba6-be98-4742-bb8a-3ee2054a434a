package com.atour.hotel.module.hotel.controller.task;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.hotel.service.TaskSupplyDetailRemoteService;
import com.atour.hotel.param.jmtask.TaskUseSupplyParam;
import com.atour.hotel.remote.TaskSupplyDetailRemote;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 任务物料的rpc服务实现
 * @date 2024年02月29日16:06
 * @since JDK1.8
 */
@RestController
@RequestMapping("/inner/api/web/taskSupply")
public class TaskSupplyDetailRemoteController implements TaskSupplyDetailRemote {

    @Resource
    private TaskSupplyDetailRemoteService remoteService;

    /**
     * 新增任务物料
     * @param taskUseSupplyParam
     */
    @Override
    @PostMapping("/addTaskSupply")
    public ApiResult<Boolean> addTaskSupply(@RequestBody @Valid TaskUseSupplyParam taskUseSupplyParam) {
        return remoteService.addTaskSupply(taskUseSupplyParam);
    }

    /**
     * 任务回收时调用，回收物料
     * @param taskUseSupplyParam
     */
    @Override
    @PostMapping("/taskRecycleSupply")
    public ApiResult<Boolean> taskRecycleSupply(@RequestBody @Valid TaskUseSupplyParam taskUseSupplyParam) {
        return remoteService.taskRecycleSupply(taskUseSupplyParam);
    }

    /**
     * 删除任务物料
     * @param taskUseSupplyParam
     */
    @Override
    @PostMapping("/deleteTaskSupply")
    public ApiResult<Boolean> deleteTaskSupply(@RequestBody @Valid TaskUseSupplyParam taskUseSupplyParam) {
        return remoteService.deleteTaskSupply(taskUseSupplyParam);
    }
}
