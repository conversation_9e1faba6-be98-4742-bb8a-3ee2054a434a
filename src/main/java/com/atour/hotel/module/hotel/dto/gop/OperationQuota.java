package com.atour.hotel.module.hotel.dto.gop;

import com.atour.hotel.module.hotel.config.BigDecimalRoundingModeConfig;
import com.atour.hotel.module.hotel.enums.GopUnitType;
import com.atour.hotel.module.hotel.util.GopFormatUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
public class OperationQuota {
    //类目名称
//    private String itemName;
//    类目code
//    private String itemCode;
    //记录值
    @Setter
    @JsonIgnore
    private BigDecimal curValue;
    //参考值
    @Setter
    @JsonIgnore
    private BigDecimal referValue;
    @Setter
    @JsonIgnore
    private GopUnitType unitType;

    //当前值
    @Getter
    private String value;
    //参考值
    @Getter
    private String comparedValue;

    private static final String DEFAULT_VAL = "-";


    public void compute() {
        if (curValue == null) {
            this.value = DEFAULT_VAL;
            this.comparedValue = DEFAULT_VAL;
            return;
        }
        if (unitType == GopUnitType.NO_FORMAT) {
            if (referValue != null) {
                double calValue = curValue.subtract(referValue).setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE).doubleValue();
                this.comparedValue = GopFormatUtil.formatNumberValue(calValue);
            } else {
                this.comparedValue = DEFAULT_VAL;
            }
            this.value = GopFormatUtil.formatNumberValue(curValue.setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE).doubleValue(), false);
        } else if (unitType == GopUnitType.PP) {
            if (referValue != null) {
                Double compareValue = curValue.subtract(referValue).setScale(BigDecimalRoundingModeConfig.SCALE_3, BigDecimalRoundingModeConfig.ROUNDING_MODE).multiply(BigDecimal.valueOf(100)).doubleValue();
                this.comparedValue = GopFormatUtil.formatPPValue(compareValue);
            } else {
                this.comparedValue = DEFAULT_VAL;
            }
//            this.value = GopFormatUtil.formatPPValue(curValue.setScale(BigDecimalRoundingModeConfig.SCALE_2, BigDecimalRoundingModeConfig.ROUNDING_MODE).multiply(BigDecimal.valueOf(100)).intValue(), false);
            this.value = GopFormatUtil.formatPercentValue(curValue.multiply(BigDecimal.valueOf(100)).setScale(BigDecimalRoundingModeConfig.SCALE_3,BigDecimalRoundingModeConfig.ROUNDING_MODE), false);
        } else if (unitType == GopUnitType.PERCENT) {
            if (referValue != null) {
                BigDecimal percent = curValue.subtract(referValue).divide(referValue, BigDecimalRoundingModeConfig.SCALE_3, BigDecimalRoundingModeConfig.ROUNDING_MODE).multiply(BigDecimal.valueOf(100));
                this.comparedValue = GopFormatUtil.formatPercentValue(percent);
            } else if (referValue.doubleValue() == 0.0d) {
                this.comparedValue = GopFormatUtil.formatPercentValue(BigDecimal.valueOf(100));
            } else {
                this.comparedValue = DEFAULT_VAL;
            }
            this.value = GopFormatUtil.formatPercentValue(curValue.multiply(BigDecimal.valueOf(100)).setScale(BigDecimalRoundingModeConfig.SCALE, BigDecimalRoundingModeConfig.ROUNDING_MODE), false);
        } else {
            log.error("not supported unitType:{}", unitType);
        }
    }
}
