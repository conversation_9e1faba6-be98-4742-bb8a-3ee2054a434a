package com.atour.hotel.module.hotel.dto.construct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评价信息
 *
 * @author: 艾罗
 * @date: 2020-03-31
 */
@ApiModel("评价信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CommentDTO {

    /**
     * 评价类型（签约、开工、开业）
     */
    @ApiModelProperty("评价类型（签约、开工、开业）")
    private Integer commentType;

    /**
     * 评价类型（签约、开工、开业）
     */
    @ApiModelProperty("评价类型（签约、开工、开业）")
    private String commentName;

    @ApiModelProperty("评价状态（0:未评价 1:已评价）")
    private Integer commentState;

    @ApiModelProperty("评价内容")
    private String content;

    @ApiModelProperty("岗位评分")
    private List<ScoringItems> scoringItems;

    @ApiModelProperty("评论图标")
    private String iconUrl;

    /**
     * 打分项
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class ScoringItems {

        @ApiModelProperty("岗位")
        private Integer jobId;

        @ApiModelProperty("岗位")
        private String jobName;

        @ApiModelProperty("得分")
        private Double score;
    }
}
