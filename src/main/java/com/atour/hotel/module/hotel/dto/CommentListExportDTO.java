package com.atour.hotel.module.hotel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.atour.hotel.framework.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author：子烁
 * @date：2021/6/15 下午1:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommentListExportDTO extends BaseRowModel implements Serializable {

    /**
     * 酒店名称
     **/
    @ExcelProperty(value = "酒店名称")
    private String chainName;

    /**
     * 房单号
     **/
    @ExcelProperty(value = "房单号")
    private String folioId;

    /**
     * 会员姓名
     **/
    @ExcelProperty(value = "会员姓名")
    private String mebName;

    /**
     * 会员等级
     **/
    @ExcelProperty(value = "会员等级")
    private String mebLevel;


    /**
     * 预订时间
     **/
    @ExcelProperty(value = "预订时间")
    private String bookTime;

    /**
     * 入住时间
     **/
    @ExcelProperty(value = "入住时间")
    private String checkInTime;

    /**
     * 退房时间
     **/
    @ExcelProperty(value = "退房时间")
    private String checkOutTime;
    /**
     * 反馈状态
     **/
    @ExcelProperty(value = "反馈状态")
    private String state;


    /**
     * 反馈类型  1-续住反馈 2-退房反馈
     **/
    @ExcelProperty(value = "反馈类型")
    private String feedbackType;



    @ExcelProperty(value = "反馈人")
    private String employeeName;


    @ExcelProperty(value = "是否首次")
    private String firstCheckIn;

    @ExcelProperty(value = "点评人")
    private String commentName;

    @ExcelProperty(value = "反馈点评")
    private String commentLevel;


    @ExcelProperty(value = "点评内容")
    private String commentContext;


    @ExcelProperty(value = "接待流程")
    private String receptionProcessContext;

    @ExcelProperty(value = "喜欢和禁忌")
    private String likeAndTaboosContext;

    @ExcelProperty(value = "其他")
    private String otherContext;




}
