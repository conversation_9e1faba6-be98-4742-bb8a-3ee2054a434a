package com.atour.hotel.module.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.hotel.dto.CommonOptionDTO;
import com.atour.hotel.module.hotel.dto.CommonPageDTO;
import com.atour.hotel.module.hotel.dto.TreasuredBookDetailDTO;
import com.atour.hotel.module.hotel.dto.TreasuredBookListDTO;
import com.atour.hotel.module.hotel.param.TreasuredBookParam;
import com.atour.hotel.module.hotel.service.TreasuredBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/5/28
 */
@Api(value = "宝典相关的接口", tags = {"宝典相关的接口"})
@RestController
@RequestMapping("/app/treasuredBook")
public class TreasuredBookController {

    @Resource
    private TreasuredBookService treasuredBookService;

    @ApiOperation(value = "宝典列表", httpMethod = "GET", produces = "application/json")
    @RequestMapping("/list")
    public ApiResult<CommonPageDTO<TreasuredBookListDTO>> list(
        @ApiParam("分类id, 一个分类都没选, 不传值, 字段为空") @RequestParam(value = "categoryId", defaultValue = "") String categoryId,
        @ApiParam(value = "页码, 从 1 开始",required = true) @RequestParam("pageNo") int pageNo,
        @ApiParam(value = "页大小",required = true) @RequestParam("pageSize") int pageSize) {
        return ApiResult.success(treasuredBookService.listV2(categoryId, pageNo, pageSize));
    }

    @ApiOperation(value = "宝典列表", httpMethod = "POST", produces = "application/json")
    @PostMapping("/list_v2")
    public ApiResult<CommonPageDTO<TreasuredBookListDTO>> listV2(@RequestBody @Valid TreasuredBookParam treasuredBookParam) {
        return ApiResult.success(treasuredBookService.pageListNew(treasuredBookParam));
    }

    @ApiOperation(value = "宝典详情", httpMethod = "GET", produces = "application/json")
    @GetMapping("/detail")
    public ApiResult<TreasuredBookDetailDTO> detail(@ApiParam(value = "主键id",required = true) @RequestParam("id") Integer id) {

        return ApiResult.success(treasuredBookService.detail(id));
    }

    @ApiOperation(value = "宝典分类选项", httpMethod = "GET", produces = "application/json", notes="这个接口也会返回全部的选项, 前端不需要增加额外的选项")
    @GetMapping("/options")
    public ApiResult<List<CommonOptionDTO>> options() {

        return ApiResult.success(treasuredBookService.options());
    }

}
