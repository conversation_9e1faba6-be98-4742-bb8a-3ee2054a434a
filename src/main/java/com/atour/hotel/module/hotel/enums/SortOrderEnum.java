package com.atour.hotel.module.hotel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 任务列表筛选排序字段的枚举
 * @date 2024年02月26日19:11
 * @since JDK1.8
 */
@Getter
@AllArgsConstructor
public enum SortOrderEnum {

    /**
     * 升级状态
     */
    UPGRADE_STATE(5, "升级状态"),

    /**
     * 房间号
     */
    ROOM_NO(6, "房间号"),

    /**
     * 房态
     */
    ROOM_STATE(7, "房态"),
    ;

    private Integer code;

    private String name;

    /**
     * 获取值对应的枚举对象
     * @param code
     * @return
     */
    public static SortOrderEnum getInstance(Integer code) {
        for (SortOrderEnum obj : SortOrderEnum.values()) {
            if (obj.getCode().equals(code) ) {
                return obj;
            }
        }
        return null;
    }
}
