package com.atour.hotel.module.hotel.controller;

import com.atour.api.bean.ApiResult;
import com.atour.hotel.module.kpi.app.request.KpiMonthFinishRequest;
import com.atour.hotel.module.kpi.app.request.rectify.KpiRectifyPlanListParam;
import com.atour.hotel.module.kpi.app.response.KpiHomePageDTO;
import com.atour.hotel.module.kpi.app.response.KpiMonthFinishDTO;
import com.atour.hotel.module.kpi.app.response.rectify.KpiRectifyPlanInfoDTO;
import com.atour.hotel.module.kpi.app.response.rectify.KpiRectifyPlanListDTO;
import com.atour.hotel.module.kpi.service.KpiBaseItemService;
import com.atour.hotel.module.kpi.service.KpiFinishService;
import com.atour.hotel.module.kpi.service.KpiHomeService;
import com.atour.hotel.module.kpi.service.KpiRectifyPlanService;
import com.atour.hotel.module.kpi.web.request.recityplan.QueryKpiRectifyPlan;
import com.atour.hotel.module.kpi.web.response.KpiBaseItemTreeDTO;
import io.swagger.annotations.Api;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/30
 */
@Api(value = "业主端KPI相关接口", tags = "业主端KPI相关接口")
@RestController
@RequestMapping(value = "/app/kpi/owner", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class KpiController {

    @Resource
    private KpiBaseItemService kpiBaseItemService;
    @Resource
    private KpiHomeService kpiHomeService;
    @Resource
    private KpiFinishService kpiFinishService;
    @Resource
    private KpiRectifyPlanService kpiRectifyPlanService;

    /**
     * 基础指标树状级联信息
     * 注：当前用户权限可见范围内的
     *
     * @return 层级结构
     */
    @GetMapping(value = "/baseItem/tree/curr")
    public ApiResult<KpiBaseItemTreeDTO> treeCurr() {
        return ApiResult.success(kpiBaseItemService.owenTreeCurr());
    }


    /**
     * 业主端门店KPI
     */
    @GetMapping(value = "/home/<USER>")
    public ApiResult<KpiHomePageDTO> ownerChain(@RequestParam Long chainId) {
        return ApiResult.success(kpiHomeService.ownerChain(chainId));
    }


    /**
     * 门店、月完成列表
     */
    @PostMapping(value = "/finish/list")
    public ApiResult<List<KpiMonthFinishDTO>> finishList(@RequestBody KpiMonthFinishRequest kpiMonthFinishRequest) {
        return ApiResult.success(kpiFinishService.getKpiFinishList(kpiMonthFinishRequest,true));
    }


    /**
     * 详情
     */
    @GetMapping(value = "/rectifyPlan/info")
    public ApiResult<KpiRectifyPlanInfoDTO> info(@Valid QueryKpiRectifyPlan param) {
        KpiRectifyPlanInfoDTO kpiRectifyPlanInfoDTO = kpiRectifyPlanService.info(param,true);
        return ApiResult.success(kpiRectifyPlanInfoDTO);
    }


    /**
     * 列表
     */
    @GetMapping(value = "/rectifyPlan/list")
    public ApiResult<List<KpiRectifyPlanListDTO>> list(@Valid KpiRectifyPlanListParam param) {
        List<KpiRectifyPlanListDTO> list =  kpiRectifyPlanService.queryList(param);
        return ApiResult.success(list);
    }



}
