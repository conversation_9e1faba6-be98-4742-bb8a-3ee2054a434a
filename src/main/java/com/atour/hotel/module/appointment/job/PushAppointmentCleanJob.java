package com.atour.hotel.module.appointment.job;

import com.atour.api.bean.ApiPageResult;
import com.atour.chain.api.chain.dto.ChainDTO;
import com.atour.db.redis.RedisClient;
import com.atour.dicts.db.center.sys_message.SysMessageTypeEnum;
import com.atour.hotel.module.dkf.web.hotel.service.ChainService;
import com.atour.hotel.module.login.service.ServiceTicketService;
import com.atour.hotel.module.message.enums.AppointmentCleanEnums;
import com.atour.hotel.module.message.service.clean.factory.CleanFactory;
import com.atour.hotel.persistent.center.dao.SysMessageDao;
import com.atour.order.api.dto.AppointmentCleanOrderDTO;
import com.atour.order.api.enums.AppointmentCleanStateEnum;
import com.atour.order.api.enums.AppointmentCleanTypeEnum;
import com.atour.order.api.param.AppointmentCleanPageParam;
import com.atour.order.api.remote.AppointmentCleanRemote;
import com.atour.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author：子烁
 * @date：2021/7/30 下午2:24
 */
@Slf4j
@Component
@JobHandler(value = "pushAppointmentCleanJob")
public class PushAppointmentCleanJob extends IJobHandler {
    @Resource
    private AppointmentCleanRemote appointmentCleanRemote;
    @Resource
    private CleanFactory cleanFactory;
    @Resource
    private ChainService chainService;
    @Resource
    private SysMessageDao sysMessageDao;


    @Resource
    private RedisClient redisClient;

    private String REDIS_PUSH_PREFIX = "appointment_clean_prefix_";


    private static Integer DIFF_HOURS = 6;


    private static String MORNING_ONE;
    private static String ONE = "01";

    private static int expireTime = 24 * 60 * 60;

    static {
        MORNING_ONE = DateUtil.formatDate(new Date(), "yyyyMMdd");
        MORNING_ONE = MORNING_ONE + ONE;
    }


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<ChainDTO> allPmsChain = chainService.getAllPmsChain();
        List<Integer> chainIds = allPmsChain.stream().map(ChainDTO::getChainId).collect(Collectors.toList());


        List<List<Integer>> partition = Lists.partition(chainIds, 100);
        partition.stream().forEach(item -> {
            ApiPageResult<List<AppointmentCleanOrderDTO>> page = null;
            int pageNo = 0;
            int pageSize = 500;
            do {
                pageNo = ++pageNo;
                AppointmentCleanPageParam appointmentCleanPageParam = new AppointmentCleanPageParam();
                appointmentCleanPageParam.setChainIds(item.stream().collect(Collectors.toSet()));
                appointmentCleanPageParam.setStates(Sets.newHashSet(AppointmentCleanStateEnum.APPOINTMENT.code()));
                appointmentCleanPageParam.setPageNo(pageNo);
                appointmentCleanPageParam.setPageSize(pageSize);
                appointmentCleanPageParam.setBeginTime(new Date());
                appointmentCleanPageParam.setEndTime(new Date());
                page = appointmentCleanRemote.page(appointmentCleanPageParam);

                if (Objects.isNull(page)) {
                    break;
                }
                if (Objects.isNull(page.getResult())) {
                    break;
                }
                try {
                    String now = DateUtil.formatDate(new Date(), DateUtil.DATE_FORMAT);
                    Set<Long> collect = page.getResult().stream().filter(info ->
                            Objects.equals(info.getAppointmentType(), AppointmentCleanTypeEnum.CLEAN.code())
                    ).filter(result -> {
                        String cleanDate = DateUtil.formatDate(result.getCleanDate(), DateUtil.DATE_FORMAT);
                        if (cleanDate.equals(now)) {
                            int hour = DateUtil.getHour(new Date());
                            int date = DateUtil.getHour(DateUtil.parseHourMinut(result.getBeginTime()));
                            if (date - hour <= DIFF_HOURS) {
                                return true;
                            }
                        }
                        return false;


                    }).filter(info -> {
                        Integer hasSendCount = sysMessageDao.hasSendCount(info.getMemberId(), info.getChainId(), SysMessageTypeEnum.APPOINT_CLEAN.getCode(), info.getRoomNo()+"_"+info.getId());
                        if (hasSendCount > 0) {
                            return false;
                        }
                        return true;
                    }).map(AppointmentCleanOrderDTO::getId).collect(Collectors.toSet());
                    if (StringUtils.equals(MORNING_ONE, DateUtil.formatDate(new Date(), "yyyyMMddHH"))) {
                        if (!redisClient.exists(REDIS_PUSH_PREFIX, MORNING_ONE)) {
                            Set<Long> noClean = page.getResult().stream().filter(info ->
                                    Objects.equals(info.getAppointmentType(), AppointmentCleanTypeEnum.NOTHING.code())
                            ).map(AppointmentCleanOrderDTO::getId).collect(Collectors.toSet());
                            collect.addAll(noClean);
                            redisClient.setString(REDIS_PUSH_PREFIX, MORNING_ONE, MORNING_ONE, expireTime);
                        }
                    }
                    cleanFactory.getInstance(AppointmentCleanEnums.APPOINTMENT).execute(collect);

                } catch (Exception e) {
                  log.error("PushAppointmentCleanJob{}错误信息",e);
                }

            } while (page.getPage().getPageSize() < 500);


        });


        return ReturnT.SUCCESS;
    }


}
