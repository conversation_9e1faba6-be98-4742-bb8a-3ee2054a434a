package com.atour.hotel.module.qualitycheck.app.enums;

import com.atour.hotel.dto.qualitycheck.DictVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检查任务配置枚举类型   1：检查设备枚举 2：不合规维度枚举
 */
@Getter
public enum CheckTaskConfigDictTypeEnum {


    EQUIPMENT(1, "检查设备枚举"),
    NON_COMPLIANT(2, "不合规维度枚举");
    private Integer value;
    private String desc;

    CheckTaskConfigDictTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value 值
     * @return
     */
    public static CheckTaskConfigDictTypeEnum getInstance(Integer value) {
        for (CheckTaskConfigDictTypeEnum obj : CheckTaskConfigDictTypeEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }
    public static List<DictVO> getDictVO(){
        return Arrays.asList(CheckTaskConfigDictTypeEnum.values()).stream().map(v -> {
            return DictVO.builder().dictKey(v.getValue()).dictValue(v.getDesc()).build();
        }).collect(Collectors.toList());
    }
}
