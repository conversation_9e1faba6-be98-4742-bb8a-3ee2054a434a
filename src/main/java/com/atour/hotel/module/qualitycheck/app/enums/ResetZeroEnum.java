package com.atour.hotel.module.qualitycheck.app.enums;

import com.atour.hotel.dto.qualitycheck.DictVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 是否清零 0：是 1：否
 */
@Getter
public enum ResetZeroEnum {


    RESET_ZERO(0, "是"),
    NO_RESET_ZERO(1, "否");
    private Integer value;
    private String desc;

    ResetZeroEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取值对应的枚举对象
     *
     * @param value 值
     * @return
     */
    public static ResetZeroEnum getInstance(Integer value) {
        for (ResetZeroEnum obj : ResetZeroEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }

    public static List<DictVO> getDictVO(){
       return Arrays.asList(ResetZeroEnum.values()).stream().map(v -> {
            return DictVO.builder().dictKey(v.getValue()).dictValue(v.getDesc()).build();
        }).collect(Collectors.toList());
    }
}
