package com.atour.hotel.module.qualitycheck.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.atour.hotel.common.util.CommonUtils;
import com.atour.hotel.dto.qualitycheck.*;
import com.atour.hotel.framework.exception.BusinessException;
import com.atour.hotel.module.common.dto.SimpleChainDTO;
import com.atour.hotel.module.common.service.AtourFlowService;
import com.atour.hotel.module.common.service.CommonChainService;
import com.atour.hotel.module.common.service.CommonOssService;
import com.atour.hotel.module.common.service.ParamService;
import com.atour.hotel.module.energy.common.BizPreconditions;
import com.atour.hotel.module.hotel.config.BigDecimalRoundingModeConfig;
import com.atour.hotel.module.internalAssistant.service.CommonPushService;
import com.atour.hotel.module.qualitycheck.app.enums.*;
import com.atour.hotel.module.qualitycheck.app.request.PushDZJUnrectifiedParam;
import com.atour.hotel.module.qualitycheck.app.response.DZJAfterSaleExtjson;
import com.atour.hotel.module.qualitycheck.app.response.NonCompliantJSON;
import com.atour.hotel.module.qualitycheck.app.service.QualityCheckAfterSaleService;
import com.atour.hotel.module.qualitycheck.app.service.QualityCheckDetailService;
import com.atour.hotel.module.qualitycheck.app.service.QualityCheckTaskWebService;
import com.atour.hotel.module.qualitycheck.condition.QualityCheckTaskListPageCondition;
import com.atour.hotel.module.qualitycheck.wrapper.QualityCheckWrapper;
import com.atour.hotel.module.ue.manage.comment.helper.CommentHelper;
import com.atour.hotel.param.qualitycheck.*;
import com.atour.hotel.persistent.franchise.dao.*;
import com.atour.hotel.persistent.franchise.entity.*;
import com.atour.mq.bean.TopicConstants;
import com.atour.rbac.api.response.HotelManagerDTO;
import com.atour.rbac.api.response.UserChainDTO;
import com.atour.utils.DateUtil;
import com.atour.utils.ObjectUtil;
import com.atour.utils.Safes;
import com.google.common.collect.Lists;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import com.yaduo.infras.core.base.exception.AtourBizValidException;
import com.yaduo.resource.service.api.oss.dto.response.OssPreviewListDTO;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */
@Slf4j
@Service
public class QualityCheckTaskWebServiceImpl implements QualityCheckTaskWebService {

    @Resource
    private DzjQualityCheckMapper dzjQualityCheckMapper;

    @Resource
    private QualityCheckDetailService qualityCheckDetailService;

    @Resource
    private DzjQualityCheckAfterSaleMapper dzjQualityCheckAfterSaleMapper;

    @Resource
    private DzjQualityCheckTaskConfigMapper dzjQualityCheckTaskConfigMapper;

    @Resource
    private DzjQualityCheckTaskConfigDictMapper dzjQualityCheckTaskConfigDictMapper;

    @Resource
    private DzjQualityCheckDetailMapper dzjQualityCheckDetailMapper;

    @Resource
    private QualityCheckAfterSaleService qualityCheckAfterSaleService;

    @Resource
    private CommonOssService commonOssService;

    @Resource
    private QualityMessCenterImpl qualityMessCenter;

    @Resource
    private ProducerBean producerBean;

    @Resource
    private CommonChainService commonChainService;

    @Resource
    private AtourFlowService atourFlowService;


    @Resource
    private CommonPushService commonPushService;

    @Resource
    private CommentHelper commentHelper;

    @Resource
    private ParamService paramService;

    @Value("${dzj.mess.start.delivertime}")
    private Integer dzjMessStartDelivertime;

    @Override
    public ResponseList<QualityCheckTaskWebQueryPageVO> webCheckTaskQueryPage(AtourRequest<RequestList<QualityCheckTaskWebQueryPageParam>> request) {
        AtourRequest.Header header = request.getHeader();
        if(Objects.isNull(header) || Objects.isNull(header.getUk())){
            throw new AtourBizValidException("不符合契约");
        }

        RequestList<QualityCheckTaskWebQueryPageParam> model = request.getModel();
        QualityCheckTaskWebQueryPageParam param = model.getContent();
        Integer pageNo = model.getPage();
        Integer pageSize = model.getSize();
        String uk = header.getUk();
        QualityCheckTaskListPageCondition condition = QualityCheckWrapper.from(param);
        if (Objects.isNull(condition)) {
            throw new AtourBizValidException("参数不能为空");
        }
        Set<Integer> relationChainIdList = queryLoginUserRelationChainId(uk);
        if(CollectionUtils.isEmpty(relationChainIdList)){
            return new ResponseList<>(Collections.emptyList(), pageNo, pageSize, 0);
        }

        condition.setChainIdList(param.getChainIds());
        if(CollectionUtils.isEmpty(param.getChainIds())){
            condition.setChainIdList(Lists.newArrayList(relationChainIdList));
        }
        int totalCount = dzjQualityCheckMapper.countWebPagePage(condition);
        if (totalCount == 0) {
            return new ResponseList<>(Collections.emptyList(), pageNo, pageSize, 0);
        }

        Pair<Integer, Integer> pageParam = CommonUtils.getPageBetween(pageNo, pageSize);
        condition.setPageStart(pageParam.getLeft());
        condition.setPageEnd(pageParam.getRight());
        List<DzjQualityCheckEntity> dzjQualityCheckEntities = dzjQualityCheckMapper.queryWebPageList(condition);
        List<QualityCheckTaskWebQueryPageVO> qualityCheckTaskWebQueryPageVOS
                = QualityCheckWrapper.from(dzjQualityCheckEntities);
        // 酒店名称
        setChainName(qualityCheckTaskWebQueryPageVOS);

        ResponseList<QualityCheckTaskWebQueryPageVO> responseResponseList
                = new ResponseList<>();
        responseResponseList.setContent(qualityCheckTaskWebQueryPageVOS);
        responseResponseList.setPage(pageNo);
        responseResponseList.setSize(pageSize);
        responseResponseList.setTotal(totalCount);

        return responseResponseList;
    }

    private void setChainName(List<QualityCheckTaskWebQueryPageVO> qualityCheckTaskWebQueryPageVOS) {
        if (CollectionUtils.isEmpty(qualityCheckTaskWebQueryPageVOS)) {
            return;
        }

        Set<Integer> chainIds = qualityCheckTaskWebQueryPageVOS
                .stream()
                .map(QualityCheckTaskWebQueryPageVO::getChainId)
                .collect(Collectors.toSet());
        List<SimpleChainDTO> simpleChainListByChainIds = commonChainService.getSimpleChainListByChainIds(chainIds);
        Map<Integer, String> chainNameMap = Safes.of(simpleChainListByChainIds)
                .stream()
                .collect(Collectors.toMap(SimpleChainDTO::getChainId, SimpleChainDTO::getChainName, (a, b) -> a));
        qualityCheckTaskWebQueryPageVOS.forEach(q -> {
            String chainName = chainNameMap.get(q.getChainId());
            q.setChainName(chainName);
        });
    }

    @Override
    public QualityCheckTaskWebInfoVO webCheckTaskDetailInfo(AtourRequest<DZJQualityCheckTaskInfoParam> request) {

        DZJQualityCheckTaskInfoParam model = request.getModel();
        Long qualityCheckTaskId = model.getQualityCheckTaskId();
        if (Objects.isNull(qualityCheckTaskId)) {
            throw new AtourBizValidException("参数不能为空");
        }

        DzjQualityCheckEntity dzjQualityCheckEntity = dzjQualityCheckMapper.selectByPrimaryKey(qualityCheckTaskId);

        QualityCheckTaskWebInfoVO qualityCheckTaskWebInfoVO = QualityCheckWrapper.buildInfoVOFrom(dzjQualityCheckEntity);


        if (Objects.isNull(qualityCheckTaskWebInfoVO)) {
            throw new AtourBizValidException("日常检查不存在");
        }
        qualityCheckTaskWebInfoVO.setHotelName(paramService.getChainNameById(dzjQualityCheckEntity.getHotelId()));
        qualityCheckTaskWebInfoVO.setHotelId(dzjQualityCheckEntity.getHotelId());
        qualityCheckTaskWebInfoVO.setCheckDate(Objects.nonNull(dzjQualityCheckEntity.getCreateCheckTaskTime()) ? DateUtil.printDate(dzjQualityCheckEntity.getCreateCheckTaskTime()) : DateUtil.printDate(new Date()));
        qualityCheckTaskWebInfoVO.setTaskName(dzjQualityCheckEntity.getQualityCheckTaskConfigName());
        // 整改信息
        QualityCheckAppealInfoVO rectificationInfo = buildQualityCheckAppealInfo(qualityCheckTaskId, AfterSaleTypeEnum.RECTIFY);
        qualityCheckTaskWebInfoVO.setRectificationInfo((Objects.nonNull(rectificationInfo) && StringUtils.isNotEmpty(rectificationInfo.getContent())) ? rectificationInfo.getContent() : "");
        // 申诉信息
        QualityCheckAppealInfoVO qualityCheckAppealInfoVO = buildQualityCheckAppealInfo(qualityCheckTaskId, AfterSaleTypeEnum.APPEAL);
        qualityCheckTaskWebInfoVO.setQualityCheckAppealInfo(qualityCheckAppealInfoVO);
        if (Objects.nonNull(qualityCheckAppealInfoVO) && StringUtils.isNotEmpty(qualityCheckAppealInfoVO.getAfterSaleExtjson())) {
            DZJAfterSaleExtjson dzjAfterSaleExtjson = JSON.parseObject(qualityCheckAppealInfoVO.getAfterSaleExtjson(), DZJAfterSaleExtjson.class);
            qualityCheckTaskWebInfoVO.setOrgCheckNum(Objects.nonNull(dzjAfterSaleExtjson.getOrgCheckNum()) ? dzjAfterSaleExtjson.getOrgCheckNum() : 0);
            qualityCheckTaskWebInfoVO.setOrgPassNum(Objects.nonNull(dzjAfterSaleExtjson.getOrgPassNum()) ? dzjAfterSaleExtjson.getOrgPassNum() : 0);
            qualityCheckTaskWebInfoVO.setRejectReason(StringUtils.isNotEmpty(dzjQualityCheckEntity.getRemark()) ? dzjQualityCheckEntity.getRemark() : "");
        }

        // 检查时间信息
        List<QualityCheckTaskDetailVO> qualityCheckTaskDetailList
                = buildCheckTaskDetail(qualityCheckTaskId);
        qualityCheckTaskWebInfoVO.setCheckTaskDetail(qualityCheckTaskDetailList);


        return qualityCheckTaskWebInfoVO;
    }

    @Transactional(transactionManager = "omsTransactionManager", rollbackFor = Exception.class)
    @Override
    public void webCheckTaskCreate(AtourRequest<QualityCheckTaskCreateParam> request) {
        QualityCheckTaskCreateParam model = request.getModel();
        AtourRequest.Header header = request.getHeader();
        String employeeId = header.getUk();
        String userName = header.getUser_name();

        Long qualityCheckTaskId = model.getQualityCheckTaskId();
        BizPreconditions.checkArgument(Objects.nonNull(qualityCheckTaskId), "检查任务ID不能为空");
        BizPreconditions.checkArgument(Objects.nonNull(model.getCheckDeviceId()) && model.getCheckDeviceId() > 0, "设备类型选择错误");

        DzjQualityCheckEntity dzjQualityCheckEntity = dzjQualityCheckMapper.selectByPrimaryKey(qualityCheckTaskId);
        BizPreconditions.checkArgument(Objects.nonNull(dzjQualityCheckEntity), "检查任务不存在");


        DzjQualityCheckTaskConfigEntity configEntity = dzjQualityCheckTaskConfigMapper.selectByPrimaryKey(dzjQualityCheckEntity.getQualityCheckTaskConfigId());
        BizPreconditions.checkArgument(Objects.nonNull(configEntity), "检查配置不存在");


        DzjQualityCheckTaskConfigDictEntity dictEntity = dzjQualityCheckTaskConfigDictMapper.selectByPrimaryKey(model.getCheckDeviceId());
        BizPreconditions.checkArgument(Objects.nonNull(dictEntity), "选择的设备已失效，请重新选择");
        BizPreconditions.checkArgument(dictEntity.getQualityCheckTaskConfigId().equals(dzjQualityCheckEntity.getQualityCheckTaskConfigId()), "设备不存在");
        if(CollectionUtils.isNotEmpty(model.getQualityCheckTaskDetail())){
            for (QualityCheckTaskDetailVO detailVO : model.getQualityCheckTaskDetail()) {
                BizPreconditions.checkArgument(Objects.nonNull(detailVO.getDate()), "核查时间内容请填写完整");
                BizPreconditions.checkArgument(Objects.nonNull(detailVO.getTime()), "核查时间内容请填写完整");
                BizPreconditions.checkArgument(CollectionUtils.isNotEmpty(detailVO.getNonCompliant()), "请选择不合规枚举");
                for (Long aLong : detailVO.getNonCompliant()) {
                    DzjQualityCheckTaskConfigDictEntity checkTaskConfigDict = dzjQualityCheckTaskConfigDictMapper.selectByPrimaryKey(aLong);
                    BizPreconditions.checkArgument(Objects.nonNull(dictEntity), "存在已失效的不合规维度，请重新选择");
                }
            }
        }

        QualityCheckWrapper.buildDzjQualityCheckEntity(model, dzjQualityCheckEntity, dictEntity, configEntity);
        dzjQualityCheckEntity.setCreateUserId(employeeId);
        dzjQualityCheckEntity.setCreateUserName(userName);
        dzjQualityCheckEntity.setUpdateUserId(employeeId);
        dzjQualityCheckEntity.setUpdateUserName(userName);
        dzjQualityCheckEntity.setVersion(dzjQualityCheckEntity.getVersion() + 1);
        dzjQualityCheckEntity.setZeroThreshold(configEntity.getZeroThreshold());
        dzjQualityCheckEntity.setRectifyThreshold(configEntity.getRectifyThreshold());
        List<QualityCheckTaskDetailVO> qualityCheckTaskDetail = model.getQualityCheckTaskDetail();
        //首先清空查房明细
        qualityCheckDetailService.updatelByQualityCheckId(qualityCheckTaskId);
        // 保存核查时间
        saveQualityCheckTaskDetail(qualityCheckTaskDetail, employeeId, userName, qualityCheckTaskId);
        //发送消息
        qualityMessCenter.checkCompleted(QualityCheckWrapper.buildMessCenterDTO(dzjQualityCheckEntity));
        // 调用流程
        if (Objects.equals(dzjQualityCheckEntity.getCheckStatus(), QualityCheckStatusEnum.UNRECTIFIED.getValue()) && StringUtils.isEmpty(dzjQualityCheckEntity.getProcessId())) {

            String processId = startProcess(employeeId, userName, dzjQualityCheckEntity.getId(),dzjQualityCheckEntity.getHotelId());
            dzjQualityCheckEntity.setProcessId(processId);
            dzjCreateCheckStatusUnrectifiedSend(dzjQualityCheckEntity);
        }
        if (Objects.equals(dzjQualityCheckEntity.getCheckStatus(), QualityCheckStatusEnum.PASSED.getValue()) && StringUtils.isNotEmpty(dzjQualityCheckEntity.getProcessId())) {
            endProcess(employeeId, userName, dzjQualityCheckEntity.getId(), "合格通过", dzjQualityCheckEntity.getProcessId());
        }

        dzjQualityCheckMapper.updateByPrimaryKeySelective(dzjQualityCheckEntity);
    }

    private String startProcess(String employeeId, String userName, Long checkTaskId,Integer chainId) {
        try {
            log.info("创建检查任务启动流程checkTaskId:{}", checkTaskId);
            return atourFlowService.startProcess(employeeId, 0L, userName,commentHelper.getXianzhangListEmployeeId(chainId));
        } catch (Exception e) {
            log.error("创建检查任务启动流程失败checkTaskId:{},mess{}", checkTaskId, e);
        }
        return Strings.EMPTY;
    }

    private void endProcess(String employeeId, String userName, Long checkTaskId, String commentMsg, String processId) {
        try {
            atourFlowService.operationEnd(employeeId, 0L, userName, commentMsg, processId);
        } catch (Exception e) {
            log.error("编辑检查任务终止流程失败checkTaskId:{}", checkTaskId, e);
        }
    }

    private void saveQualityCheckTaskDetail(List<QualityCheckTaskDetailVO> qualityCheckTaskDetailList,
                                            String userId, String userName, Long checkTaskId) {

        if (CollectionUtils.isEmpty(qualityCheckTaskDetailList)) {
            return;
        }

        List<DzjQualityCheckDetailEntity> dzjQualityCheckDetailEntities = qualityCheckTaskDetailList
                .stream()
                .map(vo -> {
                    DzjQualityCheckDetailEntity dzjQualityCheckDetailEntity = new DzjQualityCheckDetailEntity();
                    String date = vo.getDate();
                    String time = vo.getTime();
                    Date checkTime = DateUtil.parseDatetime(date + " " + time,
                            "yyyy-MM-dd HH:mm:ss");

                    dzjQualityCheckDetailEntity.setQualityCheckId(checkTaskId);
                    List<DzjQualityCheckTaskConfigDictEntity> dictEntityList = dzjQualityCheckTaskConfigDictMapper.queryByIdListAndType(vo.getNonCompliant(), CheckTaskConfigDictTypeEnum.NON_COMPLIANT.getValue());
                    if (CollectionUtils.isNotEmpty(dictEntityList)) {
                        List<NonCompliantJSON> collect = dictEntityList.stream().map(a -> NonCompliantJSON.builder().key(a.getId())
                                .value(a.getDictValue()).build()).collect(Collectors.toList());
                        dzjQualityCheckDetailEntity.setCheckDetailJson(JSON.toJSONString(collect));
                    }

                    dzjQualityCheckDetailEntity.setRemark(StringUtils.isEmpty(vo.getDesc()) ? "" : vo.getDesc());
                    dzjQualityCheckDetailEntity.setVersion(0);
                    dzjQualityCheckDetailEntity.setCheckStartTime(checkTime);
                    dzjQualityCheckDetailEntity.setCreateUserId(userId);
                    dzjQualityCheckDetailEntity.setUpdateUserId(userId);
                    dzjQualityCheckDetailEntity.setCreateUserName(userName);
                    dzjQualityCheckDetailEntity.setUpdateUserName(userName);
                    return dzjQualityCheckDetailEntity;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dzjQualityCheckDetailEntities)) {
            dzjQualityCheckDetailMapper.batchInsert(dzjQualityCheckDetailEntities);
        }
    }


    @Transactional(transactionManager = "omsTransactionManager", rollbackFor = Exception.class)
    @Override
    public void webCheckTaskAppealApproval(AtourRequest<QualityCheckTaskAppealApprovalParam> request) {
        BizPreconditions.checkArgument(Objects.nonNull(request.getHeader()), "锲约不完善");
        AtourRequest.Header header = request.getHeader();
        BizPreconditions.checkArgument(StringUtils.isNotEmpty(header.getUk()), "登录人信息不完善");
        BizPreconditions.checkArgument(StringUtils.isNotEmpty(header.getUser_name()), "登录人信息不完善");
        String uk = header.getUk();
        String userName = header.getUser_name();
        QualityCheckTaskAppealApprovalParam model = request.getModel();
        if (Objects.isNull(model) || Objects.isNull(model.getQualityCheckTaskId())) {
            BizPreconditions.checkArgument(Boolean.FALSE, "参数不可以为空");
        }
        DzjQualityCheckEntity dzjQualityCheckEntity = dzjQualityCheckMapper.selectByPrimaryKey(model.getQualityCheckTaskId());
        BizPreconditions.checkArgument(Objects.nonNull(dzjQualityCheckEntity), "检查任务不存在");
        if (dzjQualityCheckEntity.getCheckStatus() != QualityCheckStatusEnum.APPELLATED.getValue()) {
            BizPreconditions.checkArgument(Boolean.FALSE, "当前检查任务非申诉状态不可以执行审批");
        }
        if (dzjQualityCheckEntity.getAppealStatus() != AppealStatusEnum.APPELLATED.getValue()) {
            BizPreconditions.checkArgument(Boolean.FALSE, "当前检查任务已经审批完成不可以再次审批");
        }
        DzjQualityCheckTaskConfigEntity dzjQualityCheckTaskConfigEntity = dzjQualityCheckTaskConfigMapper.selectByPrimaryKey(dzjQualityCheckEntity.getQualityCheckTaskConfigId());
        BizPreconditions.checkArgument(Objects.nonNull(dzjQualityCheckTaskConfigEntity), "检查任务配置不存在");
        BigDecimal zeroThreshold = dzjQualityCheckTaskConfigEntity.getZeroThreshold();
        BigDecimal rectifyThreshold = dzjQualityCheckTaskConfigEntity.getRectifyThreshold();

        AppealStatusEnum appealStatusEnum = Objects.equals(model.getAppealResult(), CheckTaskAppealResultEnum.RECTIFY.getValue()) ? AppealStatusEnum.APPEAL_PASSED : AppealStatusEnum.APPEAL_REJECTION;


        StringBuffer approvalRemark = new StringBuffer();


        DZJAfterSaleExtjson dzjAfterSaleExtjson = new DZJAfterSaleExtjson();
        dzjAfterSaleExtjson.setOrgCheckNum(dzjQualityCheckEntity.getCheckNum());
        dzjAfterSaleExtjson.setOrgPassNum(dzjQualityCheckEntity.getCheckPassNum());
        BigDecimal passRate = dzjQualityCheckEntity.getPassRate();

        BigDecimal passRate_per = passRate.multiply(new BigDecimal(100));
        BigDecimal passRate_per_org = passRate.multiply(new BigDecimal(100));

        // 申诉通过
        if (Objects.equals(appealStatusEnum, AppealStatusEnum.APPEAL_PASSED)) {
            passRate = getPassRate(model.getCheckNum(), model.getPassNum());
            passRate_per = passRate.multiply(new BigDecimal(100));
            approvalRemark.append("合格/检查：").append(dzjQualityCheckEntity.getCheckPassNum()).append("/").append(dzjQualityCheckEntity.getCheckNum()).append("→")
                    .append(model.getPassNum()).append("/").append(model.getCheckNum()).append("&");
            approvalRemark.append("合格率：").append(passRate_per_org.setScale(2, BigDecimal.ROUND_HALF_UP)).append("%").append("→").append(passRate_per.setScale(2, BigDecimal.ROUND_HALF_UP)).append("%");
            dzjAfterSaleExtjson.setRemark(approvalRemark.toString());

            // 更新核查信息
            dzjQualityCheckEntity.setCheckNum(model.getCheckNum());
            dzjQualityCheckEntity.setCheckPassNum(model.getPassNum());
            // 通过清零阀值判断
            dzjQualityCheckEntity.setResetZero(passRate_per.compareTo(zeroThreshold) < 0 ? ResetZeroEnum.RESET_ZERO.getValue() : ResetZeroEnum.NO_RESET_ZERO.getValue());
            dzjQualityCheckEntity.setPassRate(passRate);
            dzjQualityCheckEntity.setUpdateUserId(uk);
            dzjQualityCheckEntity.setUpdateUserName(userName);
            dzjQualityCheckEntity.setAppealStatus(AppealStatusEnum.APPEAL_PASSED.getValue());
            dzjQualityCheckEntity.setRemark(StringUtils.isNotEmpty(model.getRejectReason()) ? model.getRejectReason() : "");
            // 更新核查时间
            List<QualityCheckTaskDetailVO> qualityCheckTaskDetail = model.getQualityCheckTaskDetail();
            updateQualityCheckTaskDetail(model.getQualityCheckTaskId(), uk, userName, qualityCheckTaskDetail);
            QualityCheckStatusEnum qualityCheckStatusEnum = passRate_per.compareTo(rectifyThreshold) < 0 ? QualityCheckStatusEnum.UNRECTIFIED : QualityCheckStatusEnum.PASSED;
            dzjQualityCheckEntity.setCheckStatus(qualityCheckStatusEnum.getValue());

        } else {
            dzjQualityCheckEntity.setAppealStatus(AppealStatusEnum.APPEAL_REJECTION.getValue());
            dzjQualityCheckEntity.setCheckStatus(QualityCheckStatusEnum.UNRECTIFIED.getValue());
            approvalRemark.append("驳回理由：").append(model.getRejectReason());
            dzjAfterSaleExtjson.setRemark(approvalRemark.toString());
            dzjQualityCheckEntity.setRemark(StringUtils.isNotEmpty(model.getRejectReason()) ? model.getRejectReason() : "");

        }
        dzjQualityCheckEntity.setUpdateTime(new Date());
        dzjQualityCheckEntity.setUpdateUserId(uk);
        dzjQualityCheckEntity.setUpdateUserName(userName);
        // 更新检查任务
        dzjQualityCheckMapper.updateByPrimaryKeySelective(dzjQualityCheckEntity);

        // 更新审批信息
        qualityCheckAfterSaleService.updateCheckAfterSale(model.getQualityCheckTaskId(), uk, userName, appealStatusEnum, dzjAfterSaleExtjson);

        // 发送企微消息
        qualityMessCenter.checkAppealsEnd(QualityCheckWrapper.buildMessCenterDTO(dzjQualityCheckEntity));
        QualityCheckStatusEnum qualityCheckStatusEnum = passRate_per.compareTo(rectifyThreshold) < 0 ? QualityCheckStatusEnum.UNRECTIFIED : QualityCheckStatusEnum.PASSED;
        // 调用工作流退回
        if (!Objects.equals(QualityCheckStatusEnum.PASSED, qualityCheckStatusEnum) && StringUtils.isNotEmpty(dzjQualityCheckEntity.getProcessId())) {
            atourFlowService.operationBack(uk, commentHelper.getUserLoginName(uk), 0L, dzjQualityCheckEntity.getProcessId(), "整改不通过");
        }
    }

    @Override
    public ResponseList<QualityCheckReportPageVO> qualityReportPage(AtourRequest<RequestList<QualityReportQueryPageParam>> request) {
        AtourRequest.Header header = request.getHeader();
        RequestList<QualityReportQueryPageParam> requestModel = request.getModel();
        String uk = header.getUk();
        if(StringUtils.isEmpty(uk)){
            throw new BusinessException("不符合契约-用户id不存在！");
        }
        Integer pageNo = requestModel.getPage();
        Integer pageSize = requestModel.getSize();
        QualityCheckTaskListPageCondition condition = QualityCheckWrapper.from(requestModel);

        Set<Integer> chainIdSet = queryLoginUserRelationChainId(uk);
        if(CollectionUtils.isEmpty(chainIdSet)){
            return new ResponseList<QualityCheckReportPageVO>(Lists.newArrayList(), requestModel.getPage(), requestModel.getSize(), 0);
        }
        condition.setChainIdList(Lists.newArrayList(chainIdSet));
        int totalCount = dzjQualityCheckMapper.countWebPagePage(condition);
        if (totalCount == 0) {
            return new ResponseList<>(Collections.emptyList(), pageNo, pageSize, 0);
        }

        Pair<Integer, Integer> pageParam = CommonUtils.getPageBetween(pageNo, pageSize);
        condition.setPageStart(pageParam.getLeft());
        condition.setPageEnd(pageParam.getRight());
        List<DzjQualityCheckEntity> dzjQualityCheckEntityList = dzjQualityCheckMapper.queryWebPageList(condition);

        List<QualityCheckReportPageVO> qualityCheckTaskWebQueryPageVOS = QualityCheckWrapper.fromQualityCheckReportPageVO(dzjQualityCheckEntityList);

        Set<Integer> chainIds = qualityCheckTaskWebQueryPageVOS.stream().map(QualityCheckReportPageVO::getChainId).collect(Collectors.toSet());
        List<SimpleChainDTO> simpleChainListByChainIds = commonChainService.getSimpleChainListByChainIds(chainIds);
        Map<Integer, String> chainNameMap = Safes.of(simpleChainListByChainIds).stream().collect(Collectors.toMap(SimpleChainDTO::getChainId, SimpleChainDTO::getChainName, (a, b) -> a));
        qualityCheckTaskWebQueryPageVOS.forEach(q -> {
            String chainName = chainNameMap.get(q.getChainId());
            q.setChainName(chainName);
        });

        ResponseList<QualityCheckReportPageVO> responseResponseList = new ResponseList<>();
        responseResponseList.setContent(qualityCheckTaskWebQueryPageVOS);
        responseResponseList.setPage(pageNo);
        responseResponseList.setSize(pageSize);
        responseResponseList.setTotal(totalCount);

        return responseResponseList;
    }

    @Override
    public ResponseList<QualityCheckReportPageVO> exportReportPage(AtourRequest<RequestList<QualityReportQueryPageParam>> request) {

        RequestList<QualityReportQueryPageParam> requestModel = request.getModel();

        QualityReportQueryPageParam model = requestModel.getContent();
        if (Objects.isNull(model) || StringUtils.isEmpty(model.getCheckDateStart()) || StringUtils.isEmpty(model.getCheckDateEnd())) {
            throw new AtourBizValidException("请选择时间");
        }
        return qualityReportPage(request);

    }

    private List<QualityCheckTaskDetailVO> buildCheckTaskDetail(Long checkTaskId) {
        if (Objects.isNull(checkTaskId)) {
            return Collections.emptyList();
        }

        return qualityCheckDetailService.queryQualityCheckDetailByQualityCheckTaskId(checkTaskId);
    }

    private QualityCheckAppealInfoVO buildQualityCheckAppealInfo(Long checkTaskId, AfterSaleTypeEnum afterSaleTypeEnum) {

        List<DzjQualityCheckAfterSaleEntity> saleEntityList = dzjQualityCheckAfterSaleMapper.queryAfterSaleByQualityCheckId(checkTaskId, afterSaleTypeEnum.getValue());

        if (CollectionUtils.isNotEmpty(saleEntityList)) {
            DzjQualityCheckAfterSaleEntity afterSaleEntity = saleEntityList.get(0);
            List<String> osskeyList = Arrays.asList(afterSaleEntity.getAfterSaleUrl().split(","));
            List<OssPreviewListDTO> ossPreviewListDTOS = commonOssService.previewByOssKeyList(osskeyList, TimeUnit.DAYS.toSeconds(1));
            List<String> urlList = Safes.of(ossPreviewListDTOS)
                    .stream()
                    .map(OssPreviewListDTO::getShowUrl)
                    .collect(Collectors.toList());

            QualityCheckAppealInfoVO qualityCheckAppealInfoVO = new QualityCheckAppealInfoVO();
            qualityCheckAppealInfoVO.setContent(afterSaleEntity.getAfterSaleContent());
            qualityCheckAppealInfoVO.setAttachments(urlList);
            qualityCheckAppealInfoVO.setAfterSaleExtjson(StringUtils.isNotEmpty(afterSaleEntity.getAfterSaleExtjson()) ? afterSaleEntity.getAfterSaleExtjson() : "");
            return qualityCheckAppealInfoVO;
        }

        return null;
    }

    private BigDecimal getPassRate(Integer checkNum, Integer passNum) {
        if (Objects.isNull(checkNum) || Objects.isNull(passNum)) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(passNum.toString()).divide(new BigDecimal(checkNum.toString()), BigDecimalRoundingModeConfig.SCALE_4, BigDecimalRoundingModeConfig.ROUNDING_MODE);
    }

    private void updateQualityCheckTaskDetail(Long qualityCheckTaskId, String userId, String userName,
                                              List<QualityCheckTaskDetailVO> qualityCheckTaskDetailList) {
        if (CollectionUtils.isEmpty(qualityCheckTaskDetailList)) {
            return;
        }

        dzjQualityCheckDetailMapper.deleteByQualityCheckTaskId(qualityCheckTaskId);
        saveQualityCheckTaskDetail(qualityCheckTaskDetailList, userId, userName, qualityCheckTaskId);
    }

    public void dzjCreateCheckStatusUnrectifiedSend(DzjQualityCheckEntity dzjQualityCheckEntity) {
        try {
            log.info("发送朵质检整改延迟消息 chainId={},id={}", dzjQualityCheckEntity.getHotelId(), dzjQualityCheckEntity.getId());
            PushDZJUnrectifiedParam pushDZJUnrectifiedParam = new PushDZJUnrectifiedParam();
            pushDZJUnrectifiedParam.setCheckTaskId(dzjQualityCheckEntity.getId());
            pushDZJUnrectifiedParam.setHotelId(dzjQualityCheckEntity.getHotelId());
            Message message = new Message(TopicConstants.TOPIC_CHAIN, "TAG_PUSH_DZJ_CHECK_STATUS_UNRECTIFIED", ObjectUtil.toJsonQuietly(pushDZJUnrectifiedParam).getBytes());
            if (dzjMessStartDelivertime > 0) {
                message.setStartDeliverTime(LocalDateTime.now().plusHours(dzjMessStartDelivertime).toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
            }
            producerBean.send(message);
        } catch (Exception e) {
            log.warn("send_dzjCreateCheckStatusUnrectifiedSend_error mes:{} 信息相关操作mq消息 出现异常：{}", JSON.toJSONString(dzjQualityCheckEntity), e);
        }
    }



    public Set<Integer>  queryLoginUserRelationChainId(String userId){
        //获取当前登录人关联得酒店信息
        Map<String, Set<Integer>> employeeIdChainIdsMap = Safes.of(commonPushService.getUserChainIds(com.google.common.collect.Lists.newArrayList(String.valueOf(userId)))).stream().collect(Collectors.toMap(UserChainDTO::getUserId, UserChainDTO::getChainId));
        if(employeeIdChainIdsMap==null || employeeIdChainIdsMap.size()==0){
            return new HashSet<>();
        }
        return employeeIdChainIdsMap.get(userId);

    }


}
