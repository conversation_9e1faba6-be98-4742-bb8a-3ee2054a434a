package com.atour.hotel.module.qualitycheck.app.service.impl;

import com.atour.hotel.dto.qualitycheck.QualityCheckTaskDetailVO;
import com.atour.hotel.module.qualitycheck.app.service.QualityCheckDetailService;
import com.atour.hotel.module.qualitycheck.wrapper.QualityCheckDetailWrapper;
import com.atour.hotel.persistent.franchise.dao.DzjQualityCheckDetailMapper;
import com.atour.hotel.persistent.franchise.entity.DzjQualityCheckDetailEntity;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class QualityCheckDetailServiceImpl implements QualityCheckDetailService {

    @Resource
    private DzjQualityCheckDetailMapper dzjQualityCheckDetailMapper;

    @Override
    public List<QualityCheckTaskDetailVO> queryQualityCheckDetailByQualityCheckTaskId(Long qualityCheckTaskId) {

        if (Objects.isNull(qualityCheckTaskId)) {
            return Lists.newArrayList();
        }
        List<DzjQualityCheckDetailEntity> checkDetailEntityList = dzjQualityCheckDetailMapper.queryCheckDetailEntityByqualityCheckId(qualityCheckTaskId);


        return checkDetailEntityList
                .stream()
                .map(QualityCheckDetailWrapper::from)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void    updatelByQualityCheckId(Long qualityCheckId){
        if(Objects.isNull(qualityCheckId) ||  qualityCheckId==0L){
            return;
        }
        dzjQualityCheckDetailMapper.updatelByQualityCheckId(qualityCheckId);
    }



}
