package com.atour.hotel.module.qualitycheck.app.service;

import com.atour.hotel.dto.qualitycheck.QualityCheckReportPageVO;
import com.atour.hotel.dto.qualitycheck.QualityCheckTaskConfigVO;
import com.atour.hotel.dto.qualitycheck.QualityCheckTaskWebInfoVO;
import com.atour.hotel.dto.qualitycheck.QualityCheckTaskWebQueryPageVO;
import com.atour.hotel.param.qualitycheck.*;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */
public interface QualityCheckTaskWebService {

    /**
     * 朵质检-日常检查WEB端-列表
     */
    ResponseList<QualityCheckTaskWebQueryPageVO>
    webCheckTaskQueryPage(AtourRequest<RequestList<QualityCheckTaskWebQueryPageParam>> request);

    /**
     * 朵质检-日常检查WEB端-详情
     */
    QualityCheckTaskWebInfoVO webCheckTaskDetailInfo(AtourRequest<DZJQualityCheckTaskInfoParam> request);

    /**
     * 朵质检-日常检查WEB端-创建
     */
    void webCheckTaskCreate(AtourRequest<QualityCheckTaskCreateParam> request);

    /**
     * 朵质检-日常检查WEB端-申诉审批
     */
    void webCheckTaskAppealApproval(AtourRequest<QualityCheckTaskAppealApprovalParam> request);

    /**
     * 朵质检-报表
     *
     * @param request
     * @return
     */
    ResponseList<QualityCheckReportPageVO> qualityReportPage(AtourRequest<RequestList<QualityReportQueryPageParam>> request);


    /**
     * 朵质检导出
     *
     * @param request
     * @return
     */
    ResponseList<QualityCheckReportPageVO> exportReportPage(AtourRequest<RequestList<QualityReportQueryPageParam>> request);


}
