package com.atour.hotel.common.util;

import com.alibaba.fastjson.JSON;
import com.atour.api.bean.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {

    private static final String SUS_CODE = "0";

    /**
     * 通过GET方式发起http请求
     */
    public static String requestByGetMethod(String url) {
        //创建默认的httpClient实例
        CloseableHttpClient httpClient = getHttpClient();
        try {
            //用get方法发送http请求
            HttpGet get = new HttpGet(url);
            CloseableHttpResponse httpResponse = null;
            //发送get请求
            httpResponse = httpClient.execute(get);
            try {
                //response实体
                HttpEntity entity = httpResponse.getEntity();
                if (null != entity) {
                    String responseBody = EntityUtils.toString(entity);
                    ApiResult httpResponse1=JSON.parseObject(responseBody, ApiResult.class);
                    if (SUS_CODE.equals(httpResponse1.getCode()+"")){
                        return JSON.toJSONString(httpResponse1.getResult());
                    }else {
                        log.warn("requestByGetMethod error url:{} get response:{}", url, responseBody);
                    }

                }
            } finally {
                httpResponse.close();
            }
        } catch (Exception e) {
            log.error("call {} get error", url, e);
        } finally {
            try {
                closeHttpClient(httpClient);
            } catch (IOException e) {
                log.error("closeHttpClient get error", e);
            }
        }
        return null;
    }

    /**
     * 通过get请求下载文件获取文件字节数组
     * */
    public static byte[] requestFileByGetMethod(String url) {
        //创建默认的httpClient实例
        CloseableHttpClient httpClient = getHttpClient();
        try {
            //用get方法发送http请求
            HttpGet get = new HttpGet(url);
            CloseableHttpResponse httpResponse = null;
            //发送get请求
            httpResponse = httpClient.execute(get);
            try {
                //response实体
                HttpEntity entity = httpResponse.getEntity();
                if (null != entity) {
                    byte[] byteArray = EntityUtils.toByteArray(entity);
                    return byteArray;
                }
            } finally {
                httpResponse.close();
            }
        } catch (Exception e) {
            log.error("call {} get error", url, e);
        } finally {
            try {
                closeHttpClient(httpClient);
            } catch (IOException e) {
                log.error("closeHttpClient get error", e);
            }
        }
        return null;
    }
    private static CloseableHttpClient getHttpClient() {
        return HttpClients.createDefault();
    }

    private static void closeHttpClient(CloseableHttpClient client) throws IOException {
        if (client != null) {
            client.close();
        }
    }

}
