package com.atour.hotel.common.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.atour.hotel.common.enums.ExportReportEnum;
import com.atour.hotel.framework.annotation.ExcelAnnotation;
import com.atour.hotel.framework.annotation.ExcelHeadValues;
import com.atour.hotel.framework.annotation.ExcelHeads;
import com.atour.hotel.framework.annotation.ExcelMapValues;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * ExcelUtils
 *
 * <AUTHOR>
 * @date 2019-09-26
 */
@Slf4j
public class ExcelUtils<T> {

    /**
     * CSV文件列分隔符
     */
    private static final String CSV_COLUMN_SEPARATOR = ",";

    /**
     * CSV文件列分隔符
     */
    private static final String CSV_RN = "\r\n";

    /**
     * 导出CSV
     * <p>
     * 第一列: 姓名  年龄
     * 张三  李四
     * 第二列: 大老婆 二老婆 三老婆
     * a     b       c
     *
     * @param fileName 文件名
     * @param dataset  导出第一列数据
     * @param response 响应结果
     */
    public void exportCSV(String fileName, List<T> dataset, HttpServletResponse response) {
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            responseSetProperties(fileName, response);
            StringBuilder buf = new StringBuilder();
            if (dataset == null || dataset.isEmpty()) {
                os.write(buf.toString()
                        .getBytes("GB18030"));
                os.flush();
                return;
            }
            Iterator<T> iterator = dataset.iterator();
            T tObject = iterator.next();
            List<Field> fields = new ArrayList<>();
            Class<?> clazz = tObject.getClass();
            for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
                fields.addAll(0, Arrays.asList(clazz.getDeclaredFields()));
            }
            // 先对有注解的单个字段进行处理
            List<Field> hasAnnotationFields = fields.stream()
                    .filter(field -> Objects.nonNull(field.getAnnotation(ExcelAnnotation.class)))
                    .collect(Collectors.toList());
            for (Field field : hasAnnotationFields) {
                if (field.getAnnotations().length == 0) {
                    continue;
                }
                ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
                String fieldTitle = excelAnnotation.name();
                buf.append(fieldTitle)
                        .append(CSV_COLUMN_SEPARATOR);
            }
            buf.append(CSV_RN);

            for (T t : dataset) {

                for (Field field : hasAnnotationFields) {
                    ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);

                    field.setAccessible(true);
                    Object o = field.get(t);
                    String value = StringUtils.EMPTY;
                    if (o != null) {
                        if (o instanceof String) {
                            value = (String) field.get(t);
                        } else if (o instanceof Date) {
                            String dateFormat = excelAnnotation.dateFormat();
                            Date date = (Date) field.get(t);
                            value = DateFormatUtils.format(date, dateFormat);
                        } else {
                            value = String.valueOf(o);
                        }
                        buf.append(StringEscapeUtils.escapeCsv(value))
                                .append(CSV_COLUMN_SEPARATOR);
                    } else {
                        buf.append(value)
                                .append(CSV_COLUMN_SEPARATOR);
                    }
                }
                buf.append(CSV_RN);

                // 第二列表头
                Field hasNextField = fields.stream()
                        .filter(field -> Objects.nonNull(field.getAnnotation(ExcelHeads.class)))
                        .findFirst()
                        .orElse(null);
                List o2 = null;
                if (Objects.nonNull(hasNextField)) {
                    hasNextField.setAccessible(true);
                    Object o = hasNextField.get(t);
                    if (o instanceof ArrayList) {
                        o2 = (List) o;
                        for (Object o1 : o2) {
                            if (o1 instanceof String) {
                                buf.append(o1)
                                        .append(CSV_COLUMN_SEPARATOR);
                            }
                        }
                        buf.append(CSV_RN);
                    }
                }

                Field hasNextFieldValues = fields.stream()
                        .filter(field -> Objects.nonNull(field.getAnnotation(ExcelHeadValues.class)))
                        .findFirst()
                        .orElse(null);

                if (Objects.nonNull(hasNextFieldValues)) {
                    hasNextFieldValues.setAccessible(true);
                    Object o = hasNextFieldValues.get(t);
                    if (o instanceof ArrayList) {

                        Iterator iterator1 = ((ArrayList) o).iterator();
                        Object next = iterator1.next();
                        List<Field> fieldValues = new ArrayList<>();
                        Class<?> clazzValues = next.getClass();
                        for (; clazzValues != Object.class; clazzValues = clazzValues.getSuperclass()) {
                            fieldValues.addAll(0, Arrays.asList(clazzValues.getDeclaredFields()));
                        }

                        for (Object o1 : (List) o) {
                            for (Field fieldValue : fieldValues) {

                                ExcelAnnotation excelAnnotation = fieldValue.getAnnotation(ExcelAnnotation.class);
                                ExcelMapValues excelMapValues = fieldValue.getAnnotation(ExcelMapValues.class);

                                if (Objects.nonNull(excelAnnotation)) {
                                    fieldValue.setAccessible(true);
                                    Object obj = fieldValue.get(o1);
                                    String value = StringUtils.EMPTY;
                                    if (obj != null) {
                                        if (obj instanceof String) {
                                            value = (String) fieldValue.get(o1);
                                        } else if (obj instanceof Date) {
                                            String dateFormat = excelAnnotation.dateFormat();
                                            Date date = (Date) fieldValue.get(o1);
                                            value = DateFormatUtils.format(date, dateFormat);
                                        } else {
                                            value = String.valueOf(obj);
                                        }
                                        buf.append(StringEscapeUtils.escapeCsv(value))
                                                .append(CSV_COLUMN_SEPARATOR);
                                    } else {
                                        buf.append(value)
                                                .append(CSV_COLUMN_SEPARATOR);
                                    }
                                }
                                if (Objects.nonNull(excelMapValues)) {
                                    fieldValue.setAccessible(true);
                                    Object obj = fieldValue.get(o1);
                                    if (obj instanceof HashMap) {
                                        if (Objects.nonNull(o2)) {
                                            for (Object o3 : o2) {
                                                if (o3 instanceof String) {
                                                    Object o4 = ((HashMap) obj).get(o3);
                                                    if (Objects.nonNull(o4) && o4 instanceof String) {
                                                        buf.append(StringEscapeUtils.escapeCsv(o4.toString()))
                                                                .append(CSV_COLUMN_SEPARATOR);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                            }
                            buf.append(CSV_RN);
                        }
                    }
                }

            }

            os.write(buf.toString()
                    .getBytes("GB18030"));
            os.flush();
        } catch (Exception e) {
            log.error("doCsvExport错误,", e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("doCsvExport错误...", e);
                }
            }
        }
    }

    /**
     * 设置response属性
     *
     * @param fileName 文件名
     * @param response 响应结果
     * @throws UnsupportedEncodingException 异常
     */
    private static void responseSetProperties(String fileName, HttpServletResponse response)
            throws UnsupportedEncodingException {
        // 设置文件后缀
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fn = fileName + sdf.format(new Date()) + ".csv";
        // 读取字符编码
        String utf = "UTF-8";
        // 设置响应
        response.setContentType("application/ms-txt.numberformat");
        response.setCharacterEncoding(utf);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "max-age=30");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fn, utf));
    }

    /**
     * 获取表头风格
     */
    public static CellStyle getHeaderStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.TAN.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 获取表数据体风格
     *
     * @param wb
     * @return
     */
    public static CellStyle getBodyStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontName("宋体");
        style.setFont(font);
        style.setWrapText(true);
        return style;
    }

    /**
     * 自动列宽导出 Excel
     *
     * @param data      数据 data，每个元素为一个 BaseRowModel
     * @param model     映射实体类，Excel 模型
     * @param fileName  导出的文件名
     * @param sheetName 导入文件的 sheet 名
     * @param response  HttpServletResponse
     * @throws Exception 异常
     */
    public static void writeAutoCellWidthExcel(List<? extends Object> data, Class model, String fileName,
                                               String sheetName, HttpServletResponse response) throws Exception {

        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setLocked(true);
        // 颜色
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setFontHeightInPoints((short) 11);
        // 字体
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setWrapped(false);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setLocked(false);
        //设置内容靠中对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        EasyExcel.write(getOutputStream(fileName, response), model)
                .needHead(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new CustomCellWriteHandler())
                .sheet(sheetName)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .doWrite(data);
    }

    /**
     * 导出文件时为Writer生成OutputStream.
     *
     * @param fileName 文件名
     * @param response response
     * @return ""
     */
    private static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
            return response.getOutputStream();
        } catch (IOException e) {
            throw new Exception("导出excel表格失败!", e);
        }
    }

    /**
     * easy poi 方式导出
     *
     * @param list
     * @param pojoClass
     * @param fileName
     * @param exportParams
     * @param response
     * @param maxColumnNum 最大列数量
     * @throws IOException
     */
    public static void easyPoiExport(List<?> list, Class<?> pojoClass, String fileName, ExportParams exportParams,
                                     HttpServletResponse response, int maxColumnNum) throws IOException {
        //自定义样式
        exportParams.setStyle(ExcelExportStylerImpl.class);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setColor(Font.COLOR_NORMAL);
        style.setFont(font);

        CellStyle style1 = workbook.createCellStyle();
        style1.setAlignment(HorizontalAlignment.CENTER);
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font1 = workbook.createFont();
        font1.setColor(Font.COLOR_NORMAL);
        font1.setBold(true);
        style1.setFont(font1);

        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(0);
        for (int i = 0; i < maxColumnNum; i++) {
            row.getCell(i).setCellStyle(style1);
        }
        downLoadExcel(fileName, response, workbook);
    }

    /**
     * 下载
     *
     * @param fileName 文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook)
            throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName + "." + "xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * 下载
     *
     * @param fileName 文件名称
     * @param response
     */
    public static void downLoadExcel(HttpServletResponse response, String fileName, Consumer<OutputStream> writeConsumer)
            throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            writeConsumer.accept(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * 下载模板
     *
     * @param response
     */
    //下载模板
    public static void downloadExcel(ExportReportEnum exportReportEnum, HttpServletResponse response) {
        InputStream resourceAsStream = null;
        try {

            response.setHeader("Content-disposition",
                    "attachment; filename=" + URLEncoder.encode(exportReportEnum.getExportFileName() + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
            response.setContentType("application/octet-stream;charset=UTF-8");

            //获取文件的路径
            String filePath = FileUtil.class.getResource(exportReportEnum.getTemplatePath()).getPath();
            FileInputStream input = new FileInputStream(filePath);
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[2048];
            int len;
            while ((len = input.read(b)) != -1) {
                out.write(b, 0, len);
            }
            //修正 Excel在“xxx.xlsx”中发现不可读取的内容。是否恢复此工作薄的内容？如果信任此工作簿的来源，请点击"是"
            response.setHeader("Content-Length", String.valueOf(input.getChannel().size()));
            input.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }finally {
            try {
                if(Objects.nonNull(resourceAsStream)){
                    resourceAsStream.close();
                }
            }catch (Exception e){
            }
        }
    }
}
