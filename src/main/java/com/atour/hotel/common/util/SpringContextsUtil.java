/*
 * Copyright (C) 2016-2020 IMassBank Corporation
 *
 */
package com.atour.hotel.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;

/**
 * Spring反射获取Bean工具类
 *
 * <AUTHOR>
 */
@Component
public class SpringContextsUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 根据类名获取Bean
     *
     * @param beanName 类名(Bean名字)
     * @return Bean实体
     */
    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

    public static <T> T getBean(Class<T> clazz) throws BeansException {
        return applicationContext.getBean(clazz);
    }

    /**
     * 根据类名获取Bean
     *
     * @param beanName 类名(Bean名字)
     * @param clazz    泛型
     * @return Bean实体
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        return clazz.cast(getBean(beanName));
    }

    /**
     * 获取Spring启动的Context
     */
    @Override
    public void setApplicationContext(@Nullable ApplicationContext applicationContext) throws BeansException {
        SpringContextsUtil.applicationContext = applicationContext;
    }

    /**
     * 获取method
     *
     * @param className  类名 要符合spring的默认规则
     * @param methodName 方法名
     * @param paramTypes 参数类型
     * @return 方法实体
     */
    public static Method findMethod(String className, String methodName, Class<?>... paramTypes) {
        return ReflectionUtils.findMethod(getBean(className).getClass(), methodName, paramTypes);
    }

    /**
     * 执行该方法
     *
     * @param method 方法实体
     * @param target 对应类的Bean
     * @param args   参数
     * @return 返回结果
     */
    public static Object invokeMethod(Method method, String target, Object... args) {
        return ReflectionUtils.invokeMethod(method, getBean(target), args);
    }
}
