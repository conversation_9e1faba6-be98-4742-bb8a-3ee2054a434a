package com.atour.hotel.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目节点控制
 *
 * <AUTHOR>
 * @date 2020-04-02
 */
public enum ProjectNodeTypeEnum {

    PROJECT_START(0,"项目启动",0d),
    COMMUNICATE_MEETING(1, "营建沟通会",0d),
    FORMAL_PROJECT_START(2, "正式开工",0d),
    MOULD_HIDE(3, "样板间隐蔽",13.9),
    MOULD_CHECK(4, "样板间验收",27.8),
    HIDE_PROJECT_CHECK(5, "隐蔽工程验收",38.9),
    UPDATE_CONFIRM(6, "整改确认封板",44.4),
    ADD_FURNIURE(7, "家具进场",66.7),
    ENSEMBLE_COMPLETE(8, "整体进度80%",80d),
    PROJECT_CHECK(9, "工程竣工自检",83.4),
    //开业前节点
    PROJECT_COMPLETE(20, "工程竣工验收",100d),
    PROJECT_OPENING_CHECK(21, "运营开业检查",100d),
    PROJECT_OPENING(22, "开业",100d);

    private static Map<Integer, String> projectNodeMap = new HashMap<>();

    static {
        for (ProjectNodeTypeEnum value : ProjectNodeTypeEnum.values()) {
            projectNodeMap.put(value.getType(), value.getNodeName());
        }
    }

    /**
     * 类型
     */
    private Integer type;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点进度比例
     */
    private Double progressRate;

    ProjectNodeTypeEnum(Integer type, String nodeName, Double progressRate) {
        this.type = type;
        this.nodeName = nodeName;
        this.progressRate = progressRate;
    }

    public static String matchTypeName(Integer type) {
        if (type != null) {
            return projectNodeMap.get(type);
        }
        return null;
    }

    public static ProjectNodeTypeEnum getInstance(int type) {

        for (ProjectNodeTypeEnum typeEnum : ProjectNodeTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getNodeName() {
        return nodeName;
    }

    public Double getProgressRate() {
        return progressRate;
    }
}
