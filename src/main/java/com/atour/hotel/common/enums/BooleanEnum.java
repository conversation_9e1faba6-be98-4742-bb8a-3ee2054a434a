package com.atour.hotel.common.enums;

/**
 * 枚举: 无有定义
 *
 * <AUTHOR>
 * @date 2019-07-23
 */
public enum BooleanEnum {

    /**
     * 有
     */
    YES("有", 1),

    /**
     * 无
     */
    NO("无", 0);

    private String desc;

    private Integer value;

    BooleanEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }
}
