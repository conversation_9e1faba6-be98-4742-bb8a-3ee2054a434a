package com.atour.hotel.common.enums;

/**
 * 负责人类型
 *
 * <AUTHOR>
 * @date 2020-04-02
 */
public enum ManagerTypeEnum {

    /**
     * 不表示任何含义
     */
    NO(0, "不表示任何含义"),

    /**
     * 开发经理
     */
    DEV_MANAGER(1, "开发经理"),

    /**
     * 营建区域负责人
     */
    AREA_MANAGER(2, "营建区域负责人"),

    /**
     * 项目经理
     */
     PROJECT_MANAGER(3, "项目经理"),

    /**
     * 设计师
     */
    DESIGNER(4, "设计师"),

    /**
     * 弱电工程师
     */
    WEAK_CURRENT(5, "弱电工程师"),

    /**
     * 机电设计师
     */
    DESIGN_ENGINEER(6, "机电设计师"),

    /**
     * 软装设计师
     */
    SOFT_DESIGNER(7, "软装设计师"),

    /**
     * 总监
     */
    MASTER_PROJECT(8, "总监");

    ManagerTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;
    private String name;

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ManagerTypeEnum getInstance(int value) {

        for (ManagerTypeEnum typeEnum : ManagerTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                return typeEnum;
            }
        }
        return null;
    }
}
