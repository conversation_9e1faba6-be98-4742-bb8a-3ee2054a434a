package com.atour.hotel.common.enums;

/**
 * 第三方对接版本枚举
 *
 * <AUTHOR>
 * @date 2019年6月13日15:07:21
 */
public enum ThirdPartVersionEnum {

    /**
     * 验签加入时间戳
     */
    VERSION_CHECK_TIMESTAMP("1.0.0");

    private String version;

    ThirdPartVersionEnum(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
