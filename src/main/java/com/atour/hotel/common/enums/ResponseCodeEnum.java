package com.atour.hotel.common.enums;

import com.atour.web.exception.BusinessException;

/**
 * 返回信息代码枚举
 *
 * <AUTHOR>
 * @date 2019年5月28日15:07:21
 */
public enum ResponseCodeEnum {

	/**
	 * 通用错误码
	 */
	SUCCESS(0, "success"),
	FAILED(-1, "failed"),
	PARAM_ERROR(1001, "参数错误"),
	CHANNEL_ERROR(1002, "渠道不存在"),
	CHANNEL_IP_ERROR(1003, "请求IP非法"),
	COOKIE_CONVERT_ERROR(1004, "cookie解析异常"),
	COOKIE_SAVE_ERROR(1005, "保存cookie异常"),
	COOKIE_REMOVE_ERROR(1006, "删除cookie异常"),
	PERMISSION_DENIED_TOKEN(1007, "当前无权限操作"),
	KEYSIGN_ISNULL_ERROR(1008, "渠道配置的keySign为空"),
	KEY_SIGN_CHECK_FAILED(1009, "校验sign失败"),
	MESSAGE_LIST_OVER_MAX_SIZE(1010, "消息数量超出1000条，请分批同步"),
	PARAM_TIMESTAMP_IS_NULL(1011, "传递时间戳参数为空"),
	PARAM_TIMESTAMP_EXPIRE(1012, "传递时间戳过期了"),
	PARAM_VERSION_IS_NULL(1013, "传递版本参数有误"),
	CHAIN_NOT_EXIST(1014, "酒店不存在"),
	MORE_EQUIPMENT_ERROR(40002,"此账号已在另一台设备登录，请重新输入密码登录。"),

	RESULT_IS_EMPTY(1109,"查询结果集为空"),
	USER_TIMEOUT(2000,"登录超时"),
	USER_REGISTER_SUCCESS(2001,"您已提交注册申请，我们将尽快审核"),
	USER_REGISTER_DUPLICATE_ERROR(2002,"您已注册，我们正在加急审核，请勿重复注册"),
	AUTH_NOT_LOGIN_INFO(40001, "请先登录!"),
	RELOGIN_ERROR(40002, "请手动输入账号及密码,重新登录"),
	LOGIN_MOBILE_OR_ACCESS_TOKEN_ERROR(40003, "请输入正确的手机号或授权阿里一键登录!"),
	LOGIN_MOBILE_CODE_ERROR(40004, "请输入正确的手机号验证码!"),
	LOGIN_MOBILE_NOT_AUTH_ERROR(40006, "该手机号无登录权限"),
	LOGIN_MOBILE_FORMAT_ERROR(40005, "请输入正确的手机号!"),
	LOGIN_APP_TYPE_UNKNOWN_ERROR(40007, "登录app的来源未知"),
	FORBID_ACCESS(40008, "无权限，请联系管理员"),
	CHAIN_ID_IS_NULL_ERROR(40009, "酒店id不能为空"),
	VERIFY_CODE_ERROR_TO_MUCH(40010, "您的验证码错误次数过多"),
    CLIENT_ABORT_EXCEPTION(40011, "连接中断,请稍后再试"),
	BUSINESS_ERROR(9998,"业务异常，请联系管理员"),
	SYSTEM_ERROR(9999, "系统出现异常，请联系管理员"),

	//能耗相关
	PARAM_ILLEGAL_ERROR(2001, "参数类型非法"),
	PARAM_INVALID_ERROR(2002, "参数非法"),
	LOGIN_ERROR(2003, "登录失败"),
	DAYS_MORE_ERROR(2004, "异常规则对比的天数不允许超出30日"),
	NOT_CONFIG_ELECTRICITY_PRICE_ERROR(2005, "当前营业日未配置总电价，无法录入读表数。"),
	NOT_CONFIG_ELECTRICITY_METER_ERROR(2006, "当前营业日未配置总电表，无法录入读表数。"),
	NOT_CONFIG_ELECTRICITY_PRICE_METER_ERROR(2015, "当前营业日未配置总电表、总电价，无法录入读表数。"),
	ACCDATE_ILLEGAL_METER_ERROR(2007, "只可录入当前营业日的抄表记录"),
	READING_PRICE_ERROR(2008, "传入的价格类型错误"),
	ENERGY_DATA_MISSING_ERROR(2009, "尚未填写能耗数据，无法保存"),
	ENERGY_DATA_COMPARE_ILLEGAL_ERROR(2010, "您当日所填写数值小于前一日或大于后一日，请重新填写"),
	ENERGY_DATA_METER_ILLEGAL_ERROR(2011, "表不存在"),
	ENERGY_DATA_METER_PLACE_ERROR(2012, "该表不是内部场地表"),
	ENERGY_DATA_MONTH_PARAM_ERROR(2013, "没有补录数据"),
    ENERGY_LASTER_MONTH_RECORD_ERROR(2014, "当前营业日，不能补录数据"),
	ENERGY_SNAPSHOT_EMPTY(2016, "未找到配置数据"),
	NOT_CONFIG_WATER_PRICE_ERROR(2017, "当前营业日未配置总水价，无法录入读表数。"),
	NOT_CONFIG_WATER_METER_ERROR(2018, "当前营业日未配置总水表，无法录入读表数。"),
	NOT_CONFIG_WATER_PRICE_METER_ERROR(2019, "当前营业日未配置总水表、总水价，无法录入读表数。"),
	NOT_CONFIG_GAS_PRICE_ERROR(2020, "当前营业日未配置总燃气价，无法录入读表数。"),
	NOT_CONFIG_GAS_METER_ERROR(2021, "当前营业日未配置总燃气表，无法录入读表数。"),
	NOT_CONFIG_GAS_PRICE_METER_ERROR(2022, "当前营业日未配置总燃气表、总燃气价，无法录入读表数。"),
	NOT_CONFIG_STEAM_PRICE_ERROR(2023, "当前营业日未配置总蒸汽价，无法录入读表数。"),
	NOT_CONFIG_STEAM_METER_ERROR(2024, "当前营业日未配置总蒸汽表，无法录入读表数。"),
	NOT_CONFIG_STEAM_PRICE_METER_ERROR(2025, "当前营业日未配置总蒸汽表、总蒸汽价，无法录入读表数。"),
	ENERGY_DATA_COMPARE_ILLEGAL_ERROR_DETAIL(2026, "您当日所填写数值%s%s所填数值，请重新填写"),

	NO_AUTH_TO_OPERATE_CURRENT_CHAIN(3000, "没有当前酒店的权限"),

	// 任务
	TASK_FINISHED_WARNING(4000, "已有别的伙伴完成该任务了哦"),
	TASK_INFO_ERROR(4001, "任务信息不全请联系管理员"),
	TASK_IMAGE_INFO_ERROR(4002, "任务图片信息找不到请联系管理员"),
	TASK_FINISHED_REPEATED(4003, "正在处理中，请勿重复提交"),
	TASK_FINISHED_NO_INFO(4004, "请提交完成的任务信息"),
	TASK_FINISHED_NO_SUPPORT_DELAY(4005, "该布房任务不支持延后配送"),

	TASK_EXPIRED_WARNING(4006, "任务已经过期或超时，无法继续操作哦"),

	TASK_NOT_EXIST(4007, "任务不存在"),

	RBAC_REQUEST_FAIL(5000, "查询 rbac 失败"),

	//营建
	NOT_FOUND_PROJECT(6000,"未找到项目信息"),
	NOT_START_PROJECT(6001,"当前项目未启动"),
	NOT_FOUND_PROJECT_PROGRESS(6002,"未找到项目阶段信息信息"),

	NO_FOLIO_TRANS_LOCK(60001,"获取房单调账申请锁失败"),
	FOLIO_TRANS_CHANGE_ERROR(60002,"当前房单状态或账务状态或消费结算金额发生变化，请确认后再提交"),
	FOLIO_TRANS_NOT_SELF_ERROR(60003,"非申请本人，无法操作撤回"),
	FOLIO_TRANS_AUTH_ERROR(60004,"无权限操作"),
	FOLIO_TRANS_ERROR(60005,"审批失败"),
	NO_PUSH_APPROVAL_LOCK(60006, "推送审批操作冲突，请稍后重试"),
	PUSH_APPROVAL_NOT_EXIT_OR_STATE_CHANGE(60007, "审批数据不存在或者已提审"),
	NO_VALID_TASK_TO_HANDLE(60008, "没有对应的任务"),
	START_PROCESS_FAILED(60009, "提交申请失败，请重新提交"),
	DUPLICATE_TASK_ERROR(60010, "提交审批失败,出现多个流程任务"),
	APPROVAL_STATE_ERROR(60011, "审批状态不对,不能进行该操作"),
	APPROVAL_REQUEST_NOT_EXIST_ERROR(60012, "审批编号不能为空"),
	NO_AGREEMENT_LOCK(60013,"获取协议房价代码申请锁失败"),
	NO_TEAM_ROOM_ORDER_LOCK(60014,"获取团队房订单审批锁失败"),

	DKF_CLEANNO_EMPTY(70001, "当前房间不是通过系统打扫，请至查房页直接操作“置干净”。"),

	FINISHED_QUESTIONNAIRE(80000, "当前已完成过问卷"),

	/**
	 * KPI管理
	 */
	PARAM_DATA_ERROR(7010, "数据格式有误"),

	/**
	 * 成本Excel 导入
	 */
	IMPORT_EXCEL_ERROR(8001, "数据导入失败，请联系管理员"),

	APPROVE_ERROR(8002,"不满足申请条件"),

	/**
	 * 文件相关操作
	 */
	FILE_UPLOAD_ERROR(9001,"文件上传失败"),

	FILE_PREVIEW_ERROR(9002,"文件预览失败"),


	CHECK_ROOM_ERROR(10001,"三级查房任务提示code"),


	CLEAN_STATE_ARRANGE_ROOM_DATA_EXIST_ERROR(20001,"%s已安排%s进行大清打扫"),

	CLEAN_STATE_ARRANGE_ROOM_USER_DATA_ERROR(20002,"%s不是%s的排房!请选择%s的排房人%s"),

	CLEAN_STATE_ARRANGE_ROOM_USER_TODAY_ERROR(20003,"%s今日已安排%s间大清！不可再排大清房了"),

	CLEAN_STATE_ARRANGE_ROOM_CLEANING_ERROR(20004,"%s已经开始打扫，不可排大清"),

	CANCEL_CLEAN_STATE_ARRANGE_ROOM_CLEANING_ERROR(20005,"%s已经开始打扫或检查中，不可取消大清排房"),
	CANCEL_CLEAN_STATE_ARRANGE_ROOM_ERROR(10002,"大清排房提示code"),

	CANCEL_CLEAN_STATE_CHECK_ROOM_ERROR(10003,"大清查房提示code"),

	DKF_HAS_ARR_ROOM_ERROR(10004,"%s当前已有排房"),

	DKF_NO_HAS_WORK_ERROR(10005,"未查询到排班记录，无法进行排房，请先确认排班"),

	DKF_NO_HAS_ROOM_WORK_ERROR(10006,"未查询到%s的排班记录，无法进行排房，排房记录已清除，其他人均已排房成功"),
	;

	private int code;

	private String message;

	ResponseCodeEnum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public void throwException() {
		throw new BusinessException(this.getMessage(), this.getCode());
	}
}
