package com.atour.hotel.common.enums;

import com.atour.hotel.module.energy.biz.json.electricity.ElectricityJson;
import com.atour.hotel.module.energy.biz.json.gas.GasJson;
import com.atour.hotel.module.energy.biz.json.other.OtherJson;
import com.atour.hotel.module.energy.biz.json.steam.SteamJson;
import com.atour.hotel.module.energy.biz.json.water.WaterJson;
import com.atour.hotel.module.energy.web.bo.ChainFacilityJsonBo;
import com.atour.hotel.module.energy.web.dto.InvoiceTaxRateRecordDTO;
import com.atour.hotel.persistent.energy.entity.WarnReasonEntity;

/**
 * 日志类型
 *
 * <AUTHOR>
 * @date 2019/7/23
 */
public enum LogModelTypeEnum {

    /**
     * 酒店设施
     */
    CHAIN_FACILITY(1, "酒店设施配置日志", ChainFacilityJsonBo.class),

    /**
     * 电力配置
     */
    ELECTRIC_CONFIG(2, "电力配置日志", ElectricityJson.class),

    /**
     * 水配置
     */
    WATER_CONFIG(3, "水配置日志", WaterJson.class),

    /**
     * 燃气配置
     */
    GAS_CONFIG(4, "燃气配置日志", GasJson.class),

    /**
     * 蒸汽配置
     */
    STEAM_CONFIG(5, "蒸汽配置日志", SteamJson.class),

    /**
     * 其他配置
     */
    OTHER_CONFIG(6, "其他配置日志", OtherJson.class),

    /**
     * 其他配置
     */
    WARN(7, "预警原因日志", WarnReasonEntity.class),

    /**
     * 发票-水
     */
    INVOICE_WATER(8, "发票-水", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-电
     */
    INVOICE_ELECTRICITY(9, "发票-电", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-燃气
     */
    INVOICE_GAS(10, "发票-燃气", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-蒸汽
     */
    INVOICE_STEAM(11, "发票-蒸汽", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-市政供暖
     */
    INVOICE_HEAT(12, "发票-市政供暖", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-外购热水
     */
    INVOICE_OUTHOTWATER(13, "发票-外购热水", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-外购柴油
     */
    INVOICE_OUTDIESEL(14, "发票-外购柴油", InvoiceTaxRateRecordDTO.class),

    /**
     * 发票-外购煤气
     */
    INVOICE_OUTGAS(15, "发票-外购煤气", InvoiceTaxRateRecordDTO.class),

    ;


    LogModelTypeEnum(int value, String name, Class clazz) {
        this.value = value;
        this.name = name;
        this.clazz = clazz;
    }

    private int value;
    private String name;

    private Class clazz;

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Class getClazz() {
        return clazz;
    }

    public static LogModelTypeEnum getInstance(int value) {

        for (LogModelTypeEnum typeEnum : LogModelTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                return typeEnum;
            }
        }
        return null;
    }
}
