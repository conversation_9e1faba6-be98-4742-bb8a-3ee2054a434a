package com.atour.hotel.common.enums;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @description pms任务类型
 * @date 2024年02月29日16:21
 * @since JDK1.8
 */
public enum PmsNewTaskTypeEnum {

    /**
     * 发票
     */
    INVOICE(1, "发票"),

    /**
     * aPlus布房任务
     */
    ROOM_A_PLUS(2, "aPlus布房任务"),

    /**
     * 客需任务
     */
    CUSTOMER_TASK(3, "客需、借物任务"),

    /**
     * 打扫任务
     */
    CLEAN_TASK(4, "打扫任务"),

    /**
     * 任务中台过来的布房任务
     */
    LAYOUT_TASK( 5,"布房任务"),

    /**
     * 任务中台过来的前台
     */
    FOREGROUND_TASK(6,"前台任务"),

    ;

    private static final Map<Integer, PmsNewTaskTypeEnum> MAPPING = Maps.newHashMap();

    static {
        for (PmsNewTaskTypeEnum value : PmsNewTaskTypeEnum.values()) {
            MAPPING.put(value.getType(), value);
        }
    }

    private int type;

    private String typeName;

    PmsNewTaskTypeEnum(int type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public static PmsNewTaskTypeEnum typeOf(int type) {
        return MAPPING.get(type);
    }

    public int getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }
}
