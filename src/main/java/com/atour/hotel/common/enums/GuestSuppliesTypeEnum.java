package com.atour.hotel.common.enums;

/**
 * 客用品录入类型
 */
public enum GuestSuppliesTypeEnum {
    STORAGE(1, "录入"),
    SURPLUS_OMS(2, "上月结余(OMS手动录入)"),
    SURPLUS_APP(3, "上月结余(APP自动同步)");

    private Integer type;

    private String description;

    GuestSuppliesTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

	public Integer getType() {
		return type;
	}

	public String getDescription() {
        return description;
    }
}
