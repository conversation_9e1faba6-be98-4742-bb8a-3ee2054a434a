package com.atour.hotel.persistent.franchise.dao;

import com.atour.hotel.persistent.franchise.entity.NoticeUserReadMappingEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoticeUserReadMappingDao {

    /**
     * 用户公告阅读映射
     *
     * @param userId
     * @param userType
     * @param noticeIds
     * @return
     */
    List<NoticeUserReadMappingEntity> getNoticeReadState(@Param("userId") Integer userId,
        @Param("userType") Integer userType, @Param("noticeIds") List<Integer> noticeIds);

    /**
     * 新增
     *
     * @param noticeUserReadMappingEntity
     * @return
     */
    Integer insertNoticeUserReadMapping(NoticeUserReadMappingEntity noticeUserReadMappingEntity);
}