package com.atour.hotel.persistent.franchise.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 业主反馈 entity
 *
 * <AUTHOR>
 * @date 2019/9/25
 */
@Data
public class FeedbackEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 酒店id
     */
    private Integer chainId;

    /**
     * 反馈人id
     */
    private Integer userId;

    /**
     * 反馈人姓名
     */
    private String userName;

    /**
     * 反馈人姓名
     */
    private String mobile;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型 1-合同相关、2-设计/工程、3-采购相关、4-酒店经营&管理、5-现长&政委、6-员工薪酬奖金、7-生活体验中心、8-流动图书馆、9-财务相关、10-其他'
     */
    private Integer type;

    /**
     * 图片路径集合逗号分开
     */
    private List<String> images;

    /**
     * (缩略)图片路径集合逗号分开
     */
    private List<String> imagesThumb;

    /**
     * 反馈状态 1-待解决，2-解决中，3-待评价，4-已评价
     */
    private Integer state;

    /**
     * 是否已读 0-未读，1-已读
     */
    private Integer read;

    /**
     * 是否升级 0-正常，1-升级
     */
    private Integer upgraded;

    /**
     * 是否删除 0-正常，1-删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 扩展字段
     */
    private String extraContent;

}