package com.atour.hotel.persistent.franchise.entity;

import lombok.Data;

import java.util.Date;

/**
 * 周定义
 *
 * <AUTHOR>
 * @date 2019/11/20
 */
@Data
public class WeekendDefinitionEntity {

    /**
     * id
     */
    private Integer id;

    /**
     * 日期
     */
    private Date date;

    /**
     * ??
     */
    private Integer dateInt;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 日期
     */
    private Integer day;

    /**
     * 星期
     */
    private Integer weekday;

    /**
     * 第几周
     * 周日为一周的开始
     */
    private Integer weekNo;

    /**
     * 第几周
     * 周五为一周的开始
     */
    private Integer weekNo5;

    /**
     * 季度
     */
    private Integer quarter;

    /**
     * 配合经营周使用
     */
    private Integer yearFw;

    /**
     * 经营周(上周五-本周四)
     */
    private Integer weekNo5Fw;

    /**
     * 配合会员部定制周使用
     */
    private Integer yearMeb;

    /**
     * 会员部定制周(上周六-本周五)
     */
    private Integer weekMeb;

    /**
     * 节假日
     */
    private String holiday;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    /**
     * 软删除标记
     */
    private Integer deleted;
}
