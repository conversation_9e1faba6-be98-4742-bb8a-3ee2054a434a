package com.atour.hotel.persistent.franchise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 
 * 
 * <AUTHOR>
 * @@date 2021-03-03
 * 
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExceptionSignDetailEntity implements Serializable {

	/**
	 * 主键id
	 **/
	private Long id;

	/**
	 * 酒店id
	 **/
	private Integer chainId;

	/**
	 * folio表主键id
	 **/
	private Long folioId;

	/**
	 * 发生营业日
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private LocalDate accDate;
	/**
	 * 有效审核日期
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime examineDateTime;

	/**
	 * 签阅结果 0-默认 1-异常 2-正常
	 **/
	private Integer signResult;

	/**
	 * 签约时间状态 0-默认 1-按时签 2-补签
	 **/
	private Integer signState;

	/**
	 * 明细数据
	 **/
	private String jsonValue;

	/**
	 * 签约状态 0-未签阅 1-已签阅 2-签阅不合格 -3签阅合格
	 **/
	private Integer state;


	/**
	 * 风险类型 1-侵占房费
	 **/
	private Integer groupType;

	/**
	 * 签约类型 1-悬单 2-AR账 3-转账异常 4-免升 5-换房 6-免费房 7-现金收支 8-离店后入账 9超时离店未加收 10-发票 11-方位调整 12-减少客人
	 **/
	private Integer type;

	/**
	 * 删除状态 0-删除 1-有效
	 **/
	private Integer deleted;

	/**
	 * 创建时间
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime updateTime;

	/**
	 * 扩展字段
	 **/
	private String extendField;


}
