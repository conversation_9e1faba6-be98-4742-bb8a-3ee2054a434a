package com.atour.hotel.persistent.franchise.entity;


import java.io.Serializable;
import java.util.Date;

/**
 * 三级查房类型表
 * @TableName check_room_config_type
 */
public class CheckRoomConfigTypeEntity  {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 检查类型value
     */
    private String configTypeValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private Integer updateUserId;

    /**
     * 更新人
     */
    private String updateUserName;

    /**
     * 软删除 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 版本ID
     */
    private Integer version;

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 检查类型value
     */
    public String getConfigTypeValue() {
        return configTypeValue;
    }

    /**
     * 检查类型value
     */
    public void setConfigTypeValue(String configTypeValue) {
        this.configTypeValue = configTypeValue;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建人ID
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * 创建人ID
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 创建人
     */
    public String getCreateUserName() {
        return createUserName;
    }

    /**
     * 创建人
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    /**
     * 更新人ID
     */
    public Integer getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 更新人ID
     */
    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 更新人
     */
    public String getUpdateUserName() {
        return updateUserName;
    }

    /**
     * 更新人
     */
    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    /**
     * 软删除 0:未删除 1:已删除
     */
    public Integer getDeleted() {
        return deleted;
    }

    /**
     * 软删除 0:未删除 1:已删除
     */
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    /**
     * 版本ID
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * 版本ID
     */
    public void setVersion(Integer version) {
        this.version = version;
    }


}