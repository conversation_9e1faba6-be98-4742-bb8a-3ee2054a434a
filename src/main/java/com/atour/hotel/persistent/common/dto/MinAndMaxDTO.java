package com.atour.hotel.persistent.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * 查询最大最小Id
 *
 * <AUTHOR>
 * @date 2019-02-21 11:52
 */
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class MinAndMaxDTO {

    /**
     * 最小Id
     */
    private Long minId;
    /**
     * 最大Id
     */
    private Long maxId;
}
