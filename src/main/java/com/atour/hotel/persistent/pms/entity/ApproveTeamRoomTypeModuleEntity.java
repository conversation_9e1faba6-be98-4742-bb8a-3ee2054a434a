package com.atour.hotel.persistent.pms.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ApproveTeamRoomTypeModuleEntity {
    private Long id;
    /**
     *
     */
    private Long teamRoomId;
    /**
     * 房型
     */
    private Integer roomTypeId;
    /**
     * 房型名称
     */
    private String roomTypeName;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;
    /**
     * 审批状态 0,待审核，1,通过，2拒绝
     */
    private Integer approveState;
    /**
     * 最后审批时间
     */
    private Date approveTime;
    /**
     * 顺序序号
     */
    private Integer orderNum;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 时间范围数组
     */
    private String timeRangeArray;

    /**
     * 总收益
     */
    private BigDecimal totalProfit;
    /**
     * 平均门市价
     */
    private BigDecimal avgRoomPrice;


}