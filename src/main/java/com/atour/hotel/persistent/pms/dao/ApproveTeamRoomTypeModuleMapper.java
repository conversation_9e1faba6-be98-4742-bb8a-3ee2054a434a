package com.atour.hotel.persistent.pms.dao;

import com.atour.hotel.persistent.pms.entity.ApproveTeamRoomTypeModuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApproveTeamRoomTypeModuleMapper {
    int insert(ApproveTeamRoomTypeModuleEntity record);

    int insertSelective(ApproveTeamRoomTypeModuleEntity record);

    List<ApproveTeamRoomTypeModuleEntity> selectByOrderId(Long teamRoomId);

    int updateByPrimaryKey(ApproveTeamRoomTypeModuleEntity roomTypeModule);

    int updateApproveStateById(ApproveTeamRoomTypeModuleEntity updateItem);

    int deleteDataByState(@Param("approveState") int approveState, @Param("teamRoomId") Long teamRoomId);

    int updateByPrimaryKeySelective(ApproveTeamRoomTypeModuleEntity roomTypeModule);

    int deleteByPrimaryKey(@Param("id") Long id);
}