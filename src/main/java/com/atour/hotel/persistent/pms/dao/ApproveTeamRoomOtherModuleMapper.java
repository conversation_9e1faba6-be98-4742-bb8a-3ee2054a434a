package com.atour.hotel.persistent.pms.dao;

import com.atour.hotel.persistent.pms.entity.ApproveTeamRoomOtherModuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApproveTeamRoomOtherModuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ApproveTeamRoomOtherModuleEntity record);

    int insertSelective(ApproveTeamRoomOtherModuleEntity record);

    ApproveTeamRoomOtherModuleEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ApproveTeamRoomOtherModuleEntity record);

    int updateByPrimaryKey(ApproveTeamRoomOtherModuleEntity record);

    List<ApproveTeamRoomOtherModuleEntity> selectByOrderId(Long teamRoomId);

    void deleteByTeamRoomId( @Param("teamRoomId") Long teamRoomId);
}