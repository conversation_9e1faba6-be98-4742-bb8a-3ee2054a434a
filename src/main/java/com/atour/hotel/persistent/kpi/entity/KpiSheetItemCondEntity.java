package com.atour.hotel.persistent.kpi.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 考核指标值设置
 *
 * <AUTHOR>
 * @date 2020/8/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KpiSheetItemCondEntity implements Serializable {
    /**
     * 设置ID
     */
    private Integer id;

    /**
     * 考核指标设置ID。kpi_sheet_item.id
     */
    private Integer sheetItemId;

    /**
     * 考核ID。kpi_sheet.id
     */
    private Integer sheetId;

    /**
     * 基础指标ID。kpi.items.itemId
     */
    private Integer itemId;

    /**
     * 设置条件类型。1-门店；2-区域
     *
     * @see com.atour.hotel.module.kpi.enums.KpiCondTypeEnum
     */
    private Integer condType;

    /**
     * 设置方式。1-单店；2-批量
     *
     * @see com.atour.hotel.module.kpi.enums.KpiCondMethodEnum
     */
    private Integer condMethod;

    /**
     * 筛选条件JSON
     * 例如：{condType:2, condMethod:1, areas: [17-上海区域]}
     *
     * @see com.atour.hotel.module.kpi.web.response.cond.KpiSheetItemCondJsonDTO
     */
    private String condJson;

    /**
     * 保底。存储真实值。90000(9W), 0.95(95%)
     */
    private BigDecimal pass;

    /**
     * 目标
     */
    private BigDecimal target;

    /**
     * 达标线
     */
    private BigDecimal reach;

    /**
     * 达标线逻辑。只有正向，0-等于；1-大于；2-大于等于
     *
     * @see com.atour.hotel.module.kpi.enums.KpiReachLogicEnum
     */
    private Integer reachLogic;

    /**
     * 软删除。0-有效；1-已删除
     *
     * @see com.atour.dicts.db.common.DeletedEnum
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}