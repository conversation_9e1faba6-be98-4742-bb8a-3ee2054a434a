package com.atour.hotel.persistent.kpi.param;

import com.atour.dicts.db.common.DeletedEnum;
import com.atour.hotel.framework.mybatis.annotation.JavaMapperTotalInfo;
import com.atour.hotel.module.kpi.constants.KpiConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/1 7:53 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KpiSheetItemCondParam implements Serializable {

	/**
	 * 软删除。0-有效；1-已删除
	 *
	 * @see DeletedEnum
	 */
	private Integer deleted;

	/**
	 * sheetItemId
	 */
	private List<Integer> sheetItemId;

	/**
	 * 设置门店类型 1-门店；2-区域
	 */
	private Integer condType;


}
