package com.atour.hotel.persistent.kpi.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * 考核指标-指标结果明细表
 * <AUTHOR>
 * @@date 2020-09-01
 * 
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryKpiReportListParam implements Serializable {

	/**
     * 地域类型
	 */
	private Integer entityType;

	/**
	 * 地域值
	 */
	private List<Long> entityValList;

    /**
     * 年份
	 */
	private Integer year;

	/**
	 * 时间颗粒
	 */
	private Integer periodType;

	/**
	 * 最小值
	 */
	private Long minPeriodVal;


	/**
	 * 最大值
	 */
	private Long maxPeriodVal;

	/**
	 * 页码
	 */
	private Integer pageNo;

	/**
	 * 页长度
	 */
	private Integer pageSize;

}
