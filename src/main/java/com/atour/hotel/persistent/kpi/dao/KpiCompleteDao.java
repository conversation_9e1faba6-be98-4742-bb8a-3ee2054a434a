package com.atour.hotel.persistent.kpi.dao;

import com.atour.hotel.framework.mybatis.enhance.SelectByParamExtendedLanguageDriver;
import com.atour.hotel.persistent.kpi.entity.KpiCompleteEntity;
import com.atour.hotel.persistent.kpi.entity.KpiCompleteSaveEntity;
import com.atour.hotel.persistent.kpi.param.KpiCompleteQueryWithLimit;
import com.atour.hotel.persistent.kpi.param.QueryKpiCompleteParam;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * KpiCompleteDao
 *
 * <AUTHOR>
 * @date 2020/8/28
 */
@Mapper
public interface KpiCompleteDao {

	/**
	 * 批量插入
	 *
	 * @param list
	 * @return
	 */
	int batchInsert(List<KpiCompleteEntity> list);


	int batchInsertSave(List<KpiCompleteSaveEntity> list);

	List<KpiCompleteEntity> selectByParamWithLimit(KpiCompleteQueryWithLimit query);


	int countByParam(KpiCompleteQueryWithLimit query);


	@Select("(#{query})")
	@Lang(SelectByParamExtendedLanguageDriver.class)
	List<KpiCompleteEntity> selectByParam(KpiCompleteEntity query);


	/**
	 * gen根据update时间查询
	 *
	 * @param localDateTime
	 * @return
	 */
	List<KpiCompleteEntity> queryByUpdateTime(String localDateTime);

	/**
	 * gen根据update时间查询
	 *
	 * @param localDateTime
	 * @return
	 */
	Long queryCountByUpdateTime(String localDateTime);


	List<KpiCompleteEntity> selectByPeriodAndEntityList(QueryKpiCompleteParam build);


	List<KpiCompleteEntity> queryByIds(@Param("ids") List<Integer> ids);




}