package com.atour.hotel.persistent.app.dao;

import com.atour.hotel.persistent.app.entity.ComplaintInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 吐槽信息Dao
 * （complaint 投诉）
 *
 * <AUTHOR>
 * @date 2019-05-30
 */
public interface ComplaintInfoDAO {

    /**
     * 通过主键id查找
     *
     * @param complaintId
     * @return
     */
    ComplaintInfoEntity selectById(Long complaintId);

    List<ComplaintInfoEntity> selectByIds(@Param("complaintIds") List<Integer>  complaintIds);
}
