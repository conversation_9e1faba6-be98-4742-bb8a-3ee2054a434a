package com.atour.hotel.persistent.app.entity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 *
 * 点评信息
 * <AUTHOR>
 * @@date 2019-10-31
 *
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TJudgementInfoEntity implements Serializable {

	/**
	 * 点评ID
	 **/
	private Long FjudgementId;

	/**
	 * 点评内容
	 **/
	private String FjudgementInfo;

	/**
	 * 第1张图片的地址
	 **/
	private String FimageUrl1;

	/**
	 * 第1张图片的宽度
	 **/
	private Integer Fwidth1;

	/**
	 * 第1张图片的高度
	 **/
	private Integer Fheight1;

	/**
	 * 第2张图片的地址
	 **/
	private String FimageUrl2;

	/**
	 * 第2张图片的宽度
	 **/
	private Integer Fwidth2;

	/**
	 * 第2张图片的高度
	 **/
	private Integer Fheight2;

	/**
	 * 第3张图片的地址
	 **/
	private String FimageUrl3;

	/**
	 * 第3张图片的宽度
	 **/
	private Integer Fwidth3;

	/**
	 * 第3张图片的高度
	 **/
	private Integer Fheight3;

	/**
	 * 第4张图片的地址
	 **/
	private String FimageUrl4;

	/**
	 * 第4张图片的宽度
	 **/
	private Integer Fwidth4;

	/**
	 * 第4张图片的高度
	 **/
	private Integer Fheight4;

	/**
	 * 第5张图片的地址
	 **/
	private String FimageUrl5;

	/**
	 * 第5张图片的宽度
	 **/
	private Integer Fwidth5;

	/**
	 * 第5张图片的高度
	 **/
	private Integer Fheight5;

	/**
	 * 第6张图片的地址
	 **/
	private String FimageUrl6;

	/**
	 * 第6张图片的宽度
	 **/
	private Integer Fwidth6;

	/**
	 * 第6张图片的高度
	 **/
	private Integer Fheight6;

	/**
	 * 第7张图片的地址
	 **/
	private String FimageUrl7;

	/**
	 * 第7张图片的宽度
	 **/
	private Integer Fwidth7;

	/**
	 * 第7张图片的高度
	 **/
	private Integer Fheight7;

	/**
	 * 第8张图片的地址
	 **/
	private String FimageUrl8;

	/**
	 * 第8张图片的宽度
	 **/
	private Integer Fwidth8;

	/**
	 * 第8张图片的高度
	 **/
	private Integer Fheight8;

	/**
	 * 第9张图片的地址
	 **/
	private String FimageUrl9;

	/**
	 * 第9张图片的宽度
	 **/
	private Integer Fwidth9;

	/**
	 * 第9张图片的高度
	 **/
	private Integer Fheight9;

	/**
	 * 缩略图第1张图片的地址
	 **/
	private String FimageUrlThumb1;

	/**
	 * 缩略图第2张图片的地址
	 **/
	private String FimageUrlThumb2;

	/**
	 * 缩略图第3张图片的地址
	 **/
	private String FimageUrlThumb3;

	/**
	 * 缩略图第4张图片的地址
	 **/
	private String FimageUrlThumb4;

	/**
	 * 缩略图第5张图片的地址
	 **/
	private String FimageUrlThumb5;

	/**
	 * 缩略图第6张图片的地址
	 **/
	private String FimageUrlThumb6;

	/**
	 * 缩略图第7张图片的地址
	 **/
	private String FimageUrlThumb7;

	/**
	 * 缩略图第8张图片的地址
	 **/
	private String FimageUrlThumb8;

	/**
	 * 缩略图第9张图片的地址
	 **/
	private String FimageUrlThumb9;

	/**
	 * 缩略图图片的宽度
	 **/
	private Integer FwidthThumb;

	/**
	 * 缩略图图片的高度
	 **/
	private Integer FheightThumb;

	/**
	 * 发布者uid
	 **/
	private Long Fuid;

	/**
	 * 发布者注册手机号码
	 **/
	private String FphoneNum;

	/**
	 * 发布者昵称
	 **/
	private String FnickName;

	/**
	 * 发布者头像
	 **/
	private String FheadIcon;

	/**
	 * 发布时APP版本号
	 **/
	private String FappVer;

	/**
	 * 渠道号
	 **/
	private Integer FchannelId;

	/**
	 * 平台号
	 **/
	private Integer FplatType;

	/**
	 * 发布时的IP
	 **/
	private String FclientIp;

	/**
	 * 1有效，99删除，对用户而言
	 **/
	private Integer Flstate;

	/**
	 * 99删除，100新提单，101已交谈
	 **/
	private Integer Fstate;

	/**
	 * 酒店已发生交谈
	 **/
	private Integer FsStateAdminChat;

	/**
	 * 卫生评分
	 **/
	private Float FhygieneMarkScore;

	/**
	 * 服务评分
	 **/
	private Float FserviceMarkScore;

	/**
	 * 设施评分
	 **/
	private Float FfacilitiesMarkScore;

	/**
	 * 性价比评分（网络评分）
	 **/
	private Float FpriceMarkScore;

	/**
	 * 用户评分,合并为一个了
	 **/
	private Float FmarkScore;

	/**
	 * 点评归属哪个酒店，显示给用户看的
	 **/
	private Long FhotelId;

	/**
	 * 点评归属哪个入住订单
	 **/
	private Long ForderId;

	/**
	 * 点评管理员首次回复时间间隔
	 **/
	private Long FadminDuration;

	/**
	 * 备注信息
	 **/
	private String Fnote;

	/**
	 * 0:处理中,1:已处理,2:未处理
	 **/
	private Integer DisposeState;

	/**
	 *
	 **/
	private String DisposeRemark;

	/**
	 *
	 **/
	private LocalDateTime FcreateTime;

	/**
	 *
	 **/
	private LocalDateTime FmodifyTime;

	/**
	 * 推荐分值
	 **/
	private Float FrecommendScore;

	/**
	 * 是否来源Aplus用户
	 **/
	private Integer isFromAplusUser;

	/**
	 * rank评分
	 **/
	private Float rankScore;

	/**
	 * 是否展示 0否 1是
	 **/
	private Integer isShow;

	/**
	 * 是否记入评分 0否 1是
	 **/
	private Integer isCalculateScore;



}
