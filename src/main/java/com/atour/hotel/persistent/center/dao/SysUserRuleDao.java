package com.atour.hotel.persistent.center.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统用户角色
 *
 * <AUTHOR>
 * @date 2019/6/4
 */
@Repository
public interface SysUserRuleDao {

    /**
     * 根据系统id查询用户
     *
     * @param userIdList
     * @return
     */
    List<Integer> getUserIdList(@Param("sysId") Integer sysId, @Param("userIdList") List<Integer> userIdList);
}
