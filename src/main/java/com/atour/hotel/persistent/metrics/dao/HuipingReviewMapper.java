package com.atour.hotel.persistent.metrics.dao;

import com.atour.hotel.persistent.metrics.entity.HuipingReviewEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface HuipingReviewMapper {

    List<HuipingReviewEntity> selectBySelective(HuipingReviewEntity huipingReviewEntity);

    List<HuipingReviewEntity> queryListBigThanMaxId(@Param("reviewId") Integer reviewId,
        @Param("handleSize") Integer handleSize);

    List<HuipingReviewEntity> getHuipingReviewListByReviewTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<HuipingReviewEntity> getHuipingReviewListByUpdateTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
