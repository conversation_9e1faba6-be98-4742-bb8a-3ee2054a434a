package com.atour.hotel.persistent.hlm.dao;

import com.atour.hotel.persistent.hlm.entity.ProjectStopWorkEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 数据层: 项目营建-停工记录表
 * <AUTHOR>
 * @@date 2020-02-05
 * 
 **/

@Mapper
public interface ProjectStopWorkDAO {

	/**
	 * 
	 * 查询（根据主键ID查询）
	 * 
	 **/
	List<ProjectStopWorkEntity> selectByStartId(@Param("startId") Long startId);
}