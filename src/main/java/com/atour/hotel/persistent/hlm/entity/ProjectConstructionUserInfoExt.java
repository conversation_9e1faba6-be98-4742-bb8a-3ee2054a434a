package com.atour.hotel.persistent.hlm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * atour-new-hlm
 *
 * <AUTHOR>
 * @date 2020-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectConstructionUserInfoExt extends ProjectConstructionUserInfoEntity {

    /**
     * 施工单位名称
     **/
    private String unitName;

}
