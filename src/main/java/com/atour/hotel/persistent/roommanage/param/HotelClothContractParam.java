package com.atour.hotel.persistent.roommanage.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 门店布草合同查询条件
 *
 * <AUTHOR>
 * @@date 2020-06-04
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HotelClothContractParam {


    /**
     * 最小合同起始日期
     **/
    private LocalDate minBeginDay;

    /**
     * 最大合同起始日期
     **/
    private LocalDate maxBeginDay;

	/**
	 * 门店集合
	 */
	private List<Integer> chainIdList;

	/**
	 * 删除 1-删除
	 */
	private Integer disable;

}
