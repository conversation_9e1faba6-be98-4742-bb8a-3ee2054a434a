package com.atour.hotel.persistent.roommanage.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *@description 大清房配置表
 *<AUTHOR>
 *@date 2024年04月23日10:08
 *@since JDK1.8
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CleanRoomConfigEntity {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 配置类型 1-大清排房-规则配置 2-大清打扫-规则 3-大清任务配置
     */
    private Integer configType;

    /**
     * 配置内容
     */
    private String configValue;

    /**
     * 页面筛选条件
     */
    private String filterCondition;

    /**
     * TraceID
     */
    private String traceId;

    /**
     * 删除标记(0:未删除 1:删除)
     */
    private Integer deleted;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
