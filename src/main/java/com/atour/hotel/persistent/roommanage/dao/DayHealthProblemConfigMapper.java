package com.atour.hotel.persistent.roommanage.dao;

import com.atour.hotel.framework.annotation.LogMapperDiff;
import com.atour.hotel.persistent.roommanage.entity.DayHealthProblemConfigEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据层: 日常卫生问题配置表
 *
 * <AUTHOR>
 * @date 2019-08-30
 */
@LogMapperDiff
@Mapper
public interface DayHealthProblemConfigMapper {

    /**
     * 新增数据
     *
     * @param record 日常卫生问题配置表实体
     * @return 影响行数
     */
    int insert(DayHealthProblemConfigEntity record);

    /**
     * 根据条件新增数据
     *
     * @param record 日常卫生问题配置表实体
     * @return 影响行数
     */
    int insertSelective(DayHealthProblemConfigEntity record);

    /**
     * 根据主键ID查询
     *
     * @param id 主键
     * @return 查询结果
     */
    DayHealthProblemConfigEntity selectByPrimaryKey(Integer id);

    /**
     * 根据条件有选择的查询
     *
     * @param record 主键
     * @return 查询结果
     */
    List<DayHealthProblemConfigEntity> selectBySelective(DayHealthProblemConfigEntity record);

    /**
     * 根据条件有选择的模糊查询
     *
     * @param record 主键
     * @return 查询结果
     */
    List<DayHealthProblemConfigEntity> selectBySelectiveLikeName(DayHealthProblemConfigEntity record);

    /**
     * 根据主键有选择的更新数据
     *
     * @param record 日常卫生问题配置表实体
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(DayHealthProblemConfigEntity record);
}