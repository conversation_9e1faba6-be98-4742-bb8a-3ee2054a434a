package com.atour.hotel.persistent.roommanage.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryBySuppliesAndMonthParam {

    /**
     * 酒店id
     */
    private Integer chainId;
    /**
     * 库存登记月
     */
    private String inventoryMonth;
    /**
     * 客用品id
     */
    private Integer suppliesId;

}
