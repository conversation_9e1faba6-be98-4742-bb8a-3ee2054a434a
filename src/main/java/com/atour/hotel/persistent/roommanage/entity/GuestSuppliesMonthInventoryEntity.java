package com.atour.hotel.persistent.roommanage.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 月客用品盘点
 *
 * <AUTHOR>
 * @@date 2019-09-23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GuestSuppliesMonthInventoryEntity implements Serializable {

	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 酒店ID
	 */
	private Integer chainId;
	/**
	 * 日期(yyyy-MM)
	 */
	private String inventoryMonth;
	/**
	 * 客用品ID
	 **/
	private Long guestSuppliesId;
	/**
	 * 客用品名称
	 **/
	private String guestSuppliesName;
	/**
	 * 上月结余数量
	 **/
	private Integer lastMonthQuantity;
	/**
	 * 上月结余单价（含税）
	 **/
	private BigDecimal lastMonthUnitPriceInTax;
	/**
	 * 上月结余单价（去税）
	 **/
	private BigDecimal lastMonthUnitPriceNonTax;
	/**
	 * 本月入库数量
	 **/
	private Integer monthStorageQuantity;
	/**
	 * 本月入库金额（含税）
	 **/
	private BigDecimal monthStorageAmountInTax;
	/**
	 * 本月入库金额（去税）
	 **/
	private BigDecimal monthStorageAmountNonTax;
	/**
	 * 本月单价（含税）
	 **/
	private BigDecimal monthUnitPriceInTax;
	/**
	 * 本月单价（去税）
	 **/
	private BigDecimal monthUnitPriceNonTax;
	/**
	 * 外部领用
	 **/
	private Integer consumeQuantity;
	/**
	 * 本月剩余数(盘点数)
	 **/
	private Integer monthRemainingQuantity;
	/**
	 * 创建时间
	 **/
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 **/
	private LocalDateTime updateTime;

	/**
	 * 当前单价，同步b2b库存用
	 */
	private BigDecimal curUnitPrice = BigDecimal.ZERO;


}
