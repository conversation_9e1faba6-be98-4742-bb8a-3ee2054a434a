package com.atour.hotel.persistent.roommanage.dao;

import com.atour.hotel.persistent.roommanage.entity.TaskTemplateConfigEntity;
import com.atour.hotel.persistent.roommanage.param.TaskConfigListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 数据层: 任务模板配置表
 * <AUTHOR>
 * @@date 2020-07-27
 * 
 **/

@Mapper
public interface TaskTemplateConfigMapper{


	/**
	 * 
	 * 查询（根据主键ID查询）
	 * 
	 **/
	TaskTemplateConfigEntity  selectByPrimaryKey(@Param("id") Integer id);

	/**
	 * 
	 * 删除（根据主键ID删除）
	 * 
	 **/
	int deleteByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 添加 （匹配有值的字段）
	 * 
	 **/
	int insertSelective(TaskTemplateConfigEntity record);

	/**
	 * 
	 * 修改 （匹配有值的字段）
	 * 
	 **/
	int updateByPrimaryKeySelective(TaskTemplateConfigEntity record);

	/**
	 * 
	 * 查询（匹配有值的字段）
	 * 
	 **/
	List<TaskTemplateConfigEntity> selectBySelective(TaskTemplateConfigEntity record);


	/**
	 *
	 * 查询（匹配有值的字段）
	 *
	 **/
	List<TaskTemplateConfigEntity> getTaskConfigList(TaskConfigListParam record);

	/**
	 * 获取已发布的任务模板列表
	 *
	 * @param taskType
	 * @param taskStatus 任务状态:：0未发布，1已发布 {@link com.atour.hotel.module.taskcenter.enums.TaskStatusEnum}
	 * @param taskIdList 任务Id清单。传空时查全部
	 * @return 模板列表
	 */
	List<TaskTemplateConfigEntity> getTaskTemplateListByStatus(@Param("taskType") String taskType,
															   @Param("taskStatus") Integer taskStatus,
															   @Param("taskIdList") List<String> taskIdList);


	/**
	 * 获取任务最高版本信息
	 * @param taskId
	 * @return
	 */
	TaskTemplateConfigEntity getTaskConfigByMaxVersion(@Param("taskId") Long taskId);

	/**
	 * 根据任务id及版本获取任务信息
	 *
	 * @param taskId
	 * @return
	 */
	TaskTemplateConfigEntity getTaskConfigByVersion(@Param("taskId") Long taskId, @Param("version") Integer version);


	/**
	 * 更新任务内容
	 * @param record
	 * @return
	 */
	int updateTaskContentConfig(TaskTemplateConfigEntity record);

	/**
	 * 拷贝任务配置，版本号+1
	 * @param record
	 * @return
	 */
	int copyTaskConfig(TaskTemplateConfigEntity record);
}