package com.atour.hotel.persistent.roommanage.entity;

import com.atour.hotel.common.enums.DkfLogModelTypeEnum;
import com.atour.hotel.framework.annotation.DeleteFlag;
import com.atour.hotel.framework.annotation.DiffClass;
import com.atour.hotel.framework.annotation.DiffField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据层: 日常卫生问题配置表
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DiffClass(module = DkfLogModelTypeEnum.DAY_HEALTH_PROBLEM)
public class DayHealthProblemConfigEntity {

    /**
     * ID主键
     */
    @DiffField(desc = "ID")
    private Integer id;

    /**
     * 问题类型 A:A类 B:B类
     */
    @DiffField(desc = "问题类型(A:A类 B:B类)")
    private String problemType;

    /**
     * 日常卫生问题名称
     */
    @DiffField(desc = "返工项名称")
    private String dayHealthProblemName;

    /**
     * 删除标记 0:未删除 1：删除
     */
    @DeleteFlag
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}