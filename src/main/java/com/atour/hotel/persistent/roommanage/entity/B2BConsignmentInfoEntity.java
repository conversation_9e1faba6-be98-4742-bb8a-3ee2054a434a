package com.atour.hotel.persistent.roommanage.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存系统出库单表
 * <AUTHOR>
 * @date 2021/9/6
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class B2BConsignmentInfoEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 出库单主键
     */
    private Integer consignmentId;
    /**
     * 酒店id
     */
    private Integer chainId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 发货单编号
     */
    private String shipVoucherCode;
    /**
     * 商品sku编码
     */
    private String skuNo;
    /**
     * 商品sku名称
     */
    private String skuName;

    /**
     * 系统客用品id
     */
    private Integer guestSuppliesId;
    /**
     * 发货数量
     */
    private Integer sendCount;

    /**
     * 入库数量
     */
    private Integer stockInCount;
    /**
     * 单价（含税）
     */
    private BigDecimal unitPriceInTax;
    /**
     * 箱规
     */
    private String boxGauge;
    /**
     * 采购单位
     */
    private String unitName;

    /**
     * 下单时间
     */
    private Date orderCreateTime;

    /**
     * 发货时间
     */
    private Date shipTime;
    /**
     * 入库状态（0：未入库；1-已入库）
     */
    private Integer inventoryState;
    /**
     * 入库时间
     */
    private Date inventoryTime;
    /**
     * 乐观锁版本号
     */
    private Integer version;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除标识（0：未删除；1：已删除）
     */
    private Integer deleted;

}
