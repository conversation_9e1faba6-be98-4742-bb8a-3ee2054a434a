package com.atour.hotel.persistent.roommanage.dao;

import com.atour.hotel.persistent.common.dto.MinAndMaxDTO;
import com.atour.hotel.persistent.roommanage.entity.GuestSupplySkuNewAllEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/13
 * 酒店配置
 */
@Mapper
public interface GuestSupplySkuNewAllEntityDao extends BaseMapper<GuestSupplySkuNewAllEntity> {


    int batchInsert(List<GuestSupplySkuNewAllEntity> list);


    MinAndMaxDTO getMinAndMaxId();


}
