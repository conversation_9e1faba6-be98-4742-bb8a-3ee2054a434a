package com.atour.hotel.persistent.roommanage.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 布草杯具撤换表
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClothCupsReplaceEntity {

	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 酒店ID
	 */
	private Integer chainId;
	/**
	 * 房间号
	 */
	private String roomNo;
	/**
	 * 房间打扫初始房态(1:VD 2:OD 3:V_C 4:VDS)
	 */
	private String roomInitStatus;
	/**
	 * 房间打扫人
	 */
	private Long cleanUserId;
	/**
	 * 撤换时营业日
	 */
	private LocalDate replaceAccDate;
	/**
	 * 填写节点(做房、后补)
	 */
	private String writeNode;
	/**
	 * 计件业务唯一编号
	 */
	private String pieceNo;
	/**
	 * 布草杯具明细数量
	 * @see ClothCupsWashingDetailEntity
	 */
	private String clothCupsDetail;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	public enum WriteNode {
		/**
		 * 打扫
		 */
		CLEANUP("做房"),
		/**
		 * 补充
		 */
		SUPPLEMENT("后补");

		String cnName;

		WriteNode(String cnName) {
			this.cnName = cnName;
		}

		public static String asCnName(String code) {
			for (WriteNode value : WriteNode.values()) {
				if (value.name().equalsIgnoreCase(code)) {
					return value.cnName;
				}
			}
			return StringUtils.EMPTY;
		}
	}

}

