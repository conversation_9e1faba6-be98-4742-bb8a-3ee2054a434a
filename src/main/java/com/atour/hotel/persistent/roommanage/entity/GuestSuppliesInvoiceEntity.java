package com.atour.hotel.persistent.roommanage.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客用品发票
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GuestSuppliesInvoiceEntity {
	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 酒店ID
	 */
	private Integer chainId;
	/**
	 * 发票种类(专票，普票)，或者无
	 * @see com.atour.hotel.common.enums.GuestSuppliesInvoiceEnum
	 */
	private String invoiceType;
	/**
	 * 税率
	 */
	private BigDecimal taxRate;
	/**
	 * 上传发票文件信息
	 */
	private String invoiceDetail;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


}
