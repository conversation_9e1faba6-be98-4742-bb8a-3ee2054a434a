package com.atour.hotel.persistent.roommanage.dao;

import com.atour.hotel.framework.annotation.LogMapperDiff;
import com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据层:计划卫生配置表
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@LogMapperDiff
@Mapper
public interface HealthPlanConfigMapper {

    /**
     * 新增数据
     *
     * @param record 计划卫生配置表实体
     * @return 影响行数
     */
    int insert(HealthPlanConfigEntity record);

    /**
     * 根据条件新增数据
     *
     * @param record 计划卫生配置表实体
     * @return 影响行数
     */
    int insertSelective(HealthPlanConfigEntity record);

    /**
     * 根据主键ID查询
     *
     * @param id 主键
     * @return 查询结果
     */
    HealthPlanConfigEntity selectByPrimaryKey(Integer id);

    /**
     * 根据主键有选择的更新数据
     *
     * @param record 计划卫生配置表实体
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(HealthPlanConfigEntity record);

    /**
     * 根据酒店ID和计划卫生类型查看计划卫生
     *
     * @param healthPlanType 计划卫生类型
     * @return 查询结果
     */
    List<HealthPlanConfigEntity> selectByHealthPlanType(@Param("healthPlanType") Integer healthPlanType);

    /**
     * 有选择的条件查询
     *
     * @param record 计划卫生配置参数
     * @return 查询结果
     */
    List<HealthPlanConfigEntity> selectBySelective(HealthPlanConfigEntity record);

    /**
     * 有选择的条件模糊查询
     *
     * @param record 计划卫生配置参数
     * @return 查询结果
     */
    List<HealthPlanConfigEntity> selectBySelectiveLikeName(HealthPlanConfigEntity record);
}