package com.atour.hotel.persistent.energy.entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * 预警规则与酒店关联表
 * <AUTHOR>
 * @date 2019年7月26日17:16:13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleHotelConfigEntity implements Serializable {

	/**
	 * 
	 * 主键
	 * 
	 **/
	private Integer id;

	/**
	 * 
	 * 预警规则配置表主键
	 * 
	 **/
	private Integer ruleConfigId;

	/**
	 * 
	 * 关联的酒店ID
	 * 
	 **/
	private Integer chainId;

	/**
	 * 
	 * 删除标记 0:未删除 1：删除
	 * 枚举：DeleteFlagEnum
	 **/
	private Integer deleteFlag;

	/**
	 * 创建时间
	 **/
	private LocalDateTime createTime;

	/**
	 * 
	 * 更新时间
	 * 
	 **/
	private LocalDateTime updateTime;


}
