package com.atour.hotel.persistent.energy.dao;

import com.atour.hotel.persistent.energy.entity.ExternalVenueMonthReportDateRangeParam;
import com.atour.hotel.persistent.energy.entity.ExternalVenueMonthReportEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据层: 每月外部场地表
 *
 * <AUTHOR>
 * @@date 2019-08-21
 **/

@Mapper
public interface ExternalVenueMonthReportMapper {

    /**
     * 查询（根据主键ID查询）
     **/
    ExternalVenueMonthReportEntity selectByPrimaryKey(@Param("id") Long id);

    /**
     * 删除（根据主键ID删除）
     **/
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加 （匹配有值的字段）
     **/
    int insertSelective(ExternalVenueMonthReportEntity record);

    /**
     * 修改 （匹配有值的字段）
     **/
    int updateByPrimaryKeySelective(ExternalVenueMonthReportEntity record);

    /**
     * 查询（匹配有值的字段）
     **/
    List<ExternalVenueMonthReportEntity> selectBySelective(ExternalVenueMonthReportEntity record);

    /**
     * 查询（匹配有值的字段）
     **/
    List<ExternalVenueMonthReportEntity> selectByDateRange(ExternalVenueMonthReportDateRangeParam dateRangeParam);

    /**
     * 批量插入
     *
     * @param recordList
     * @return
     */
    int batchInsert(@Param("recordList") List<ExternalVenueMonthReportEntity> recordList);

    /**
     * 列表
     */
    List<ExternalVenueMonthReportEntity> selectList(@Param("chainId") Integer chainId,
        @Param("placeId") Integer placeId, @Param("startYear") Integer startYear, @Param("endYear") Integer endYear,
        @Param("startMonth") Integer startMonth, @Param("endMonth") Integer endMonth, @Param("offset") Integer offset,
        @Param("limit") Integer limit);

    /**
     * 列表count
     */
    Integer selectListCount(@Param("chainId") Integer chainId, @Param("placeId") Integer placeId,
        @Param("startYear") Integer startYear, @Param("endYear") Integer endYear,
        @Param("startMonth") Integer startMonth, @Param("endMonth") Integer endMonth);

    /**
     * 删除数据
     *
     * @return
     */
    Integer deleteAll(@Param("chainId") Integer chainId, @Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询（匹配有值的字段）
     **/
    List<ExternalVenueMonthReportEntity> selectDataByYearAndMonth(@Param("chainId") Integer chainId,
        @Param("year") Integer year, @Param("month") Integer month);
}