package com.atour.hotel.persistent.energy.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/8/6
 * 每月外部场地表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalVenueMonthReportEntity {
    private Integer id;

    private Integer chainId;
    private Integer placeId;
    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 外部场地信息(json)
     */
    private String external;

    private Integer deleteFlag;

    private Date createTime;

    private Date updateTime;
}
