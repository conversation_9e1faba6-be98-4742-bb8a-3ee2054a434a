package com.atour.hotel.persistent.energy.dao;

import com.atour.hotel.persistent.energy.entity.EnergyCostDayReportEntity;
import com.atour.hotel.persistent.energy.entity.EnergyCostDayReportQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 数据层: 每日能耗成本表
 *
 * <AUTHOR>
 * @date 2019-08-11
 */
@Mapper
public interface EnergyCostDayReportMapper {
    /**
     * 新增数据
     *
     * @param record 每日能耗成本表实体
     * @return 影响行数
     */
    int insert(EnergyCostDayReportEntity record);

    /**
     * 根据条件新增数据
     *
     * @param record 每日能耗成本表实体
     * @return 影响行数
     */
    int insertSelective(EnergyCostDayReportEntity record);

    /**
     * 根据主键ID查询
     *
     * @param id 主键
     * @return 查询结果
     */
    EnergyCostDayReportEntity selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 根据主键有选择的更新数据
     *
     * @param record 每日能耗成本表实体
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(EnergyCostDayReportEntity record);

    /**
     * 根据酒店ID和营业日删除数据
     *
     * @param chainId 酒店ID
     * @param accDate 酒店营业日
     * @return 影响行数
     */
    int deleteByChainId(@Param("chainId") Integer chainId, @Param("accDate") Date accDate);

    /**
     * 根据参数有选择的查询每日报表
     *
     * @param energyCostDayReportQueryParam 查询参数
     * @return 每日报表结果集合
     */
    List<EnergyCostDayReportEntity> selectBySelective(EnergyCostDayReportQueryParam energyCostDayReportQueryParam);

    /**
     * 根据参数查询数量
     *
     * @param energyCostDayReportQueryParam 查询参数
     * @return 每日报表数量
     */
    int countBySelective(EnergyCostDayReportQueryParam energyCostDayReportQueryParam);

    /**
     * 查询（匹配有值的字段）
     *
     * @param record 查询对象
     * @return 每日报表集合
     */
    List<EnergyCostDayReportEntity> selectListBySelective(EnergyCostDayReportEntity record);
}