package com.atour.hotel.persistent.energy.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 能耗管理-月底录入查询参数
 *
 * <AUTHOR>
 * @date 2019-08-20
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EndMonthRecordMeterParam implements Serializable {

    /**
     * 酒店id
     **/
    private List<Integer> chainIds;

    /**
     * 登记月份
     **/
    private List<String> recordMonths;

    /**
     * 录入渠道：APP录入；PC补录
     **/
    private String channel;

}
