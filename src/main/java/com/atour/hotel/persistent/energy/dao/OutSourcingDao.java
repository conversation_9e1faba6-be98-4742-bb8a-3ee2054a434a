package com.atour.hotel.persistent.energy.dao;

import com.atour.hotel.persistent.energy.entity.OutBuyMeterEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
@Mapper
public interface OutSourcingDao {

    /**
     * 保存数据
     *
     * @param record
     * @return
     */
    int insertSelective(OutBuyMeterEntity record);

    /**
     * 根据条件查询
     *
     * @param record
     * @return
     */
    OutBuyMeterEntity selectBySelective(OutBuyMeterEntity record);

    /**
     * 返回集合
     *
     * @param record
     * @return
     */
    List<OutBuyMeterEntity> selectListBySelective(OutBuyMeterEntity record);
}
