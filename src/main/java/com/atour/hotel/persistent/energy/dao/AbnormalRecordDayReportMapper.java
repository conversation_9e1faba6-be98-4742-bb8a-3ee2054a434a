package com.atour.hotel.persistent.energy.dao;

import com.atour.hotel.persistent.energy.entity.AbnormalRecordDayReportEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据层：异常记录
 *
 * <AUTHOR>
 * @@date 2019-08-16
 **/

@Mapper
public interface AbnormalRecordDayReportMapper {

    /**
     * 查询（根据主键ID查询）
     **/
    AbnormalRecordDayReportEntity selectByPrimaryKey(@Param("id") Long id);

    /**
     * 删除（根据主键ID删除）
     **/
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加 （匹配有值的字段）
     **/
    int insertSelective(AbnormalRecordDayReportEntity record);

    /**
     * 修改 （匹配有值的字段）
     **/
    int updateByPrimaryKeySelective(AbnormalRecordDayReportEntity record);

    /**
     * 查询（匹配有值的字段）
     **/
    List<AbnormalRecordDayReportEntity> selectBySelective(AbnormalRecordDayReportEntity record);

    /**
     * 报表列表
     *
     * @param chainId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AbnormalRecordDayReportEntity> selectList(@Param("chainId") List<Integer> chainId,
        @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("type") Integer type,
        @Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 报表列表count
     *
     * @param chainId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer selectListCount(@Param("chainId") List<Integer> chainId, @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate, @Param("type") Integer type, @Param("offset") Integer offset,
        @Param("limit") Integer limit);

    /**
     * 删除数据
     *
     * @return
     */
    Integer deleteAll(@Param("chainId") Integer chainId, @Param("currAccDate") LocalDate currAccDate,
        @Param("type") Integer type);
}