package com.atour.hotel.persistent.safety.dao;

import com.atour.hotel.persistent.safety.entity.SafetyCheckConfigEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 
 * 数据层: 巡检配置表
 *
 * <AUTHOR>
 * @@date 2021-09-22
 * 
 **/

@Mapper
public interface SafetyCheckConfigDao {


	/**
	 * 
	 * 查询（根据主键ID查询）
	 * 
	 **/
	SafetyCheckConfigEntity selectByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 删除（根据主键ID删除）
	 * 
	 **/
	int deleteByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 添加 （匹配有值的字段）
	 * 
	 **/
	int insertSelective(SafetyCheckConfigEntity record);

	/**
	 * 
	 * 修改 （匹配有值的字段）
	 * 
	 **/
	int updateByPrimaryKeySelective(SafetyCheckConfigEntity record);

	/**
	 * 
	 * 查询（匹配有值的字段）
	 * 
	 **/
	List<SafetyCheckConfigEntity> selectBySelective(SafetyCheckConfigEntity record);


	/**
	 * 通过酒店ID查询
	 * @param chainId
	 * @return
	 */
	SafetyCheckConfigEntity getByChainId(@Param("chainId") Integer chainId);
}