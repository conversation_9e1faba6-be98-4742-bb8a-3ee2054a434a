package com.atour.hotel.persistent.cost.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CostFromBigData implements CostInf {
    /**
     * 账期
     */
    private String eventMonth;
    /**
     * 项目code
     */
    private String itemCode;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 值
     */
    private BigDecimal value;

    @Override
    public Integer getId() {
        return null;
    }
}
