package com.atour.hotel.persistent.cost.dao;

import com.atour.hotel.persistent.cost.condition.CostCondition;
import com.atour.hotel.persistent.cost.entity.CostEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2020/3/5
 * 成本表
 */
@Repository
public interface CostDAO {

    /**
     * 更新
     *
     * @param costEntity
     */
    void updateByPrimaryKeySelective(CostEntity costEntity);

    /**
     * 通过主键查询
     *
     * @param key
     * @return
     */
    CostEntity getByPrimaryKey(Integer key);

    /**
     * 插入
     *
     * @param costEntity
     */
    void insertSelective(CostEntity costEntity);

    /**
     * 根据账期区间查询成本
     *
     * @param chainIdList     酒店id
     * @param eventMonthStart 账期开始时间
     * @param eventMonthEnd   账期结束时间
     * @param leafCategory    底层分类
     * @return
     */

    List<CostEntity> queryByLeafCategory(@Param("chainIdList") List<Integer> chainIdList,
        @Param("eventMonthStart") String eventMonthStart, @Param("eventMonthEnd") String eventMonthEnd,
        @Param("leafCategory") String leafCategory);

    /**
     * 根据主键查询
     *
     * @param costIds
     * @return
     */
    List<CostEntity> queryByCostIds(@Param("costIds") Set<Integer> costIds);

    /**
     * 删除ids
     *
     * @param costIds
     * @return
     */
    Integer deleteByPrimaryKeys(@Param("costIds") List<Integer> costIds);

    /**
     * 获取第一个月份
     *
     * @return
     */
    String getFirstMonth(@Param("chainId") Integer chainId, @Param("rootCategoryCode") String rootCategoryCode);

    /**
     * 条件查询
     *
     * @param costCondition
     * @return
     */
    List<CostEntity> queryByCondition(CostCondition costCondition);

    /**
     * 查询其他运营冲减
     *
     * @param costCondition
     * @return
     */
    List<CostEntity> queryOtherDeduct(CostCondition costCondition);

    /**
     * 根据均摊周期和二级分类获取成本数据
     *
     * @param chainIdList      酒店ID集合
     * @param shareStart       均摊周期开始时间
     * @param shareEnd         均摊周期结束时间
     * @param categoryCodeList 成本分类
     * @return
     */
    List<CostEntity> getSharedListBySharingPeriod(@Param("chainIdList") List<Integer> chainIdList,
        @Param("shareStart") String shareStart, @Param("shareEnd") String shareEnd,
        @Param("costCodeList") List<String> categoryCodeList);

    /**
     * 通过酒店和月份查询原始成本记录
     * @param chainId 酒店id
     * @param eventMonth 月份
     * @return 酒店月份
     */
    List<CostEntity> queryOriginByChainIdAndMonth(@Param("chainId") Integer chainId, @Param("eventMonth") String eventMonth);

    /**
     * 通过酒店和月份查询成本记录(含冲减)
     * @param chainId 酒店id
     * @param startEventMonth 开始月份（含）
     * @param endEventMonth 结束月份（不含）
     * @return 酒店月份
     */
    List<CostEntity> queryAllByChainIdAndMonthArea(@Param("chainId") Integer chainId, @Param("startEventMonth") String startEventMonth, @Param("endEventMonth") String endEventMonth);

    /**
     * 通过costIdList查询冲减记录
     * @param costIdList 原始成本
     * @return 冲减记录
     */
    List<CostEntity> queryDeductCostByCostIdList(@Param("costIdList") List<Integer> costIdList);

    /**
     * 通过酒店，月份，成本code查询成本
     * @param chainId 酒店id
     * @param eventMonth 月份
     * @param categoryCode 成本code
     * @return 成本
     */
    CostEntity queryOriginByChainMonthAndCategory(@Param("chainId") Integer chainId, @Param("eventMonth") String eventMonth, @Param("categoryCode") String categoryCode);

    /**
     * 通过酒店id，分类查询一定时间内的冲减记录
     * @param chainId 酒店id
     * @param categoryCode 分类code
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 成本列表
     */
    List<CostEntity> queryDeductByChainMonthAreaAndCategory(@Param("chainId") Integer chainId, @Param("categoryCode") String categoryCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 批量新增
     * @param costEntities 成本实体类
     * @return 新增的条数
     */
    Integer insertList(@Param("list") List<CostEntity> costEntities);

    /**
     * 批量更新
     * @param costEntities 成本实体类
     * @return 更新的条数
     */
    Integer updateList(@Param("list") List<CostEntity> costEntities);

    /**
     * 往新表新增数据，已废弃
     * @param costEntity 新增的数据
     */
    void insertToNew(CostEntity costEntity);

    /**
     * 直营店删除导入数据
     * @param chainIdList
     * @param eventMonth
     */
    void deleteList(@Param("chainIdList") Set<Integer> chainId,@Param("eventMonth") String eventMonth);

    /**
     * 直营店更新数据
     * @param chainId
     * @param eventMonth
     * @return
     */
    Integer updateListByChainIdMonth(@Param("chainIdList") Set<Integer> chainId,@Param("eventMonth") String eventMonth);





}