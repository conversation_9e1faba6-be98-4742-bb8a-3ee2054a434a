package com.atour.hotel.persistent.cost.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 成本操作记录
 * @author: 雷欧
 * @date: 2020-03-31 12:05:59
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostOperationLogCondition {

    /**
     * 父类
     */
    @ApiModelProperty("父类编码")
    private String rootCategoryCode;

    /**
     * 酒店id
     */
    @ApiModelProperty("酒店id")
    private Integer chainId;

    @ApiModelProperty("开始时间")
    private String start;

    @ApiModelProperty("结束时间")
    private String end;

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("条数")
    private Integer pageSize;

}
