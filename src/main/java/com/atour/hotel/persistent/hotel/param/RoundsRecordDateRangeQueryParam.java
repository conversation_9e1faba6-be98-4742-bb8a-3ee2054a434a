package com.atour.hotel.persistent.hotel.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 数据层: 查房记录表
 *
 * <AUTHOR>
 * @since 2019/04/08
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoundsRecordDateRangeQueryParam {

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 酒店ID
     */
    private Collection<Integer> chainIdList;

    /**
     * 查询开始营业日
     */
    private Date startAccDate;

    /**
     * 查询结束营业日
     */
    private Date endAccDate;
}