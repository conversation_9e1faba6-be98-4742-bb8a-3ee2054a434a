package com.atour.hotel.persistent.hotel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据层: 查房记录表
 *
 * <AUTHOR>
 * @since 2019/04/08
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoundsRecordEntity {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 营业日
     */
    private Date accDate;

    /**
     * 检查时间
     */
    private Date examineTime;

    /**
     * 检查前房态
     */
    private Integer examineBeforeRoomState;

    /**
     * 检查后房态
     */
    private Integer examineAfterRoomState;

    /**
     * 检查情况(其他问题)
     */
    private String remark;

    /**
     * 检查人ID
     */
    private String examineUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 最大id
     */

    private Integer pageNum;

    /**
     * 每页记录数
     */

    private Integer pageSize;

    /**
     * 日常卫生json DayHealthInfoDTO.class
     */
    private String dayHealthJson;

    /**
     * 周计划卫生json  HealthPlanInfoDTO.class
     */
    private String weekHealthJson;

    /**
     * 月计划卫生json HealthPlanInfoDTO.class
     */
    private String monthHealthJson;

    /**
     * 查房表唯一编号
     */
    private String roundsNo;
    /**
     * 当时分库合并时候加上去的.
     * 生成pkId的地方在https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/ZVBCbYSVkofKluxX9FQcB73xnkh/?preview_type=16
     */
    private Long pkId;
}