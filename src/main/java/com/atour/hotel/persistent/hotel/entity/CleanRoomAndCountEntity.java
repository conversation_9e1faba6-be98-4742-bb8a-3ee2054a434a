package com.atour.hotel.persistent.hotel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据层: 打扫房间表
 *
 * <AUTHOR>
 * @since 2019/04/08
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CleanRoomAndCountEntity {

    /**
     * id
     */
    private Integer id;

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 打扫开始时间
     */
    private Date startTime;

    /**
     * 打扫结束时间
     */
    private Date endTime;

    /**
     * 营业日
     */
    private Date accDate;


    /**
     * 计件房态(打扫前)
     */
    private Integer pieceRoomState;

    /**
     * 打扫后房态
     */
    private Integer afterCleanRoomState;

    /**
     * 计件数量
     */
    private Integer pieceCount;

    /**
     * 计件类型(1:VD 2:V_C 3:OD 4:VDS)
     */
    private Integer pieceType;

    /**
     * 被排房人ID
     */
    private Integer arrangementPeopleId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计件作废标志
     */
    private Boolean cancelFlag;

    /**
     * 计件唯一id
     */
    private String pieceNo;

    /**
     * 计件人ID
     */
    private Integer pieceUserId;

    /**
     * 计件状态
     */
    private Integer pieceStatus;

    /**
     * 排房业务编号
     */
    private String roomArrangementNo;

    /**
     * 打扫时长是否少于结束提醒时长标识 （0.否；1.是）
     */
    private Integer lessRemindTimeState;

}