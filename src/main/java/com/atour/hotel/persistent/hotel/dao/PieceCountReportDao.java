package com.atour.hotel.persistent.hotel.dao;

import com.atour.hotel.persistent.hotel.entity.CleanRoomAndCountEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 数据层: 计件表
 *
 * <AUTHOR>
 * @since 2019/04/08
 */
@Repository
public interface PieceCountReportDao {

    /**
     * chainId OK
     * JOIN OK
     * done
     * 按条件查询被寄件人
     *
     * @param userId      被计件人
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param chainId     酒店id
     * @param pieceStatus 计件确认状态
     * @return 结果集
     */
    List<CleanRoomAndCountEntity> selectCountListPage(@Param("chainId") Integer chainId, @Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime, @Param("userId") String userId, @Param("pieceStatus") Integer pieceStatus,
                                                      @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * chainId OK
     * JOIN OK
     * done
     * 按被计件人和营业日查询
     */
    List<CleanRoomAndCountEntity> listByAccDateAndPieceUserId(@Param("chainId") Integer chainId,
                                                              @Param("pieceStatus") Integer pieceStatus, @Param("accDateSet") Set<Date> accDateSet,
                                                              @Param("pieceUserIdSet") Set<Integer> pieceUserIdSet);

    /**
     * chainId OK
     * JOIN OK
     * done
     * 按被计件人和营业日查询
     */
    List<CleanRoomAndCountEntity> listByChainIdAndPieceUserIdAndAccDate(@Param("chainId") Integer chainId,
                                                                        @Param("pieceStatus") Integer pieceStatus,
                                                                        @Param("pieceUserId") Integer pieceUserId,
                                                                        @Param("accDate") Date accDate);


    /**
     * 按条件统计
     * chainId OK
     * JOIN OK
     * done
     *
     * @param userId      被计件人
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param chainId     酒店id
     * @param pieceStatus 计件确认状态
     * @return 结果集
     */
    Integer selectCount(@Param("chainId") Integer chainId, @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                        @Param("userId") String userId, @Param("pieceStatus") Integer pieceStatus);


}