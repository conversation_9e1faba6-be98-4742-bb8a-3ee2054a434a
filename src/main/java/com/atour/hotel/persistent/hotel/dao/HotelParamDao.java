package com.atour.hotel.persistent.hotel.dao;

import com.atour.hotel.persistent.hotel.entity.ChainAccDateEntity;
import com.atour.hotel.persistent.hotel.param.ChainAccDateParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店属性信息DAO
 *
 * <AUTHOR>
 * @date 2020/9/29
 */
@Mapper
public interface HotelParamDao {

    /**
     * 查询门店营业日
     *
     * @param chainAccDateParam 门店Id清单和合并库ID
     * @return 门店营业日
     */
    List<ChainAccDateEntity> queryAccDateList(ChainAccDateParam chainAccDateParam);
}
