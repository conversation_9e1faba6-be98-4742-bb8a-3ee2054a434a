package com.atour.hotel.persistent.hotel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * hotel-manage-app-api
 *
 * <AUTHOR>
 * @date 2019/8/12
 * 数据层: 酒店客房用品消耗登记表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConsumerGoodsConsumptionEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 客用品ID
     */
    private Integer supplyId;

    /**
     * 剩余数量
     */
    private Integer surplusCount;

    /**
     * 使用数量(预计领用)
     */
    private Integer useCount;

    /**
     * 登记人ID
     */
    private String registerUserId;

    /**
     * 班次
     */
    private String shift;

    /**
     * 营业日
     */
    private Date accDate;

    /**
     * 营业日
     */
    private String accMonth;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开始时间
     */

    private Date beginTime;


    /**
     * 结束时间
     */

    private Date endTime;

    /**
     * 1做房登记 2月度盘点
     * @see com.atour.dicts.enums.inventory.ConsumerGoodsRegisterTypeEnum
     */
    private Integer registerType;
}
