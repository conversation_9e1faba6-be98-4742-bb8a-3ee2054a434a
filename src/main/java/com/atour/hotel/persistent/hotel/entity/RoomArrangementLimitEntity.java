package com.atour.hotel.persistent.hotel.entity;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据层: 排房和房间信息查询参数
 *
 * <AUTHOR>
 * @since 2019/04/16
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomArrangementLimitEntity {

    /**
     * roomNo
     */
    private String roomNo;

    /**
     * 被排房人
     */
    private Integer userId;

    /**
     * 酒店id
     */
    private Integer chainId;

    /**
     * 营业日
     */
    private Date accDate;

    /**
     * 查询条数
     */
    private Integer limit;
}