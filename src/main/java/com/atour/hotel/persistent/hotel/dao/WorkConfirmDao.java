package com.atour.hotel.persistent.hotel.dao;

import com.atour.hotel.persistent.hotel.entity.WorkConfirmEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * 数据层 ：排班表
 * 不要使用id做CRUD.
 */
@Mapper
public interface WorkConfirmDao {

	/**
	 * 查询
	 * chainId OK
	 * JOIN OK
	 * done
	 *
	 * @param workConfirmEntity 实体
	 * @return 实体类
	 */
    List<WorkConfirmEntity> select(WorkConfirmEntity workConfirmEntity);

	/**
	 * 删除
	 * chainId OK
	 * JOIN OK
	 * done
	 * @param record 参数
	 * @return 受影响的行数
	 */
	int deleteByParam(WorkConfirmEntity record);

	/**
	 * 插入
	 * chainId OK
	 * JOIN OK
	 * done
	 * @param entity 实体类
	 * @return 受影响的行数
	 */
	int insert(WorkConfirmEntity entity);

	/**
	 * 查询
	 * chainId OK
	 * JOIN OK
	 * done
	 * @param workConfirmEntity 实体
	 * @return 实体类
	 */
	List<WorkConfirmEntity> selectReport(WorkConfirmEntity workConfirmEntity);

	/**
	 * 查询
	 * chainId OK
	 * JOIN OK
	 * done
	 *
	 * @return 实体类
	 */
	List<WorkConfirmEntity> selectByUserIdList(Integer chainId, Date accDate,List<Integer> userIdList);

}