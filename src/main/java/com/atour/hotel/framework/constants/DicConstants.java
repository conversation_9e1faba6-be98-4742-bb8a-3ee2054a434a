/**
 * 
 */
package com.atour.hotel.framework.constants;

/**
 * <AUTHOR>
 *
 */
public class DicConstants {
	
	/**
     * 品牌CODE
     */
    public static final String BRAND_CODE = "Brand";

    /**
     * 一级来源CODE
     */
    public static final String SOURCE_ID_CODE = "SourceID";

    /**
     * 二级来源CODE
     */
    public static final String SUB_SOURCE_ID_CODE = "SubSourceID";

    /**
     * 储值充值支付方式
     */
    public static final String STORE_VALUE_PAY_METHOD = "StoreValuePayMethod";

    /**
     * 注册来源CODE
     */
    public static final String REGISTER_SOURCE_CODE = "RegistSource";

    /**
     * 优惠券CODE
     */
    public static final String DISCOUNT_TYPE_CODE = "DiscountType";

    /**
     * 证件类型CODE
     */
    public static final String DOC_TYPE_CODE = "DocType";

    /**
     * 会员状态CODE
     */
    public static final String MEB_STATE_CODE = "MebState";

}
