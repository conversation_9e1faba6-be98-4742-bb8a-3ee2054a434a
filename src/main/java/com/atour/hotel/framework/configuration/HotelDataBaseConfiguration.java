package com.atour.hotel.framework.configuration;

import com.atour.db.interceptor.SqlInterceptor;
import com.atour.migrate.helper.datasource.dynamic.SpringHandleDynamicDataSource;
import com.atour.migrate.helper.mybatis.interceptor.MyBatisMigrateChainIdKiller;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.annotation.Resource;

/**
 * 分库的配置
 *
 * <AUTHOR>
 * @date 2018/4/8
 */
@Configuration
@MapperScan(basePackages = "com.atour.hotel.persistent.hotel.dao", sqlSessionTemplateRef = "hotelSqlSessionTemplate")
public class HotelDataBaseConfiguration {
	@Resource
	private SpringHandleDynamicDataSource springHandleDynamicDataSource;
	@Resource
	private MyBatisMigrateChainIdKiller myBatisMigrateChainIdKiller;


	@Bean(name = "hotelSqlSessionFactory")
	public SqlSessionFactory sqlSessionFactory(@Qualifier("sqlInterceptor") SqlInterceptor sqlInterceptor) throws Exception {
		SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
		sqlSessionFactoryBean.setDataSource(springHandleDynamicDataSource);
		sqlSessionFactoryBean.setMapperLocations(
				new PathMatchingResourcePatternResolver().getResources("classpath:mapper/hotel/*.xml"));
		//合并分库增加的拦截 zipkin
		sqlSessionFactoryBean.setPlugins(new Interceptor[]{sqlInterceptor, myBatisMigrateChainIdKiller});
		return sqlSessionFactoryBean.getObject();
	}

    @Bean(name = "hotelSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
        @Qualifier("hotelSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
