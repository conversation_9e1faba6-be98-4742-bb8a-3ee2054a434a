package com.atour.hotel.framework.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 客房管理 DataSource配置
 *
 * <AUTHOR>
 * @date 2019/07/29
 */
@Configuration
@MapperScan(basePackages = "com.atour.hotel.persistent.cem.dao", sqlSessionTemplateRef = "cemSqlSessionTemplate")
public class CemDataBaseConfiguration {

    @Bean(name = "cemDataSource")
    @ConfigurationProperties("spring.datasource.druid.cem")
    public DataSource cemDataSource() {
        return DruidDataSourceBuilder.create()
            .build();
    }

    @Bean(name = "cemSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("cemDataSource") DataSource dataSource, @Qualifier("mybatisConf")
        Resource resource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath:mapper/cem/*.xml"));
        sqlSessionFactoryBean.setConfigLocation(resource);
        return sqlSessionFactoryBean.getObject();
    }

    /**
     * myBatis配置
     *
     * @return 配置文件
     */
    @Bean(name = "mybatisConf")
    @ConditionalOnMissingBean
    public Resource mybatisConf() {
        return new PathMatchingResourcePatternResolver().getResource("classpath:mybatis-config.xml");
    }

    @Bean(name = "cemTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("cemDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cemSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("cemSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


//    @Bean
//    public PageInterceptor pageInterceptor() {
//        PageInterceptor pageInterceptor = new PageInterceptor();
//        Properties properties = new Properties();
//        properties.setProperty("rowBoundsWithCount", "value");
//        properties.setProperty("pageSizeZero", "value");
//        properties.setProperty("reasonable", "value");
//        properties.setProperty("params", "pageNum=pageNo;pageSize=pageSize;");
//        properties.setProperty("supportMethodsArguments", "value");
//        pageInterceptor.setProperties(properties);
//
//        return pageInterceptor;
//    }
}
