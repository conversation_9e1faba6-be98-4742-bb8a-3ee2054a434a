package com.atour.hotel.framework.configuration.es;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.searchbox.action.Action;
import io.searchbox.client.AbstractJestClient;
import io.searchbox.client.JestClient;
import io.searchbox.client.JestResult;
import io.searchbox.client.JestResultHandler;
import io.searchbox.cluster.UpdateSettings;
import io.searchbox.core.*;
import io.searchbox.indices.CreateIndex;
import io.searchbox.indices.DeleteIndex;
import io.searchbox.indices.mapping.DeleteMapping;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.atour.hotel.framework.configuration.es.AtourJestClient.ActionType.READ;
import static com.atour.hotel.framework.configuration.es.AtourJestClient.ActionType.WRITE;


/**
 * <AUTHOR>
 * @Date 2022/4/11 0011
 **/
public class AtourJestClient extends AbstractJestClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(AtourJestClient.class);

    /**
     * 默认的clientName
     **/
    @Value("${atour.es.default-client:es1}")
    private String defaultClientName;

    /**
     * 选择需要写入的client，支持: all、具体的clientName
     **/
    @Value("${atour.es.write-client:all}")
    private String writeClientName;

    /**
     * 选择读取数据的client，支持: 具体的clientName
     **/
    @Value("${atour.es.read-client:es1}")
    private String readClientName;

    private JestClient defaultClient;

    private Map<String, JestClient> clientMap;

    private static List<Class> readClassList = Lists.newArrayList(Search.class, SearchScroll.class);

    private static List<Class> writeClassList = Lists.newArrayList(Bulk.class, CreateIndex.class, DeleteByQuery.class,
            Delete.class, DeleteIndex.class, Update.class, UpdateByQuery.class, UpdateByQueryResult.class, UpdateSettings.class,
            io.searchbox.indices.settings.UpdateSettings.class, DeleteMapping.class);

    public AtourJestClient(Map<String, JestClient> clientMap) {
        this.clientMap = clientMap;
    }

    @PostConstruct
    public void init() {
        defaultClient = clientMap.get(defaultClientName);
        LOGGER.info("clientMap: {}", clientMap);
    }

    private JestClient getReadClient() {
        JestClient client = clientMap.getOrDefault(readClientName, defaultClient);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("尝试通过{}选择ReadClient, 最终选择的nodes: {}", readClientName, client);
        }
        return client;
    }

    private Map<String, JestClient> getWriteClient() {
        HashMap<String, JestClient> resultMap = Maps.newHashMap();
        if("all".equals(writeClientName)) {
            resultMap.putAll(clientMap);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("选择全部的WriteClient");
            }
        } else {
            JestClient client = clientMap.get(writeClientName);
            if(client == null) {
                resultMap.put(defaultClientName, defaultClient);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("通过{}选择WriteClient失败, 选择默认的Client, 最终选择的nodes: {}", writeClientName, defaultClient);
                }
            } else {
                resultMap.put(writeClientName, client);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("尝试通过{}选择WriteClient, 最终选择的nodes: {}", writeClientName, client);
                }            }
        }
        return resultMap;
    }

    private <T extends JestResult> ActionType actionType(Action<T> clientRequest) {
        Class<? extends Action> actionClass = clientRequest.getClass();
        if(readClassList.contains(actionClass)) {
            return READ;
        } else if(writeClassList.contains(actionClass)) {
            return WRITE;
        }
        return READ;
    }

    @Override
    public <T extends JestResult> T execute(Action<T> clientRequest) throws IOException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("进入AtourJestClient的execute方法, 执行类型为：{}", clientRequest.getClass());
        }
        ActionType actionType = actionType(clientRequest);
        if(READ.equals(actionType)) {
            return getReadClient().execute(clientRequest);
        } else if(WRITE.equals(actionType)) {
            T result = null;
            Map<String, JestClient> writeClientMap = getWriteClient();
            for (Map.Entry<String, JestClient> clientEntry : writeClientMap.entrySet()) {
                String key = clientEntry.getKey();
                JestClient client = clientEntry.getValue();
                T r = client.execute(clientRequest);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("选择{}的es集群：{}, 执行结果为: {} {}", key, client, r.isSucceeded(), r.getErrorMessage());
                }
                if(writeClientMap.size() == 1) {
                    // 只选中一个client，则返回它的response
                    result = r;
                } else if(defaultClientName.equals(key)) {
                    // 有多个client时，返回默认client的响应
                    result = r;
                }
            }
            return result;
        }
        throw new RuntimeException("不支持该actionType");
    }

    @Override
    public <T extends JestResult> void executeAsync(Action<T> clientRequest, JestResultHandler<? super T> jestResultHandler) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("进入AtourJestClient的executeAsync方法, 执行类型为：{}", clientRequest.getClass());
        }
        ActionType actionType = actionType(clientRequest);
        if(READ.equals(actionType)) {
            getReadClient().executeAsync(clientRequest, jestResultHandler);
        } else if(WRITE.equals(actionType)) {
            Map<String, JestClient> writeClientMap = getWriteClient();
            for (Map.Entry<String, JestClient> clientEntry : writeClientMap.entrySet()) {
                JestClient client = clientEntry.getValue();
                client.executeAsync(clientRequest, jestResultHandler);
            }
        }
        throw new RuntimeException("不支持该actionType");
    }

    @Override
    public void shutdownClient() {
        for (JestClient client : clientMap.values()) {
            client.hashCode();
        }
    }

    @Override
    public void close() throws IOException {
        for (JestClient client : clientMap.values()) {
            client.close();
        }
    }


    @AllArgsConstructor
    @Getter
    enum ActionType {
        READ("read","读"),
        WRITE("write","写"),
        ;

        private String code;

        private String msg;

    }
}
