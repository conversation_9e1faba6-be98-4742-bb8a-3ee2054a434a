package com.atour.hotel.framework.configuration.bean;

import com.atour.utils.json.JsonUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 门店通 首页轮播图配置
 *
 * <AUTHOR>
 * @date 2019年5月31日20:24:15
 */
public class BannerConfigBean {

    private static final String HOTEL_SYSTEM_DETAIL = "hotel.banner.info";

    @ApolloConfig
    private Config config;

    private List<String> bannerUrlList;

    @PostConstruct
    public void init() {
        reload();
    }

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(HOTEL_SYSTEM_DETAIL)) {
            reload();
        }
    }

    private void reload() {
        String raw = config.getProperty(HOTEL_SYSTEM_DETAIL, StringUtils.EMPTY);
        if (StringUtils.isNotEmpty(raw)) {
            bannerUrlList = JsonUtils.parseList(raw,String.class);
            return;
        }
        bannerUrlList = Lists.newArrayListWithExpectedSize(0);
    }

    /**
     * 获取所有banner图片url地址
     * @return
     */
    public List<String> getAlBannerUrlList() {
        return bannerUrlList;
    }


}
