package com.atour.hotel.framework.configuration;

import com.google.common.base.Predicates;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import static springfox.documentation.builders.PathSelectors.ant;

/**
 * hotel-manage
 *
 * <AUTHOR>
 * @date: 2019年5月24日16:02:12
 * @desc: Swagger2 config
 */
@Configuration
@EnableSwagger2
@ConditionalOnProperty(value = "swagger.enable", havingValue = "true", matchIfMissing = false)
public class Swagger2Configuration {

    @Value("${swagger.enable}")
    private boolean enableSwagger;

    /**
     * 扫描包路径
     */
    @Value("${swagger.package:com.atour.hotel}")
    private String basePackage;
    /**
     * 此组只匹配请求路径
     */
    //    @Value("${swagger.apiurl}")
    private String apiUrl = "/**";


    @Bean
    public Docket oms() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("oms")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage("com.atour.hotel"))
                .paths(ant(apiUrl))
                .build();
    }


    @Bean
    public Docket helloDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("report")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant(apiUrl))
                .build();
    }

    @Bean
    public Docket studyTaskDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("studyTask")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/hotel/sturdyTask/*"))
                .build();
    }

    @Bean
    public Docket gas() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("gas")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/gas/**"))
                .build();
    }

    @Bean
    public Docket electricity() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("electricity")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/electricity/**"))
                .build();
    }

    @Bean
    public Docket other() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("other")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/other/**"))
                .build();
    }


    @Bean
    public Docket sparkle() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("sparkle")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/sparkle/*"),
                        ant("/chainSparkle/*")
                ))
                .build();
    }

    @Bean
    public Docket steam() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("steam")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/steam/**"))
                .build();
    }

    @Bean
    public Docket water() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("water")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/water/**"))
                .build();
    }

    @Bean
    public Docket hotelBase() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("hotelBase")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/hotel/base/**"))
                .build();
    }

    @Bean
    public Docket message() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("messageList")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/sturdyTask/getMessageBarList"))
                .build();
    }

    @Bean
    public Docket messageDetail() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("messageDetail")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/sturdyTask/getStudyTaskDetailList"))
                .build();
    }

    @Bean
    public Docket hotelCostDayReport() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("costDayReport")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/report/costDay/**"))
                .build();
    }

    @Bean
    public Docket accItem() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("mebAccItem")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/payment/returned/report"))
                .build();
    }

    @Bean
    public Docket hotelCostMonthReport() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("costMonthReport")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/energy/report/costMonth/**"))
                .build();
    }

    @Bean
    public Docket appDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("app")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/app/roomTask/*"))
                .build();
    }

    @Bean
    public Docket appUserInfoDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("appUser")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/sysUser/**"))
                .build();
    }

    @Bean
    public Docket healPlanDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("healPlan")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/dkf/healthPlan/**"))
                .build();
    }

    @Bean
    public Docket costDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("cost")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(ant("/app/help/problemList")))
                .build();
    }

    @Bean
    public Docket healPlanReportDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("report-healthPlan")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/dkf/report/healthPlan/**"))
                .build();
    }

    /**
     * @return
     */
    @Bean
    public Docket couponDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("coupon")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/coupon/**"))
                .build();
    }

    @Bean
    public Docket workOrderDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("workOrder")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/app/workOrder/**"))
                .build();
    }

    @Bean
    public Docket costReportDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("costReport")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/cost/report/**"))
                .build();
    }

    @Bean
    public Docket roleDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("role")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/role/**"))
                .build();
    }

    @Bean
    public Docket userAuthDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("userAuth")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/user/queryGrantedElement"))
                .build();
    }

    @Bean
    public Docket fullAuth() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("全员授权")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/app/auth/**"))
                .build();
    }

    @Bean
    public Docket costOnline() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("costOnline")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/cost/**"))
                .build();
    }

    @Bean
    public Docket constructDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("construct")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/app/construct/**"))
                .build();
    }

    @Bean
    public Docket homePageDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("homePage")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/app/homePage/integrateHomePage"))
                .build();
    }

    /**
     * 移动审批消息
     */
    @Bean
    public Docket studyTaskV2Docket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("studyTaskV2")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/sturdyTask/*"))
                .build();
    }

    /**
     * OMS审批明细报表
     */
    @Bean
    public Docket approveDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("approve")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/approve/**"))
                .build();
    }

    /**
     * 业主端六月版
     */
    @Bean
    public Docket ownerJuneDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("owner_june")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/app/roomTypeStatistics/*"),
                        ant("/app/revenue/dailyReport"),
                        ant("/app/user/*"),
                        ant("/app/treasuredBook/*"),
                        ant("/app/ownerBanner/*"),
                        ant("/sysUser/getUserInfoByToken"),
                        ant("/app/homePage/openHint")
                ))
                .build();
    }

    @Bean
    public Docket messageDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("message")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/message/*")
                ))
                .build();
    }

    /**
     * 推送审批
     */
    @Bean
    public Docket pushApproveDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("pushApprove")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/approve/push/**"))
                .build();
    }

    @Bean
    public Docket appPushApproveDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("appPushApprove")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/app/approve/push/**"))
                .build();
    }

    /**
     * 门店自促一期
     *
     * @return
     */
    @Bean
    public Docket chainMarketingDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("chain_marketing")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/api/web/chainMarketingActivity/**"),
                        ant("/api/web/log/**"),
                        ant("/api/web/coupon/publish"),
                        ant("/api/web/coupon/getPublishList"),
                        ant("/api/web/coupon/getPublishDetail"),
                        ant("/api/web/coupon/queryRoomTypeInfo")
                ))
                .build();
    }

    /**
     * 语义分析
     *
     * @return
     */
    @Bean
    public Docket analysisDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("emotion_analysis")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/api/web/ue/appeal/**"),
                        ant("/api/web/ue/analysisErrorFeedback/**"),
                        ant("/api/web/ue/comment/**"),
                        ant("/api/web/ue/comment/negative/**"),
                        ant("/api/web/ue/reform/**")
                ))
                .build();
    }

    /**
     * 黑金消息提醒
     *
     * @return
     */
    @Bean
    public Docket blackGoldRemindDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("black_gold_remind")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/message/blackGoldRemind/**")
                ))
                .build();
    }

    /**
     * 业主端八月版
     *
     * @return
     */
    @Bean
    public Docket owner8thDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("owner8th")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/app/franchiseBill/*")
                ))
                .build();
    }

    /**
     * @return
     */
    @Bean
    public Docket RevenueCost() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("revenueCostItem")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/app/revenue/queryRevenueData"),
                        ant("/app/revenue/queryGOPData")
                ))
                .build();
    }

    @Bean
    public Docket commonDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("common")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/common/**"))
                .build();
    }


    /**
     * 日成本
     */
    @Bean
    public Docket dayCost() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("dayCost")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/app/cost/queryCostStats"),
                        ant("/app/revenue/queryRevenueData"),
                        ant("/app/franchiseBill/detail"),
                        ant("/app/revenue/dailyReport")
                ))
                .build();
    }


    @Bean
    public Docket meetingDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("meeting")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(true)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(ant("/api/web/meeting/room/**"))
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder().title("hotel-manage")
                .description("hotel-manage")
                .contact(new Contact("hotel", "", ""))
                .version("1.0.0")
                .build();
    }


    /**
     * 慧评点评
     */
    @Bean
    public Docket huiPingComment() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("huiPingComment")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/api/huiping/comment/**"),
                        ant("/api/huiping/negative/comment/**")
                ))
                .build();
    }
    /**
     * 团队房订单
     */
    @Bean
    public Docket teamRoomOrder() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("teamRoomOrder")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                .paths(Predicates.or(
                        ant("/api/web/team/room/order/**"),
                        ant("/inner/api/web/team/room/order/**")
                ))
                .build();
    }
    /**
     * 团队房订单
     */
    @Bean
    public Docket taskCenter() {
        return new Docket(DocumentationType.SWAGGER_2)
                //用于分组功能
                .groupName("com.atour.hotel.module.taskcenter")
                //注册整体api信息
                .apiInfo(apiInfo())
                //swagger功能是否启用
                .enable(enableSwagger)
                .select()
                //指定扫描的包
                .apis(RequestHandlerSelectors.basePackage("com.atour.hotel.module.taskcenter"))
                .paths(Predicates.alwaysTrue())
                .build();
    }

}
