package com.atour.hotel.framework.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解: Excel导出
 *
 * <AUTHOR>
 * @date 2019/03/14
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RoleFilterAnnotation {

    /**
     * 权限的key
     */
    String[] key();

}
