package com.atour.hotel.framework.enums;

/**
 * <AUTHOR>
 * @date 2020-09-29
 * 工单详情页的提醒按钮显示状态，只有该页面使用这个枚举
 *
 **/
public enum ShowButtonStatusEnum {
    NO_SHOW(0, "不显示"),
    REMIND(1, "提醒处理"),
    AGAIN_ENABLE(2, "再次提醒(可用)"),
    AGAIN_DISABLE(3, "再次提醒(置灰)");

    private int code;
    private String name;

    private ShowButtonStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ShowButtonStatusEnum getInstance(int value) {
        ShowButtonStatusEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ShowButtonStatusEnum obj = var1[var3];
            if (obj.getCode() == value) {
                return obj;
            }
        }

        return null;
    }

    public static String getInstanceName(int value) {
        ShowButtonStatusEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ShowButtonStatusEnum obj = var1[var3];
            if (obj.getCode() == value) {
                return obj.getName();
            }
        }

        return null;
    }
}
