package com.atour.hotel.framework.apllo.conf;

import com.atour.hotel.framework.apllo.entity.BrokenMorningApolloEntity;
import com.atour.utils.Safes;
import com.atour.utils.json.JsonUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 破晓需要的一些阿波罗配置
 *
 * <AUTHOR>
 * @date 2024/12/9 19:41
 */
@Slf4j
@Component
public class BrokenMorningApolloConf {


    private static final String KEY = "broken_morning.info";


    private static BrokenMorningApolloEntity _INNER_ = new BrokenMorningApolloEntity();

    @ApolloConfig
    private Config config;

    public static List<Integer> getPreChainList() {
        return Safes.of(_INNER_.getPreChainIdList());
    }

    public static List<Long> getCategorizeIdList() {
        return Safes.of(_INNER_.getCategorizeIdList());
    }

    //如果配了-1就是全部酒店.这时候就没有必要再次过滤了.而且这么多不可能全部返回去
    public static Boolean getShouldFilter() {
        return !Safes.of(_INNER_.getPreChainIdList())
                    .contains(-1);
    }

    //如果类别id包括-1代表不限制分类 如果不包含-1就代表查询时候需要加上分类id了
    public static Boolean shouldSyncSku() {
        return !Safes.of(_INNER_.getCategorizeIdList())
                     .contains(-1L);
    }

    public static Boolean shouldSyncCategorize(Long categorizeId) {
        return getCategorizeIdList().contains(categorizeId) || getCategorizeIdList().contains(-1L);
    }


    public static Boolean isPre(Integer chainId) {
        return !getShouldFilter() || getPreChainList().contains(chainId);
    }


    @PostConstruct
    public void startUp() {
        init();
    }

    private void init() {
        String conf = config.getProperty(KEY, StringUtils.EMPTY);
        log.info("init_BrokenMorningApolloConf {}", conf);
        if (StringUtils.isBlank(conf)) {
            _INNER_ = new BrokenMorningApolloEntity();
            return;
        }
        BrokenMorningApolloEntity tmp = _INNER_;
        try {
            tmp = JsonUtils.parseObject(conf, BrokenMorningApolloEntity.class);
        } catch (Exception e) {
            log.error("BrokenMorningApolloConf配置错误 {}", conf);
        }
        _INNER_ = tmp;
        log.info("final_BrokenMorningApolloConf {}", _INNER_);
    }

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent configChangeEvent) {
        if (configChangeEvent.isChanged(KEY)) {
            init();
        }
    }
}
