package com.atour.hotel.framework.mybatis.wrapper;

import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.MapWrapper;

import java.util.Map;

/**
 * MyMapWrapper
 *
 * <AUTHOR>
 * @date 2019-08-05
 */
public class MyMapWrapper extends MapWrapper {

    /**
     * 字母: A
     */
    private static final char A = 'A';

    /**
     * 字母: Z
     */
    private static final char Z = 'Z';

    /**
     * 字母: Z
     */
    private static final String UNDERLINE = "_";

    public MyMapWrapper(MetaObject metaObject, Map<String, Object> map) {
        super(metaObject, map);
    }

    @Override
    public String findProperty(String name, boolean useCamelCaseMapping) {
        if (useCamelCaseMapping && ((name.charAt(0) >= A && name.charAt(0) <= Z) || name.contains(UNDERLINE))) {
            return underlineToCamelHump(name);
        }
        return name;
    }

    /**
     * 将下划线风格替换为驼峰风格
     *
     * @param inputString
     * @return
     */
    public String underlineToCamelHump(String inputString) {
        StringBuilder sb = new StringBuilder();

        boolean nextUpperCase = false;
        for (int i = 0; i < inputString.length(); i++) {
            char c = inputString.charAt(i);
            if (c == '_') {
                if (sb.length() > 0) {
                    nextUpperCase = true;
                }
            } else {
                if (nextUpperCase) {
                    sb.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    sb.append(Character.toLowerCase(c));
                }
            }
        }
        return sb.toString();
    }
}
