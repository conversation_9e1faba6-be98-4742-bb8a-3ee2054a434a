<included>
    <!-- Spring默认日志框架配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 父类的日志根目录 -->
    <property name="LOG_HOME" value="${catalina.base}/logs"/>
    <!-- 全局参数配置 -->
    <include resource="com/yaduo/tomcat/skywalking/logger/logback/base_logback.xml"/>
    <!-- Web日志配置 -->
    <include resource="com/yaduo/tomcat/skywalking/logger/logback/web_logback.xml"/>
    <!-- Feign日志配置 -->
    <include resource="com/yaduo/tomcat/skywalking/logger/logback/feign_logback.xml"/>
    <!-- Biz日志配置 -->
    <include resource="com/yaduo/tomcat/skywalking/logger/logback/biz_logback.xml"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder 默认配置为PatternLayoutEncoder -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </layout>
            <!--<pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%thread][%highlight(%p)][%logger{50}] - %msg%n</pattern>-->
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!--warn级别以上日志-->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.jLog</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <!--如下是新增的配置，解决中文乱码-->
            <jsonFactoryDecorator class="net.logstash.logback.decorate.CharacterEscapesJsonFactoryDecorator">
                <escape>
                    <targetCharacterCode>10</targetCharacterCode>
                    <escapeSequence>\n</escapeSequence>
                </escape>
            </jsonFactoryDecorator>
            <providers>
                <provider class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.TraceIdJsonProvider"/>
                <pattern>
                    <pattern>
                        {
                        "app_id": "${springAppId:-}",
                        "application_name": "${springAppName:-}",
                        "source_host": "%ip",
                        "log_file": "error.log",
                        "time": "%date{\"yyyy-MM-dd HH:mm:ss.SSS\"}",
                        "log_level": "%level",
                        "trace_id": "%tid",
                        "thread_name": "%thread",
                        "logger": "%logger{40}",
                        "content": "%message",
                        "throwable": "%exception{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>error</level>
        </filter>
        <!--日志按天备份-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error.%d{yyyy-MM-dd}.jLog</fileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>64</discardingThreshold>
        <!-- 更改默认的队列的大小,该值会影响性能.默认值为256 -->
        <queueSize>1024</queueSize>
        <!--提取调用者数据,默认为false-->
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ERROR"/>
    </appender>

    <appender name="SLOW_SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/slow.jLog</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/slow.%d{yyyy-MM-dd}.jLog</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <!--如下是新增的配置，解决中文乱码-->
            <jsonFactoryDecorator class="net.logstash.logback.decorate.CharacterEscapesJsonFactoryDecorator">
                <escape>
                    <targetCharacterCode>10</targetCharacterCode>
                    <escapeSequence>\n</escapeSequence>
                </escape>
            </jsonFactoryDecorator>
            <providers>
                <provider class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.TraceIdJsonProvider"/>
                <pattern>
                    <pattern>
                        {
                        "app_id": "${springAppId:-}",
                        "application_name": "${springAppName:-}",
                        "source_host": "%ip",
                        "log_file": "slow.log",
                        "time": "%date{\"yyyy-MM-dd HH:mm:ss.SSS\"}",
                        "log_level": "%level",
                        "trace_id": "%tid",
                        "thread_name": "%thread",
                        "logger": "%logger{40}",
                        "content": "%message",
                        "throwable": "%exception{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    <!--async-->
    <appender name="SLOWSQLAsync" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>
        <queueSize>1500</queueSize>
        <appender-ref ref="SLOW_SQL_FILE"/>
        <neverBlock>true</neverBlock>
    </appender>

    <appender name="job_registry" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/job_registry.jLog</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/job_registry.%d{yyyy-MM-dd}.%i.jLog.gz</FileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>${LOG_MAX_HISTORY:-10}</maxHistory>
            <maxFileSize>${LOG_FILE_MAX_SIZE:-1GB}</maxFileSize>
            <totalSizeCap>${LOG_TOTAL_SIZE_CAP:-10GB}</totalSizeCap>
            <!-- 除按日志记录之外，还配置了日志文件不能超过自定大小，若超过，日志文件会以索引0开始，命名日志文件，例如sys.2018-03-25.0.log -->
            <!--<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
            <!--<maxFileSize>2MB</maxFileSize>-->
            <!--</timeBasedFileNamingAndTriggeringPolicy>-->
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <!--如下是新增的配置，解决中文乱码-->
            <jsonFactoryDecorator class="net.logstash.logback.decorate.CharacterEscapesJsonFactoryDecorator">
                <escape>
                    <targetCharacterCode>10</targetCharacterCode>
                    <escapeSequence>\n</escapeSequence>
                </escape>
            </jsonFactoryDecorator>
            <providers>
                <provider class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.TraceIdJsonProvider"/>
                <pattern>
                    <pattern>
                        {
                        "app_id": "${springAppId:-}",
                        "application_name": "${springAppName:-}",
                        "source_host": "%ip",
                        "log_file": "job_registry.log",
                        "time": "%date{\"yyyy-MM-dd HH:mm:ss.SSS\"}",
                        "log_level": "%level",
                        "trace_id": "%tid",
                        "thread_name": "%thread",
                        "logger": "%logger{40}",
                        "content": "%message",
                        "throwable": "%exception{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!--async-->
    <appender name="job_registry_async" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>
        <queueSize>1500</queueSize>
        <appender-ref ref="job_registry"/>
        <neverBlock>true</neverBlock>
    </appender>


    <appender name="Prewarm" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/prewarm.jLog</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/prewarm.%d{yyyy-MM-dd}.%i.jLog.gz</FileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>${LOG_MAX_HISTORY:-10}</maxHistory>
            <maxFileSize>${LOG_FILE_MAX_SIZE:-1GB}</maxFileSize>
            <totalSizeCap>${LOG_TOTAL_SIZE_CAP:-10GB}</totalSizeCap>
            <!-- 除按日志记录之外，还配置了日志文件不能超过自定大小，若超过，日志文件会以索引0开始，命名日志文件，例如sys.2018-03-25.0.log -->
            <!--<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
            <!--<maxFileSize>2MB</maxFileSize>-->
            <!--</timeBasedFileNamingAndTriggeringPolicy>-->
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <!--如下是新增的配置，解决中文乱码-->
            <jsonFactoryDecorator class="net.logstash.logback.decorate.CharacterEscapesJsonFactoryDecorator">
                <escape>
                    <targetCharacterCode>10</targetCharacterCode>
                    <escapeSequence>\n</escapeSequence>
                </escape>
            </jsonFactoryDecorator>
            <providers>
                <provider class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.TraceIdJsonProvider"/>
                <pattern>
                    <pattern>
                        {
                        "app_id": "${springAppId:-}",
                        "application_name": "${springAppName:-}",
                        "source_host": "%ip",
                        "log_file": "prewarm.log",
                        "time": "%date{\"yyyy-MM-dd HH:mm:ss.SSS\"}",
                        "log_level": "%level",
                        "trace_id": "%tid",
                        "thread_name": "%thread",
                        "logger": "%logger{40}",
                        "content": "%message",
                        "throwable": "%exception{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    <!--async-->
    <appender name="PrewarmAsync" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>
        <queueSize>1500</queueSize>
        <appender-ref ref="Prewarm"/>
        <neverBlock>true</neverBlock>
    </appender>

    <logger name="com.alibaba.druid.filter.stat.StatFilter" level="ERROR" additivity="false">
        <appender-ref ref="SLOWSQLAsync"/>
    </logger>


    <logger name="com.xxl.job.core.thread.ExecutorRegistryThread" level="INFO" additivity="false">
        <appender-ref ref="job_registry_async"/>
    </logger>

    <logger name="com.atour.concurrency.thread.AtourUncaughtExceptionHandler" level="ERROR" additivity="false">
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>

    <logger name="com.atour.web.prewarm" level="DEBUG" additivity="false">
        <appender-ref ref="PrewarmAsync"/>
    </logger>

    <logger name="com.atour.hotel.framework.configuration.es.AtourJestClient" level="info" />

</included>