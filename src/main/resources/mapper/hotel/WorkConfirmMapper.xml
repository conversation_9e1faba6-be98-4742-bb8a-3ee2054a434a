<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.hotel.dao.WorkConfirmDao">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.hotel.entity.WorkConfirmEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="operator_id" jdbcType="INTEGER" property="operatorId"/>
        <result column="chain_id" jdbcType="INTEGER" property="chainId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="work_type" jdbcType="INTEGER" property="workType"/>
        <result column="acc_date" jdbcType="TIMESTAMP" property="accDate"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        operator_id,
        chain_id,
        user_id,
        work_type,
        acc_date
    </sql>

    <delete id="deleteByParam" parameterType="com.atour.hotel.persistent.hotel.entity.WorkConfirmEntity">
        delete
        from dkf_work_confirm
        WHERE
        chain_id=#{chainId}
            <if test="userId != null">
                and user_id=#{userId}
            </if>
            <if test="accDate != null">
                and acc_date=#{accDate}
            </if>
    </delete>


    <insert id="insert" keyProperty="id" parameterType="com.atour.hotel.persistent.hotel.entity.WorkConfirmEntity" useGeneratedKeys="true">
        insert into dkf_work_confirm
        (operator_id,
         chain_id,
         user_id,
         work_type,
         acc_date)
        values
        (#{operatorId,jdbcType=INTEGER}, #{chainId,jdbcType=INTEGER},
         #{userId,jdbcType=INTEGER}, #{workType,jdbcType=INTEGER},
         #{accDate,jdbcType=TIMESTAMP})
    </insert>

    <select id="select" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dkf_work_confirm
        where chain_id = #{chainId}
        <if test="userId != null">
            and user_id=#{userId}
        </if>
        <if test="accDate != null">
            and acc_date=#{accDate}
        </if>
    </select>

    <select id="selectReport" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dkf_work_confirm
        where chain_id = #{chainId}
        <if test="userId != null">
            and user_id=#{userId}
        </if>
        <if test="startTime != null and endTime !=null">
            and acc_date between #{startTime} and #{endTime}
        </if>
    </select>
    <select id="selectByUserIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dkf_work_confirm
        where chain_id = #{chainId}
        <if test="userIdList != null and userIdList.size >0" >
            and user_id in
            <foreach collection="userIdList" index="index" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="accDate != null">
            and acc_date=#{accDate}
        </if>
    </select>
</mapper>