<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.safety.dao.SafetyCheckStatisticsV2Dao">

    <!--实体映射-->
    <resultMap id="safetyCheckStatisticsResultMap"
               type="com.atour.hotel.persistent.safety.entity.SafetyCheckStatisticsV2Entity">
        <!---->
        <id property="id" column="id"/>
        <!--计划ID-->
        <result property="planId" column="plan_id"/>
        <!--酒店ID-->
        <result property="chainId" column="chain_id"/>
        <!--应打点数量-->
        <result property="shouldManageCount" column="should_manage_count"/>
        <!--已打点数量-->
        <result property="hasManageCount" column="has_manage_count"/>
        
        <result property="checkDate" column="check_date"/>
        <!---->
        <result property="createTime" column="create_time"/>
        <!---->
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        id,	plan_id,	chain_id,	should_manage_count,	has_manage_count,	create_time,	update_time,	check_date
    </sql>
    <sql id="TABLE_NAME">
        safety_check_statistics_v2
    </sql>
    <sql id="COMMON">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="TABLE_NAME"/>
        where 1=1
    </sql>

    <!-- 查询（根据主键ID查询） -->
    <select id="selectByPrimaryKey" resultMap="safetyCheckStatisticsResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM safety_check_statistics_v2
        WHERE id = #{id}
    </select>


    <!-- 添加 （匹配有值的字段）-->
    <insert id="insertSelective" parameterType="com.atour.hotel.persistent.safety.entity.SafetyCheckStatisticsV2Entity">
        INSERT INTO safety_check_statistics_v2
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="planId != null">
                plan_id,
            </if>
            <if test="chainId != null">
                chain_id,
            </if>
            <if test="shouldManageCount != null">
                should_manage_count,
            </if>
            <if test="hasManageCount != null">
                has_manage_count,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="checkDate != null">
                check_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id},
            </if>
            <if test="planId!=null">
                #{planId},
            </if>
            <if test="chainId!=null">
                #{chainId},
            </if>
            <if test="shouldManageCount!=null">
                #{shouldManageCount},
            </if>
            <if test="hasManageCount!=null">
                #{hasManageCount},
            </if>
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="updateTime!=null">
                #{updateTime},
            </if>
            <if test="checkDate!=null">
                #{checkDate},
            </if>
        </trim>
    </insert>

    <insert id="batchInsertOrUpdateSelective">
        INSERT INTO safety_check_statistics_v2
            ( plan_id, chain_id, should_manage_count, has_manage_count,check_date)
            values
            <foreach collection="entityList" item="entity" separator=",">
            ( #{entity.planId}, #{entity.chainId},  #{entity.shouldManageCount}, #{entity.hasManageCount}, #{entity.checkDate})
            </foreach>
        ON DUPLICATE KEY UPDATE
        should_manage_count = VALUES(should_manage_count),
        has_manage_count = VALUES(has_manage_count)
    </insert>

    <!-- 修 改（匹配有值的字段）-->
    <update id="updateByPrimaryKeySelective"
            parameterType="com.atour.hotel.persistent.safety.entity.SafetyCheckStatisticsV2Entity">
        UPDATE safety_check_statistics_v2
        <set>
            <if test="planId != null">
                plan_id = #{planId},
            </if>
            <if test="chainId != null">
                chain_id = #{chainId},
            </if>
            <if test="shouldManageCount != null">
                should_manage_count = #{shouldManageCount},
            </if>
            <if test="hasManageCount != null">
                has_manage_count = #{hasManageCount},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="checkDate != null">
                check_date = #{checkDate},
            </if>

        </set>
        WHERE id = #{id}
    </update>

    <!-- 查询（匹配有值的字段）-->
    <select id="selectBySelective" resultMap="safetyCheckStatisticsResultMap"
            parameterType="com.atour.hotel.persistent.safety.entity.SafetyCheckStatisticsV2Entity">
        SELECT
        <include refid="Base_Column_List"/>
        from safety_check_statistics_v2
        <where>
            <if test="planId != null">
                and plan_id = #{planId}
            </if>
            <if test="chainId != null">
                and chain_id = #{chainId}
            </if>
            <if test="shouldManageCount != null">
                and should_manage_count = #{shouldManageCount}
            </if>
            <if test="hasManageCount != null">
                and has_manage_count = #{hasManageCount}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="checkDate != null">
                and check_date = #{checkDate}
            </if>
        </where>
    </select>



    <select id="queryStatisticsInfoCount" resultType="java.lang.Integer">
        select count( distinct ss.chain_id)
        from safety_check_statistics_v2 ss
        left join chain_index_mapping sm on ss.chain_id =sm.chain_id
        left join safety_check_plan sp on ss.plan_id=sp.id
        where 1=1
        <if test="type=1 and type !=null">
            <if test="chainIds != null and chainIds.size()>0">
                and ss.chain_id in
                <foreach collection="chainIds" item="chainId" index="index" open="(" close=")" separator=",">
                    #{chainId}
                </foreach>
            </if>
        </if>
        <if test="type=2 and type !=null">
            <if test="areaIds != null and areaIds.size()>0">
                and ss.area_id in
                <foreach collection="areaIds" item="areaId" index="index" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
            </if>
        </if>
        <if test="startDate != null and endDate != null">
            and ss.plan_id in (select distinct plan_id from safety_check_plan_detail scpd where scpd.create_date BETWEEN #{startDate} and #{endDate})
        </if>
        <if test="planName != null and planName != ''">
            and sp.plan_name like concat(#{planName},'%')
        </if>
    </select>


    <select id="getByChainIdAndPlanId" resultMap="safetyCheckStatisticsResultMap">
        <include refid="COMMON"/>
        and chain_id=#{chainId}
        and plan_id =#{planId}
    </select>

    <select id="selectByCondition" resultMap="safetyCheckStatisticsResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from safety_check_statistics_v2
        <where>
            <if test="planIds != null and planIds.size()>0">
                and plan_id in
                <foreach collection="planIds" item="planId" index="index" open="(" close=")" separator=",">
                    #{planId}
                </foreach>
            </if>
            <if test="chainIds != null and chainIds.size > 0">
                AND chain_id IN
                <foreach collection="chainIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="checkDateBegin != null">
                and check_date >= #{checkDateBegin}
            </if>
            <if test="checkDateEnd != null">
                and #{checkDateEnd} >= check_date
            </if>
        </where>
    </select>
</mapper>