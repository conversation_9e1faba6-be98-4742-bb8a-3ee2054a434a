<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.OptLogDao">

	<!--实体映射-->
	<resultMap id="optLogResultMap" type="com.atour.hotel.persistent.franchise.entity.OptLogEntity">
		<!--ID主键-->
		<id property="id" column="id" />
		<!--模块、日志类型信息(各个业务下的模块分类信息)-->
		<result property="moduleType" column="module_type" />
		<!--不同业务模块的entityId唯一标识-->
		<result property="bizId" column="biz_id" />
		<!--操作人-->
		<result property="operator" column="operator" />
		<!--操作类型-->
		<result property="operatorType" column="operator_type" />
		<!--diff结果内容(包含新增，删除，更新)-->
		<result property="diffContent" column="diff_content" />
		<!--删除标记 0:未删除 1：删除-->
		<result property="deleteFlag" column="delete_flag" />
		<!--创建时间-->
		<result property="createTime" column="create_time" />
		<!--更新时间-->
		<result property="updateTime" column="update_time" />
	</resultMap>


	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,	module_type,	biz_id,	operator,	operator_type,	diff_content,	delete_flag,	create_time,	update_time
	</sql>

	<insert id="insert" parameterType="com.atour.hotel.persistent.franchise.entity.OptLogEntity"
			keyProperty="id" useGeneratedKeys="true">
		INSERT INTO opt_log (module_type, biz_id, operator, operator_type, diff_content)
		VALUES (#{moduleType}, #{bizId}, #{operator}, #{operatorType}, #{diffContent})
	</insert>

    <!-- 添加 （匹配有值的字段）-->
	<insert id="batchInsert" parameterType="com.atour.hotel.persistent.franchise.entity.OptLogEntity">
		INSERT INTO opt_log
		( module_type,
		biz_id,
		operator,
		operator_type,
		diff_content) values
		<foreach collection="entities" item="item" separator="," >
			(#{item.moduleType},
			#{item.bizId},
			#{item.operator},
			#{item.operatorType},
			#{item.diffContent}
			)
		</foreach>
	</insert>

	<!-- 查询（根据bizid列表）-->
	<select id="queryByBizIds" resultMap="optLogResultMap" >
		SELECT
		<include refid="Base_Column_List" />
		from opt_log
		WHERE
		1=1
		<if test="moduleType !=null and moduleType !='' ">
			and module_type = #{moduleType}
		</if>
		<if test="bizIds !=null and bizIds.size() > 0">
			and biz_id IN
			<foreach collection="bizIds" item="item" close=")" open="(" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getLatestBizLogList" resultMap="optLogResultMap">
		select <include refid="Base_Column_List" />
		from opt_log
		where operator_type = '1'
		and module_type = '1'
		and delete_flag = 0
		and biz_id in
		<foreach open="(" close=")" separator="," item="bizIdItem" collection="bizIds">
			#{bizIdItem,jdbcType=VARCHAR}
		</foreach>
	</select>
</mapper>