<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.MaterialFontDAO">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.MaterialFontEntity">
        <!--@mbg.generated-->
        <!--@Table material_font-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="font_name" jdbcType="VARCHAR" property="fontName"/>
        <result column="style" jdbcType="VARCHAR" property="style"/>
        <result column="oss_key" jdbcType="VARCHAR" property="ossKey"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, font_name, `style`, oss_key, create_time, update_time, deleted
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.atour.hotel.persistent.franchise.entity.MaterialFontEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into material_font (font_name, `style`, oss_key)
        values (#{fontName,jdbcType=VARCHAR}, #{style,jdbcType=VARCHAR}, #{ossKey,jdbcType=VARCHAR})
    </insert>

    <select id="selectFont" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from material_font where font_name=#{name,jdbcType=VARCHAR} and style=#{style,jdbcType=VARCHAR}
    </select>
</mapper>