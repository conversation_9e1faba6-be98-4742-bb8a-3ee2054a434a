<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.OwnerCollectDAO">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.OwnerCollectEntity">
        <!--@mbg.generated-->
        <!--@Table owner_collect-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="content_id" jdbcType="INTEGER" property="contentId"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, content_id, `state`, create_time, update_time, deleted
    </sql>
    <insert id="insertOrUpdate">
        insert into owner_collect(user_id, content_id, state)
            value (#{userId,jdbcType=INTEGER}, #{contentId,jdbcType=INTEGER}, #{state,jdbcType=INTEGER})
        on duplicate key update state = #{state,jdbcType=INTEGER}
    </insert>

    <resultMap id="extResultMap" type="com.atour.hotel.persistent.franchise.entity.OwnerTreasuredBookExtEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="sub_title" jdbcType="VARCHAR" property="subTitle"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="listWithPage" resultMap="extResultMap">
        select otb.id, otb.title, otb.sub_title, otb.create_time, otbc.tag
        from owner_collect oc
                 join owner_treasured_book_v2 otb on oc.content_id = otb.id
                 join owner_treasured_book_category otbc on otb.category_id = otbc.id
        where oc.state = 1
          and oc.user_id = #{userId}
          and otb.state = 1
          and otbc.deleted = 0
        order by otb.create_time desc
        limit #{offset}, #{limit}
    </select>
    <select id="countWithPage" resultType="int">
        select count(*)
        from owner_collect oc
                 join owner_treasured_book_v2 otb on oc.content_id = otb.id
                 join owner_treasured_book_category otbc on otb.category_id = otbc.id
        where oc.state = 1
          and oc.user_id = #{userId}
          and otb.state = 1
          and otbc.deleted = 0
    </select>

    <select id="countByContentId" resultType="int">
        select count(*)
        from owner_collect
        where user_id = #{userId}
          and content_id = #{contentId}
          and state = 1
    </select>
    <select id="selectByContentId" resultType="java.lang.Integer">
        select content_id
        from owner_collect
        where user_id = #{userId}
          and state = 1
          and content_id in
          <foreach collection="contentIds" separator="," open="(" close=")" item="contentId">
            #{contentId, jdbcType=INTEGER}
          </foreach>

    </select>
</mapper>