<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.OwnerTreasuredBookDAO">
  <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.OwnerTreasuredBookEntity">
    <!--@mbg.generated-->
    <!--@Table owner_treasured_book_v2-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
<!--    <result column="version" jdbcType="INTEGER" property="version" />-->
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
<!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
<!--    <result column="deleted" jdbcType="TINYINT" property="deleted" />-->
<!--    <result column="sorted_no" jdbcType="BIGINT" property="sortedNo"/>-->
<!--    <result column="is_top" jdbcType="TINYINT" property="isTop"/>-->
<!--    <result column="is_bottom" jdbcType="TINYINT" property="isBottom"/>-->
<!--    <result column="brand" jdbcType="TINYINT" property="brand"/>-->
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, category_id, `state`, title, sub_title, content, version, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from owner_treasured_book_v2
    where id = #{id,jdbcType=INTEGER} and state = 1
  </select>

    <resultMap id="extResultMap" type="com.atour.hotel.persistent.franchise.entity.OwnerTreasuredBookExtEntity" extends="BaseResultMap">
      <result column="tag" property="tag" jdbcType="VARCHAR"/>
      <result column="roleId" property="roleId" jdbcType="INTEGER"/>
      <result column="product" property="product" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectWithPage" resultMap="extResultMap">
      select otb.id, otb.title, otb.sub_title, otb.create_time, otbc.tag
      from owner_treasured_book_v2 otb
      join owner_treasured_book_category otbc on otb.category_id = otbc.id
      where
      <if test="categoryIds != null and categoryIds.size() > 0">
        otb.category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
            #{categoryId,jdbcType=INTEGER}
        </foreach>
      </if>
      and otb.state = 1 and otbc.deleted = 0
      order by otb.sorted_no
      limit #{offset}, #{limit}
    </select>

    <select id="countWithPage" resultType="int">
      select count(*) from owner_treasured_book_v2 otb
      join owner_treasured_book_category otbc on otb.category_id = otbc.id
      where
      <if test="categoryIds != null and categoryIds.size() > 0">
        otb.category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
            #{categoryId,jdbcType=INTEGER}
      </foreach>
        and otb.state = 1 and otbc.deleted = 0
      </if>
    </select>

  <select id="countWithPageV2" resultType="int">
    select count(distinct otb.id)
    from owner_treasured_book_v2 otb
    join owner_treasured_book_category otbc on otb.category_id = otbc.id
    left join owner_treasured_book_role otbr on otbr.book_id = otb.id
    <if test="products != null and products.size() != 0">
      left join owner_treasured_book_product otbp on otbp.book_id = otb.id
    </if>
    <where>
      <if test="categoryIds != null and categoryIds.size() > 0">
        otb.category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
          #{categoryId,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="products == null and products.size() == 0">
        and otb.brand != 0
      </if>
      <if test="products != null and products.size() != 0">
        and (otbp.product in <foreach collection="products" item="product" open="(" close=")" separator=",">
        #{product,jdbcType=VARCHAR}
      </foreach>)
      </if>
      <if test="brandIds!=null and brandIds.size() > 0">
        and otb.brand in
        <foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
          #{brandId,jdbcType=TINYINT}
        </foreach>
      </if>
      and otbr.role_id in
      <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
        #{roleId,jdbcType=TINYINT}
      </foreach>
      and otb.state = 1 and otbc.deleted = 0
    </where>
  </select>


  <select id="selectWithPageV2" resultMap="extResultMap">
    select otb.id, otb.title, otb.sub_title, otb.create_time, otbc.tag,otb.category_id,
<!--    otb.sorted_no,otb.is_top,otb.is_bottom, otb.brand,-->
    otbr.role_id as roleId
    <if test="products != null and products.size() != 0">
      , otbp.product
    </if>
    from owner_treasured_book_v2 otb
    join owner_treasured_book_category otbc on otb.category_id = otbc.id
    left join owner_treasured_book_role otbr on otbr.book_id = otb.id
    <if test="products != null and products.size() != 0">
      left join owner_treasured_book_product otbp on otbp.book_id = otb.id
    </if>
    <where>
      <if test="categoryIds != null and categoryIds.size() > 0">
        otb.category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
          #{categoryId,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="products == null and products.size() == 0">
        and otb.brand != 0
      </if>
      <if test="products != null and products.size() != 0">
        and (otbp.product in <foreach collection="products" item="product" open="(" close=")" separator=",">
            #{product,jdbcType=VARCHAR}
      </foreach>)
      </if>
      <if test="brandIds!=null and brandIds.size() > 0">
        and otb.brand in
        <foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
          #{brandId,jdbcType=TINYINT}
        </foreach>
      </if>
      and otbr.role_id in
      <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
        #{roleId,jdbcType=TINYINT}
      </foreach>
      and otb.state = 1 and otbc.deleted = 0
    </where>
    group by otb.id, otb.sorted_no
    order by otb.sorted_no
    limit #{offset}, #{limit}
  </select>


</mapper>