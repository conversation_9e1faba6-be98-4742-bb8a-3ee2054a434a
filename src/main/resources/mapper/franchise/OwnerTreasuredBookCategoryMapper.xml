<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.OwnerTreasuredBookCategoryDAO">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.OwnerTreasuredBookCategoryEntity">
      <!--@mbg.generated-->
      <!--@Table owner_treasured_book_category-->
      <id column="id" jdbcType="INTEGER" property="id" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="tag" jdbcType="VARCHAR" property="tag" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
      <!--@mbg.generated-->
      id, `name`, tag, create_time, update_time, deleted
    </sql>

    <select id="selectAllValid" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from owner_treasured_book_category
      where deleted=0
    </select>

    <insert id="insert">
        insert into owner_treasured_book_category (name, tag) values (#{name,jdbcType=VARCHAR},#{tag,jdbcType=VARCHAR})
    </insert>
</mapper>