<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.CheckRoomConfigTypeDao">

    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="configTypeValue" column="config_type_value" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUserId" column="create_user_id" jdbcType="INTEGER"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateUserId" column="update_user_id" jdbcType="INTEGER"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,config_type_value,create_time,
        update_time,create_user_id,create_user_name,
        update_user_id,update_user_name,deleted,
        version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from check_room_config_type
        where id = #{id,jdbcType=BIGINT} and deleted=0
    </select>


    <select id="selectBatchByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from check_room_config_type
        where deleted=0 and id in
        <foreach open="(" close=")" separator="," collection="idList" item="idItem">
            #{idItem}
        </foreach>
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from check_room_config_type
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity"
            useGeneratedKeys="true">
        insert into check_room_config_type
        ( id, config_type_value, create_time
        , update_time, create_user_id, create_user_name
        , update_user_id, update_user_name, deleted
        , version)
        values ( #{id,jdbcType=BIGINT}, #{configTypeValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER}, #{createUserName,jdbcType=VARCHAR}
               , #{updateUserId,jdbcType=INTEGER}, #{updateUserName,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}
               , #{version,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity"
            useGeneratedKeys="true">
        insert into check_room_config_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="configTypeValue != null">config_type_value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createUserName != null">create_user_name,</if>
            <if test="updateUserId != null">update_user_id,</if>
            <if test="updateUserName != null">update_user_name,</if>
            <if test="deleted != null">deleted,</if>
            <if test="version != null">version,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="configTypeValue != null">#{configTypeValue,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createUserId != null">#{createUserId,jdbcType=INTEGER},</if>
            <if test="createUserName != null">#{createUserName,jdbcType=VARCHAR},</if>
            <if test="updateUserId != null">#{updateUserId,jdbcType=INTEGER},</if>
            <if test="updateUserName != null">#{updateUserName,jdbcType=VARCHAR},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="version != null">#{version,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity">
        update check_room_config_type
        <set>
            <if test="configTypeValue != null">
                config_type_value = #{configTypeValue,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=INTEGER},
            </if>
            <if test="updateUserName != null">
                update_user_name = #{updateUserName,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.atour.hotel.persistent.franchise.entity.CheckRoomConfigTypeEntity">
        update check_room_config_type
        set config_type_value = #{configTypeValue,jdbcType=VARCHAR},
            create_time       = #{createTime,jdbcType=TIMESTAMP},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            create_user_id    = #{createUserId,jdbcType=INTEGER},
            create_user_name  = #{createUserName,jdbcType=VARCHAR},
            update_user_id    = #{updateUserId,jdbcType=INTEGER},
            update_user_name  = #{updateUserName,jdbcType=VARCHAR},
            deleted           = #{deleted,jdbcType=TINYINT},
            version           = #{version,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="countCheckRoomConfigTypeByValue" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from check_room_config_type
        where config_type_value = #{configTypeValue,jdbcType=VARCHAR}
          and deleted = 0
    </select>

    <select id="queryConfigTypeEntityList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from check_room_config_type where deleted =0
        order  by  create_time desc
    </select>
</mapper>
