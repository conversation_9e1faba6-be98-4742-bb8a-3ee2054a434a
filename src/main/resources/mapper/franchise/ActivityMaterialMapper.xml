<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.franchise.dao.ActivityMaterialDAO">
  <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.franchise.entity.CouponActivityMaterialEntity">
    <!--@mbg.generated-->
    <!--@Table activity_material-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, activity_id, template_id, `state`, url, create_time, update_time, deleted
  </sql>
  <update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.franchise.entity.CouponActivityMaterialEntity">
    <!--@mbg.generated-->
    update activity_material
    <set>
      <if test="state != null">
        `state` = #{state,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
    <insert id="batchInsert">
      insert into activity_material(activity_id,template_id) values
      <foreach collection="list" item="item" separator=",">
        (#{item.activityId,jdbcType=INTEGER}, #{item.templateId,jdbcType=INTEGER})
      </foreach>
    </insert>
  <select id="selectByActivityId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from activity_material where activity_id=#{activityId} and deleted=0
  </select>
  <insert id="batchInsertOrUpdate">
    insert into activity_material values
    <foreach collection="list" item="item" separator=",">
      (#{item.activityId,jdbcType=INTEGER}, #{item.templateId,jdbcType=INTEGER})
    </foreach>
    on duplicate key update
    deleted = 0
  </insert>
  <resultMap id="extResultMap" type="com.atour.hotel.persistent.franchise.entity.CouponActivityMaterialExtEntity" extends="BaseResultMap">
    <result column="type" property="type"/>
  </resultMap>
  <select id="selectExtByActivityId" resultMap="extResultMap">
    select cam.id, cam.activity_id, cam.template_id, cam.url, cam.state, mt.type from material_template mt join activity_material cam on mt.id = cam.template_id
    where cam.activity_id=#{activityId} and cam.deleted=0
    order by cam.id
  </select>

  <select id="selectNotHandled" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from activity_material where state=1 and deleted=0 and id > #{id}
    limit #{limit}
  </select>

  <select id="selectByPrimaryKeys" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from activity_material where deleted=0 and id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id,jdbcType=INTEGER}
    </foreach>
    order by id
  </select>
</mapper>