<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.cem.dao.UeNegativeTypeRelationMapper">

	<!--实体映射-->
	<resultMap id="ueNegativeTypeRelationResultMap" type="com.atour.hotel.persistent.cem.entity.UeNegativeTypeRelationEntity">
		<!---->
		<id property="id" column="id" />
		<!--差评ID-->
		<result property="commentId" column="comment_id" />
		<!--差评类型ID-->
		<result property="typeId" column="type_id" />
		<!--酒店ID-->
		<result property="chainId" column="chain_id" />
		<!--来源类型-->
		<result property="sourceType" column="source_type" />
		<!--差评时间-->
		<result property="negativeTime" column="negative_time" />
		<!---->
		<result property="createTime" column="create_time" />
	</resultMap>


	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,	comment_id,	type_id,	chain_id,	source_type,	negative_time,	create_time
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="ueNegativeTypeRelationResultMap" parameterType="java.lang.Integer">
		SELECT
		<include refid="Base_Column_List" />
		FROM ue_negative_type_relation
		WHERE id = #{id}
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		 DELETE FROM ue_negative_type_relation
		 WHERE id = #{id}
	</delete>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByCommentId" parameterType="java.lang.Long">
		 DELETE FROM ue_negative_type_relation
		 WHERE comment_id = #{commentId}
	</delete>

	<!-- 添加 （匹配有值的字段）-->
	<insert id="insertSelective" parameterType="com.atour.hotel.persistent.cem.entity.UeNegativeTypeRelationEntity">
		INSERT INTO ue_negative_type_relation
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="commentId != null">
				comment_id,
			</if>
			<if test="typeId != null">
				type_id,
			</if>
			<if test="chainId != null">
				chain_id,
			</if>
			<if test="sourceType != null">
				source_type,
			</if>
			<if test="negativeTime != null">
				negative_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id!=null">
				#{id},
			</if>
			<if test="commentId!=null">
				#{commentId},
			</if>
			<if test="typeId!=null">
				#{typeId},
			</if>
			<if test="chainId!=null">
				#{chainId},
			</if>
			<if test="sourceType!=null">
				#{sourceType},
			</if>
			<if test="negativeTime!=null">
				#{negativeTime},
			</if>
			<if test="createTime!=null">
				#{createTime},
			</if>
		</trim>
	</insert>

	<!-- 修 改（匹配有值的字段）-->
	<update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.cem.entity.UeNegativeTypeRelationEntity">
		UPDATE ue_negative_type_relation
		<set>
			<if test="commentId != null">
				comment_id = #{commentId},
			</if>
			<if test="typeId != null">
				type_id = #{typeId},
			</if>
			<if test="chainId != null">
				chain_id = #{chainId},
			</if>
			<if test="sourceType != null">
				source_type = #{sourceType},
			</if>
			<if test="negativeTime != null">
				negative_time = #{negativeTime},
			</if>
			<if test="createTime != null">
				create_time = #{createTime},
			</if>

		</set>
		WHERE id = #{id}
	</update>

	<!-- 查询（匹配有值的字段）-->
	<select id="selectBySelective" resultMap="ueNegativeTypeRelationResultMap" parameterType="com.atour.hotel.persistent.cem.entity.UeNegativeTypeRelationEntity">
		SELECT
		<include refid="Base_Column_List" />
		from ue_negative_type_relation
		<where>
			<if test="commentId != null">
				and comment_id = #{commentId}
			</if>
			<if test="typeId != null">
				and type_id = #{typeId}
			</if>
			<if test="chainId != null">
				and chain_id = #{chainId}
			</if>
			<if test="sourceType != null">
				and source_type = #{sourceType}
			</if>
			<if test="negativeTime != null">
				and negative_time = #{negativeTime}
			</if>
			<if test="createTime != null">
				and create_time = #{createTime}
			</if>
		</where>
	</select>

	<!-- 查询（匹配有值的字段）-->
	<select id="groupByTypeId" resultType="com.atour.hotel.module.ue.manage.comment.sql.result.NegativeTypeGroupResult" parameterType="com.atour.hotel.module.ue.manage.comment.sql.param.NegativeTypeGroupParam">
		SELECT
		type_id as negativeType, count(1) count
		from ue_negative_type_relation
		<where>
			<if test="sourceType != null">
				and source_type = #{sourceType}
			</if>
			<if test="chainIdList != null and chainIdList.size() > 0">
				AND chain_id IN
				<foreach collection="chainIdList" index="index" item="chainId" open="(" separator="," close=")">
					#{chainId}
				</foreach>
			</if>
			<if test="beginTime != null">
				and negative_time &gt;= #{beginTime}
			</if>
			<if test="endTime != null">
				and negative_time &lt;= #{endTime}
			</if>
			group by type_id
		</where>
	</select>

	<select id="countByCommentId" resultType="int">
		select count(*) from ue_negative_type_relation where comment_id in
		<foreach collection="commentIds" open="(" close=")" separator="," item="commentId">
			#{commentId}
		</foreach>
	</select>

</mapper>