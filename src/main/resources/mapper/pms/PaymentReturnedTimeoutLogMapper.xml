<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.pms.dao.PaymentReturnedTimeoutLogDAO">

    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.pms.entity.PaymentReturnedTimeoutLogEntity">
        <id column="id"  property="id" />
        <result column="chain_id"  property="chainId" />
        <result column="acc_date"  property="accDate" />
        <result column="num"  property="num" />
        <result column="type"  property="type" />
        <result column="trans_amount"  property="transAmount" />
        <result column="create_time"  property="createTime" />
        <result column="update_time"  property="updateTime" />
        <result column="deleted"  property="deleted" />
        <result column="extra_content"  property="extraContent" />
    </resultMap>

    <insert id="insert" parameterType="com.atour.hotel.persistent.pms.entity.PaymentReturnedTimeoutLogEntity">
        INSERT  INTO payment_returned_timeout_log
        (chain_id, acc_date, num, type, trans_amount, create_time, update_time, deleted, extra_content) values
        (#{chainId}, #{accDate}, #{num}, #{type}, #{transAmount}, #{createTime}, #{updateTime}, #{deleted}, #{extraContent})
    </insert>

    <select id="selectByChainIdList" resultMap="BaseResultMap">
        select * from payment_returned_timeout_log where chain_id in
        <foreach collection="chainIds" item="chainId" open="(" close=")"  separator="," >
            #{chainId}
        </foreach>
        and  acc_date = #{accDate} and deleted = 0
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO payment_returned_timeout_log
        (chain_id, acc_date, num, type, trans_amount, create_time, update_time, deleted, extra_content)
        VALUES
        <foreach collection="entityList" separator="," item="entity">
            (#{entity.chainId}, #{entity.accDate}, #{entity.num}, #{entity.type}, #{entity.transAmount}, #{entity.createTime}, #{entity.updateTime}, #{entity.deleted},
            #{entity.extraContent})
        </foreach>
    </insert>

    <update id="deleteByCreateTime" >
        update payment_returned_timeout_log set deleted =  1, update_time = now() where create_time &gt; #{start} and create_time &lt; #{end}
    </update>

</mapper>