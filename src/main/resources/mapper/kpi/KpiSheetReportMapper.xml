<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.kpi.dao.KpiSheetReportMapper">

    <!--实体映射-->
    <resultMap id="kpiSheetReportResultMap" type="com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity">
        <!--主键id-->
        <id property="id" column="id"/>
        <!--实体类型	 1-门店；2-区域；3-员工-->
        <result property="periodType" column="period_type"/>
        <!--周期值 2021；1-上半年，2-下半年；1-1季度；1-1月；1-第1周-->
        <result property="periodVal" column="period_val"/>
        <!--周期值-年-->
        <result property="periodYear" column="period_year"/>
        <!--城市id-->
        <result property="cityId" column="city_id"/>
        <!--区域-->
        <result property="areaId" column="area_id"/>
        <!--实体类型-->
        <result property="entityType" column="entity_type"/>
        <!--实体ID-->
        <result property="entityId" column="entity_id"/>
        <!--实体信息快照-->
        <result property="entityJson" column="entity_json"/>
        <!--指标设置JSON快照-->
        <result property="infoJson" column="info_json"/>
        <!--0-有效；1-已删除	-->
        <result property="deleted" column="deleted"/>
        <!--创建时间-->
        <result property="createTime" column="create_time"/>
        <!--修改时间-->
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		id,		period_type,	period_val,	period_year,	city_id,	area_id,	entity_type,	entity_id,	entity_json,	info_json,		deleted,	create_time,	update_time
	</sql>

    <!-- 查询（根据主键ID查询） -->
    <select id="selectByPrimaryKey" resultMap="kpiSheetReportResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM kpi_sheet_report
        WHERE id = #{id}
    </select>

    <!--删除：根据主键ID删除-->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		 DELETE FROM kpi_sheet_report
		 WHERE id = #{id}
	</delete>
    <update id="deleteByIdList">
        update kpi_sheet_report set deleted = 1
        where id in
        <foreach collection="ids" separator="," open="(" close=")" item="idItem">
            #{idItem}
        </foreach>
    </update>

    <!-- 添加 （匹配有值的字段）-->
    <insert id="insertSelective" parameterType="com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity">
        INSERT INTO kpi_sheet_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="periodType != null">
                period_type,
            </if>
            <if test="periodVal != null">
                period_val,
            </if>
            <if test="periodYear != null">
                period_year,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="entityType != null">
                entity_type,
            </if>
            <if test="entityId != null">
                entity_id,
            </if>
            <if test="entityJson != null">
                entity_json,
            </if>
            <if test="infoJson != null">
                info_json,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id},
            </if>
            <if test="periodType!=null">
                #{periodType},
            </if>
            <if test="periodVal!=null">
                #{periodVal},
            </if>
            <if test="periodYear!=null">
                #{periodYear},
            </if>
            <if test="cityId!=null">
                #{cityId},
            </if>
            <if test="areaId!=null">
                #{areaId},
            </if>
            <if test="entityType!=null">
                #{entityType},
            </if>
            <if test="entityId!=null">
                #{entityId},
            </if>
            <if test="entityJson!=null">
                #{entityJson},
            </if>
            <if test="infoJson!=null">
                #{infoJson},
            </if>
            <if test="deleted!=null">
                #{deleted},
            </if>
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="updateTime!=null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <!-- 修 改（匹配有值的字段）-->
    <update id="updateByPrimaryKeySelective"
            parameterType="com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity">
        UPDATE kpi_sheet_report
        <set>
            <if test="periodType != null">
                period_type = #{periodType},
            </if>
            <if test="periodVal != null">
                period_val = #{periodVal},
            </if>
            <if test="periodYear != null">
                period_year = #{periodYear},
            </if>
            <if test="cityId != null">
                city_id = #{cityId},
            </if>
            <if test="areaId != null">
                area_id = #{areaId},
            </if>
            <if test="entityType != null">
                entity_type = #{entityType},
            </if>
            <if test="entityId != null">
                entity_id = #{entityId},
            </if>
            <if test="entityJson != null">
                entity_json = #{entityJson},
            </if>
            <if test="infoJson != null">
                info_json = #{infoJson},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>

        </set>
        WHERE id = #{id}
    </update>

    <!-- 查询（匹配有值的字段）-->
    <select id="selectBySelective" resultMap="kpiSheetReportResultMap"
            parameterType="com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity">
        SELECT
        <include refid="Base_Column_List"/>
        from kpi_sheet_report
        <where>
            <if test="periodType != null">
                and period_type = #{periodType}
            </if>
            <if test="periodVal != null">
                and period_val = #{periodVal}
            </if>
            <if test="periodYear != null">
                and period_year = #{periodYear}
            </if>
            <if test="cityId != null">
                and city_id = #{cityId}
            </if>
            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
            <if test="entityType != null">
                and entity_type = #{entityType}
            </if>
            <if test="entityId != null">
                and entity_id = #{entityId}
            </if>
            <if test="entityJson != null">
                and entity_json = #{entityJson}
            </if>
            <if test="infoJson != null">
                and info_json = #{infoJson}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <!-- 查询（匹配有值的字段）-->
    <select id="selectIdBySelective" resultType="java.lang.Integer"
            parameterType="com.atour.hotel.persistent.kpi.entity.KpiSheetReportEntity">
        SELECT
        id
        from kpi_sheet_report
        <where>
            <if test="periodType != null">
                and period_type = #{periodType}
            </if>
            <if test="periodVal != null">
                and period_val = #{periodVal}
            </if>
            <if test="periodYear != null">
                and period_year = #{periodYear}
            </if>
            <if test="cityId != null">
                and city_id = #{cityId}
            </if>
            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
            <if test="entityType != null">
                and entity_type = #{entityType}
            </if>
            <if test="entityId != null">
                and entity_id = #{entityId}
            </if>
            <if test="entityJson != null">
                and entity_json = #{entityJson}
            </if>
            <if test="infoJson != null">
                and info_json = #{infoJson}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <select id="selectByPeriodYearAndEntityList"
            parameterType="com.atour.hotel.persistent.kpi.param.QueryKpiSheetItemReportParam"
            resultMap="kpiSheetReportResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from kpi_sheet_report
        where period_year = #{year}
        and entity_type = #{entityType}
        and entity_id in
        <foreach collection="entityIdList" open="(" separator="," close=")" item="entityIdItem">
            #{entityIdItem}
        </foreach>
    </select>
    <select id="selectByParamAndEntityList"
            parameterType="com.atour.hotel.persistent.kpi.param.QueryKpiSheetItemReportParam"
            resultMap="kpiSheetReportResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from kpi_sheet_report
        where period_year = #{year}
        and period_type = #{periodType}
        and period_val = #{periodVal}
        and entity_type = #{entityType}
        <if test="entityIdList !=null and entityIdList.size >0">
        and entity_id in
        <foreach collection="entityIdList" open="(" separator="," close=")" item="entityIdItem">
            #{entityIdItem}
        </foreach>
        </if>
    </select>
    <select id="queryListByPage" resultMap="kpiSheetReportResultMap"
            parameterType="com.atour.hotel.persistent.kpi.param.QueryKpiReportListParam">
        select
        <include refid="Base_Column_List"/>
        from kpi_sheet_report
        <where>
            <if test="yearParam.entityType != null">
                and entity_type =#{yearParam.entityType}
            </if>
            <if test="yearParam.entityValList != null and yearParam.entityValList.size >0">
                and entity_id in
                <foreach collection="yearParam.entityValList" item="entityIdItem" separator="," open="(" close=")">
                    #{entityIdItem}
                </foreach>
            </if>
            <if test="yearParam.minPeriodVal != null and yearParam.maxPeriodVal != null">
                and period_val between #{yearParam.minPeriodVal} and #{yearParam.maxPeriodVal}
            </if>
            <if test="yearParam.periodType != null">
                and period_type = #{yearParam.periodType}
            </if>
            <if test="yearParam.year != null">
                and period_year = #{yearParam.year}
            </if>
            <if test="toYearParam != null and toYearParam.entityType != null and toYearParam.entityValList != null and toYearParam.entityValList.size >0 and toYearParam.minPeriodVal != null and toYearParam.maxPeriodVal != null">
                or ( entity_type =#{toYearParam.entityType}
                and entity_id in
                <foreach collection="toYearParam.entityValList" item="entityIdItem" separator="," open="(" close=")">
                    #{entityIdItem}
                </foreach>
                and period_val between #{toYearParam.minPeriodVal} and #{toYearParam.maxPeriodVal}
                and period_year = #{toYearParam.year}
                and period_type = #{toYearParam.periodType}
                )
            </if>
            order by id desc
            <if test="yearParam.pageNo != null and yearParam.pageSize != null">
                limit #{yearParam.pageNo},#{yearParam.pageSize}
            </if>
        </where>
    </select>

    <select id="queryCountByParam" resultType="java.lang.Long"
            parameterType="com.atour.hotel.persistent.kpi.param.QueryKpiReportListParam">
        select
        count(1)
        from kpi_sheet_report
        <where>
            <if test="yearParam.entityType != null">
                and entity_type =#{yearParam.entityType}
            </if>
            <if test="yearParam.entityValList != null and yearParam.entityValList.size >0">
                and entity_id in
                <foreach collection="yearParam.entityValList" item="entityIdItem" open="(" close=")" separator=",">
                    #{entityIdItem}
                </foreach>
            </if>
            <if test="yearParam.minPeriodVal != null and yearParam.maxPeriodVal != null">
                and period_val between #{yearParam.minPeriodVal} and #{yearParam.maxPeriodVal}
            </if>
            <if test="yearParam.year != null">
                and period_year = #{yearParam.year}
            </if>
            <if test="yearParam.periodType != null">
                and period_type = #{yearParam.periodType}
            </if>
            <if test="toYearParam != null and toYearParam.entityType != null and toYearParam.entityValList != null and toYearParam.entityValList.size >0 and toYearParam.minPeriodVal != null and toYearParam.maxPeriodVal != null">
                or (
                entity_type =#{toYearParam.entityType}
                and entity_id in
                <foreach collection="toYearParam.entityValList" item="toEntityIdItem" open="(" close=")" separator=",">
                    #{toEntityIdItem}
                </foreach>
                and period_val between #{toYearParam.minPeriodVal} and #{toYearParam.maxPeriodVal}
                and period_year = #{toYearParam.year}
                and period_type = #{toYearParam.periodType}
                )
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO kpi_sheet_report( period_type, period_val, period_year, city_id,
        area_id, entity_type, entity_id,entity_json, info_json, deleted)
        VALUES
        <foreach collection="list" item="reportItem" separator=",">
            (
            #{reportItem.periodType}, #{reportItem.periodVal}, #{reportItem.periodYear}, #{reportItem.cityId},
            #{reportItem.areaId},#{reportItem.entityType},#{reportItem.entityId},#{reportItem.entityJson},#{reportItem.infoJson},#{reportItem.deleted}
            )
        </foreach>
    </insert>


</mapper>