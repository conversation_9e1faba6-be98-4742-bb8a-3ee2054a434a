<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.kpi.dao.KpiOnlineSheetDetailDao">

    <!--实体映射-->
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.kpi.entity.KpiOnlineSheetDetailEntity">
        <id property="id" column="id" />
        <result property="sheetId" column="sheet_id" />
        <result property="entityType" column="entity_type" />
        <result property="entitiesJson" column="entities_json" />
        <result property="revparConfigJson" column="revpar_config_json" />
        <result property="bonusPackMultiplier" column="bonus_pack_multiplier" />
        <result property="bonusClearBaseLine" column="bonus_clear_base_line" />
        <result property="periodType" column="period_type" />
        <result property="bonusDiscount" column="bonus_discount" />
        <result property="deleted" column="deleted" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		id, sheet_id, entity_type, entities_json, revpar_config_json,
		bonus_pack_multiplier,	bonus_clear_base_line, period_type,
		bonus_discount, deleted,	create_time,	update_time
	</sql>

    <select id="getBySheetId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM  kpi_online_sheet_detail
        WHERE sheet_id = #{sheetId}
    </select>

    <insert id="insert" useGeneratedKeys="false" keyColumn="id" keyProperty="id" parameterType="com.atour.hotel.persistent.kpi.entity.KpiOnlineSheetDetailEntity">
        INSERT INTO kpi_online_sheet_detail (sheet_id, entity_type, entities_json, revpar_config_json, bonus_pack_multiplier, bonus_clear_base_line, period_type, bonus_discount)
        VALUES
        (#{sheetId}, #{entityType}, #{entitiesJson}, #{revparConfigJson}, #{bonusPackMultiplier}, #{bonusClearBaseLine}, #{periodType}, #{bonusDiscount})
    </insert>
    
    <update id="updateBySheetId" parameterType="com.atour.hotel.persistent.kpi.entity.KpiOnlineSheetDetailEntity">
        UPDATE kpi_online_sheet_detail
        <set>
            <if test="entitiesJson != null">
                entities_json = #{entitiesJson},
            </if>

            <if test="revparConfigJson != null">
                revpar_config_json = #{revparConfigJson},
            </if>
            <if test="bonusPackMultiplier != null">
                bonus_pack_multiplier = #{bonusPackMultiplier},
            </if>
            <if test="bonusClearBaseLine != null">
                bonus_clear_base_line = #{bonusClearBaseLine},
            </if>
            <if test="bonusDiscount != null">
                bonus_discount = #{bonusDiscount}
            </if>
        </set>
        WHERE sheet_id = #{sheetId} AND deleted = 0
    </update>

    <update id="removeBySheetId" parameterType="java.lang.Integer">
        UPDATE kpi_online_sheet_detail
        SET deleted = 1
        WHERE sheet_id = #{sheetId} AND deleted = 0;
    </update>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM kpi_online_sheet_detail WHERE deleted = 0
    </select>

    <select id="listByPage" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List" /> FROM kpi_online_sheet_detail
        WHERE deleted = 0 AND id &gt; #{lastId} ORDER BY id ASC LIMIT #{limit}
    </select>

    <select id="listBySheetIds" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List" /> FROM kpi_online_sheet_detail
        WHERE deleted = 0 AND sheet_id IN
        <foreach collection="sheetIds" close=")" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>