<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.atour.hotel.persistent.workorder.dao.WorkOrderReplyDao">

    <resultMap id="workOrderReplyEntity" type="com.atour.hotel.persistent.workorder.entity.WorkOrderReplyEntity">
    	<id column="id" jdbcType="BIGINT" property="id"/>
    	<result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    	<result column="type" jdbcType="INTEGER" property="type" />
        <result column="to_user_id" jdbcType="BIGINT" property="toUserId" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="image_urls" jdbcType="VARCHAR" property="imageUrls" />
        <result column="is_read" jdbcType="INTEGER" property="isRead" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

        <result column="reply_type" jdbcType="INTEGER" property="replyType" />
        <result column="forward_to_ids" jdbcType="VARCHAR" property="forwardToIds" />

    </resultMap>
    
    <resultMap id="workOrderReplyExtEntity" type="com.atour.hotel.persistent.workorder.entity.WorkOrderReplyExtEntity" extends="workOrderReplyEntity">
        <result column="order_content" jdbcType="VARCHAR" property="orderContent" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, order_num, to_user_id, content, image_urls, is_read, is_deleted, create_user_id, create_time, update_user_id, update_time
        ,reply_type, forward_to_ids
    </sql>
    
    <!-- 根据userId查询 -->
    <select id="queryWorkOrderRepliesByUserId" resultMap="workOrderReplyExtEntity">
        SELECT
        r.id, r.order_num, r.to_user_id, r.content, o.content as order_content, r.image_urls, r.is_read, r.is_deleted, r.create_user_id, r.create_time, r.update_user_id, r.update_time
        FROM work_order_reply r
        LEFT JOIN work_order o ON r.order_num = o.order_num
        WHERE r.to_user_id = #{userId}
        ORDER BY r.is_read ASC, r.create_time DESC
        LIMIT #{startNum} , #{pageSize}
    </select>
    
    <!-- 根据userId查询数量 -->
    <select id="queryWorkOrderRepliesCountByUserId" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM work_order_reply
        WHERE to_user_id = #{userId}
    </select>
    
    <!-- 根据工单号查询 -->
    <select id="queryWorkOrderRepliesByOrderNum" resultMap="workOrderReplyEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM work_order_reply
        WHERE order_num = #{orderNum}
        ORDER BY create_time ASC
    </select>
    
    <!-- 根据条件查询 -->
    <select id="queryWorkOrderReplyByCondition" resultMap="workOrderReplyEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM work_order_reply
        <where>
        	<if test="orderNum != null">
                and order_num = #{orderNum}
            </if>
            <if test="replyId != null">
                and id = #{replyId}
            </if>
            <if test="toUserId != null">
                and to_user_id = #{toUserId}
            </if>
            <if test="isRead != null">
                and is_read = #{isRead}
            </if>
       	</where>
    </select>
    
    <!-- 更新工单 -->
    <update id="updateWorkOrderReply" parameterType="java.lang.Long">
        UPDATE work_order_reply
        SET is_read = 1
        WHERE id = #{replyId}
	</update>

	<!-- 添加工单 -->
    <insert id="addWorkOrderReply" parameterType="com.atour.hotel.persistent.workorder.entity.WorkOrderReplyEntity">
    	INSERT INTO work_order_reply
    	<trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="toUserId != null">
                to_user_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="imageUrls != null">
                image_urls,
            </if>
            <if test="isRead != null">
                is_read,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
			<if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
			<if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id},
            </if>
            <if test="orderNum != null">
                #{orderNum},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="toUserId != null">
                #{toUserId},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="imageUrls != null">
                #{imageUrls},
            </if>
            <if test="isRead != null">
                #{isRead},
            </if>
            <if test="createUserId != null">
                #{createUserId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateUserId != null">
                #{updateUserId},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

</mapper>