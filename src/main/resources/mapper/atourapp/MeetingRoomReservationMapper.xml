<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.atour.hotel.persistent.atourapp.dao.MeetingRoomReservationDAO">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.atourapp.entity.MeetingRoomReservationEntity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_count" property="userCount" jdbcType="INTEGER"/>
        <result column="budget" property="budget" jdbcType="INTEGER"/>
        <result column="meeting_type" property="meetingType" jdbcType="INTEGER"/>
        <result column="chain_id" property="chainId" jdbcType="INTEGER"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="meeting_needs" property="meetingNeeds" jdbcType="VARCHAR"/>
        <result column="room_id" property="roomId" jdbcType="VARCHAR"/>
        <result column="room_name" property="roomName" jdbcType="VARCHAR"/>
        <result column="chain_name" property="chainName" jdbcType="VARCHAR"/>
        <result column="meb_id" property="mebId" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
       id,user_count,budget,meeting_type,chain_id,phone,meeting_needs,room_id,room_name,chain_name,meb_id,start_time,end_time,create_time,update_time,deleted
    </sql>


    <!--通过id查询记录-->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM meeting_room_reservation_log
        WHERE id = #{id}
    </select>

</mapper>