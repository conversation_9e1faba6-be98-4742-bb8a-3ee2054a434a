<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.center.dao.SysMessageDao">
    <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.center.entity.SysMessageEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="message_source" property="messageSource"/>
        <result column="content" property="content"/>
        <result column="relate_id" property="relateId"/>
        <result column="relate_type" property="relateType"/>
        <result column="state" property="state"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="message_type" property="messageType"/>
        <result column="delayed_time" property="delayedTime"/>
        <result column="room_no" property="roomNo"/>
        <result column="extend_id" property="extendId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, hotel_id, message_source, content, relate_id, relate_type, state, create_time,
        update_time, is_delete, message_type , delayed_time , room_no , extend_id
    </sql>

    <select id="getRecentOneMessageByRelateId" resultMap="BaseResultMap">
        SELECT
        TOP 1
        <include refid="Base_Column_List"/>
        FROM
        dbo.sys_message WITH (NOLOCK)
        WHERE relate_id = #{userId} AND state = 1
        <if test="messageSource != null and messageSource != '' ">
            AND message_source = #{messageSource}
        </if>
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        AND create_time BETWEEN #{beginTime} AND #{endTime}
        ORDER BY create_time DESC
    </select>

    <select id="getStudyTaskDetailList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.sys_message WITH (NOLOCK)
        WHERE relate_id = #{userId} AND message_source = #{messageSource}
        AND create_time BETWEEN #{beginTime} AND #{endTime}
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getNotReadMessageCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM dbo.sys_message WITH (NOLOCK)
        WHERE relate_id = #{userId} AND state = 1 and is_delete=0
        <if test="messageSource != null and messageSource != '' ">
            AND message_source = #{messageSource}
        </if>
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        <if test="beginTime != null and endTime != null">
            AND create_time BETWEEN #{beginTime} AND #{endTime}
        </if>

    </select>

    <!-- 设置为已读  -->
    <update id="updateStudyTaskState">
        UPDATE dbo.sys_message
        SET state = #{state},update_time=GETDATE()
        WHERE relate_id = #{userId} AND extend_id=#{taskId}
        <if test="messageSource != null">
            AND message_source = #{messageSource}
        </if>
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
    </update>

    <!-- 更新消息状态  -->
    <update id="updateMessageStateById">
        UPDATE dbo.sys_message
        SET state = #{state},update_time=GETDATE()
        WHERE id = #{id}
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>

    </update>

    <insert id="insertSelective" parameterType="com.atour.hotel.persistent.center.entity.SysMessageEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            select @@IDENTITY
        </selectKey>
        insert into dbo.sys_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotelId != null">
                hotel_id,
            </if>
            <if test="messageSource != null">
                message_source,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="relateId != null">
                relate_id,
            </if>
            <if test="relateType != null">
                relate_type,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="messageType != null">
                message_type,
            </if>
            <if test="roomNo != null">
                room_no,
            </if>
            <if test="extendId != null">
                extend_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotelId != null">
                #{hotelId,jdbcType=INTEGER},
            </if>
            <if test="messageSource != null">
                #{messageSource,jdbcType=TINYINT},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="relateId != null">
                #{relateId,jdbcType=INTEGER},
            </if>
            <if test="relateType != null">
                #{relateType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="messageType != null">
                #{messageType,jdbcType=TINYINT},
            </if>
            <if test="roomNo != null">
                #{roomNo},
            </if>
            <if test="extendId != null">
                #{extendId,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO dbo.sys_message (hotel_id, message_source, content,
        relate_id, relate_type, state,
        create_time, update_time, is_delete,
        message_type, extend_id)
        VALUES
        <foreach collection="sysMessageEntityList" item="item" index="index" separator=",">
            (#{item.hotelId,jdbcType=INTEGER}, #{item.messageSource,jdbcType=TINYINT}, #{item.content,jdbcType=VARCHAR},
            #{item.relateId,jdbcType=INTEGER}, #{item.relateType,jdbcType=TINYINT}, #{item.state,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDelete,jdbcType=TINYINT},
            #{item.messageType,jdbcType=TINYINT},#{item.extendId,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="updateStateByUserIdType">
        UPDATE dbo.sys_message
        SET state = #{state},update_time=GETDATE()
        WHERE relate_id = #{userId}
       and message_source =#{messageSource}
    </update>

    <select id="countMessage" resultType="java.lang.Integer" parameterType="com.atour.hotel.persistent.center.param.QueryMessageParam">
        SELECT
         count(id)
        FROM dbo.sys_message WITH (NOLOCK)
        <where>
            <if test="chainId != null">
                AND hotel_id = #{chainId}
            </if>
            <if test="messageSource != null">
                AND message_source = #{messageSource}
            </if>
            <if test="messageType != null">
                AND message_type = #{messageType}
            </if>
            <if test="state != null">
                AND state = #{state}
            </if>
            <if test="extendId != null">
                AND extend_id = #{extendId}
            </if>
            <if test="userId != null">
                and relate_id = #{userId,jdbcType=INTEGER}
            </if>
            AND is_delete = 0
        </where>
    </select>

    <select id="queryMessage" resultMap="BaseResultMap" parameterType="com.atour.hotel.persistent.center.param.QueryMessageParam">
        SELECT * FROM (
            SELECT ROW_NUMBER() OVER (ORDER BY state DESC,id DESC) AS rowNumber,
            <include refid="Base_Column_List"/>
            FROM dbo.sys_message WITH (NOLOCK)
            <where>
                <if test="chainId != null">
                    AND hotel_id = #{chainId}
                </if>
                <if test="messageSource != null">
                    AND message_source = #{messageSource}
                </if>
                <if test="messageType != null">
                    AND message_type = #{messageType}
                </if>
                <if test="state != null">
                    AND state = #{state}
                </if>
                <if test="extendId != null">
                    AND extend_id = #{extendId}
                </if>
                <if test="userId != null">
                    and relate_id = #{userId,jdbcType=INTEGER}
                </if>
                AND is_delete = 0
            </where>
        ) AS temp_table
        WHERE rowNumber BETWEEN #{pageStart} AND #{pageEnd}
    </select>

    <select id="getMessageTopNew" resultMap="BaseResultMap" parameterType="com.atour.hotel.persistent.center.param.QueryMessageParam">
        SELECT TOP 1
            <include refid="Base_Column_List"/>
        FROM dbo.sys_message WITH (NOLOCK)
        <where>
            <if test="chainId != null">
                AND hotel_id = #{chainId}
            </if>
            <if test="chainId != null">
                AND message_source = #{messageSource}
            </if>
            <if test="messageType != null">
                AND message_type = #{messageType}
            </if>
            AND is_delete = 0
        </where>
        ORDER BY id DESC
    </select>

    <!-- 更新消息状态  -->
    <update id="updateMessageReadByIds">
        UPDATE dbo.sys_message
        SET state = 0,update_time=GETDATE()
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="countMultiTypeMessageUnread" resultType="java.lang.Integer">
        SELECT count(*)
        FROM dbo.sys_message WITH (NOLOCK)
        WHERE relate_id = #{userId}
        <if test="messageSources != null and messageSources.size() != 0">
            AND message_source in
            <foreach collection="messageSources" item="messageSource" open="(" close=")" separator=",">
                #{messageSource,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        AND state = 1
        AND create_time BETWEEN #{beginTime} AND #{endTime}
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from dbo.sys_message WITH (NOLOCK)
        where id = #{id}
    </select>

    <select id="selectMessageWithPage" resultMap="BaseResultMap" parameterType="com.atour.hotel.persistent.center.param.QueryMessageParam">
        SELECT * FROM (
        SELECT ROW_NUMBER() OVER (ORDER BY id DESC) AS rowNumber,
        <include refid="Base_Column_List"/>
        FROM dbo.sys_message WITH (NOLOCK)
        <where>
            <if test="chainId != null">
                AND hotel_id = #{chainId}
            </if>
            <if test="messageSource != null">
                AND message_source = #{messageSource}
            </if>
            <if test="messageType != null">
                AND message_type = #{messageType}
            </if>
            <if test="state != null">
                AND state = #{state}
            </if>
            <if test="extendId != null">
                AND extend_id = #{extendId}
            </if>
            <if test="userId != null">
                and relate_id = #{userId,jdbcType=INTEGER}
            </if>
            AND is_delete = 0
        </where>
        ) AS temp_table
        WHERE rowNumber BETWEEN #{pageStart} AND #{pageEnd}
    </select>

    <select id="hasSendCount" resultType="java.lang.Integer">
        select count(*) FROM   dbo.sys_message WITH (NOLOCK)
        WHERE relate_id = #{userId}
        AND hotel_id = #{chainId}
        AND message_type = #{messageType}
        AND room_no= #{roomNo}
    </select>
</mapper>