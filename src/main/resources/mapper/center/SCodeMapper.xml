<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.center.dao.SCodeDao">
  <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.center.entity.SCodeEntity">
    <id column="CodeID" jdbcType="INTEGER" property="codeID" />
    <result column="CodeType" jdbcType="VARCHAR" property="codeType" />
    <result column="TypeName" jdbcType="VARCHAR" property="typeName" />
    <result column="CodeNo" jdbcType="INTEGER" property="codeNo" />
    <result column="CodeName" jdbcType="VARCHAR" property="codeName" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="Sort" jdbcType="INTEGER" property="sort" />
    <result column="ExtendInfo" jdbcType="VARCHAR" property="extendInfo" />
    <result column="Flag" jdbcType="INTEGER" property="flag" />
    <result column="CodeEnumName" jdbcType="VARCHAR" property="codeEnumName" />
  </resultMap>

  <sql id="Base_Column_List">
    CodeID, CodeType, TypeName, CodeNo, CodeName, Remark, Sort, ExtendInfo, Flag, CodeEnumName
  </sql>
  
  <select id="getDicts" parameterType="Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
      FROM dbo.sys_Code WITH(nolock)
    WHERE CodeType = #{codeType} AND Flag = 1
  </select>

</mapper>