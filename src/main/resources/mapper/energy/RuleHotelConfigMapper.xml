<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.energy.dao.RuleHotelConfigMapper">

    <!--实体映射-->
    <resultMap id="ruleHotelConfigResultMap" type="com.atour.hotel.persistent.energy.entity.RuleHotelConfigEntity">
        <!--主键-->
        <id property="id" column="id"/>
        <!--预警规则配置表主键-->
        <result property="ruleConfigId" column="rule_config_id"/>
        <!--关联的酒店ID-->
        <result property="chainId" column="chain_id"/>
        <!--删除标记 0:未删除 1：删除-->
        <result property="deleteFlag" column="delete_flag"/>
        <!--创建时间-->
        <result property="createTime" column="create_time"/>
        <!--更新时间-->
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		id,	rule_config_id,	chain_id,	delete_flag,	create_time,	update_time
	</sql>

    <!-- 查询（根据主键ID查询） -->
    <select id="selectByPrimaryKey" resultMap="ruleHotelConfigResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_hotel_config
        WHERE id = #{id}
    </select>

    <!--删除：根据主键ID删除-->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		 DELETE FROM rule_hotel_config
		 WHERE id = #{id}
	</delete>

    <!-- 添加 （匹配有值的字段）-->
    <insert id="insertSelective" parameterType="com.atour.hotel.persistent.energy.entity.RuleHotelConfigEntity">
        INSERT INTO rule_hotel_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ruleConfigId != null">
                rule_config_id,
            </if>
            <if test="chainId != null">
                chain_id,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id},
            </if>
            <if test="ruleConfigId!=null">
                #{ruleConfigId},
            </if>
            <if test="chainId!=null">
                #{chainId},
            </if>
            <if test="deleteFlag!=null">
                #{deleteFlag},
            </if>
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="updateTime!=null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <!-- 修 改-->
    <update id="updateByPrimaryKeySelective"
            parameterType="com.atour.hotel.persistent.energy.entity.RuleHotelConfigEntity">
        UPDATE rule_hotel_config
        <set>
            <if test="ruleConfigId != null">
                rule_config_id = #{ruleConfigId},
            </if>
            <if test="chainId != null">
                chain_id = #{chainId},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>

        </set>
        WHERE id = #{id}
    </update>

    <select id="getRuleHotelList" parameterType="com.atour.hotel.persistent.energy.param.RuleHotelQuery" resultMap="ruleHotelConfigResultMap">
		select *
        from rule_hotel_config
        <where>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="ruleConfigId != null">
                and rule_config_id = #{ruleConfigId}
            </if>
            <if test="chainId != null">
                and chain_id = #{chainId}
            </if>
        </where>
	</select>

    <!-- 批量修改-->
    <update id="batchUpdateByRuleId"
            parameterType="com.atour.hotel.persistent.energy.param.RuleHotelQuery">
        UPDATE rule_hotel_config
        set delete_flag = #{deleteFlag}
        WHERE rule_config_id = #{ruleConfigId}
        AND chain_id IN
        <foreach collection="chainIdList" item="chainId" open="(" close=")" separator=",">
            #{chainId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rule_hotel_config (rule_config_id, chain_id)
        VALUES
        <foreach collection="entities" separator="," item="entity">
            (#{entity.ruleConfigId}, #{entity.chainId})
        </foreach>
    </insert>
</mapper>