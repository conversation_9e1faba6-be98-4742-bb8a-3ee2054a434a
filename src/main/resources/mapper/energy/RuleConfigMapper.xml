<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.energy.dao.RuleConfigMapper">

	<!--实体映射-->
	<resultMap id="ruleConfigResultMap" type="com.atour.hotel.persistent.energy.entity.RuleConfigEntity">
		<!--主键-->
		<id property="id" column="id" />
		<!--预警规则(json)-->
		<result property="ruleConfig" column="rule_config" />
		<!--删除标记 0:未删除 1：删除-->
		<result property="deleteFlag" column="delete_flag" />
		<!--创建时间-->
		<result property="createTime" column="create_time" />
		<!--更新时间-->
		<result property="updateTime" column="update_time" />
	</resultMap>


	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,	rule_config,	delete_flag,	create_time,	update_time
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="ruleConfigResultMap" parameterType="java.lang.Integer">
		 SELECT
		 <include refid="Base_Column_List" />
		 FROM rule_config
		 WHERE id = #{id}
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		 DELETE FROM rule_config
		 WHERE id = #{id}
	</delete>


	<!-- 添加 （匹配有值的字段）-->
	<insert id="insertSelective" parameterType="com.atour.hotel.persistent.energy.entity.RuleConfigEntity">
		 INSERT INTO rule_config
		 <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				 id,
			</if>
			<if test="ruleConfig != null">
				 rule_config,
			</if>
			<if test="deleteFlag != null">
				 delete_flag,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
			<if test="updateTime != null">
				 update_time,
			</if>

		 </trim>
		 <trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id!=null">
				 #{id},
			</if>
			<if test="ruleConfig!=null">
				 #{ruleConfig},
			</if>
			<if test="deleteFlag!=null">
				 #{deleteFlag},
			</if>
			<if test="createTime!=null">
				 #{createTime},
			</if>
			<if test="updateTime!=null">
				 #{updateTime},
			</if>
		 </trim>
	</insert>

	<!-- 修 改-->
	<update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.energy.entity.RuleConfigEntity">
		 UPDATE rule_config
 		 <set> 
			<if test="ruleConfig != null">
				 rule_config = #{ruleConfig},
			</if>
			<if test="deleteFlag != null">
				 delete_flag = #{deleteFlag},
			</if>
			<if test="createTime != null">
				 create_time = #{createTime},
			</if>
			<if test="updateTime != null">
				 update_time = #{updateTime},
			</if>

 		 </set>
		 WHERE id = #{id}
	</update>

	<!-- 查询（根据主键ID查询） -->
	<select id="getRuleConfigList" resultMap="ruleConfigResultMap" >
		SELECT
		<include refid="Base_Column_List" />
		FROM rule_config
		WHERE delete_flag = 0
	</select>

</mapper>