<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.energy.dao.SteamMeterMapper">

	<!--实体映射-->
	<resultMap id="steamMeterResultMap" type="com.atour.hotel.persistent.energy.entity.SteamMeterEntity">
		<!--ID主键-->
		<id property="id" column="id" />
		<!--酒店id-->
		<result property="chainId" column="chain_id" />
		<!--登记日期-->
		<result property="recordDate" column="record_date" />
		<!--燃气信息-->
		<result property="steam" column="steam" />
		<!--删除标记 0:未删除 1：删除-->
		<result property="deleteFlag" column="delete_flag" />
		<!--创建时间-->
		<result property="createTime" column="create_time" />
		<!--更新时间-->
		<result property="updateTime" column="update_time" />
	</resultMap>


	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,	chain_id,	record_date,	steam,	delete_flag,	create_time,	update_time
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="steamMeterResultMap" parameterType="java.lang.Integer">
		 SELECT
		 <include refid="Base_Column_List" />
		 FROM steam_meter
		 WHERE id = #{id}
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		 DELETE FROM steam_meter
		 WHERE id = #{id}
	</delete>

	<!-- 添加 （匹配有值的字段）-->
	<insert id="insertSelective" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity" useGeneratedKeys="true" keyProperty="id">
		 INSERT INTO steam_meter
		 <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				 id,
			</if>
			<if test="chainId != null">
				 chain_id,
			</if>
			<if test="recordDate != null">
				 record_date,
			</if>
			<if test="steam != null">
				 steam,
			</if>
			<if test="deleteFlag != null">
				 delete_flag,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
			<if test="updateTime != null">
				 update_time,
			</if>

		 </trim>
		 <trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id!=null">
				 #{id},
			</if>
			<if test="chainId!=null">
				 #{chainId},
			</if>
			<if test="recordDate!=null">
				 #{recordDate},
			</if>
			<if test="steam!=null">
				 #{steam},
			</if>
			<if test="deleteFlag!=null">
				 #{deleteFlag},
			</if>
			<if test="createTime!=null">
				 #{createTime},
			</if>
			<if test="updateTime!=null">
				 #{updateTime},
			</if>
		 </trim>
	</insert>

	<!-- 修 改-->
	<update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity">
		 UPDATE steam_meter
 		 <set> 
			<if test="chainId != null">
				 chain_id = #{chainId},
			</if>
			<if test="recordDate != null">
				 record_date = #{recordDate},
			</if>
			<if test="steam != null">
				 steam = #{steam},
			</if>
			<if test="deleteFlag != null">
				 delete_flag = #{deleteFlag},
			</if>
			<if test="createTime != null">
				 create_time = #{createTime},
			</if>
			<if test="updateTime != null">
				 update_time = #{updateTime},
			</if>

 		 </set>
		 WHERE id = #{id}
	</update>

	<!-- 修 改-->
	<update id="updateByPrimaryKey" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity">
		 UPDATE steam_meter
		 SET 
			 chain_id = #{chainId},
			 record_date = #{recordDate},
			 steam = #{steam},
			 delete_flag = #{deleteFlag},
			 create_time = #{createTime},
			 update_time = #{updateTime}
		 WHERE id = #{id}
	</update>

	<select id="selectBySelective" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity" resultMap="steamMeterResultMap">
		select *
		from steam_meter
		<where>
			<if test="chainId != null">
				and chain_id = #{chainId}
			</if>
			<if test="recordDate != null">
				and record_date = #{recordDate}
			</if>
			<if test="historyDate != null">
				and record_date &lt;= #{historyDate}
			</if>
			<if test="steam != null">
				and steam = #{steam}
			</if>
			<if test="deleteFlag != null">
				and delete_flag = #{deleteFlag}
			</if>
		</where>
		order by id desc
	</select>

	<select id="selectBySelectiveSort" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity" resultMap="steamMeterResultMap">
		select *
		from steam_meter
		<where>
			<if test="chainId != null">
				and chain_id = #{chainId}
			</if>
			<if test="recordDate != null">
				and record_date = #{recordDate}
			</if>
			<if test="historyDate != null">
				and record_date &lt;= #{historyDate}
			</if>
			<if test="afterDate != null">
				and record_date >= #{afterDate}
			</if>
			<if test="steam != null">
				and steam = #{steam}
			</if>
			<if test="deleteFlag != null">
				and delete_flag = #{deleteFlag}
			</if>
		</where>
		<if test="sort != null and sort != ''">
			ORDER BY id desc, ${sort}
		</if>
	</select>

	<select id="selectListBySelective" parameterType="com.atour.hotel.persistent.energy.entity.SteamMeterEntity" resultMap="steamMeterResultMap">
		select *
		from steam_meter
		<where>
			<if test="chainId != null">
				and chain_id = #{chainId}
			</if>
			<if test="recordDate != null">
				and record_date = #{recordDate}
			</if>
			<if test="steam != null">
				and steam = #{steam}
			</if>
			<if test="deleteFlag != null">
				and delete_flag = #{deleteFlag}
			</if>
		</where>
		order by id desc
	</select>


</mapper>