<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.roommanage.dao.MonthCostGuestgoodsMapper">

    <!--实体映射-->
    <resultMap id="monthCostGuestgoodsResultMap"
               type="com.atour.hotel.persistent.roommanage.entity.MonthCostGuestgoodsEntity">
        <!---->
        <id property="id" column="id"/>
        <!--酒店ID-->
        <result property="chainId" column="chain_id"/>
        <!--酒店名称-->
        <result property="chainName" column="chain_name"/>
        <!--区域名称-->
        <result property="accMonth" column="acc_month"/>
        <!--已售间夜-->
        <result property="soldNight" column="sold_night"/>
        <!--总使用费用（去税）-->
        <result property="totalFee" column="total_fee"/>
        <!--客房使用费用（去税）-->
        <result property="guestFee" column="guest_fee"/>
        <!--外部使用费用（去税）-->
        <result property="outerFee" column="outer_fee"/>
        <!--单间夜成本（元）含税-->
        <result property="singleNightCost" column="single_night_cost"/>
        <!--json-->
        <result property="monthCostGuestgoodsJson" column="month_cost_guestgoods_json"/>
        <!--操作用户-->
        <result property="operationUserId" column="operation_user_id"/>
        <!--操作人名称-->
        <result property="operationUserName" column="operation_user_name"/>
        <!--删除标记 0:未删除 1：删除-->
        <result property="deleteFlag" column="delete_flag"/>
        <!--创建时间-->
        <result property="createTime" column="create_time"/>
        <!---->
        <result property="updateTime" column="update_time"/>
        <!--客用品费用json-->
        <result property="guestSuppliesCostJson" column="guest_supplies_cost_json"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		id,	chain_id,	chain_name,	acc_month,	sold_night,	total_fee,	guest_fee,	outer_fee,	single_night_cost,	month_cost_guestgoods_json,	operation_user_id,	operation_user_name,	delete_flag,	create_time,	update_time, guest_supplies_cost_json
	</sql>

    <!-- 查询（根据主键ID查询） -->
    <select id="selectByPrimaryKey" resultMap="monthCostGuestgoodsResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM month_cost_guestgoods
        WHERE id = #{id}
    </select>

    <!--删除：根据主键ID删除-->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		 DELETE FROM month_cost_guestgoods
		 WHERE id = #{id}
	</delete>

    <!--删除：根据accMonth删除-->
    <delete id="deleteByAccMonth" parameterType="java.lang.Long">
        UPDATE month_cost_guestgoods
        <set>
            delete_flag = 1,
        </set>
        WHERE chain_id = #{chainId}
        AND acc_month = #{accMonth}
    </delete>

    <!-- 添加 （匹配有值的字段）-->
    <insert id="insertSelective" parameterType="com.atour.hotel.persistent.roommanage.entity.MonthCostGuestgoodsEntity">
        INSERT INTO month_cost_guestgoods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="chainId != null">
                chain_id,
            </if>
            <if test="chainName != null">
                chain_name,
            </if>
            <if test="accMonth != null">
                acc_month,
            </if>
            <if test="soldNight != null">
                sold_night,
            </if>
            <if test="totalFee != null">
                total_fee,
            </if>
            <if test="guestFee != null">
                guest_fee,
            </if>
            <if test="outerFee != null">
                outer_fee,
            </if>
            <if test="singleNightCost != null">
                single_night_cost,
            </if>
            <if test="monthCostGuestgoodsJson != null">
                month_cost_guestgoods_json,
            </if>
            <if test="operationUserId != null">
                operation_user_id,
            </if>
            <if test="operationUserName != null">
                operation_user_name,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="guestSuppliesCostJson != null">
                guest_supplies_cost_json,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id},
            </if>
            <if test="chainId!=null">
                #{chainId},
            </if>
            <if test="chainName!=null">
                #{chainName},
            </if>
            <if test="accMonth!=null">
                #{accMonth},
            </if>
            <if test="soldNight!=null">
                #{soldNight},
            </if>
            <if test="totalFee!=null">
                #{totalFee},
            </if>
            <if test="guestFee!=null">
                #{guestFee},
            </if>
            <if test="outerFee!=null">
                #{outerFee},
            </if>
            <if test="singleNightCost!=null">
                #{singleNightCost},
            </if>
            <if test="monthCostGuestgoodsJson!=null">
                #{monthCostGuestgoodsJson},
            </if>
            <if test="operationUserId!=null">
                #{operationUserId},
            </if>
            <if test="operationUserName!=null">
                #{operationUserName},
            </if>
            <if test="deleteFlag!=null">
                #{deleteFlag},
            </if>
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="updateTime!=null">
                #{updateTime},
            </if>
            <if test="guestSuppliesCostJson != null">
                #{guestSuppliesCostJson},
            </if>
        </trim>
    </insert>

    <!-- 修 改（匹配有值的字段）-->
    <update id="updateByPrimaryKeySelective"
            parameterType="com.atour.hotel.persistent.roommanage.entity.MonthCostGuestgoodsEntity">
        UPDATE month_cost_guestgoods
        <set>
            <if test="chainId != null">
                chain_id = #{chainId},
            </if>
            <if test="chainName != null">
                chain_name = #{chainName},
            </if>
            <if test="accMonth != null">
                acc_month = #{accMonth},
            </if>
            <if test="soldNight != null">
                sold_night = #{soldNight},
            </if>
            <if test="totalFee != null">
                total_fee = #{totalFee},
            </if>
            <if test="guestFee != null">
                guest_fee = #{guestFee},
            </if>
            <if test="outerFee != null">
                outer_fee = #{outerFee},
            </if>
            <if test="singleNightCost != null">
                single_night_cost = #{singleNightCost},
            </if>
            <if test="monthCostGuestgoodsJson != null">
                month_cost_guestgoods_json = #{monthCostGuestgoodsJson},
            </if>
            <if test="operationUserId != null">
                operation_user_id = #{operationUserId},
            </if>
            <if test="operationUserName != null">
                operation_user_name = #{operationUserName},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="guestSuppliesCostJson != null">
                guest_supplies_cost_json = #{guestSuppliesCostJson},
            </if>

        </set>
        WHERE id = #{id}
    </update>

    <!-- 查询（匹配有值的字段）-->
    <select id="selectBySelective" resultMap="monthCostGuestgoodsResultMap"
            parameterType="com.atour.hotel.persistent.roommanage.entity.MonthCostGuestgoodsEntity">
        SELECT
        <include refid="Base_Column_List"/>
        from month_cost_guestgoods
        <where>
            <if test="chainId != null">
                and chain_id = #{chainId}
            </if>
            <if test="chainName != null">
                and chain_name = #{chainName}
            </if>
            <if test="accMonth != null">
                and acc_month = #{accMonth}
            </if>
            <if test="soldNight != null">
                and sold_night = #{soldNight}
            </if>
            <if test="totalFee != null">
                and total_fee = #{totalFee}
            </if>
            <if test="guestFee != null">
                and guest_fee = #{guestFee}
            </if>
            <if test="outerFee != null">
                and outer_fee = #{outerFee}
            </if>
            <if test="singleNightCost != null">
                and single_night_cost = #{singleNightCost}
            </if>
            <if test="monthCostGuestgoodsJson != null">
                and month_cost_guestgoods_json = #{monthCostGuestgoodsJson}
            </if>
            <if test="operationUserId != null">
                and operation_user_id = #{operationUserId}
            </if>
            <if test="operationUserName != null">
                and operation_user_name = #{operationUserName}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <sql id="limit">
        <if test='offset != 0 and limit != 0'>
            LIMIT #{offset}, #{limit}
        </if>

        <if test='offset == 0 and limit != 0'>
            LIMIT #{limit}
        </if>
    </sql>

    <select id="selectList" resultMap="monthCostGuestgoodsResultMap">
        select
        <include refid="Base_Column_List"/>
        from month_cost_guestgoods
        <where>
            AND chain_id IN
            <foreach close=")" collection="chainIds" index="index" item="chainId" open="(" separator=",">
                #{chainId}
            </foreach>
            and (acc_month BETWEEN #{startDate} AND #{endDate})
            and delete_flag = 0
        </where>
        order by acc_month desc
        <include refid="limit"/>
    </select>

    <select id="selectListCount" resultType="int">
        select
        count(1)
        from month_cost_guestgoods
        <where>
            AND chain_id IN
            <foreach close=")" collection="chainIds" index="index" item="chainId" open="(" separator=",">
                #{chainId}
            </foreach>
            and (acc_month BETWEEN #{startDate} AND #{endDate})
            and delete_flag = 0
        </where>
    </select>

    <select id="exportList" resultMap="monthCostGuestgoodsResultMap">
        select
        <include refid="Base_Column_List"/>
        from month_cost_guestgoods
        <where>
            AND chain_id IN
            <foreach close=")" collection="chainIds" index="index" item="chainId" open="(" separator=",">
                #{chainId}
            </foreach>
            and (acc_month BETWEEN #{startDate} AND #{endDate})
            and delete_flag = 0
        </where>
    </select>

    <select id="getMonthCostGuestgoodsList" resultMap="monthCostGuestgoodsResultMap">
        select
        <include refid="Base_Column_List"/>
        from month_cost_guestgoods
        <where>
            <if test="chainIds != null and chainIds.size > 0">
                AND chain_id IN
                <foreach close=")" collection="chainIds" index="index" item="chainId" open="(" separator=",">
                    #{chainId}
                </foreach>
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND acc_month BETWEEN #{startDate} AND #{endDate}
            </if>
            and delete_flag = 0
        </where>
    </select>

</mapper>