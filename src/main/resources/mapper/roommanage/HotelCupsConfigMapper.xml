<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.roommanage.dao.HotelCupsConfigMapper">

	<!--实体映射-->
	<resultMap id="hotelCupsConfigResultMap" type="com.atour.hotel.persistent.roommanage.entity.HotelCupsConfigEntity">
		<!---->
		<id property="id" column="id" />
		<!--酒店ID-->
		<result property="chainId" column="chain_id" />
		<!--配置信息-->
		<result property="configDetail" column="config_detail" />
		<!--创建时间-->
		<result property="createTime" column="create_time" />
		<!--修改时间-->
		<result property="updateTime" column="update_time" />
	</resultMap>


	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,	chain_id,	config_detail,	create_time,	update_time
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="hotelCupsConfigResultMap" parameterType="java.lang.Long">
		 SELECT
		 <include refid="Base_Column_List" />
		 FROM hotel_cups_config
		 WHERE id = #{id} and disable = 0
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		 DELETE FROM hotel_cups_config
		 WHERE id = #{id}
	</delete>

	<!-- 添加 （匹配有值的字段）-->
	<insert id="insertSelective" parameterType="com.atour.hotel.persistent.roommanage.entity.HotelCupsConfigEntity">
		 INSERT INTO hotel_cups_config
		 <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				 id,
			</if>
			<if test="chainId != null">
				 chain_id,
			</if>
			<if test="configDetail != null">
				 config_detail,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
			<if test="updateTime != null">
				 update_time,
			</if>

		 </trim>
		 <trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id!=null">
				 #{id},
			</if>
			<if test="chainId!=null">
				 #{chainId},
			</if>
			<if test="configDetail!=null">
				 #{configDetail},
			</if>
			<if test="createTime!=null">
				 #{createTime},
			</if>
			<if test="updateTime!=null">
				 #{updateTime},
			</if>
		 </trim>
	</insert>

	<!-- 修 改（匹配有值的字段）-->
	<update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.roommanage.entity.HotelCupsConfigEntity">
		 UPDATE hotel_cups_config
 		 <set> 
			<if test="chainId != null">
				 chain_id = #{chainId},
			</if>
			<if test="configDetail != null">
				 config_detail = #{configDetail},
			</if>
			<if test="createTime != null">
				 create_time = #{createTime},
			</if>
			<if test="updateTime != null">
				 update_time = #{updateTime},
			</if>

 		 </set>
		 WHERE id = #{id}
	</update>

	<!-- 查询（匹配有值的字段）-->
	<select id="selectBySelective" resultMap="hotelCupsConfigResultMap" parameterType="com.atour.hotel.persistent.roommanage.entity.HotelCupsConfigEntity">
		 SELECT * 
		 from hotel_cups_config
		 where 1=1
			<if test="chainId != null">
				 and chain_id = #{chainId}
			</if>
			<if test="configDetail != null">
				 and config_detail = #{configDetail}
			</if>
			<if test="createTime != null">
				 and create_time = #{createTime}
			</if>
			<if test="updateTime != null">
				 and update_time = #{updateTime}
			</if>
	</select>

</mapper>