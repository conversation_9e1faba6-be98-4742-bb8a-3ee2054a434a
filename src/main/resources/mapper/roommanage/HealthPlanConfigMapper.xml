<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.atour.hotel.persistent.roommanage.dao.HealthPlanConfigMapper">
  <resultMap id="BaseResultMap" type="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="health_plan_type" jdbcType="TINYINT" property="healthPlanType" />
    <result column="health_plan_name" jdbcType="VARCHAR" property="healthPlanName" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, health_plan_type, health_plan_name, delete_flag, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from health_plan_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity">
    insert into health_plan_config (health_plan_type,
      health_plan_name, delete_flag)
    values (#{healthPlanType,jdbcType=TINYINT},
      #{healthPlanName,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyProperty="id" parameterType="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity" useGeneratedKeys="true">
    insert into health_plan_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="healthPlanType != null">
        health_plan_type,
      </if>
      <if test="healthPlanName != null">
        health_plan_name,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="healthPlanType != null">
        #{healthPlanType,jdbcType=TINYINT},
      </if>
      <if test="healthPlanName != null">
        #{healthPlanName,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity">
    update health_plan_config
    <set>
      <if test="healthPlanType != null">
        health_plan_type = #{healthPlanType,jdbcType=TINYINT},
      </if>
      <if test="healthPlanName != null">
        health_plan_name = #{healthPlanName,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByHealthPlanType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from health_plan_config
        where health_plan_type = #{healthPlanType}
        and delete_flag = 0
    </select>

    <select id="selectBySelective" parameterType="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from health_plan_config
        <where>
            <if test="healthPlanType != null">
                and health_plan_type = #{healthPlanType}
            </if>
            <if test="healthPlanName != null">
                and health_plan_name = #{healthPlanName}
            </if>
            and delete_flag = 0
        </where>
    </select>

    <select id="selectBySelectiveLikeName" parameterType="com.atour.hotel.persistent.roommanage.entity.HealthPlanConfigEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from health_plan_config
        <where>
            <if test="healthPlanType != null">
                and health_plan_type = #{healthPlanType}
            </if>
            <if test="healthPlanName != null">
                and health_plan_name like concat('%',#{healthPlanName},'%')
            </if>
            and delete_flag = 0
        </where>
    </select>
</mapper>