apollo:
  bootstrap:
    enabled: true
spring.servlet.multipart.maxFileSize: 10MB
spring.servlet.multipart.maxRequestSize: 10MB
server:
  servlet:
    # hotel服务访问地址前需加/hotel
    path: /hotel
  port: 10030
eureka:
  client:
    #不需要注册成服务
    register-with-eureka: true
    # eureka client刷新本地缓存时间,默认30s
    registry-fetch-interval-seconds: 5
    # 向 server 提交实例信息的时间, 包含 health check 的结果, 默认 40s
    instance-info-replication-interval-seconds: 2
    healthcheck:
      enabled: true
    service-url:
      defaultZone: https://qa-eureka.at-our.com/eureka
  instance:
    # 超过这个时间没收到心跳就剔除这个服务，这个配置一般为服务刷新时间配置的三倍，默认90s
    #注意，EurekaServer一定要设置eureka.server.eviction-interval-timer-in-ms否则这个配置无效
    lease-expiration-duration-in-seconds: 6
    # 服务刷新时间，默认30s
    lease-renewal-interval-in-seconds: 2
    prefer-ip-address: true
spring:
  profiles:
    active: dev
  mvc:
    static-path: /**
  application:
    name: hotel-manage-dev
  datasource:
    druid:
      # spring boot 2.0 开始, 配置不支持继承, 只能每个展开写一遍
      center:
        url: *****************************************************************
        username: sa
        password: XGBI2dvgf5kQrl9W8lsOQcrp
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      ops:
        url: *******************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      pms:
        url: **************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 10
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      energy:
        url: *********************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      kpi:
        url: **********************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 1
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      room-manage:
        url: **************************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      atour-oms:
        url: **************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      cem:
        url: *****************************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      app:
        url: **************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      atourapp:
        url: **************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 1
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      cost:
        url: *******************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 1
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      workorder:
        url: **************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 3
        max-active: 200
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat,wall
      hlm:
        url:  *********************************************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 1
        max-active: 100
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat
      metrics:
        url:  *********************************************************************************************************************************************************************************
        username: yaduodb
        password: $^mvObKz6mIfnsWbBd6vYVjl
        initial-size: 1
        max-active: 100
        min-idle: 1
        max-wait: 60000
        validation-query: SELECT 1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        use-global-data-source-stat: true
        filters: slf4j,stat

      web-stat-filter:
        url-pattern: /*
        exclusions: /druid/*
      stat-view-servlet:
        url-pattern: /views/druid/*
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 5000
          merge-sql: true
        slf4j:
          result-set-log-enabled: true
          connection-log-enabled: true
          connection-log-error-enabled: true
          statement-log-enabled: true
          statement-log-error-enabled: true
          enabled: true

  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  jackson:
    time-zone: GMT+8
#redis 配置
redis:
  host: qa-redis.at-our.com
  port: 6379
  password: Atour2017
  database: 14
  maxActive: 200
  timeOut: 1000
  maxIdle: 5
  maxTotal: 50
  minIdle: 5
mybatis:
  config-location: classpath:mybatis-config.xml


# 暴露健康、信息和下线接口
management:
  server:
    servlet:
      context-path: /hotel
  endpoint:
    health:
      enabled: true
      show-details: always
    info:
      enabled: true
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,shutdown,httptrace
  health:
    db:
      enabled: false
    solr:
      enabled: false
    elasticsearch:
      enabled: false
#分库初始化配置
hotelInitialSize: 1
hotelMaxActive: 100

swagger:
  enable: true
  package: com.atour.hotel
  apiurl: /hotel/**

#jpush config
jiguang:
  appKey: 3b5d891c2182ae33a3cf619c
  newAppKey: 3b083343c4b3b96e53b0791c
  ownerAppKey: dbce33ecd212eb428617fcc6
  newOwnerAppKey: e58daf4c61643fd76bd56489

#chain-center
chain-center-dev:
  ribbon:
    # Max number of retries on the same server (excluding the first try)
    MaxAutoRetries: 0
    # Max number of next servers to retry (excluding the first server)
    MaxAutoRetriesNextServer: 0
    # Whether all operations can be retried for this client
    OkToRetryOnAllOperations: false
    # Interval to refresh the server list from the source
    ServerListRefreshInterval: 1000
    ConnectTimeout: 5000
    ReadTimeout: 10000

#pay-center
pay-center-dev:
  ribbon:
    # Max number of retries on the same server (excluding the first try)
    MaxAutoRetries: 0
    # Max number of next servers to retry (excluding the first server)
    MaxAutoRetriesNextServer: 0
    # Whether all operations can be retried for this client
    OkToRetryOnAllOperations: false
    # Interval to refresh the server list from the source
    ServerListRefreshInterval: 1000
    ConnectTimeout: 5000
    ReadTimeout: 10000

default:
  domain: at-our.com

rbac-url:
  host: https://qa0-api-rbac.corp.at-our.com
  login: ${rbac-url.host}/hotel/login/loginUserInfo
  loginMobile: ${rbac-url.host}/hotel/login/getUserInfoByMobile
  getSystemListByChainId: ${rbac-url.host}/hotel/system/getSystemListByChainId
  getManagerByChainId: ${rbac-url.host}/hotel/system/getManagerByChainId
  getRegionByChainIdUrl: ${rbac-url.host}/comm/region/chain/getRegionBChainId
  getHotelRelateSystemList: ${rbac-url.host}/hotel/system/getHotelRelateSystemList
  getManagerByChainIdAndJobId: ${rbac-url.host}/hotel/system/getManagerByChainIdAndJobId
  getEmailByJobId: ${rbac-url.host}/user/getSpecificEmails
  getUserDetailUrl: ${rbac-url.host}/inner/user/detail
  getUserByRuleIdUrl: ${rbac-url.host}/hotel/system/getUserByRuleId
  getUserDetailByUserCodeUrl: ${rbac-url.host}/inner/user/detailByUserCode
  #根据地区获取地区所有的子集（包括所有层级的子集）
  getAreaSubDeptUrl: ${rbac-url.host}/inner/chain/areaSubDept
  #获取酒店现长和兼店现长
  getXianZhangAndPartTimeXianZhang: ${rbac-url.host}/hotel/system/getXianZhangAndPartTimeXianZhang
  getUserDetailDTOUrl: ${rbac-url.host}/user/detail
  getManagerByChainIdUrl: ${rbac-url.host}/inner/chain/getManagerByChainId
  getAllUserByChainIdUrl: ${rbac-url.host}/hotel/system/getAllUserByChainIds
  getValidUserIdsByFuc: ${rbac-url.host}/hotel/system/getValidUserIdsByFuc
  getChainListByDeptIdUrl: ${rbac-url.host}/comm/region/chain/getChainListByDeptId
  getRegionByChainIdNewUrl: ${rbac-url.host}/comm/region/chain/getRegionByChainId/v1
  getChainListByDeptIdNewUrl: ${rbac-url.host}/comm/region/chain/getChainListByDeptId/v1
  getUserlistByRoleIds: ${rbac-url.host}/inner/user/getUserlistByRoleIds
  regionCityChainTreeByChainIds: ${rbac-url.host}/inner/chain/regionCityChainTreeByChainIds

report-url:
  host: https://qa0-api-pms.at-our.com
  queryPriceTaxSummary: ${report-url.host}/report/inner/finance/pricetax/summary/list
  querySellVipCard: ${report-url.host}/report/inner/sellVipCard/getSellVipCardStatistics
  queryAccTransSummary: ${report-url.host}/report/inner/accTransSummary/list
  querySignReportList: ${report-url.host}/report/inner/signReport/list
  querySignReportSummary: ${report-url.host}/report/inner/signReport/omsSummary
  querySignCountByStateUrl: ${report-url.host}/report/inner/signReport/countByState
  querySignCountByChainIdStateUrl: ${report-url.host}/report/inner/signReport/countByChainIdState
  querySignCountByChainIdsForPushUrl: ${report-url.host}/report/inner/signReport/omsSignCount
  queryExamineCountByChainIdsForPushUrl: ${report-url.host}/report/inner/signReport/omsExamineCount
  queryArTransAlert: ${report-url.host}/report/inner/signReport/omsArTransAlert
  querySuspendTransAlert: ${report-url.host}/report/inner/signReport/omsSuspendTransAlert
  queryAccountBalanceAlert: ${report-url.host}/report/inner/signReport/omsAccountBalanceAlert


pms-url:
  host: https://qa0-pmsapi2.at-our.com
  bookingOrder: ${pms-url.host}/pms/inner/order/getOrderList
  computeDiscountRate: ${pms-url.host}/pms/inner/folioTrans/computeDiscountRate
  getFolioTransBaseInfo: ${pms-url.host}/pms/inner/folioTrans/getFolioTransBaseInfo
  getApplyReason: ${pms-url.host}/pms/inner/folioTrans/getApplyReason
  checkFolioIsOverdueUrl: ${pms-url.host}/pms/inner/folioTrans/checkFolioIsOverdue
  checkCommissionTransApplyParamUrl: ${pms-url.host}/pms/inner/folioTrans/checkCommissionTransApplyParam
  approvalFinishTransfer: ${pms-url.host}/pms/inner/mebTrans/approvalFinishTransfer
  queryCheckTransferUrl: ${pms-url.host}/pms/inner/mebTrans/queryCheckTransfer
  queryMebTagByDayRule: ${pms-url.host}/pms//inner/mebJmlTagRemind/queryMebTagByDayRule
  queryMyPiece: ${pms-url.host}/pms/inner/dkf/waiter/query/myPiece
  roomPieceUpdate: ${pms-url.host}/pms/inner/dkf/waiter/room/piece/update
  planHygieneQuery: ${pms-url.host}/pms/inner/dkf/common/plan/hygiene/query
  roomQuery: ${pms-url.host}/pms/inner/dkf/common/room/query
  roomCleanStart: ${pms-url.host}/pms/inner/dkf/common/room/clean/start
  roomCleanEnd: ${pms-url.host}/pms/inner/dkf/common/room/clean/end
  roomCleanCancel: ${pms-url.host}/pms/inner/dkf/common/room/clean/cancel
  roomMe: ${pms-url.host}/pms/inner/dkf/common/room/me
  roomPoolMe: ${pms-url.host}/pms/inner/dkf/common/room/pool/me
  roomStatistical: ${pms-url.host}/pms/inner/dkf/common/room/statistical
  roomQueryByState: ${pms-url.host}/pms/inner/dkf/common/room/queryByState
  personalCenter: ${pms-url.host}/pms/inner/dkf/common/personal/center
  supplyQuery: ${pms-url.host}/pms/inner/dkf/common/supply/query
  guestSuppliesRegister: ${pms-url.host}/pms/inner/dkf/common/guest/supplies/register
  roomCheck: ${pms-url.host}/pms/inner/dkf/manager/room/check
  pieceUserList: ${pms-url.host}/pms/inner/dkf/manager/piece/user/list
  roomCheckList: ${pms-url.host}/pms/inner/dkf/manager/room/check/list
  roomPieceSave: ${pms-url.host}/pms/inner/dkf/manager/room/piece/save
  roomStateUpdate: ${pms-url.host}/pms/inner/dkf/manager/room/state/update
  managerRoomQuery: ${pms-url.host}/pms/inner/dkf/manager/room/query
  token: 39d4b480-5f92-48e8-b602-7248d33f3b2c


crs-url:
  queryMebRoomRateDetailUrl: https://qa0-api-crs.corp.at-our.com/rms/budget/getMebRoomRateDetail
  checkCanAutoApproval: https://qa0-api-crs.corp.at-our.com/protocolFixedPrice/canAutoApproval
  checkCanAutoFloatApproval: https://qa0-api-crs.corp.at-our.com/protocolFixedPrice/canAutoApprovalFloat

crm-url:
  checkRateCodeValidUrl: https://qa-mix-api-crm.corp.at-our.com/inner/corp/checkRateCodeValid
  callBack: https://qa-mix-api-crm.corp.at-our.com/inner/corp/addCorpCrsMapping



hotel-manage:
  url: https://qa0-api-hotel-manage.corp.at-our.com/

#朵客房
dkf:
  systemId: 11
  #客房服务员角色权限ID
  attendant-funcId: 91542
  #客房服务员角色权限(rbac code)
  attendantCode: jimuli_entrance_duoke_server
  #客房经理角色权限ID
  manager-funcId: 9154
  attendant:
    roleId: 2426
#能耗管理
energy:
  #能耗管理-移动端
  app:
    #系统id
    systemId: 102
    #登录权限id
    login-funcId: 1

#几木课堂
school:
  systemId: 98
  #登录权限id
  loginFuncId: 1
  #几木学堂url
  url: http://beta.jmxt.at-our.com/transfer.aspx

# 布房任务系统
room:
  task:
    #系统id
    systemId: 106
    listUrl: atouroms://page/roomTaskList
    loginFuncId: 1
# 全员授权
auth:
  # 全员授权-移动端
  app:
    #系统id
    systemId: 109
    #登录权限id
    login-funcId: 1

# 内控助手
internalAssistant:
  systemId: 148

#工单
workOrder:
  systemId: 134

#企业微信ID
wechatAppId: 1000115
#企业微信业主端提醒ID
wechat:
  teamRoomAppId: 1000115
  ownerRemindAgentId: 1000115
  # 黑金会员行为消息
  blackGoldRemindAgentId: 1000115
  platinumRemindAgentId: 1000115
  miniapp:
    appId: wx2033bf218e04ad79
  internalAssistantAlert: 1000115
  safetyNotifyAppId: 1000115
  #业主端消息推送
  ownerGopPushAppId: 1000115

notify-center-dev:
  ribbon:
    # Max number of retries on the same server (excluding the first try)
    MaxAutoRetries: 0
    # Max number of next servers to retry (excluding the first server)
    MaxAutoRetriesNextServer: 0
    # Whether all operations can be retried for this client
    OkToRetryOnAllOperations: false
    # Interval to refresh the server list from the source
    ServerListRefreshInterval: 1000
    ConnectTimeout: 5000
    ReadTimeout: 10000

#order-center
order-center-dev:
  ribbon:
    # Max number of retries on the same server (excluding the first try)
    MaxAutoRetries: 0
    # Max number of next servers to retry (excluding the first server)
    MaxAutoRetriesNextServer: 0
    # Whether all operations can be retried for this client
    OkToRetryOnAllOperations: false
    # Interval to refresh the server list from the source
    ServerListRefreshInterval: 1000
    ConnectTimeout: 5000
    ReadTimeout: 10000

#order-center
galaxy-dev:
  ribbon:
    # Max number of retries on the same server (excluding the first try)
    MaxAutoRetries: 0
    # Max number of next servers to retry (excluding the first server)
    MaxAutoRetriesNextServer: 0
    # Whether all operations can be retried for this client
    OkToRetryOnAllOperations: false
    # Interval to refresh the server list from the source
    ServerListRefreshInterval: 1000
    ConnectTimeout: 5000
    ReadTimeout: 10000

#user-center
user-center:
  DiscountPublishAuditRemote:
    pool:
      socketTimeout: 30000

#xxl-job配置
xxl:
  job:
    admin:
      ### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
      addresses: https://qa-schedule-corp.at-our.com
    ### xxl-job executor address
    executor:
      appname: hotel-manage-dev
      ip:
      port: 23199
      ### xxl-job log path
      logpath: ${catalina.base}/logs
      ### xxl-job log retention days
      logretentiondays: 10
    ### xxl-job, access token
    accessToken: Zd11EP9Er5TwLwcSSFp1wcr9

#差评整改
ue:
  #差评提醒相关负责人job
  job:
    id:
      yunying:
        jingli: 156   #运营经理
      jiancha:
        jingli: J20190109249   #监察经理
        zhuguan: J20190109251   #监察主管
        zongjian: J20190109250   #监察总监
      shengzhang: 23   #省长
      shengzhengwei: J20180927193   #省政委
      shizhang: 157   #事长
  #权限
  authority:
    #设为已查看
    set-rewiewed: 3400
    #设为未查看
    set-unreview: 3401
    #设为差评
    set-negative: 3402
    #差评处理提交
    negative-process-commit: 3403
    #差评处理审批
    negative-process-approval: 3396
  #系统id
  systemId: 105





budgetProgress:
  api:
    budget-url: https://qa-mix-api-crs.corp.at-our.com/inner/rms/budget/chain
    statistics-url: https://qa-mix-api-crs.corp.at-our.com/inner/rms/statistics/detail

#首页固定展示问题ids
problem:
  ids: 1,2

#mail config
mail:
  server: smtp.feishu.cn
  port: 465
  username: <EMAIL>
  password: OlA5RaUgYkFJK0X1
  notification:
    username: <EMAIL>
    password: iqYWt4sKK7HTgqgd
  corpWechatServerExceptionUrl: https://qa0-application-center.corp.at-our.com/corpWechat/sysException/sendMessageByEmails

#阿里一键登录
ali:
  AccessKeyID: LTAI4Fdh552o4oFMShLaMAM3
  AccessKeySecret: ******************************

#\u4e03\u725b\u7a7a\u9593 七牛空间
qiniu:
  space: atour-dev

#\u4e03\u725b\u56fe\u7247\u670d\u52a1\u5730\u5740
image:
  qiniu:
    server: http://atour-dev.qiniu.yaduo.com/
  image:
    server: http://***************/share_file/upload_image/

#业主端展示权限id
show:
  job:
    ids: 23,32,J20180927193,J20181026209
    managerIds: J20181026209,32

# 朵客房，几木学堂，能耗管理
owner:
  systemIds: 11,98,102,106,109,136
  bill:
    limit:
      version: 1.3.9
  homeQuestionnaireTip:
    moths: 1,4,7,10
    days: 3,8,13,18,23,28

appstore:
  audit:
    mobile: ***********
    verifycode: 8888


#区分环境bundleId
bundle:
  owner: com.atour.business.owner.test,com.atour.owner.test
  duo: com.atour.duo.test,com.atour.jmlapp.test
#区分环境user-agent
agent:
  owner: atourOMS
  jml: atourJML

#优惠券
coupon:
  systemId: 108
  publishFuncId: 5
  auditFuncId: 8
  popularFuncId: 6

#ES
es:
  uris: http://**************:9200
  username: admin
  password: d230a116a1953b0eff21
  read:
    timeout: 30000

  member:
    index: gdl_mkt_dmp_member_test
  order:
    index: gdl_mkt_dmp_order_test
  non:
    member:
      index: gdl_user_recall
  recall:
    member:
      index: gdl_user_recall_new

  cluster1:
    # **************,**************,**************
    uris:
      - http://**************:9200
      - http://**************:9200
      - http://**************:9200
    username: admin
    password: d230a116a1953b0eff21
    conn-timeout: 1500
    read-timeout: 3000
  cluster2:
    # **************,**************,**************
    uris:
      - https://qa-es6.corp.at-our.com
    username: elastic
    password: yaduo321
    conn-timeout: 1500
    read-timeout: 3000

security-api-name: security-center-beta
#单点登录接入系统
asso:
  system: OMS

#aliyun rocketmq
profile: dev
order:
  message:
    topic: atour_order

#移动审批
approve:
  #移动审批-移动端
  app:
    #系统id
    systemId: 132
    #审批页面地址
    url: https://qa-approve2.corp.at-our.com
manager:
  role:
    districtSalesManager-role-id: J20190827393
    #收益管理高级总监
    syglgjzj-role-id: 760

message:
  host: https://qa-manage-pms.at-our.com/


#日常任务
taskCenter:
  #日常任务-移动端
  app:
    #系统id
    systemId: 142
    #待办页面地址
    url: https://qa-manage-pms.at-our.com/taskList
  warn:
    #企微消息appid
    appId: 1000115
    #收益组
    revenueEmailGroup: <EMAIL>
#KPI管理
kpiMgr:
  systemId: 144
  app:
    systemId: 145

#问卷系统三方校验
thirdCheck:
  owner:
    channel: OWNER
    keySign: de06d20060c639c59c42737ddf002535
xian:
  zhang:
    role:
      id: 215,311,340,680,249

#会议室推送兼职人员jobid 现长 储备现长 现政委
meeting:
  push:
    job:
      id: 32,128,J20181026209

#erp系统域名，新增机器需配置白名单
erp:
  domain: https://qa-api-xbu-erp.corp.at-our.com/
#废弃 已对接rbac接口
##EHR数据源,不配置spring数据源,单独配置,不影响项目启动及主业务流程
#ehr:
#  data:
#    source:
#      url: ********************************************************
#      username: rbac_reader_1433
#      password: GYN&&*Be4576f1
#      tablename: rbac_gmbonus_a8rzgj
#oms的web端的前端host
oms:
  web:
    host: https://qa-oms.at-our.com

app_id: hotel-manage
mq:
  topic:
    taskCenterBiz: topic_task_center_message
newFeedback:
  hlmUrl: https://qa0-hlm.corp.at-our.com/owner

fastdep:
  redis:
    redis-asso:
      database: 1
      host: qa-redis.at-our.com
      port: 6379
      password: Atour2017
      lettuce: #下面为连接池的补充设置
        shutdown-timeout: 100 # 关闭超时时间
        pool:
          max-active: 18 # 连接池最大连接数（使用负值表示没有限制）
          max-idle: 8 # 连接池中的最大空闲连接
          max-wait: 30 # 连接池最大阻塞等待时间（使用负值表示没有限制）
          min-idle: 0 # 连接池中的最小空闲连接
golden:
  butterfly:
    appid: OMS
    appsecret: OmsSys@********@OmsSys
    host: https://yaduo.test.kdcloud.com/
    userName: xitongyonghu11
    accountId: 1977751652429463552
    mulcostaccount: CBZB-0001



mybatis-plus:
  configuration:
    # MyBatis 配置
    map-underscore-to-camel-case: true
  global-config:
    # 全局配置
    db-config:
      # 数据库配置
      id-type: auto
  config-location: classpath:/mybatis-config.xml
  mapper-locations: classpath*:/mapper/**/*.xml

large:
  size:
    sql:
      mail: server:smtp.feishu.cn
      password : OlA5RaUgYkFJK0X1
bigdata:
  request:
    url: https://bigdata-api.yaduo.com/bigdatadev/dev/