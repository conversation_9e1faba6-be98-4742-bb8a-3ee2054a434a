app-repository-service.ribbon.DeploymentContextBasedVipAddresses=APP-REPOSITORY-SERVICE
app-repository-service.ribbon.NIWSServerListClassName=com.netflix.niws.loadbalancer.DiscoveryEnabledNIWSServerList
app-repository-service.ribbon.ServerListRefreshInterval=5000
# 请求连接的超时时间
app-repository-service.ribbon.ConnectTimeout=10000
# 请求处理的超时时间
app-repository-service.ribbon.ReadTimeout=10000
# 可以再重试几台机器
app-repository-service.ribbon.MaxAutoRetriesNextServer=0
#同一台实例最大重试次数,不包括首次调用（对当前实例的重试次数）
app-repository-service.ribbon.MaxAutoRetries=0