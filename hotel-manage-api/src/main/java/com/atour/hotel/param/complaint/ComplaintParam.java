package com.atour.hotel.param.complaint;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年01月02日22:08
 * @since JDK1.8
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ComplaintParam implements Serializable {
    private static final long serialVersionUID = 468230071855800555L;

    /**
     * 酒店id
     */
    @NotNull(message = "酒店ID 不能为空")
    private Integer chainId;

    /**
     * 点评开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commentStatTime;

    /**
     * 点评截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commentEndTime;

    /**
     * 处理状态
     * com.atour.hotel.module.ue.manage.comment.sql.param.ProcessState
     */
    private Integer processState;

    /**
     * 是否超时 1:超时 0:未超时
     */
    private Integer processTimeoutFlag;

    /**
     * 来源
     * com.atour.hotel.module.ue.manage.comment.enums.HuiPingSourceTypeEnum
     */
    private Integer sourceType;
}
