package com.atour.hotel.param.conllection;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @program: hotel-manage-app-api
 * @ClassName ReciveCollectionParam
 * @description:接收财务催收请求入参
 * @author: zhu<PERSON>
 * @create: 2024-11-22 16:29
 * @Version 1.0
 **/
@Data
public class ReciveCollectionParam {
    /**
     * 催收id
     */
    @NotEmpty
    private List<Long> financeCollectionId;


}
