package com.atour.hotel.param.supplies;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SuppliesCategorizeSyncParam implements Serializable {

    /**
     * 商品分类id
     */
    private Long categorizeId;

    /**
     * 商品分类名称
     */
    private String categorizeName;

    /**
     * 商品分类code
     */
    private String categorizeCode;


    /**
     * 父商品分类id 如果是顶级请传0
     */
    private Long parentCategorizeId;

}
