package com.atour.hotel.param.blacklist;

import java.io.Serializable;

/**
 * 黑榜web 查询
 */
public class DZJBlackListWebPageParam implements Serializable {


    /**
     * 区域id
     */
    private Integer regionId;


    /**
     * 酒店id
     */
    private Integer hotelId;



    /**
     * 执法开始日期
     */
    private String lawEnforcementTimeStart;


    /**
     * 执法结束日期
     */
    private String lawEnforcementTimeEnd;


    /**
     * 执法人
     */
    private String lawEnforcementEmployeeId;



    /**
     * 黑榜状态：1:整改方案 2：整改方案确认 3：整改落地 4：待检核 5：检核通过 6：检核不通过  7：已取消
     */
    private Integer blackListStatus;

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getHotelId() {
        return hotelId;
    }

    public void setHotelId(Integer hotelId) {
        this.hotelId = hotelId;
    }

    public String getLawEnforcementTimeStart() {
        return lawEnforcementTimeStart;
    }

    public void setLawEnforcementTimeStart(String lawEnforcementTimeStart) {
        this.lawEnforcementTimeStart = lawEnforcementTimeStart;
    }

    public String getLawEnforcementTimeEnd() {
        return lawEnforcementTimeEnd;
    }

    public void setLawEnforcementTimeEnd(String lawEnforcementTimeEnd) {
        this.lawEnforcementTimeEnd = lawEnforcementTimeEnd;
    }

    public String getLawEnforcementEmployeeId() {
        return lawEnforcementEmployeeId;
    }

    public void setLawEnforcementEmployeeId(String lawEnforcementEmployeeId) {
        this.lawEnforcementEmployeeId = lawEnforcementEmployeeId;
    }

    public Integer getBlackListStatus() {
        return blackListStatus;
    }

    public void setBlackListStatus(Integer blackListStatus) {
        this.blackListStatus = blackListStatus;
    }
}
