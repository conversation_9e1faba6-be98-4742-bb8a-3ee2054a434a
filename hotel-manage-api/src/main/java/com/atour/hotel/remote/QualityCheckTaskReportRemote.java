package com.atour.hotel.remote;

import com.atour.eureka.spring.common.constants.HttpConstants;
import com.atour.hotel.dto.qualitycheck.QualityCheckReportPageVO;
import com.atour.hotel.param.qualitycheck.QualityReportQueryPageParam;
import com.yaduo.infras.core.base.bean.AtourRequest;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.RequestList;
import com.yaduo.infras.core.base.bean.ResponseList;
import feign.Headers;
import feign.RequestLine;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/28
 */
public interface QualityCheckTaskReportRemote {


    /**
     * 朵质检-报表
     */
    @Headers(HttpConstants.HEADER_CONTENT_TYPE_JSON)
    @RequestLine("POST /hotel/inner/api/web/quality/report/query/page")
    AtourResponse<ResponseList<QualityCheckReportPageVO>> qualityReportPage(@Validated @RequestBody AtourRequest<RequestList<QualityReportQueryPageParam>> request);
    /**
     * 朵质检-报表
     */
    @Headers(HttpConstants.HEADER_CONTENT_TYPE_JSON)
    @RequestLine("POST /hotel/inner/api/web/quality/report/query/export")
    AtourResponse<ResponseList<QualityCheckReportPageVO>> exportReportPage(@Validated @RequestBody AtourRequest<RequestList<QualityReportQueryPageParam>> request);
}
