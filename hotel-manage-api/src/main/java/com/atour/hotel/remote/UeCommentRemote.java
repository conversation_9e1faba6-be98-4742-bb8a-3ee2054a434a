package com.atour.hotel.remote;

import com.atour.eureka.spring.common.constants.HttpConstants;
import com.atour.hotel.dto.ueComment.RemindUeCommentDTO;
import com.yaduo.infras.core.base.bean.AtourResponse;
import com.yaduo.infras.core.base.bean.ResponseList;
import feign.Headers;
import feign.RequestLine;

import java.util.List;

public interface UeCommentRemote {

    /**
     * 获取需要提醒的一键吐槽列表
     * @return
     */
    @Headers(HttpConstants.HEADER_CONTENT_TYPE_JSON)
    @RequestLine("POST /hotel/inner/api/web/comment/remindUeCommentList")
    AtourResponse<List<RemindUeCommentDTO>> remindUeCommentList();




}
