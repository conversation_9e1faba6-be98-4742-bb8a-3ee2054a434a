package com.atour.hotel.dto.black;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 黑榜app列表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlackAppPageDTO implements Serializable {


    /**
     * 黑榜id
     */
    private Long blackListId;


    /**
     * 酒店id
     */
    private Integer hotelId;


    /**
     * 酒店名称
     */
    private String hotelName;


    /**
     * 执法人员工id
     */
    private String lawEnforcementEmployeeId;


    /**
     * 执法人花名
     */
    private String lawEnforcementFlowerName;

    /**
     * law_enforcement_time
     */
    private String lawEnforcementTime;


    /**
     * 黑榜状态 ：1:整改方案 2：整改方案确认 3：整改落地 4：待检核 5：检核通过 6：检核不通过  7：已取消
     */
    private Integer blackListStatus;


    /**
     * 黑榜状态view
     */
    private String blackListStatusView;


    /**
     * 核检结果 ：1:待检核 2:通过 3:未通过
     */
    private Integer reviewStatus;

    /**
     * 核检结果view
     */
    private String reviewStatusView;


}
