package com.atour.hotel.dto.qualitycheck;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */

@Data
public class QualityCheckTaskWebQueryPageVO {

    /**
     * 战区
     */
    private String regionName;

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 酒店名称
     */
    private String chainName;

    /**
     * 任务检查ID
     */
    private Long checkTaskId;

    /**
     * 检查任务配置ID
     */
    private Long checkTaskConfigId;

    /**
     * 检查任务
     */
    private String checkTaskConfigName;

    /**
     * 检查设备
     */
    private Long checkDeviceId;

    /**
     * 检查设备
     */
    private String checkDeviceName;

    /**
     * 抽查数
     */
    private Integer checkNum;

    /**
     * 合格数
     */
    private Integer passNum;

    /**
     * 合格率
     */
    private String passRate;

    /**
     * 是否清零
     */
    private Integer resetZero;

    /**
     * 是否清零
     */
    private String resetZeroView;

    /**
     * 检查日期
     * yyyy-MM-dd
     */
    private String checkDate;

    /**
     * 检查状态 0:未查 1:已申诉 2:未整改 3:已通过 4:已整改
     */
    private Integer checkStatus;

    /**
     * 检查状态 0:未查 1:已申诉 2:未整改 3:已通过 4:已整改
     */
    private String checkStatusView;

    /**
     * 申诉状态 0：未申诉 1：已申诉 2：申诉通过 3：申诉驳回
     */
    private Integer appealStatus;

    /**
     * 申诉状态
     */
    private String appealStatusView;

    /**
     * 检查月份
     */
    private String createDate;
}
