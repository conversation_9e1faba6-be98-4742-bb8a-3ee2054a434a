package com.atour.hotel.dto.black;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 黑榜规则员工
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeAllDTO implements Serializable {


    /**
     * 配置id
     */
    private Long id;

    /**
     * 员工id
     */
    private String employeeId;


    /**
     * 员工花名
     */
    private String flowerName;


    /**
     * 邮箱
     */
    private String email;


}
