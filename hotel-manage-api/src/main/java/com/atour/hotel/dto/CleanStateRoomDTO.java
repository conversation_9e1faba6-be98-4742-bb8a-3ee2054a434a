package com.atour.hotel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年05月06日14:29
 * @since JDK1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CleanStateRoomDTO implements Serializable {
    private static final long serialVersionUID = -6011974056524908894L;

    /**
     * 酒店ID
     */
    private Integer chainId;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 打扫人ID
     */
    private String cleanUserId;

    /**
     * 打扫人名称
     */
    private String cleanUserName;

    /**
     * 上次大清日期
     */
    private Date latestCleanDate;

    /**
     * 排房状态 1-已排 2-取消排房 3-排房且打扫完成 4-已排房打扫中
     */
    private Integer status;

    /**
     * 营业日
     */
    private Date accDate;
}
