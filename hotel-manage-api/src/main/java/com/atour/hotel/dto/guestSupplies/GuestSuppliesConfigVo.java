package com.atour.hotel.dto.guestSupplies;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 客用品配置信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuestSuppliesConfigVo {

    private Integer id;

    private Integer createUserId;

    private Integer updateUserId;

    private String name;

    private Date updateTime;

    private Date createTime;

    private Integer deleteFlag;

    /**
     * 客用品分类：0:客用品，1:客用水
     * @see com.atour.hotel.module.dkf.enums.SuppliesTypeEnum
     */
    private Integer suppliesType;

    /**
     * 客用品来源 1-门店自采；2：总部商品
     * @see com.atour.hotel.module.dkf.enums.SuppliesSourceEnum
     */
    private Integer suppliesSource;

    /**
     * 总部商品客用品skuno
     */
    private String suppliesSkuno;

    /**
     * 商品分类ID
     */
    private Long secondCategoryId;

    /**
     * 商品分类名称
     */
    private String secondCategoryName;

    /**
     * 酒店品牌
     */
    private String hotelBrands;
}
